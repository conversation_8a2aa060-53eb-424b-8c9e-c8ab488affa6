var _class,_class4,_excluded=["progressiveUrl","url"];function _objectWithoutProperties(e,t){if(null==e)return{};var r,n=_objectWithoutPropertiesLoose(e,t);if(Object.getOwnPropertySymbols)for(var a=Object.getOwnPropertySymbols(e),i=0;i<a.length;i++)r=a[i],0<=t.indexOf(r)||Object.prototype.propertyIsEnumerable.call(e,r)&&(n[r]=e[r]);return n}function _objectWithoutPropertiesLoose(e,t){if(null==e)return{};for(var r,n={},a=Object.keys(e),i=0;i<a.length;i++)r=a[i],0<=t.indexOf(r)||(n[r]=e[r]);return n}function _objectDestructuringEmpty(e){if(null==e)throw new TypeError("Cannot destructure "+e)}function _extends(){return(_extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r,n=arguments[t];for(r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function _get(){return(_get="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,r){var n=_superPropBase(e,t);if(n){n=Object.getOwnPropertyDescriptor(n,t);return n.get?n.get.call(arguments.length<3?e:r):n.value}}).apply(this,arguments)}function _superPropBase(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=_getPrototypeOf(e)););return e}function _toArray(e){return _arrayWithHoles(e)||_iterableToArray(e)||_unsupportedIterableToArray(e)||_nonIterableRest()}function _wrapNativeSuper(e){var r="function"==typeof Map?new Map:void 0;return(_wrapNativeSuper=function(e){if(null===e||!_isNativeFunction(e))return e;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==r){if(r.has(e))return r.get(e);r.set(e,t)}function t(){return _construct(e,arguments,_getPrototypeOf(this).constructor)}return t.prototype=Object.create(e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),_setPrototypeOf(t,e)})(e)}function _construct(e,t,r){return(_construct=_isNativeReflectConstruct()?Reflect.construct.bind():function(e,t,r){var n=[null];n.push.apply(n,t);n=new(Function.bind.apply(e,n));return r&&_setPrototypeOf(n,r.prototype),n}).apply(null,arguments)}function _isNativeFunction(t){try{return-1!==Function.toString.call(t).indexOf("[native code]")}catch(e){return"function"==typeof t}}function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _createSuper(r){var n=_isNativeReflectConstruct();return function(){var e,t=_getPrototypeOf(r);return _possibleConstructorReturn(this,n?(e=_getPrototypeOf(this).constructor,Reflect.construct(t,arguments,e)):t.apply(this,arguments))}}function _possibleConstructorReturn(e,t){if(t&&("object"===_typeof(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return _assertThisInitialized(e)}function _assertThisInitialized(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}function _getPrototypeOf(e){return(_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function _regeneratorRuntime(){"use strict";_regeneratorRuntime=function(){return o};var u,o={},e=Object.prototype,c=e.hasOwnProperty,l=Object.defineProperty||function(e,t,r){e[t]=r.value},t="function"==typeof Symbol?Symbol:{},n=t.iterator||"@@iterator",r=t.asyncIterator||"@@asyncIterator",a=t.toStringTag||"@@toStringTag";function i(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{i({},"")}catch(u){i=function(e,t,r){return e[t]=r}}function s(e,t,r,n){var a,i,o,s,t=t&&t.prototype instanceof v?t:v,t=Object.create(t.prototype),n=new k(n||[]);return l(t,"_invoke",{value:(a=e,i=r,o=n,s=p,function(e,t){if(s===h)throw new Error("Generator is already running");if(s===m){if("throw"===e)throw t;return{value:u,done:!0}}for(o.method=e,o.arg=t;;){var r=o.delegate;if(r){var n=function e(t,r){var n=r.method,a=t.iterator[n];if(a===u)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=u,e(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),g;a=d(a,t.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,g;var a=a.arg;return a?a.done?(r[t.resultName]=a.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=u),r.delegate=null,g):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,g)}(r,o);if(n){if(n===g)continue;return n}}if("next"===o.method)o.sent=o._sent=o.arg;else if("throw"===o.method){if(s===p)throw s=m,o.arg;o.dispatchException(o.arg)}else"return"===o.method&&o.abrupt("return",o.arg);s=h;n=d(a,i,o);if("normal"===n.type){if(s=o.done?m:f,n.arg===g)continue;return{value:n.arg,done:o.done}}"throw"===n.type&&(s=m,o.method="throw",o.arg=n.arg)}})}),t}function d(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}o.wrap=s;var p="suspendedStart",f="suspendedYield",h="executing",m="completed",g={};function v(){}function b(){}function y(){}var _={};i(_,n,function(){return this});t=Object.getPrototypeOf,t=t&&t(t(R([])));t&&t!==e&&c.call(t,n)&&(_=t);var w=y.prototype=v.prototype=Object.create(_);function E(e){["next","throw","return"].forEach(function(t){i(e,t,function(e){return this._invoke(t,e)})})}function T(o,s){var t;l(this,"_invoke",{value:function(r,n){function e(){return new s(function(e,t){!function t(e,r,n,a){e=d(o[e],o,r);if("throw"!==e.type){var i=e.arg,r=i.value;return r&&"object"==_typeof(r)&&c.call(r,"__await")?s.resolve(r.__await).then(function(e){t("next",e,n,a)},function(e){t("throw",e,n,a)}):s.resolve(r).then(function(e){i.value=e,n(i)},function(e){return t("throw",e,n,a)})}a(e.arg)}(r,n,e,t)})}return t=t?t.then(e,e):e()}})}function S(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function x(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function k(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(S,this),this.reset(!0)}function R(t){if(t||""===t){var e=t[n];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var r=-1,e=function e(){for(;++r<t.length;)if(c.call(t,r))return e.value=t[r],e.done=!1,e;return e.value=u,e.done=!0,e};return e.next=e}}throw new TypeError(_typeof(t)+" is not iterable")}return l(w,"constructor",{value:b.prototype=y,configurable:!0}),l(y,"constructor",{value:b,configurable:!0}),b.displayName=i(y,a,"GeneratorFunction"),o.isGeneratorFunction=function(e){e="function"==typeof e&&e.constructor;return!!e&&(e===b||"GeneratorFunction"===(e.displayName||e.name))},o.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,y):(e.__proto__=y,i(e,a,"GeneratorFunction")),e.prototype=Object.create(w),e},o.awrap=function(e){return{__await:e}},E(T.prototype),i(T.prototype,r,function(){return this}),o.AsyncIterator=T,o.async=function(e,t,r,n,a){void 0===a&&(a=Promise);var i=new T(s(e,t,r,n),a);return o.isGeneratorFunction(t)?i:i.next().then(function(e){return e.done?e.value:i.next()})},E(w),i(w,a,"Generator"),i(w,n,function(){return this}),i(w,"toString",function(){return"[object Generator]"}),o.keys=function(e){var t,r=Object(e),n=[];for(t in r)n.push(t);return n.reverse(),function e(){for(;n.length;){var t=n.pop();if(t in r)return e.value=t,e.done=!1,e}return e.done=!0,e}},o.values=R,k.prototype={constructor:k,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=u,this.done=!1,this.delegate=null,this.method="next",this.arg=u,this.tryEntries.forEach(x),!e)for(var t in this)"t"===t.charAt(0)&&c.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=u)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(r){if(this.done)throw r;var n=this;function e(e,t){return i.type="throw",i.arg=r,n.next=e,t&&(n.method="next",n.arg=u),!!t}for(var t=this.tryEntries.length-1;0<=t;--t){var a=this.tryEntries[t],i=a.completion;if("root"===a.tryLoc)return e("end");if(a.tryLoc<=this.prev){var o=c.call(a,"catchLoc"),s=c.call(a,"finallyLoc");if(o&&s){if(this.prev<a.catchLoc)return e(a.catchLoc,!0);if(this.prev<a.finallyLoc)return e(a.finallyLoc)}else if(o){if(this.prev<a.catchLoc)return e(a.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return e(a.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;0<=r;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&c.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var a=n;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=e,i.arg=t,a?(this.method="next",this.next=a.finallyLoc,g):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),g},finish:function(e){for(var t=this.tryEntries.length-1;0<=t;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),x(r),g}},catch:function(e){for(var t=this.tryEntries.length-1;0<=t;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n,a=r.completion;return"throw"===a.type&&(n=a.arg,x(r)),n}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,r){return this.delegate={iterator:R(e),resultName:t,nextLoc:r},"next"===this.method&&(this.arg=u),g}},o}function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function _defineProperties(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,_toPropertyKey(n.key),n)}}function _createClass(e,t,r){return t&&_defineProperties(e.prototype,t),r&&_defineProperties(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function _createForOfIteratorHelper(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=_unsupportedIterableToArray(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,t=function(){};return{s:t,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:t}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,i=!0,o=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return i=e.done,e},e:function(e){o=!0,a=e},f:function(){try{i||null==r.return||r.return()}finally{if(o)throw a}}}}function asyncGeneratorStep(e,t,r,n,a,i,o){try{var s=e[i](o),u=s.value}catch(e){return void r(e)}s.done?t(u):Promise.resolve(u).then(n,a)}function _asyncToGenerator(s){return function(){var e=this,o=arguments;return new Promise(function(t,r){var n=s.apply(e,o);function a(e){asyncGeneratorStep(n,t,r,a,i,"next",e)}function i(e){asyncGeneratorStep(n,t,r,a,i,"throw",e)}a(void 0)})}}function _wrapRegExp(){_wrapRegExp=function(e,t){return new n(e,void 0,t)};var a=RegExp.prototype,o=new WeakMap;function n(e,t,r){t=new RegExp(e,t);return o.set(t,r||o.get(e)),_setPrototypeOf(t,n.prototype)}function i(a,e){var i=o.get(e);return Object.keys(i).reduce(function(e,t){var r=i[t];if("number"==typeof r)e[t]=a[r];else{for(var n=0;void 0===a[r[n]]&&n+1<r.length;)n++;e[t]=a[r[n]]}return e},Object.create(null))}return _inherits(n,RegExp),n.prototype.exec=function(e){var t=a.exec.call(this,e);return t&&(t.groups=i(t,this),(e=t.indices)&&(e.groups=i(e,this))),t},n.prototype[Symbol.replace]=function(e,t){if("string"==typeof t){var r=o.get(this);return a[Symbol.replace].call(this,e,t.replace(/\$<([^>]+)>/g,function(e,t){t=r[t];return"$"+(Array.isArray(t)?t.join("$"):t)}))}if("function"!=typeof t)return a[Symbol.replace].call(this,e,t);var n=this;return a[Symbol.replace].call(this,e,function(){var e=arguments;return"object"!=_typeof(e[e.length-1])&&(e=[].slice.call(e)).push(i(e,n)),t.apply(this,e)})},_wrapRegExp.apply(this,arguments)}function _inherits(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&_setPrototypeOf(e,t)}function _setPrototypeOf(e,t){return(_setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function ownKeys(t,e){var r,n=Object.keys(t);return Object.getOwnPropertySymbols&&(r=Object.getOwnPropertySymbols(t),e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)),n}function _objectSpread(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ownKeys(Object(r),!0).forEach(function(e){_defineProperty(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ownKeys(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function _defineProperty(e,t,r){return(t=_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function _toPropertyKey(e){e=_toPrimitive(e,"string");return"symbol"===_typeof(e)?e:String(e)}function _toPrimitive(e,t){if("object"!==_typeof(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0===r)return("string"===t?String:Number)(e);t=r.call(e,t||"default");if("object"!==_typeof(t))return t;throw new TypeError("@@toPrimitive must return a primitive value.")}function _toConsumableArray(e){return _arrayWithoutHoles(e)||_iterableToArray(e)||_unsupportedIterableToArray(e)||_nonIterableSpread()}function _nonIterableSpread(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _iterableToArray(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function _arrayWithoutHoles(e){if(Array.isArray(e))return _arrayLikeToArray(e)}function _slicedToArray(e,t){return _arrayWithHoles(e)||_iterableToArrayLimit(e,t)||_unsupportedIterableToArray(e,t)||_nonIterableRest()}function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return _arrayLikeToArray(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?_arrayLikeToArray(e,t):void 0}}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function _iterableToArrayLimit(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,a,i,o,s=[],u=!0,c=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(s.push(n.value),s.length!==t);u=!0);}catch(e){c=!0,a=e}finally{try{if(!u&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(c)throw a}}return s}}function _arrayWithHoles(e){if(Array.isArray(e))return e}function _asyncIterator(e){var t,r,n,a=2;for("undefined"!=typeof Symbol&&(r=Symbol.asyncIterator,n=Symbol.iterator);a--;){if(r&&null!=(t=e[r]))return t.call(e);if(n&&null!=(t=e[n]))return new AsyncFromSyncIterator(t.call(e));r="@@asyncIterator",n="@@iterator"}throw new TypeError("Object is not async iterable")}function AsyncFromSyncIterator(e){function r(e){if(Object(e)!==e)return Promise.reject(new TypeError(e+" is not an object."));var t=e.done;return Promise.resolve(e.value).then(function(e){return{value:e,done:t}})}return(AsyncFromSyncIterator=function(e){this.s=e,this.n=e.next}).prototype={s:null,n:null,next:function(){return r(this.n.apply(this.s,arguments))},return:function(e){var t=this.s.return;return void 0===t?Promise.resolve({value:e,done:!0}):r(t.apply(this.s,arguments))},throw:function(e){var t=this.s.return;return void 0===t?Promise.reject(e):r(t.apply(this.s,arguments))}},new AsyncFromSyncIterator(e)}var SEARCHBOX_UA_REGEX=/alexa|bot|crawl(er|ing)|facebookexternalhit|feedburner|google web preview|nagios|postrank|pingdom|slurp|spider|yahoo!|yandex/,SEARCHBOT_OS_REGEX=/(nuhk|curl|Googlebot|Yammybot|Openbot|Slurp|MSNBot|Ask\ Jeeves\/Teoma|ia_archiver)/,REQUIRED_VERSION_PARTS=3,userAgentRules=[["aol",/AOLShield\/([0-9\._]+)/],["edge",/Edge\/([0-9\._]+)/],["edge-ios",/EdgiOS\/([0-9\._]+)/],["yandexbrowser",/YaBrowser\/([0-9\._]+)/],["kakaotalk",/KAKAOTALK\s([0-9\.]+)/],["samsung",/SamsungBrowser\/([0-9\.]+)/],["silk",/\bSilk\/([0-9._-]+)\b/],["miui",/MiuiBrowser\/([0-9\.]+)$/],["beaker",/BeakerBrowser\/([0-9\.]+)/],["edge-chromium",/EdgA?\/([0-9\.]+)/],["chromium-webview",/(?!Chrom.*OPR)wv\).*Chrom(?:e|ium)\/([0-9\.]+)(:?\s|$)/],["chrome",/(?!Chrom.*OPR)Chrom(?:e|ium)\/([0-9\.]+)(:?\s|$)/],["phantomjs",/PhantomJS\/([0-9\.]+)(:?\s|$)/],["crios",/CriOS\/([0-9\.]+)(:?\s|$)/],["firefox",/Firefox\/([0-9\.]+)(?:\s|$)/],["fxios",/FxiOS\/([0-9\.]+)/],["opera-mini",/Opera Mini.*Version\/([0-9\.]+)/],["opera",/Opera\/([0-9\.]+)(?:\s|$)/],["opera",/OPR\/([0-9\.]+)(:?\s|$)/],["pie",/^Microsoft Pocket Internet Explorer\/(\d+\.\d+)$/],["pie",/^Mozilla\/\d\.\d+\s\(compatible;\s(?:MSP?IE|MSInternet Explorer) (\d+\.\d+);.*Windows CE.*\)$/],["netfront",/^Mozilla\/\d\.\d+.*NetFront\/(\d.\d)/],["ie",/Trident\/7\.0.*rv\:([0-9\.]+).*\).*Gecko$/],["ie",/MSIE\s([0-9\.]+);.*Trident\/[4-7].0/],["ie",/MSIE\s(7\.0)/],["bb10",/BB10;\sTouch.*Version\/([0-9\.]+)/],["android",/Android\s([0-9\.]+)/],["ios",/Version\/([0-9\._]+).*Mobile.*Safari.*/],["safari",/Version\/([0-9\._]+).*Safari/],["facebook",/FB[AS]V\/([0-9\.]+)/],["instagram",/Instagram\s([0-9\.]+)/],["ios-webview",/AppleWebKit\/([0-9\.]+).*Mobile/],["ios-webview",/AppleWebKit\/([0-9\.]+).*Gecko\)$/],["curl",/^curl\/([0-9\.]+)$/],["searchbot",SEARCHBOX_UA_REGEX]],operatingSystemRules=[["iOS",/iP(hone|od|ad)/],["Android OS",/Android/],["BlackBerry OS",/BlackBerry|BB10/],["Windows Mobile",/IEMobile/],["Amazon OS",/Kindle/],["Windows 3.11",/Win16/],["Windows 95",/(Windows 95)|(Win95)|(Windows_95)/],["Windows 98",/(Windows 98)|(Win98)/],["Windows 2000",/(Windows NT 5.0)|(Windows 2000)/],["Windows XP",/(Windows NT 5.1)|(Windows XP)/],["Windows Server 2003",/(Windows NT 5.2)/],["Windows Vista",/(Windows NT 6.0)/],["Windows 7",/(Windows NT 6.1)/],["Windows 8",/(Windows NT 6.2)/],["Windows 8.1",/(Windows NT 6.3)/],["Windows 10",/(Windows NT 10.0)/],["Windows ME",/Windows ME/],["Windows CE",/Windows CE|WinCE|Microsoft Pocket Internet Explorer/],["Open BSD",/OpenBSD/],["Sun OS",/SunOS/],["Chrome OS",/CrOS/],["Linux",/(Linux)|(X11)/],["Mac OS",/(Mac_PowerPC)|(Macintosh)/],["QNX",/QNX/],["BeOS",/BeOS/],["OS/2",/OS\/2/]];function matchUserAgent(n){return""!==n&&userAgentRules.reduce(function(e,t){var r=t[0],t=t[1];if(e)return e;t=t.exec(n);return!!t&&[r,t]},!1)}function detectOS(e){for(var t=0,r=operatingSystemRules.length;t<r;t++){var n=_slicedToArray(operatingSystemRules[t],2),a=n[0];if(n[1].exec(e))return a}return null}function parseUserAgent(e){var t=matchUserAgent(e);if(!t)return null;var r=t[0],n=t[1],t=n[1]&&n[1].split(".").join("_").split("_").slice(0,3);t?t.length<REQUIRED_VERSION_PARTS&&(t=[].concat(_toConsumableArray(t),_toConsumableArray(function(e){for(var t=[],r=0;r<e;r++)t.push("0");return t}(REQUIRED_VERSION_PARTS-t.length)))):t=[];n=t.join("."),t=detectOS(e),e=SEARCHBOT_OS_REGEX.exec(e);return e&&e[1]?{browser:r,version:n,os:t,bot:e[1]}:{browser:r,version:n,os:t}}var browser,isChrome=!navigator.userAgent.includes("Edg")&&!navigator.userAgent.includes("OPR")&&(navigator.userAgent.includes("Chrome")||navigator.userAgent.includes("Crios")),chromeVersion=isChrome&&Number((navigator.userAgent.match(/(?:Chrome|Crios)\/(\d+)/)||[])[1]||0)||0;navigator.userAgent.includes("Chrome")&&("function"==typeof Window?window.browser=chrome:browser=chrome);var extension_source="chrome",enable_yt=!1,enable_check_update=!1,freeLimit={hqDownload:{maxCount:999,hours:1},streamDownload:{maxCount:999,hours:1},videoRecord:{maxCount:999,hours:1},audioRecord:{maxCount:999,hours:1},tabRecord:{maxCount:999,hours:1},screenRecord:{maxCount:999,hours:1}},configuration={BRAND:"VidHelper",DOMAIN:"https://vidhelper.app",FAQ:"https://vidhelper.app#faqs",MEASUREMENT_ID:"G-LNBWCVYXVX",API_SECRET:"vzBAqqNRSb6ugOR-mqf6CA",LICENSE_BASEURL:"https://vidhelper.app/api",CHECK_FOR_UPDATE:enable_check_update,UPDATE_INTERVAL:36e5,CHECK_UPDATE_URL:"https://app.vidhelper.app/latest",HOWTO_INSTAL_URL:"https://vidhelper.app/how-to-manually-install-vidhelper-extension",STORE_ID:19749,PRODUCT_IDS:[170497,170523,170502,170524,170494,170522],PRODUCT:{name:browser.i18n.getMessage("Pro"),features:[{name:browser.i18n.getMessage("UnlimitMediaDL"),free:browser.i18n.getMessage("Yes"),paid:browser.i18n.getMessage("Yes")},{name:browser.i18n.getMessage("sitesSupported"),free:browser.i18n.getMessage("Yes"),paid:browser.i18n.getMessage("Yes")},{name:browser.i18n.getMessage("1080pSupport"),free:browser.i18n.getMessage("NumEveryHours").replace("{{1}}",freeLimit.hqDownload.maxCount).replace("{{2}}",freeLimit.hqDownload.hours),paid:browser.i18n.getMessage("Unlimited")},{name:browser.i18n.getMessage("NumStreamLimit"),free:browser.i18n.getMessage("NumEveryHours").replace("{{1}}",freeLimit.streamDownload.maxCount).replace("{{2}}",freeLimit.streamDownload.hours),paid:browser.i18n.getMessage("Unlimited")},{name:browser.i18n.getMessage("EachRecordLimit"),free:browser.i18n.getMessage("NumEveryHours").replace("{{1}}",freeLimit.videoRecord.maxCount).replace("{{2}}",freeLimit.videoRecord.hours),paid:browser.i18n.getMessage("Unlimited")},{name:browser.i18n.getMessage("hlsDashMerge"),free:browser.i18n.getMessage("No"),paid:browser.i18n.getMessage("Yes")}],discount:browser.i18n.getMessage("discountInfo"),plans:[{name:browser.i18n.getMessage("Monthly"),checkoutUrl:"https://vidhelper.app/checkout?plan=monthly",price:"$5.9",priceMonthly:"$5.9",origPriceMonthly:"$7.9",description:browser.i18n.getMessage("MonthlyDes")},{name:browser.i18n.getMessage("Yearly"),checkoutUrl:"https://vidhelper.app/checkout?plan=yearly",price:"$29",priceMonthly:"$2.4",origPriceMonthly:"$3.2",description:browser.i18n.getMessage("YearlyDes").replace("{{1}}","$29").replace("{{2}}","$30"),mostPopular:!0},{name:browser.i18n.getMessage("Lifetime"),checkoutUrl:"https://vidhelper.app/checkout?plan=lifetime",price:"$55",origPrice:"$75",description:browser.i18n.getMessage("LifetimeDes")}]},sentryDSN:"https://<EMAIL>/4508324004036608",FREE_LIMIT:freeLimit,GENERAL:{showMergeInfo:!1,showVideoElRecordTip:!0,showTabRecordTip:!1,showDownloadStreamTip:!0,recordCountDown:3,useDedicatedDownloader:!1,dedicatedDownloader:"downloadingPage",defaultDownloader:"downloadingFrame",enableRegionSelector:!0,enableScreenRecord:!0,tabRecordType:"recordingPage"},MEDIA_FILTER:{VIDEO:{enable:!0,size:1048576,range:[0,104857600],step:1048576,candisable:!1},AUDIO:{enable:!0,size:307200,range:[0,20971520],step:1024,candisable:!1}},CSP_LIST:["www.facebook.com","www.instagram.com","twitter.com","x.com","youtube.com","vimeo.com","www.tokopedia.com"],SHAKA_PLAYER_LIST:["vk.com","ok.ru"],SKIP_BG_DOWNLOAD:["www.tiktok.com","streamplay.pw"],ENABLE_POPUP_QUERY:!1,INFOBOX_UP:!1,CONTACT_US:"mailto:<EMAIL>",RATE_US:"https://chrome.google.com/webstore/detail/".concat(browser.runtime.id,"/reviews"),DROPBOX_KEY:"6awh8g09fftibys",ENABLE_CONTEXTMENU:!1,CONTEXTMENU_TITLE:"Search Videos for",ENABLE_INSTALL_URL:!1,INSTAL_SUFFIX:"/install",ENABLE_UNINSTALL_URL:!1,UNINSTAL_SUFFIX:"/uninstall",ENABLE_UPDATE_URL:!1,UPDATE_URL:"/uninstall",DISALLOW_YOUTUBE:!enable_yt,DISALLOW_DOMAINS_REGEXP:enable_yt?null:/^(?:(?:https?:)?\/\/)?(?:[^/]*.)?youtube.com/,DISABLE_HEADRECEIVE_DOMAINS_REGEXP:/^(?:(?:https?:)?\/\/)?(?:[^/]*.)?(?:www.tiktok.com|www.instagram.com|www.facebook.com|vimeo.com|youtube.com|www.bilibili.com|www.bilibili.tv)/,DISABLE_IMAGE_HEADRECEIVE_DOMAINS_REGEXP:/^(?:(?:https?:)?\/\/)?(?:[^/]*.)?(?:vidhelper.app)/,INSTALL_DAYS:30,USE_COUNT:10,MAX_USE_STATISTICS:60,ENABLE_FINDERS:_objectSpread(_objectSpread({vimeo:"VMFinder",dailymotion:"DMFinder",facebook:"FBFinder",tiktok:"TTFinder",instagram:"IGFinder",soundcloud:"SCFinder"},enable_yt?{youtube:"YTFinder"}:{}),{},{bilibili:"BILIFinder",xx:"XXFinder"}),FINDER_MATCHES:["<all_urls>"],FINDER_EXCLUDE_MATCHES:["*://https://vidhelper.app/*"]},contentMap={m3u8:"video/mp4",m3u:"video/mp4",mp4:"video/mp4","3gp":"video/3gpp",flv:"video/x-flv",mov:"video/quicktime",avi:"video/x-msvideo",wmv:"video/x-ms-wmv",webm:"video/webm",ogg:"video/ogg",ogv:"video/ogg",f4v:"video/x-f4v",acc:"application/vnd.americandynamics.acc",mkv:"video/x-matroska",rmvb:"application/vnd.rn-realmedia-vbr",m4s:"video/iso.segment"},streamExts=["m3u8","m3u","mpd"],audioExts=["aac","mid","mp3","oga","wav","wma","flac","acc"],videoExts=["webm","3gp","3g2","rmvb","mp4","3gp2","flv","mov","avi","wmv","ogg","ogv","f4v","mkv"],imageExts=["bmp","gif","avif","ico","png","apng","svg","tif","tiff","webp","jpeg","jpg"],imageFormats={"image/bmp":["bmp"],"image/gif":["gif"],"image/avif":["avif"],"image/x-icon":["ico"],"image/vnd.microsoft.icon":["ico"],"image/png":["png"],"image/apng":["apng"],"image/svg+xml":["svg"],"image/tiff":["tif","tiff"],"image/webp":["webp"],"image/jpeg":["jpeg","jpg"],"image/jpg":["jpeg","jpg"]},imageMimeTypes=Object.keys(imageFormats),audioFormats={"audio/mp3":["mp3"],"audio/mp4":["m4a"],"audio/aac":["aac"],"audio/midi":["mid"],"audio/x-midi":["mid"],"audio/mpeg":["mp3"],"audio/ogg":["oga"],"audio/wav":["wav"],"audio/x-wav":["wav"],"audio/vnd.wave":["wav"],"audio/wave":["wav"],"audio/x-pn-wav":["wav"],"audio/webm":["webm"],"audio/3gpp":["3gp"],"audio/3gpp2":["3g2"],"audio/x-ms-wma":["wma"],"audio/flac":["flac"]},audioMimeTypes=Object.keys(audioFormats),videoFormats={"application/vnd.americandynamics.acc":["acc"],"application/vnd.rn-realmedia-vbr":["rmvb"],"video/mp4":["mp4","m4s","m4v"],"video/3gpp":["3gp"],"video/3gpp2":["3gp2"],"video/x-flv":["flv"],"video/x-m4v":["m4v"],"video/quicktime":["mov"],"video/x-msvideo":["avi"],"video/x-ms-wmv":["wmv"],"video/webm":["webm"],"video/ogg":["ogg","ogv"],"application/ogg":["ogg"],"video/x-f4v":["f4v"],"video/x-matroska":["mkv"],"video/iso.segment":["m4s"],"application/mp4":["mp4"]},videoMimeTypes=Object.keys(videoFormats),streamFormats={"audio/mpegurl":["m3u8","m3u"],"audio/x-mpegurl":["m3u8","m3u"],"application/vnd.apple.mpegurl":["m3u8","m3u"],"application/x-mpegurl":["m3u8","m3u"],"application/dash+xml":["mpd"],"application/octet-stream":["m3u8","m3u","mpd","mp4","webm","avi","ogg","flv","mkv","3gp","ts","m4v","mp3","wav"],"binary/octet-stream":["m3u8","m3u","mpd","mp4","webm","avi","ogg","flv","mkv","3gp","ts","m4v","mp3","wav"]},streamMimeTypes=Object.keys(streamFormats),mimeTypeFormats={audio:audioFormats,video:videoFormats,stream:streamFormats},allowedExtensions=[audioExts,videoExts,streamExts].flat(),typeExts=Object.entries(mimeTypeFormats).flatMap(function(e){var t=_slicedToArray(e,2),e=t[0],t=t[1];if("stream"!==e)return _defineProperty({},e,Object.values(t).flatMap(function(e){return e}))}).reduce(function(e,t){return Object.assign(e,t)},{}),mimeTypes=Object.entries(mimeTypeFormats).flatMap(function(e){e=_slicedToArray(e,2),e[0],e=e[1];return Object.keys(e)});function getTypeFromExt(e){if("string"!=typeof e)return null;for(var t=0,r=Object.keys(mimeTypeFormats);t<r.length;t++){var n=r[t],a=Object.values(mimeTypeFormats[n]).flat();if("stream"!=n&&-1!=a.indexOf(e))return n}return null}function getFileNameFromURL(e){var t=e.match(/\/?([^/?#]+)(?:$|\?|#)/);return(t=t||e.match(/\/([^/]+)\/?$/))?t[1]:"unknown"}function getFileNameFromURLPathName(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:64,r=2<arguments.length&&void 0!==arguments[2]&&arguments[2];try{var n,a=new URL(t).pathname;r&&(n=a.match(_wrapRegExp(/(.+?)(?:\.(\w{2,4}))?$/,{name:1,ext:2})),n&&(a=n.groups.name),n.groups.ext);var a=a.split("").reverse().join(""),i=_toConsumableArray(a.matchAll(/\//g)).map(function(e){return e.index}),o=e;if(0<i.length)for(var s=i.length-1;0<=s;s--)if(e>=i[s]){o=i[s];break}return a.slice(0,o).split("").map(function(e){return"/"===e?"-":e}).join("").split("").reverse().join("")}catch(e){return getFileNameFromURL(t)}}function getTitleExtFromFileName(e){var t=_slicedToArray(e.split(/\.(\w{2,4})$/),2),e=t[0],t=t[1],t=void 0===t?null:t;return{title:e,ext:(null==t?void 0:t.toLowerCase())||null}}function getTitleExtFromUrl(e){var t=1<arguments.length&&void 0!==arguments[1]&&arguments[1],r={title:"unknow",ext:null};return"string"!=typeof e||(r=getTitleExtFromFileName(getFileNameFromURL(e))).ext&&t&&allowedExtensions.indexOf(r.ext)<0&&(r.ext=null),r}function getTypeFromExt(e){return videoExts.includes(e)?"video":audioExts.includes(e)?"audio":streamExts.includes(e)?"stream":null}function getTypeFormatFromExtMime(e,t){var r={type:null,ext:null};if("string"!=typeof(t=t&&t.toLowerCase().split(";")[0]))return r;for(var n=0,a=Object.keys(mimeTypeFormats);n<a.length;n++){var i=a[n],o=mimeTypeFormats[i][t];if("stream"!=i&&o){var s=o.indexOf(e);return r.type=i,r.ext=s<0?o[0]:o[s],r}}if(mimeTypeFormats.stream[t]){var u=mimeTypeFormats.stream[t].indexOf(e);if(r.type="stream",0<=t.indexOf("octet-stream")){r.ext=u<0?null:mimeTypeFormats.stream[t][u];for(var c=0,l=Object.entries(typeExts);c<l.length;c++){var d=_slicedToArray(l[c],2),p=d[0];if(d[1].includes(r.ext)){r.type=p;break}}}else r.ext=u<0?mimeTypeFormats.stream[t][0]:mimeTypeFormats.stream[t][u];return r}return r}function areObjectsEqual(e,t){var r=Object.keys(e),n=Object.keys(t);if(r.length!==n.length)return!1;for(var a=0,i=r;a<i.length;a++){var o=i[a];if(null==t||!t.key||e[o]!==t[o])return!1}return!0}function p(e){return _p.apply(this,arguments)}function _p(){return(_p=_asyncToGenerator(_regeneratorRuntime().mark(function e(n){var t,a,r,i=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:for(t=i.length,a=new Array(1<t?t-1:0),r=1;r<t;r++)a[r-1]=i[r];return e.abrupt("return",new Promise(function(t,r){n.apply(void 0,a.concat([function(e){browser.runtime.lastError?r(browser.runtime.lastError):t(e)}]))}));case 2:case"end":return e.stop()}},e)}))).apply(this,arguments)}function isDevelop(){return browser.runtime.getManifest().name.startsWith("Develop")}function parse_match_pattern(e){if("string"!=typeof e)return null;function t(e){return e.replace(/[[^$.|?*+(){}\\]/g,"\\$&")}var r="(?:^",n=/^(\*|https?|file|ftp|chrome-extension):\/\//.exec(e);if(!n)return null;if(e=e.substr(n[0].length),r+="*"===n[1]?"https?://":n[1]+"://","file"!==n[1]){if(!(n=/^(?:\*|(\*\.)?([^\/*]+))(?=\/)/.exec(e)))return null;e=e.substr(n[0].length),"*"===n[0]?r+="[^/]+":(n[1]&&(r+="(?:[^/]+\\.)?"),r+=t(n[2]))}return r+=e.split("*").map(t).join(".*"),r+="$)"}function formatBytes(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:2,r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:1024;if("string"==typeof e&&(e=parseInt(e)),0===e)return"0 Bytes";var n=t<0?0:t,t=Math.floor(Math.log(e)/Math.log(r));return parseFloat((e/Math.pow(r,t)).toFixed(n))+" "+["Bytes","KB","MB","GB","TB","PB","EB","ZB","YB"][t]}function is1080pOrHigher(e){if(null!=e&&e.width&&null!=e&&e.height){if(1920<=e.width&&1080<=e.height||1080<=e.width&&1920<=e.height)return!0}else if(e.resolution)try{var t=e.resolution.split("x"),r=parseInt(t[0],10),t=parseInt(t[1],10);if(1920<=r&&1080<=t||1080<=r&&1920<=t)return!0}catch(e){}else if(e.quality)try{if(1080<=parseInt(e.quality.split("p")[0]))return!0}catch(e){}return!1}function getFileNameFromcontentDisposition(e){e=e.match(/filename=["']?([^'"\s;]+)["']?/i);return e?e[1]:null}function getRangeInfo(e){var t=e.split(" ");if(2===t.length){e=t[1].split("/");if(2===e.length){t=parseInt(e[1]);if(t)return{chunk:e[0],total:t}}}return null}function getSizeFromReceivedHeader(e){var t=e.get("content-length",null),e=e.get("content-range",null);if(t)return parseInt(t);if(e){e=getRangeInfo(e);if(e)return e.total}return null}function jsonHeadersToResponseHeaders(e){var r=_objectSpread({},e);return{get:function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:null,t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:null;if(r){if(e){e=e.toLowerCase();return r[e]||t}return r}return r}}}function responseHeadersToJson(e){var t={},r=_createForOfIteratorHelper(e.entries());try{for(r.s();!(a=r.n()).done;){var n=_slicedToArray(a.value,2),a=n[0],n=n[1];t[a]=n}}catch(e){r.e(e)}finally{r.f()}return t}function needPicker(e){var t=1610612736;if((null==e?void 0:e.size)>t)return!0;if(null!=e&&e.duration){var r,n,a=0;if(null!=e&&e.bandwidth?a=e.bandwidth:e.width&&e.height?(n=(null==e?void 0:e.frameRate)||30,a=e.width*e.height*n*.1):e.resolution&&(r=e.resolution.split("x"),n=(null==e?void 0:e.frameRate)||30,a=r[0]*r[1]*n*.1),t<e.duration*a/8)return!0}return!1}function hms(e){var t,r=e<3600?(t=14,5):(t=11,8);return new Date(1e3*e).toISOString().substr(t,r)}function getQualityFromVideoLink(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"";return e.width&&e.height&&(e.resolution="".concat(e.width,"x").concat(e.height)),e.bandwidth&&e.resolution?t="".concat(Math.floor(e.bandwidth/1e3)," kbps (").concat(e.resolution,")"):e.resolution?t=e.resolution:e.bandwidth&&e.quality?t="".concat(Math.floor(e.bandwidth/1e3)," kbps (").concat(e.quality,")"):e.quality?t=e.quality:e.bandwidth?t="".concat(Math.floor(e.bandwidth/1e3)," kbps"):e.size?t=formatBytes(e.size):e.duration?t=e.duration:e.name&&(t=e.name),t}function formatResolution(e,t){for(var r=0,n=Object.entries({"144p":[256,144],"240p":[426,240],"360p":[640,360],"480p":[854,480],"720p":[1280,720],"1080p":[1920,1080],"2K":[2048,1080],QHD:[2560,1440],"4K":[3840,2160],"DCI 4K":[4096,2160],"8K":[7680,4320]});r<n.length;r++){var a=_slicedToArray(n[r],2),i=a[0],o=_slicedToArray(a[1],2),a=o[0],o=o[1];if(e===a&&t===o)return i}return"".concat(e,"x").concat(t)}function getQualityFromVideoLink_new(e){var t=[];e.width&&e.height&&t.push("".concat(formatResolution(e.width,e.height))),e.frameRate&&t.push("".concat(e.frameRate," fps")),e.bandwidth?t.push("".concat(Math.floor(e.bandwidth/1e3)," kbps")):e.bitrate&&t.push("".concat(e.bitrate," kbps"));try{e.duration&&t.push(hms(e.duration))}catch(e){}return e.size&&t.push(formatBytes(e.size)),"audio"==e.type&&e.name&&t.push(e.name),t.join(" | ")}function checkAttr(e,t,r){for(var n=0,a=e.length;n<a;n++)if(e[n][t]==r)return!0;return!1}function checkScript(e,t){return checkAttr(document.scripts,e,t)}function inIframe(){try{return window.self!==window.top}catch(e){return!0}}function delay(t){return new Promise(function(e){return setTimeout(e,t)})}function waitHeaderLoaded(){return _waitHeaderLoaded.apply(this,arguments)}function _waitHeaderLoaded(){return(_waitHeaderLoaded=_asyncToGenerator(_regeneratorRuntime().mark(function e(){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",new Promise(function(e){function t(){return document.head||document.documentElement}var r;t()?e():(r=new MutationObserver(function(){t()&&(r.disconnect(),e())})).observe(document,{childList:!0,subtree:!0})}));case 1:case"end":return e.stop()}},e)}))).apply(this,arguments)}function waitBodyLoaded(){return _waitBodyLoaded.apply(this,arguments)}function _waitBodyLoaded(){return(_waitBodyLoaded=_asyncToGenerator(_regeneratorRuntime().mark(function e(){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",new Promise(function(e){function t(){return document.body}var r;t()?e():(r=new MutationObserver(function(){t()&&(r.disconnect(),e())})).observe(document,{childList:!0,subtree:!0})}));case 1:case"end":return e.stop()}},e)}))).apply(this,arguments)}function waitLoaded(){return _waitLoaded.apply(this,arguments)}function _waitLoaded(){return(_waitLoaded=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var r,n,t=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(r=0<t.length&&void 0!==t[0]?t[0]:null,"loading"!==document.readyState)return e.abrupt("return");e.next=5;break;case 5:return n=null,e.abrupt("return",new Promise(function(e,t){"number"==typeof r&&(n=setTimeout(function(){t(new Error("Timeout exceeded"))},r)),document.addEventListener("DOMContentLoaded",function(){null!=n&&(clearTimeout(n),n=null),e()})}));case 7:case"end":return e.stop()}},e)}))).apply(this,arguments)}function loadScript(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:null,r=!(2<arguments.length&&void 0!==arguments[2])||arguments[2]?document.head||document.documentElement:document.body,n=document.createElement("script"),a=setTimeout(function(){t(n)},1e3);if(e.src?n.src=e.src:e.textContent?n.textContent=e.textContent:e.innerHTML&&(n.innerHTML=e.innerHTML),e.attrs)for(var i=0,o=Object.entries(e.attrs);i<o.length;i++){var s=_slicedToArray(o[i],2),u=s[0],s=s[1],s=void 0===s||s;n.setAttribute(u,s)}t&&(n.onload=function(){null!=a&&(clearTimeout(a),a=null),t(n)}),r.appendChild(n)}function loadScriptPromise(r){var n=1<arguments.length&&void 0!==arguments[1]&&arguments[1];return new Promise(function(t,e){loadScript(r,function(e){t(e)},n)})}function checkLoadScript(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"src",r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;"innerHTML"==t?checkScript(t,e[t])||loadScript(e,r):e.attrs&&e.attrs[t]&&!checkScript(t,e.attrs[t])&&loadScript(e,r)}function loadLink(e){var t=document.head,r=document.createElement("link");if(r.rel="stylesheet",r.href=e.href,e.attrs)for(var n=0,a=Object.entries(e.attrs);n<a.length;n++){var i=_slicedToArray(a[n],2),o=i[0],i=i[1],i=void 0===i||i;r.setAttribute(o,i)}t.appendChild(r)}function checkLink(e,t){return checkAttr(document.styleSheets,e,t)}function checkLoadLink(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"href";e.attrs&&e.attrs[t]&&!checkLink(t,e.attrs[t])&&loadLink(e)}function sendMessage(e){return _sendMessage.apply(this,arguments)}function _sendMessage(){return(_sendMessage=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",p(browser.runtime.sendMessage,t).catch(function(e){return null}));case 1:case"end":return e.stop()}},e)}))).apply(this,arguments)}function XMLRequest(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:null;return null==t&&(t={method:"GET"}),t.cache=t.cache||"default",fetch(e,t).then(function(e){return e})}function syncStorageGet(e){return _syncStorageGet.apply(this,arguments)}function _syncStorageGet(){return(_syncStorageGet=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,p(browser.storage.sync.get.bind(browser.storage.sync),t).catch(function(e){});case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e)}))).apply(this,arguments)}function syncStorageSet(e){return _syncStorageSet.apply(this,arguments)}function _syncStorageSet(){return(_syncStorageSet=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,p(browser.storage.sync.set.bind(browser.storage.sync),t).catch(function(e){});case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e)}))).apply(this,arguments)}function syncStorageRemove(e){return _syncStorageRemove.apply(this,arguments)}function _syncStorageRemove(){return(_syncStorageRemove=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,p(browser.storage.sync.remove.bind(browser.storage.sync),t).catch(function(e){});case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e)}))).apply(this,arguments)}function localStorageGet(e){return _localStorageGet.apply(this,arguments)}function _localStorageGet(){return(_localStorageGet=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,p(browser.storage.local.get.bind(browser.storage.local),t).catch(function(e){});case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e)}))).apply(this,arguments)}function localStorageSet(e){return _localStorageSet.apply(this,arguments)}function _localStorageSet(){return(_localStorageSet=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,p(browser.storage.local.set.bind(browser.storage.local),t).catch(function(e){});case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e)}))).apply(this,arguments)}function localStorageRemove(e){return _localStorageRemove.apply(this,arguments)}function _localStorageRemove(){return(_localStorageRemove=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,p(browser.storage.local.remove.bind(browser.storage.local),t).catch(function(e){});case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e)}))).apply(this,arguments)}function isDocumentReady(){return!(!document.head&&!document.documentElement)}function toBlobURL(e,t){return _toBlobURL.apply(this,arguments)}function _toBlobURL(){return(_toBlobURL=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,r){var n,a=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return n=2<a.length&&void 0!==a[2]?a[2]:void 0,e.next=3,fetch(t,n);case 3:return e.next=5,e.sent.arrayBuffer();case 5:return n=e.sent,n=new Blob([n],{type:r}),e.abrupt("return",URL.createObjectURL(n));case 8:case"end":return e.stop()}},e)}))).apply(this,arguments)}function mediaIsMP4(e){return 0===e[0]&&0===e[1]&&0===e[2]&&(24===e[3]||32===e[3])&&102===e[4]&&116===e[5]&&121===e[6]&&112===e[7]}function mediaIsTs(e){if(71!==e[0])return!1;for(var t=188;t<Math.min(e.length,940);t+=188)if(71!==e[t])return!1;return!0}function determineStreamType(e){if(!e||"string"!=typeof e)return null;var t=Math.min(e.length,1e3),t=e.slice(0,t);return t.includes("#EXTM3U")?"hls":/\s*<MPD\s/.test(t)?"dash":null}function getResponseInfoSafe(e){return _getResponseInfoSafe.apply(this,arguments)}function _getResponseInfoSafe(){return(_getResponseInfoSafe=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var r,n=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return r=1<n.length&&void 0!==n[1]?n[1]:{},e.abrupt("return",getResponseInfo(t,r).catch(function(e){return null}));case 2:case"end":return e.stop()}},e)}))).apply(this,arguments)}function getResponseInfo(e){return _getResponseInfo.apply(this,arguments)}function _getResponseInfo(){return(_getResponseInfo=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var r,n,a,i,o,s=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return n=(o=1<s.length&&void 0!==s[1]?s[1]:{}).init,r=void 0===n?null:n,n=o.redirect,n=void 0===n?"follow":n,o=o.abortData,a=void 0===o||o,i=new AbortController,o=i.signal,r=null!=r?r:{headers:{accept:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9"},credentials:"include",redirect:n,method:"GET"},e.abrupt("return",XMLRequest(t,_objectSpread(_objectSpread({},r),{},{signal:o})).then(function(e){return a&&i.abort(),e}));case 6:case"end":return e.stop()}},e)}))).apply(this,arguments)}function setHeaders(e,t){return _setHeaders.apply(this,arguments)}function _setHeaders(){return(_setHeaders=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,r){var n,a,i,o,s;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:for(n=[],a=[],i=Date.now()%Math.pow(2,31),o=0;o<t.length;o++)s=o+i,t[o]&&t[o].startsWith("http")&&(n.push({id:s,priority:1,action:{type:"modifyHeaders",requestHeaders:r.map(function(e){return{operation:e.operation,header:e.header,value:e.value}})},condition:{urlFilter:"||".concat(new URL(t[o]).hostname)}}),a.push(s));return e.next=6,browser.declarativeNetRequest.updateSessionRules({addRules:n,removeRuleIds:a});case 6:return e.abrupt("return",a);case 7:case"end":return e.stop()}},e)}))).apply(this,arguments)}function removeRulesForTabs(e){return _removeRulesForTabs.apply(this,arguments)}function _removeRulesForTabs(){return(_removeRulesForTabs=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var r;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,chrome.declarativeNetRequest.getSessionRules();case 2:if(r=e.sent,0<(r=r.filter(function(e){return null===(e=e.condition)||void 0===e||null===(e=e.tabIds)||void 0===e?void 0:e.some(function(e){return t.includes(e)})}).map(function(e){return e.id})).length)return e.next=7,chrome.declarativeNetRequest.updateSessionRules({removeRuleIds:r});e.next=7;break;case 7:case"end":return e.stop()}},e)}))).apply(this,arguments)}function updateSessionRules(e,t){return _updateSessionRules.apply(this,arguments)}function _updateSessionRules(){return(_updateSessionRules=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,r){var n,a,i,o,s,u,c,l,d,p=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:for(n=(i=2<p.length&&void 0!==p[2]?p[2]:{}).tabIds,a=void 0===n?null:n,i=i.attrs,o=void 0===i?[]:i,r&&o.push({header:"Referer",value:r}),s=[],u=[],c=Date.now()%Math.pow(2,31),l=0;l<t.length;l++)d=l+c,t[l]&&t[l].startsWith("http")&&(s.push({id:d,priority:1,action:{type:"modifyHeaders",requestHeaders:o.map(function(e){return{operation:(null==e?void 0:e.operation)||"set",header:e.header,value:e.value}})},condition:{urlFilter:"||".concat(new URL(t[l]).hostname),tabIds:a||[browser.tabs.TAB_ID_NONE]}}),u.push(d));return e.prev=7,e.next=10,browser.declarativeNetRequest.updateSessionRules({addRules:s,removeRuleIds:u});case 10:e.next=19;break;case 12:if(e.prev=12,e.t0=e.catch(7),a)return e.next=17,removeRulesForTabs(a);e.next=17;break;case 17:return e.next=19,browser.declarativeNetRequest.updateSessionRules({addRules:s,removeRuleIds:u});case 19:return e.abrupt("return",u);case 20:case"end":return e.stop()}},e,null,[[7,12]])}))).apply(this,arguments)}function filterHeaders(){var e,t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},r=["x-client-data","referer","user-agent","origin","cache-control","pragma","accept-encoding","accept-language","sec-ch-ua","sec-ch-ua-mobile","sec-ch-ua-platform","sec-fetch-dest","sec-fetch-mode","sec-fetch-site"],n={};for(e in t)r.includes(e)||(n[e]=t[e]);return n}function playVideoLink(f,e){var i=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null,t=3<arguments.length&&void 0!==arguments[3]?arguments[3]:["Html5","Shaka"],r=(null==f?void 0:f.download_url)||(null==f?void 0:f.url),n="video/mp4",n=r&&"video"!==f.type||null==f||!f.manifestUrl?(0<r.indexOf(".webm")?n="video/webm":0<r.indexOf(".flv")?n="video/x-flv":0<r.indexOf(".mpd")?n="application/dash+xml":0<r.indexOf(".m3u8")&&(n="application/x-mpegURL"),{type:n,src:updatePandavideoToken(r)}):("hls"===f.streamType?n="application/x-mpegURL":"dash"===f.streamType&&(n="application/dash+xml"),{type:n,src:updatePandavideoToken(f.manifestUrl)});videojs.log.level="debug";var h=videojs(e,{controlBar:{fullscreenToggle:!1},techOrder:t,playbackRates:[.5,1,1.25,1.5,2,4]});return h.ready(function(){h.controlBar.subsCapsButton.hide()}),h.src(n),h.on("loadedmetadata",function(){if("audio"==f.type&&h.audioOnlyMode(!0),"Shaka"==h.techName_){var e,t=h.tech(!0).shaka_;t.configure({abr:{enabled:!1}});var r,n="audio"==f.type?f.language:(null==f||null===(e=f.audioLink)||void 0===e?void 0:e.language)||null;!n||(r=t.getAudioLanguagesAndRoles().filter(function(e){return e.language==n})).length&&t.selectAudioLanguage(r[0].language,r[0].role),"video"!=f.type||(r=t.getVariantTracks().filter(function(e){return!("variant"!==e.type||(null==f?void 0:f.width)!=(null==e?void 0:e.width)||(null==f?void 0:f.height)!=(null==e?void 0:e.height)||null!=f&&f.bandwidth&&(null==f?void 0:f.bandwidth)!=((null==e?void 0:e.videoBandwidth)||(null==e?void 0:e.bandwidth)))})).length&&t.selectVariantTrack(r[0],!0)}else{var a,i="audio"==f.type?f.name:(null==f||null===(a=f.audioLink)||void 0===a?void 0:a.name)||null;if(!i||(a=Array.from(h.audioTracks()).find(function(e){return e.label===i}))&&(a.enabled=!0),"video"==f.type){var o=h.qualityLevels().levels_;if(1<o.length&&(f.width&&f.height||f.bandwidth)&&1<o.length&&f){for(var s=f.width&&f.height?f.width*f.height:null,u=f.bandwidth||null,c=!1,l=0;l<o.length;l++){var d=o[l],p=s&&d.width*d.height===s||u&&d.bitrate===u;d.enabled=p,c=c||p}c||(o[0].enabled=!0)}}}}),h.on("xhr-hooks-ready",function(){isPrivacyCombrUrl(h.currentSrc())&&h.tech(!0).vhs.xhr.onRequest(function(e){return e.beforeSend=function(t){var e,r=updatePrivacyCombrHeaders(t.url,filterHeaders((null==f||null===(e=f.init)||void 0===e?void 0:e.headers)||{}));Object.keys(r).forEach(function(e){t.setRequestHeader(e,r[e])})},e});var a=!1;h.tech(!0).vhs.xhr.onResponse(function(e,t,r){var n;403==r.statusCode&&0==a&&isPandavideoTokenUrl(h.currentSrc())&&(a=!0,n=(null==f||null===(n=f.init)||void 0===n||null===(n=n.headers)||void 0===n?void 0:n.referer)||i||playUrl,updateSessionRules([e.url],n))})}),h.on(["loadstart","play","playing","firstplay","pause","ended","adplay","adplaying","adfirstplay","adpause","adended","contentplay","contentplaying","contentfirstplay","contentpause","contentended","contentupdate","loadeddata","loadedmetadata"],function(e){}),h}function getDataFromBg(e){return _getDataFromBg.apply(this,arguments)}function _getDataFromBg(){return(_getDataFromBg=_asyncToGenerator(_regeneratorRuntime().mark(function e(u){var c,l,t=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return c=1<t.length&&void 0!==t[1]?t[1]:{},l=2<t.length&&void 0!==t[2]?t[2]:location.href,e.abrupt("return",new Promise(function(r,n){function a(){e&&(clearTimeout(e),e=null)}function i(e){if(e.message==="".concat(u,"-chunk")&&e.uniqueEventName===o&&(a(),s+=e.data,e.done))try{chrome.runtime.onMessage.removeListener(i);var t=JSON.parse(s);r(t)}catch(e){n(new Error("Error parsing tab data: ".concat(e.message,", ").concat(l,", ").concat(u,", ").concat(JSON.stringify(c))))}}var o="".concat(u,"-").concat(Date.now(),"-").concat(Math.random()),s="",e=null;chrome.runtime.onMessage.addListener(i),sendMessage(_objectSpread({message:u,uniqueEventName:o},c)).then(function(e){a(),e.data&&(chrome.runtime.onMessage.removeListener(i),r(e.data))}).catch(function(e){n(e)}),e=setTimeout(function(){chrome.runtime.onMessage.removeListener(i),n(new Error("".concat(u," timed out")))},1e4)}));case 3:case"end":return e.stop()}},e)}))).apply(this,arguments)}function _linkClickDownload(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"",r=document.createElement("a");r.setAttribute("download",configuration.BRAND+"/"+t),r.setAttribute("target","_blank"),r.href=e,document.body.appendChild(r),r.click(),r.remove()}function _backgroundDownload2(e,t){return _backgroundDownload.apply(this,arguments)}function _backgroundDownload(){return(_backgroundDownload=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,r){var n,a,i,o=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(a={message:"download-video-link",url:t,fileName:r},"object"==_typeof(n=2<o.length&&void 0!==o[2]?o[2]:{}))for(i in n)a[i]=n[i];return e.next=5,sendMessage(a);case 5:if(e.t0=e.sent,e.t0){e.next=8;break}e.t0={};case 8:return e.abrupt("return",e.t0);case 9:case"end":return e.stop()}},e)}))).apply(this,arguments)}var AbstractDownloader=(_defineProperty(_class=function(){"use strict";function r(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:null,t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:null;_classCallCheck(this,r),this.stateListener=e,this.progressListener=t}var n,a,t,i;return _createClass(r,[{key:"handleProgress",value:function(e,t){var r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{};this.progressListener&&e&&this.progressListener(e,t,r)}},{key:"handleStateChange",value:function(e,t){var r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:"";this.stateListener&&e&&this.stateListener(e,t,r)}},{key:"xmlDownload",value:(i=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,r){var n,a,i,o,s,u,c,l,d,p,f,h,m=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return i=2<m.length&&void 0!==m[2]?m[2]:{},n=new AbortController,a=null,i=i.timeoutMillis,a=setTimeout(function(){n.abort()},void 0===i?3e4:i),this.handleStateChange(r,LinkDownloader.STATE.START),e.prev=6,e.next=9,XMLRequest(t.download_url,{signal:n.signal});case 9:if(o=e.sent,clearTimeout(a),o.ok){e.next=13;break}throw new Error("Network response was not ok");case 13:s=null,u=t.title+t.ext,null!==(p=o.headers.get("content-length"))&&(s=parseInt(p,10)),c=o.body.getReader(),l=0,d=[];case 20:return e.next=23,c.read();case 23:if(f=e.sent,p=f.done,f=f.value,p)return h=URL.createObjectURL(new Blob(d)),this.linkClickDownload(h,u),URL.revokeObjectURL(h),this.handleProgress(r,1),this.handleStateChange(r,LinkDownloader.STATE.COMPLETE),e.abrupt("break",39);e.next=33;break;case 33:l+=f.length,d.push(f),null!==s&&(h=Math.floor(l/s*100),this.handleProgress(r,h)),e.next=20;break;case 39:e.next=47;break;case 41:e.prev=41,e.t0=e.catch(6),clearTimeout(a),n.abort(),this.handleStateChange(r,LinkDownloader.STATE.FAILED);case 47:case"end":return e.stop()}},e,this,[[6,41]])})),function(e,t){return i.apply(this,arguments)})},{key:"linkClickDownload",value:function(e){return _linkClickDownload(e,1<arguments.length&&void 0!==arguments[1]?arguments[1]:"")}},{key:"windowOpen",value:(t=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:window.open(t);case 1:case"end":return e.stop()}},e)})),function(e){return t.apply(this,arguments)})},{key:"backgroundDownload",value:(a=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,r){var n,a=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return n=2<a.length&&void 0!==a[2]?a[2]:{},e.abrupt("return",_backgroundDownload2(t,r,n));case 2:case"end":return e.stop()}},e)})),function(e,t){return a.apply(this,arguments)})},{key:"download",value:(n=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,r){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:throw new Error("You have to implement the method doSomething!");case 2:case"end":return e.stop()}},e)})),function(e,t){return n.apply(this,arguments)})}]),r}(),"STATE",{START:"start",DOWNLOADING:"downloading",PAUSED:"paused",INTERRUPTED:"interrupted",COMPLETE:"complete",FAILED:"failed"}),_class),LinkDownloader=function(){"use strict";_inherits(i,AbstractDownloader);var r,n=_createSuper(i);function i(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:null,t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:null;return _classCallCheck(this,i),(t=n.call(this,e,t)).listener=t.initListener(),t}return _createClass(i,[{key:"initListener",value:function(){function e(e,t,r){switch(e.message){case"download-changed":n.handleStateChange(e.key,e.state,(null==e?void 0:e.errmsg)||""),r();break;case"progress-changed":n.handleProgress(e.key,e.progress),r();break;case"complete":r()}}var n=this;return browser.runtime.onMessage.addListener(e),e}},{key:"dispose",value:function(){this.listener&&(browser.runtime.onMessage.removeListener(this.listener),this.listener=null),this.stateListener=null,this.progressListener=null}},{key:"download",value:(r=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,r){var n,a;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return a=t.title,new RegExp("."+t.ext+"$").test(t.title)||(a=t.title+"."+t.ext),this.handleStateChange(r,i.STATE.START),n=t.download_url,e.prev=5,e.next=8,this.backgroundDownload(n,a);case 8:"result"in(a=e.sent)&&0<a.result?this.handleStateChange(r,i.STATE.DOWNLOADING):this.handleStateChange(r,i.STATE.FAILED),e.next=15;break;case 12:e.prev=12,e.t0=e.catch(5),this.xmlDownload(t);case 15:case"end":return e.stop()}},e,this,[[5,12]])})),function(e,t){return r.apply(this,arguments)})}]),i}();function isPandavideoUrl(e){return/https?:\/\/(?:[^.]+\.)*(?:[^/]+)\.pandavideo\.com(?:\.[^/]+)?\/.*\.m3u8/.test(e)}function isPandavideoNoTokenUrl(e){return/https?:\/\/(?:[^.]+\.)*(?:[^/]+)\.pandavideo\.com(?:\.[^/]+)?\/.*\.m3u8(?:(?!.token=[a-zA-Z0-9]{64})[?&][^&]*)?$/.test(e)}function isPandavideoTokenUrl(e){return _wrapRegExp(/https?:\/\/(?:[^.]+\.)*(?:[^/]+)\.pandavideo\.com(?:\.[^/]+)?\/.*\.m3u8\?(?:[^&]+&)?token=([a-zA-Z0-9]{64})/,{token:1}).test(e)}function updatePandavideoToken(e){var t=_wrapRegExp(/https?:\/\/(?:[^.]+\.)*(?:[^/]+)\.pandavideo\.com(?:\.[^/]+)?\/.*\.m3u8\?(?:[^&]+&)?token=([a-zA-Z0-9]{64})/,{token:1}),t=e.match(t);return t&&(e=e.replace(t.groups.token,function(){for(var e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",t="",r=0;r<64;r++){var n=Math.floor(Math.random()*e.length);t+=e.charAt(n)}return t}())),e}function isPolyvNetUrl(e){return!(null==e||!e.match(/^https?:\/\/hls\.videocc\.net/))}function getPolyvNetManifestId(e){e=null==e?void 0:e.match(/^https?:\/\/hls\.videocc\.net\/.*?\/([^\/_]+)(_\d+\.key|(_\d+)?.m3u8)/);return e?e[1]:null}function getPolyvNetManifestToken(e){e=null==e?void 0:e.match(/^https?:\/\/hls\.videocc\.net\/.*?\/([^\/]+)_\d+\.key\?token=([^&]+)/);return e?{manifestId:e[1],token:e[2]}:null}function updatePolyvNetKeyToken(e,t){var r;try{if(null!=e&&null!==(r=e[0])&&void 0!==r&&null!==(r=r.segments)&&void 0!==r&&null!==(r=r[0])&&void 0!==r&&null!==(r=r.key)&&void 0!==r&&null!==(r=r.url)&&void 0!==r&&r.match(/^https?:\/\/hls\.videocc\.net\/.*?\/([^\/]+)_\d+\.key/)){var n=_createForOfIteratorHelper(e);try{for(n.s();!(a=n.n()).done;){var a=a.value,i=_createForOfIteratorHelper(null==a?void 0:a.segments);try{for(i.s();!(s=i.n()).done;){var o=s.value,s=new URL(o.key.url);s.searchParams.set("token",t),o.key.url=s.toString()}}catch(e){i.e(e)}finally{i.f()}}}catch(e){n.e(e)}finally{n.f()}}}catch(e){}return e}function isBoomstreamUrl(e){return!(null==e||!e.match(/^https?:\/\/(?:[^\.]+\.)*?boomstream\.com/))}function updateLoomstreamMediaPlaylist(e,t,r){var n=/(?:#EXT-X-MEDIA-READY: *(\w+))\r?\n?/.exec(e);if(!n||!t)return e;var a="bv17b7v24iedrvzoaihwvugef89ewy7834f35",i="001a5068005b176d560504";function o(e,t){for(var r=[];t.length<e.length;)t+=t;for(var n=0;n<e.length;n+=2){var a=e.substring(n,n+2),a=parseInt(a,16)^t.charCodeAt(n/2);r.push(String.fromCharCode(a))}return r.join("")}var s=o(n=n[1],o(i,a)),n=(n=s,new Uint8Array([].map.call(n,function(e){return e.charCodeAt(0)})).buffer),n="0x"+(n=n.slice(20,36),Array.prototype.map.call(new Uint8Array(n),function(e){return("00"+e.toString(16)).slice(-2)}).join("")),a=r+"/process/"+function(e,t){for(var r=[];t.length<e.length;)t+=t;for(var n=0;n<e.length;n++){var a=(e.charCodeAt(n)^t.charCodeAt(n)).toString(16);a.length<2&&(a="0"+a),r.push(a)}return r.join("")}(s.slice(0,20)+t,o(i,a));return(e=e.replace("[KEY]",a)).replace("[IV]",n)}function isPrivacyCombrUrl(e){return!(null==e||!e.match(/^https?:\/\/(?:[^\.]+\.)*?privacy.com\.br/))}function updatePrivacyCombrHeaders(e,t){if(t["x-content-uri"]&&isPrivacyCombrUrl(e))try{var r=new URL(e).pathname.split("/").filter(function(e){return e});0<r.length&&(t["x-content-uri"]=r[r.length-1])}catch(e){}return t}function middleTruncate_old(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:64;if(e.length<=t)return e;var r=Math.ceil((t-3)/2),t=Math.floor((t-3)/2);return e.slice(0,r)+"..."+e.slice(e.length-t)}function middleTruncate(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:64,r=new TextEncoder;if(r.encode(e).length<=t)return e;for(var n=Math.ceil((t-3)/2),a=Math.floor((t-3)/2),i="",o="",s=0;s<e.length;s++){var u=e[s];if(r.encode(i+u).length>n)break;i+=u}for(var c=e.length-1;0<=c;c--){var l=e[c];if(r.encode(l+o).length>a)break;o=l+o}return i+"..."+o}function truncate_utf8_bak(e,t){if("string"!=typeof e)throw new Error("Input must be string");for(var r,n,a,i,o=e.length,s=0,u=0;u<o;u+=1){if(r=e.charCodeAt(u),n=e[u],55296<=(i=r)&&i<=56319&&(56320<=(a=e.charCodeAt(u+1))&&a<=57343)&&(n+=e[u+=1]),(s+=unescape(encodeURIComponent(n)).length)===t)return e.slice(0,u+1);if(t<s)return e.slice(0,u-n.length+1)}return e}function truncate_utf8(e,t){var r=!(2<arguments.length&&void 0!==arguments[2])||arguments[2];if("string"!=typeof e)throw new Error("Input must be a string");var n=_toConsumableArray(e),a=0;if(r)for(var i=0;i<n.length;i++){var o=n[i];if(t<(a+=unescape(encodeURIComponent(o)).length))return n.slice(0,i).join("")}else for(var s=n.length-1;0<=s;s--){var u=n[s];if(t<(a+=unescape(encodeURIComponent(u)).length))return n.slice(s+1).join("")}return e}function sanitizeFilename(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:100;try{return truncate_utf8(e=e.replace(/^\./,"_").replace(/[\u0000-\u001f\u007f-\u009f\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200b-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,"").replace(/&quot;/g,"").replace(/&amp;/g,"&").replace(/[\\/:*?<>|~↵"\t]/g,"_"),t)}catch(e){}}function _reportMsg(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"error";chrome.runtime.sendMessage({message:"sentry-report-msg",data:{message:e,level:t}},function(){})}function sendGaPreview(e,t){return sendMessage({message:"ga-msg",type:"pageview",title:e,location:t,params:2<arguments.length&&void 0!==arguments[2]?arguments[2]:{}},function(){})}function sendGaEvent(e){return sendMessage({message:"ga-msg",type:"event",name:e,params:1<arguments.length&&void 0!==arguments[1]?arguments[1]:{}},function(){})}function sendGaException(e){return sendMessage({message:"ga-msg",type:"exception",error:e,params:1<arguments.length&&void 0!==arguments[1]?arguments[1]:{}},function(){})}function onError(n){"object"==_typeof(window.onerror)&&(window.onerror=function(e,t,r){sendGaException("page: ".concat(n,": url:").concat(t," err: ").concat(e))})}function localizeHtmlPage(){for(var e=document.getElementsByTagName("html"),t=0;t<e.length;t++){var r=e[t],n=r.innerHTML.toString(),a=n.replace(/__MSG_(\w+)__/g,function(e,t){return t?browser.i18n.getMessage(t):""});a!=n&&(r.innerHTML=a)}}function url_host_split_bak(e){try{var t=e.match(/^(?:(?:https?:)?\/\/)?((?:[\w-]+\.)?(([\w-]+)\.[^?\/]+))/);return t.shift(),t.reverse(),t}catch(e){return null}}function url_host_split(e){try{for(var t=[],r=e.match(/^(?:(?:https?:)?\/\/)?((?:[\w-]+\.)?[^?\/]+)/)[1].split("."),n=r.length,a=0;a<n-1;a++)t.unshift(r.join(".")),2<r.length&&r.shift();return t.unshift(r[0]),t}catch(e){return null}}function storeGeneral(e){return _storeGeneral.apply(this,arguments)}function _storeGeneral(){return(_storeGeneral=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,syncStorageSet(_defineProperty({},"".concat(browser.runtime.id,"_general"),t));case 2:case"end":return e.stop()}},e)}))).apply(this,arguments)}function getStoredGeneral(){return _getStoredGeneral.apply(this,arguments)}function _getStoredGeneral(){return(_getStoredGeneral=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t,r;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t="".concat(browser.runtime.id,"_general"),e.next=3,syncStorageGet(t);case 3:return r=e.sent,e.abrupt("return",(null==r?void 0:r[t])||{});case 5:case"end":return e.stop()}},e)}))).apply(this,arguments)}function storeFilter(e){return _storeFilter.apply(this,arguments)}function _storeFilter(){return(_storeFilter=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,syncStorageSet(_defineProperty({},"".concat(browser.runtime.id,"_filter"),t));case 2:case"end":return e.stop()}},e)}))).apply(this,arguments)}function getStoredFilter(){return _getStoredFilter.apply(this,arguments)}function _getStoredFilter(){return(_getStoredFilter=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t,r;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t="".concat(browser.runtime.id,"_filter"),e.next=3,syncStorageGet(t);case 3:return r=e.sent,e.abrupt("return",(null==r?void 0:r[t])||{});case 5:case"end":return e.stop()}},e)}))).apply(this,arguments)}function updateFilter(e,t){return _updateFilter.apply(this,arguments)}function _updateFilter(){return(_updateFilter=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,r){var n,a,i,o,s,u,c;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:for(i in a=function(e,t,r){return void 0!==e&&e>=t[0]&&e<=t[1]?e:r},n={},r)u=t[i]||{},o=void 0===(c=u.enable)?r[i].enable:c,s=u.size,c=r[i],u=c.range,c=c.size,n[i]={enable:o,size:a(s,u,c)};return e.abrupt("return",n);case 4:case"end":return e.stop()}},e)}))).apply(this,arguments)}function needFilter(e,t){return!t||(e<t.size||0==t.enable)}function getStoredCspList(){return _getStoredCspList.apply(this,arguments)}function _getStoredCspList(){return(_getStoredCspList=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t,r;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t="".concat(browser.runtime.id,"_csplist"),e.next=3,syncStorageGet(t);case 3:return r=e.sent,e.abrupt("return",(null==r?void 0:r[t])||null);case 5:case"end":return e.stop()}},e)}))).apply(this,arguments)}function storeCspList(e){return _storeCspList.apply(this,arguments)}function _storeCspList(){return(_storeCspList=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,syncStorageSet(_defineProperty({},"".concat(browser.runtime.id,"_csplist"),t));case 2:case"end":return e.stop()}},e)}))).apply(this,arguments)}function addElement(e,t){switch(2<arguments.length&&void 0!==arguments[2]?arguments[2]:"prepend"){case"insertBefore":t.insertAdjacentElement("beforebegin",e);break;case"insertAfter":t.insertAdjacentElement("afterend",e);break;case"prepend":t.prepend(e);break;case"append":t.append(e);break;case"appendChild":t.appendChild(e)}}var Base64={_keyStr:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",encode:function(e){var t,r,n,a,i,o,s="",u=0;for(e=Base64._utf8_encode(e);u<e.length;)n=(o=e.charCodeAt(u++))>>2,a=(3&o)<<4|(t=e.charCodeAt(u++))>>4,i=(15&t)<<2|(r=e.charCodeAt(u++))>>6,o=63&r,isNaN(t)?i=o=64:isNaN(r)&&(o=64),s=s+this._keyStr.charAt(n)+this._keyStr.charAt(a)+this._keyStr.charAt(i)+this._keyStr.charAt(o);return s},decode:function(e){var t,r,n,a,i,o="",s=0;for(e=e.replace(/[^A-Za-z0-9+/=]/g,"");s<e.length;)t=this._keyStr.indexOf(e.charAt(s++))<<2|(n=this._keyStr.indexOf(e.charAt(s++)))>>4,r=(15&n)<<4|(a=this._keyStr.indexOf(e.charAt(s++)))>>2,n=(3&a)<<6|(i=this._keyStr.indexOf(e.charAt(s++))),o+=String.fromCharCode(t),64!=a&&(o+=String.fromCharCode(r)),64!=i&&(o+=String.fromCharCode(n));return o=Base64._utf8_decode(o)},_utf8_encode:function(e){var t="";e=e.replace(/\r\n/g,"\n");for(var r=0;r<e.length;r++){var n=e.charCodeAt(r);n<128?t+=String.fromCharCode(n):(127<n&&n<2048?t+=String.fromCharCode(n>>6|192):(t+=String.fromCharCode(n>>12|224),t+=String.fromCharCode(n>>6&63|128)),t+=String.fromCharCode(63&n|128))}return t},_utf8_decode:function(e){for(var t,r,n="",a=0,i=t=0;a<e.length;)(i=e.charCodeAt(a))<128?(n+=String.fromCharCode(i),a++):191<i&&i<224?(t=e.charCodeAt(a+1),n+=String.fromCharCode((31&i)<<6|63&t),a+=2):(t=e.charCodeAt(a+1),r=e.charCodeAt(a+2),n+=String.fromCharCode((15&i)<<12|(63&t)<<6|63&r),a+=3);return n}};function buf2hex(e){return _toConsumableArray(new Uint8Array(e)).map(function(e){return e.toString(16).padStart(2,"0")}).join("")}function hex2buf(e){return new Uint8Array(e.match(/[\da-f]{2}/gi).map(function(e){return parseInt(e,16)}))}function genUniqueId(){for(var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:8,t="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",r="",n=0;n<e;n++)r+=t[Math.floor(Math.random()*t.length)];return r}!function(e,t){"object"==("undefined"==typeof exports?"undefined":_typeof(exports))&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).m3u8Parser={})}(this,function(e){"use strict";var t,a=((t=r.prototype).on=function(e,t){this.listeners[e]||(this.listeners[e]=[]),this.listeners[e].push(t)},t.off=function(e,t){if(!this.listeners[e])return!1;t=this.listeners[e].indexOf(t);return this.listeners[e]=this.listeners[e].slice(0),this.listeners[e].splice(t,1),-1<t},t.trigger=function(e){var t=this.listeners[e];if(t)if(2===arguments.length)for(var r=t.length,n=0;n<r;++n)t[n].call(this,arguments[1]);else for(var a=Array.prototype.slice.call(arguments,1),i=t.length,o=0;o<i;++o)t[o].apply(this,a)},t.dispose=function(){this.listeners={}},t.pipe=function(t){this.on("data",function(e){t.push(e)})},r);function r(){this.listeners={}}var o=function(){_inherits(r,a);var t=_createSuper(r);function r(){var e;return _classCallCheck(this,r),(e=t.call(this)).buffer="",e}return _createClass(r,[{key:"push",value:function(e){var t;for(this.buffer+=e,t=this.buffer.indexOf("\n");-1<t;t=this.buffer.indexOf("\n"))this.trigger("data",this.buffer.substring(0,t)),this.buffer=this.buffer.substring(t+1)}}]),r}();function n(){return i=n=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r,n=arguments[t];for(r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},n.apply(this,arguments)}function l(e){var t=/([0-9.]*)?@?([0-9.]*)?/.exec(e||""),e={};return t[1]&&(e.length=parseInt(t[1],10)),t[2]&&(e.offset=parseInt(t[2],10)),e}function d(e){var t={};if(!e)return t;for(var r,n=e.split(new RegExp('(?:^|,)((?:[^=]*)=(?:"[^"]*"|[^,]*))')),a=n.length;a--;)""!==n[a]&&","!==n[a]&&((r=/([^=]*)=(.*)/.exec(n[a]).slice(1))[0]=r[0].replace(/^\s+|\s+$/g,""),r[1]=r[1].replace(/^\s+|\s+$/g,""),r[1]=r[1].replace(/^['"](.*)['"]$/g,"$1"),t[r[0]]=r[1]);return t}function p(e){var t=e.split("x"),e={};return t[0]&&(e.width=parseInt(t[0],10)),t[1]&&(e.height=parseInt(t[1],10)),e}var i=n,g=i,f=String.fromCharCode(9),v=function(){_inherits(r,a);var t=_createSuper(r);function r(){var e;return _classCallCheck(this,r),(e=t.call(this)).customParsers=[],e.tagMappers=[],e}return _createClass(r,[{key:"push",value:function(r){var s,u,c=this;0!==(r=r.trim()).length&&("#"===r[0]?this.tagMappers.reduce(function(e,t){t=t(r);return t===r?e:e.concat([t])},[r]).forEach(function(e){for(var t,r=0;r<c.customParsers.length;r++)if(c.customParsers[r].call(c,e))return;if(0===e.indexOf("#EXT"))if(e=e.replace("\r",""),s=/^#EXTM3U/.exec(e))c.trigger("data",{type:"tag",tagType:"m3u"});else{if(s=/^#EXTINF:([0-9\.]*)?,?(.*)?$/.exec(e))return u={type:"tag",tagType:"inf"},s[1]&&(u.duration=parseFloat(s[1])),s[2]&&(u.title=s[2]),void c.trigger("data",u);if(s=/^#EXT-X-TARGETDURATION:([0-9.]*)?/.exec(e))return u={type:"tag",tagType:"targetduration"},s[1]&&(u.duration=parseInt(s[1],10)),void c.trigger("data",u);if(s=/^#EXT-X-VERSION:([0-9.]*)?/.exec(e))return u={type:"tag",tagType:"version"},s[1]&&(u.version=parseInt(s[1],10)),void c.trigger("data",u);if(s=/^#EXT-X-MEDIA-SEQUENCE:(\-?[0-9.]*)?/.exec(e))return u={type:"tag",tagType:"media-sequence"},s[1]&&(u.number=parseInt(s[1],10)),void c.trigger("data",u);if(s=/^#EXT-X-DISCONTINUITY-SEQUENCE:(\-?[0-9.]*)?/.exec(e))return u={type:"tag",tagType:"discontinuity-sequence"},s[1]&&(u.number=parseInt(s[1],10)),void c.trigger("data",u);if(s=/^#EXT-X-PLAYLIST-TYPE:(.*)?$/.exec(e))return u={type:"tag",tagType:"playlist-type"},s[1]&&(u.playlistType=s[1]),void c.trigger("data",u);if(s=/^#EXT-X-BYTERANGE:(.*)?$/.exec(e))return u=g(l(s[1]),{type:"tag",tagType:"byterange"}),void c.trigger("data",u);if(s=/^#EXT-X-ALLOW-CACHE:(YES|NO)?/.exec(e))return u={type:"tag",tagType:"allow-cache"},s[1]&&(u.allowed=!/NO/.test(s[1])),void c.trigger("data",u);if(s=/^#EXT-X-MAP:(.*)$/.exec(e))u={type:"tag",tagType:"map"},s[1]&&((t=d(s[1])).URI&&(u.uri=t.URI),t.BYTERANGE&&(u.byterange=l(t.BYTERANGE))),c.trigger("data",u);else{if(s=/^#EXT-X-STREAM-INF:(.*)$/.exec(e))return u={type:"tag",tagType:"stream-inf"},s[1]&&(u.attributes=d(s[1]),u.attributes.RESOLUTION&&(u.attributes.RESOLUTION=p(u.attributes.RESOLUTION)),u.attributes.BANDWIDTH&&(u.attributes.BANDWIDTH=parseInt(u.attributes.BANDWIDTH,10)),u.attributes["FRAME-RATE"]&&(u.attributes["FRAME-RATE"]=parseFloat(u.attributes["FRAME-RATE"])),u.attributes["PROGRAM-ID"]&&(u.attributes["PROGRAM-ID"]=parseInt(u.attributes["PROGRAM-ID"],10))),void c.trigger("data",u);if(s=/^#EXT-X-MEDIA:(.*)$/.exec(e))return u={type:"tag",tagType:"media"},s[1]&&(u.attributes=d(s[1])),void c.trigger("data",u);if(s=/^#EXT-X-ENDLIST/.exec(e))c.trigger("data",{type:"tag",tagType:"endlist"});else if(s=/^#EXT-X-DISCONTINUITY/.exec(e))c.trigger("data",{type:"tag",tagType:"discontinuity"});else{if(s=/^#EXT-X-PROGRAM-DATE-TIME:(.*)$/.exec(e))return u={type:"tag",tagType:"program-date-time"},s[1]&&(u.dateTimeString=s[1],u.dateTimeObject=new Date(s[1])),void c.trigger("data",u);if(s=/^#EXT-X-KEY:(.*)$/.exec(e))return u={type:"tag",tagType:"key"},s[1]&&(u.attributes=d(s[1]),u.attributes.IV&&("0x"===u.attributes.IV.substring(0,2).toLowerCase()&&(u.attributes.IV=u.attributes.IV.substring(2)),u.attributes.IV=u.attributes.IV.match(/.{8}/g),u.attributes.IV[0]=parseInt(u.attributes.IV[0],16),u.attributes.IV[1]=parseInt(u.attributes.IV[1],16),u.attributes.IV[2]=parseInt(u.attributes.IV[2],16),u.attributes.IV[3]=parseInt(u.attributes.IV[3],16),u.attributes.IV=new Uint32Array(u.attributes.IV))),void c.trigger("data",u);if(s=/^#EXT-X-START:(.*)$/.exec(e))return u={type:"tag",tagType:"start"},s[1]&&(u.attributes=d(s[1]),u.attributes["TIME-OFFSET"]=parseFloat(u.attributes["TIME-OFFSET"]),u.attributes.PRECISE=/YES/.test(u.attributes.PRECISE)),void c.trigger("data",u);if(s=/^#EXT-X-CUE-OUT-CONT:(.*)?$/.exec(e))return u={type:"tag",tagType:"cue-out-cont"},s[1]?u.data=s[1]:u.data="",void c.trigger("data",u);if(s=/^#EXT-X-CUE-OUT:(.*)?$/.exec(e))return u={type:"tag",tagType:"cue-out"},s[1]?u.data=s[1]:u.data="",void c.trigger("data",u);if(s=/^#EXT-X-CUE-IN:?(.*)?$/.exec(e))return u={type:"tag",tagType:"cue-in"},s[1]?u.data=s[1]:u.data="",void c.trigger("data",u);if((s=/^#EXT-X-SKIP:(.*)$/.exec(e))&&s[1])return(u={type:"tag",tagType:"skip"}).attributes=d(s[1]),u.attributes.hasOwnProperty("SKIPPED-SEGMENTS")&&(u.attributes["SKIPPED-SEGMENTS"]=parseInt(u.attributes["SKIPPED-SEGMENTS"],10)),u.attributes.hasOwnProperty("RECENTLY-REMOVED-DATERANGES")&&(u.attributes["RECENTLY-REMOVED-DATERANGES"]=u.attributes["RECENTLY-REMOVED-DATERANGES"].split(f)),void c.trigger("data",u);if((s=/^#EXT-X-PART:(.*)$/.exec(e))&&s[1])return(u={type:"tag",tagType:"part"}).attributes=d(s[1]),["DURATION"].forEach(function(e){u.attributes.hasOwnProperty(e)&&(u.attributes[e]=parseFloat(u.attributes[e]))}),["INDEPENDENT","GAP"].forEach(function(e){u.attributes.hasOwnProperty(e)&&(u.attributes[e]=/YES/.test(u.attributes[e]))}),u.attributes.hasOwnProperty("BYTERANGE")&&(u.attributes.byterange=l(u.attributes.BYTERANGE)),void c.trigger("data",u);if((s=/^#EXT-X-SERVER-CONTROL:(.*)$/.exec(e))&&s[1])return(u={type:"tag",tagType:"server-control"}).attributes=d(s[1]),["CAN-SKIP-UNTIL","PART-HOLD-BACK","HOLD-BACK"].forEach(function(e){u.attributes.hasOwnProperty(e)&&(u.attributes[e]=parseFloat(u.attributes[e]))}),["CAN-SKIP-DATERANGES","CAN-BLOCK-RELOAD"].forEach(function(e){u.attributes.hasOwnProperty(e)&&(u.attributes[e]=/YES/.test(u.attributes[e]))}),void c.trigger("data",u);if((s=/^#EXT-X-PART-INF:(.*)$/.exec(e))&&s[1])return(u={type:"tag",tagType:"part-inf"}).attributes=d(s[1]),["PART-TARGET"].forEach(function(e){u.attributes.hasOwnProperty(e)&&(u.attributes[e]=parseFloat(u.attributes[e]))}),void c.trigger("data",u);if((s=/^#EXT-X-PRELOAD-HINT:(.*)$/.exec(e))&&s[1])return(u={type:"tag",tagType:"preload-hint"}).attributes=d(s[1]),["BYTERANGE-START","BYTERANGE-LENGTH"].forEach(function(e){var t;u.attributes.hasOwnProperty(e)&&(u.attributes[e]=parseInt(u.attributes[e],10),t="BYTERANGE-LENGTH"===e?"length":"offset",u.attributes.byterange=u.attributes.byterange||{},u.attributes.byterange[t]=u.attributes[e],delete u.attributes[e])}),void c.trigger("data",u);if((s=/^#EXT-X-RENDITION-REPORT:(.*)$/.exec(e))&&s[1])return(u={type:"tag",tagType:"rendition-report"}).attributes=d(s[1]),["LAST-MSN","LAST-PART"].forEach(function(e){u.attributes.hasOwnProperty(e)&&(u.attributes[e]=parseInt(u.attributes[e],10))}),void c.trigger("data",u);if((s=/^#EXT-X-DATERANGE:(.*)$/.exec(e))&&s[1]){(u={type:"tag",tagType:"daterange"}).attributes=d(s[1]),["ID","CLASS"].forEach(function(e){u.attributes.hasOwnProperty(e)&&(u.attributes[e]=String(u.attributes[e]))}),["START-DATE","END-DATE"].forEach(function(e){u.attributes.hasOwnProperty(e)&&(u.attributes[e]=new Date(u.attributes[e]))}),["DURATION","PLANNED-DURATION"].forEach(function(e){u.attributes.hasOwnProperty(e)&&(u.attributes[e]=parseFloat(u.attributes[e]))}),["END-ON-NEXT"].forEach(function(e){u.attributes.hasOwnProperty(e)&&(u.attributes[e]=/YES/i.test(u.attributes[e]))}),["SCTE35-CMD"," SCTE35-OUT","SCTE35-IN"].forEach(function(e){u.attributes.hasOwnProperty(e)&&(u.attributes[e]=u.attributes[e].toString(16))});var n,a,i,o=/^X-([A-Z]+-)+[A-Z]+$/;for(n in u.attributes)o.test(n)&&(a=/[0-9A-Fa-f]{6}/g.test(u.attributes[n]),i=/^\d+(\.\d+)?$/.test(u.attributes[n]),u.attributes[n]=a?u.attributes[n].toString(16):(i?parseFloat:String)(u.attributes[n]));c.trigger("data",u)}else if(s=/^#EXT-X-INDEPENDENT-SEGMENTS/.exec(e))c.trigger("data",{type:"tag",tagType:"independent-segments"});else{if(!(s=/^#EXT-X-I-FRAMES-ONLY/.exec(e)))return(s=/^#EXT-X-CONTENT-STEERING:(.*)$/.exec(e))?((u={type:"tag",tagType:"content-steering"}).attributes=d(s[1]),void c.trigger("data",u)):(s=/^#EXT-X-I-FRAME-STREAM-INF:(.*)$/.exec(e))?((u={type:"tag",tagType:"i-frame-playlist"}).attributes=d(s[1]),u.attributes.URI&&(u.uri=u.attributes.URI),u.attributes.BANDWIDTH&&(u.attributes.BANDWIDTH=parseInt(u.attributes.BANDWIDTH,10)),u.attributes.RESOLUTION&&(u.attributes.RESOLUTION=p(u.attributes.RESOLUTION)),u.attributes["AVERAGE-BANDWIDTH"]&&(u.attributes["AVERAGE-BANDWIDTH"]=parseInt(u.attributes["AVERAGE-BANDWIDTH"],10)),u.attributes["FRAME-RATE"]&&(u.attributes["FRAME-RATE"]=parseFloat(u.attributes["FRAME-RATE"])),void c.trigger("data",u)):(s=/^#EXT-X-DEFINE:(.*)$/.exec(e))?((u={type:"tag",tagType:"define"}).attributes=d(s[1]),void c.trigger("data",u)):void c.trigger("data",{type:"tag",data:e.slice(4)});c.trigger("data",{type:"tag",tagType:"i-frames-only"})}}}}else c.trigger("data",{type:"comment",text:e.slice(1)})}):this.trigger("data",{type:"uri",uri:r}))}},{key:"addParser",value:function(e){var t=this,r=e.expression,n=e.customType,a=e.dataParser,i=e.segment;"function"!=typeof a&&(a=function(e){return e}),this.customParsers.push(function(e){if(r.exec(e))return t.trigger("data",{type:"custom",data:a(e),customType:n,segment:i}),!0})}},{key:"addTagMapper",value:function(e){var t=e.expression,r=e.map;this.tagMappers.push(function(e){return t.test(e)?r(e):e})}}]),r}();var b=function(t){var r={};return Object.keys(t).forEach(function(e){r[e.toLowerCase().replace(/-(\w)/g,function(e){return e[1].toUpperCase()})]=t[e]}),r},y=function(e){var t,r,n,a,i=e.serverControl,o=e.targetDuration,s=e.partTargetDuration;i&&(t="#EXT-X-SERVER-CONTROL",r="holdBack",n="partHoldBack",a=o&&3*o,e=s&&2*s,o&&!i.hasOwnProperty(r)&&(i[r]=a,this.trigger("info",{message:"".concat(t," defaulting HOLD-BACK to targetDuration * 3 (").concat(a,").")})),a&&i[r]<a&&(this.trigger("warn",{message:"".concat(t," clamping HOLD-BACK (").concat(i[r],") to targetDuration * 3 (").concat(a,")")}),i[r]=a),s&&!i.hasOwnProperty(n)&&(i[n]=3*s,this.trigger("info",{message:"".concat(t," defaulting PART-HOLD-BACK to partTargetDuration * 3 (").concat(i[n],").")})),s&&i[n]<e&&(this.trigger("warn",{message:"".concat(t," clamping PART-HOLD-BACK (").concat(i[n],") to partTargetDuration * 2 (").concat(e,").")}),i[n]=e))};e.LineStream=o,e.ParseStream=v,e.Parser=function(){_inherits(n,a);var r=_createSuper(n);function n(){var e,t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};_classCallCheck(this,n),(e=r.call(this)).lineStream=new o,e.parseStream=new v,e.lineStream.pipe(e.parseStream),e.mainDefinitions=t.mainDefinitions||{},e.params=new URL(t.uri,"https://a.com").searchParams,e.lastProgramDateTime=null;var a,i,s=_assertThisInitialized(e),u=[],c={},l=!1,d={AUDIO:{},VIDEO:{},"CLOSED-CAPTIONS":{},SUBTITLES:{}},p=0;e.manifest={allowCache:!0,discontinuityStarts:[],dateRanges:[],iFramePlaylists:[],segments:[]};var f=0,h=0,m={};return e.on("end",function(){c.uri||!c.parts&&!c.preloadHints||(!c.map&&a&&(c.map=a),!c.key&&i&&(c.key=i),c.timeline||"number"!=typeof p||(c.timeline=p),e.manifest.preloadSegment=c)}),e.parseStream.on("data",function(o){var t,r;if(s.manifest.definitions)for(var e in s.manifest.definitions)if(o.uri&&(o.uri=o.uri.replace("{$".concat(e,"}"),s.manifest.definitions[e])),o.attributes)for(var n in o.attributes)"string"==typeof o.attributes[n]&&(o.attributes[n]=o.attributes[n].replace("{$".concat(e,"}"),s.manifest.definitions[e]));({tag:function(){({version:function(){o.version&&(this.manifest.version=o.version)},"allow-cache":function(){this.manifest.allowCache=o.allowed,"allowed"in o||(this.trigger("info",{message:"defaulting allowCache to YES"}),this.manifest.allowCache=!0)},byterange:function(){var e={};"length"in o&&((c.byterange=e).length=o.length,"offset"in o||(o.offset=f)),"offset"in o&&((c.byterange=e).offset=o.offset),f=e.offset+e.length},endlist:function(){this.manifest.endList=!0},inf:function(){"mediaSequence"in this.manifest||(this.manifest.mediaSequence=0,this.trigger("info",{message:"defaulting media sequence to zero"})),"discontinuitySequence"in this.manifest||(this.manifest.discontinuitySequence=0,this.trigger("info",{message:"defaulting discontinuity sequence to zero"})),o.title&&(c.title=o.title),0<o.duration&&(c.duration=o.duration),0===o.duration&&(c.duration=.01,this.trigger("info",{message:"updating zero segment duration to a small value"})),this.manifest.segments=u},key:function(){if(o.attributes)if("NONE"!==o.attributes.METHOD){if(o.attributes.URI)return"com.apple.streamingkeydelivery"===o.attributes.KEYFORMAT?(this.manifest.contentProtection=this.manifest.contentProtection||{},void(this.manifest.contentProtection["com.apple.fps.1_0"]={attributes:o.attributes})):"com.microsoft.playready"===o.attributes.KEYFORMAT?(this.manifest.contentProtection=this.manifest.contentProtection||{},void(this.manifest.contentProtection["com.microsoft.playready"]={uri:o.attributes.URI})):"urn:uuid:edef8ba9-79d6-4ace-a3c8-27dcd51d21ed"===o.attributes.KEYFORMAT?-1===["SAMPLE-AES","SAMPLE-AES-CTR","SAMPLE-AES-CENC"].indexOf(o.attributes.METHOD)?void this.trigger("warn",{message:"invalid key method provided for Widevine"}):("SAMPLE-AES-CENC"===o.attributes.METHOD&&this.trigger("warn",{message:"SAMPLE-AES-CENC is deprecated, please use SAMPLE-AES-CTR instead"}),"data:text/plain;base64,"!==o.attributes.URI.substring(0,23)?void this.trigger("warn",{message:"invalid key URI provided for Widevine"}):o.attributes.KEYID&&"0x"===o.attributes.KEYID.substring(0,2)?(this.manifest.contentProtection=this.manifest.contentProtection||{},void(this.manifest.contentProtection["com.widevine.alpha"]={attributes:{schemeIdUri:o.attributes.KEYFORMAT,keyId:o.attributes.KEYID.substring(2)},pssh:function(e){for(var t=(e=e,window.atob?window.atob(e):Buffer.from(e,"base64").toString("binary")),r=new Uint8Array(t.length),n=0;n<t.length;n++)r[n]=t.charCodeAt(n);return r}(o.attributes.URI.split(",")[1])})):void this.trigger("warn",{message:"invalid key ID provided for Widevine"})):(o.attributes.METHOD||this.trigger("warn",{message:"defaulting key method to AES-128"}),i={method:o.attributes.METHOD||"AES-128",uri:o.attributes.URI},void(void 0!==o.attributes.IV&&(i.iv=o.attributes.IV)));this.trigger("warn",{message:"ignoring key declaration without URI"})}else i=null;else this.trigger("warn",{message:"ignoring key declaration without attribute list"})},"media-sequence":function(){isFinite(o.number)?this.manifest.mediaSequence=o.number:this.trigger("warn",{message:"ignoring invalid media sequence: "+o.number})},"discontinuity-sequence":function(){isFinite(o.number)?(this.manifest.discontinuitySequence=o.number,p=o.number):this.trigger("warn",{message:"ignoring invalid discontinuity sequence: "+o.number})},"playlist-type":function(){/VOD|EVENT/.test(o.playlistType)?this.manifest.playlistType=o.playlistType:this.trigger("warn",{message:"ignoring unknown playlist type: "+o.playlist})},map:function(){a={},o.uri&&(a.uri=o.uri),o.byterange&&(a.byterange=o.byterange),i&&(a.key=i)},"stream-inf":function(){this.manifest.playlists=u,this.manifest.mediaGroups=this.manifest.mediaGroups||d,o.attributes?(c.attributes||(c.attributes={}),g(c.attributes,o.attributes)):this.trigger("warn",{message:"ignoring empty stream-inf attributes"})},media:function(){var e;this.manifest.mediaGroups=this.manifest.mediaGroups||d,o.attributes&&o.attributes.TYPE&&o.attributes["GROUP-ID"]&&o.attributes.NAME?((e=this.manifest.mediaGroups[o.attributes.TYPE])[o.attributes["GROUP-ID"]]=e[o.attributes["GROUP-ID"]]||{},t=e[o.attributes["GROUP-ID"]],(r={default:/yes/i.test(o.attributes.DEFAULT)}).default?r.autoselect=!0:r.autoselect=/yes/i.test(o.attributes.AUTOSELECT),o.attributes.LANGUAGE&&(r.language=o.attributes.LANGUAGE),o.attributes.URI&&(r.uri=o.attributes.URI),o.attributes["INSTREAM-ID"]&&(r.instreamId=o.attributes["INSTREAM-ID"]),o.attributes.CHARACTERISTICS&&(r.characteristics=o.attributes.CHARACTERISTICS),o.attributes.FORCED&&(r.forced=/yes/i.test(o.attributes.FORCED)),t[o.attributes.NAME]=r):this.trigger("warn",{message:"ignoring incomplete or missing media group"})},discontinuity:function(){p+=1,c.discontinuity=!0,this.manifest.discontinuityStarts.push(u.length)},"program-date-time":function(){void 0===this.manifest.dateTimeString&&(this.manifest.dateTimeString=o.dateTimeString,this.manifest.dateTimeObject=o.dateTimeObject),c.dateTimeString=o.dateTimeString,c.dateTimeObject=o.dateTimeObject;var e=this.lastProgramDateTime;this.lastProgramDateTime=new Date(o.dateTimeString).getTime(),null===e&&this.manifest.segments.reduceRight(function(e,t){return t.programDateTime=e-1e3*t.duration,t.programDateTime},this.lastProgramDateTime)},targetduration:function(){!isFinite(o.duration)||o.duration<0?this.trigger("warn",{message:"ignoring invalid target duration: "+o.duration}):(this.manifest.targetDuration=o.duration,y.call(this,this.manifest))},start:function(){o.attributes&&!isNaN(o.attributes["TIME-OFFSET"])?this.manifest.start={timeOffset:o.attributes["TIME-OFFSET"],precise:o.attributes.PRECISE}:this.trigger("warn",{message:"ignoring start declaration without appropriate attribute list"})},"cue-out":function(){c.cueOut=o.data},"cue-out-cont":function(){c.cueOutCont=o.data},"cue-in":function(){c.cueIn=o.data},skip:function(){this.manifest.skip=b(o.attributes),this.warnOnMissingAttributes_("#EXT-X-SKIP",o.attributes,["SKIPPED-SEGMENTS"])},part:function(){var r=this;l=!0;var e=this.manifest.segments.length,t=b(o.attributes);c.parts=c.parts||[],c.parts.push(t),t.byterange&&(t.byterange.hasOwnProperty("offset")||(t.byterange.offset=h),h=t.byterange.offset+t.byterange.length);t=c.parts.length-1;this.warnOnMissingAttributes_("#EXT-X-PART #".concat(t," for segment #").concat(e),o.attributes,["URI","DURATION"]),this.manifest.renditionReports&&this.manifest.renditionReports.forEach(function(e,t){e.hasOwnProperty("lastPart")||r.trigger("warn",{message:"#EXT-X-RENDITION-REPORT #".concat(t," lacks required attribute(s): LAST-PART")})})},"server-control":function(){var e=this.manifest.serverControl=b(o.attributes);e.hasOwnProperty("canBlockReload")||(e.canBlockReload=!1,this.trigger("info",{message:"#EXT-X-SERVER-CONTROL defaulting CAN-BLOCK-RELOAD to false"})),y.call(this,this.manifest),e.canSkipDateranges&&!e.hasOwnProperty("canSkipUntil")&&this.trigger("warn",{message:"#EXT-X-SERVER-CONTROL lacks required attribute CAN-SKIP-UNTIL which is required when CAN-SKIP-DATERANGES is set"})},"preload-hint":function(){var e=this.manifest.segments.length,t=b(o.attributes),r=t.type&&"PART"===t.type;c.preloadHints=c.preloadHints||[],c.preloadHints.push(t),t.byterange&&(t.byterange.hasOwnProperty("offset")||(t.byterange.offset=r?h:0,r&&(h=t.byterange.offset+t.byterange.length)));var n=c.preloadHints.length-1;if(this.warnOnMissingAttributes_("#EXT-X-PRELOAD-HINT #".concat(n," for segment #").concat(e),o.attributes,["TYPE","URI"]),t.type)for(var a=0;a<c.preloadHints.length-1;a++){var i=c.preloadHints[a];i.type&&i.type===t.type&&this.trigger("warn",{message:"#EXT-X-PRELOAD-HINT #".concat(n," for segment #").concat(e," has the same TYPE ").concat(t.type," as preload hint #").concat(a)})}},"rendition-report":function(){var e=b(o.attributes);this.manifest.renditionReports=this.manifest.renditionReports||[],this.manifest.renditionReports.push(e);var t=this.manifest.renditionReports.length-1,e=["LAST-MSN","URI"];l&&e.push("LAST-PART"),this.warnOnMissingAttributes_("#EXT-X-RENDITION-REPORT #".concat(t),o.attributes,e)},"part-inf":function(){this.manifest.partInf=b(o.attributes),this.warnOnMissingAttributes_("#EXT-X-PART-INF",o.attributes,["PART-TARGET"]),this.manifest.partInf.partTarget&&(this.manifest.partTargetDuration=this.manifest.partInf.partTarget),y.call(this,this.manifest)},daterange:function(){this.manifest.dateRanges.push(b(o.attributes));var e=this.manifest.dateRanges.length-1;this.warnOnMissingAttributes_("#EXT-X-DATERANGE #".concat(e),o.attributes,["ID","START-DATE"]);var t=this.manifest.dateRanges[e];t.endDate&&t.startDate&&new Date(t.endDate)<new Date(t.startDate)&&this.trigger("warn",{message:"EXT-X-DATERANGE END-DATE must be equal to or later than the value of the START-DATE"}),t.duration&&t.duration<0&&this.trigger("warn",{message:"EXT-X-DATERANGE DURATION must not be negative"}),t.plannedDuration&&t.plannedDuration<0&&this.trigger("warn",{message:"EXT-X-DATERANGE PLANNED-DURATION must not be negative"});var r=!!t.endOnNext;if(r&&!t.class&&this.trigger("warn",{message:"EXT-X-DATERANGE with an END-ON-NEXT=YES attribute must have a CLASS attribute"}),r&&(t.duration||t.endDate)&&this.trigger("warn",{message:"EXT-X-DATERANGE with an END-ON-NEXT=YES attribute must not contain DURATION or END-DATE attributes"}),t.duration&&t.endDate&&(a=t.startDate.getTime()+1e3*t.duration,this.manifest.dateRanges[e].endDate=new Date(a)),m[t.id]){for(var n in m[t.id])if(t[n]&&JSON.stringify(m[t.id][n])!==JSON.stringify(t[n])){this.trigger("warn",{message:"EXT-X-DATERANGE tags with the same ID in a playlist must have the same attributes values"});break}var a=this.manifest.dateRanges.findIndex(function(e){return e.id===t.id});this.manifest.dateRanges[a]=g(this.manifest.dateRanges[a],t),m[t.id]=g(m[t.id],t),this.manifest.dateRanges.pop()}else m[t.id]=t},"independent-segments":function(){this.manifest.independentSegments=!0},"i-frames-only":function(){this.manifest.iFramesOnly=!0,this.requiredCompatibilityversion(this.manifest.version,4)},"content-steering":function(){this.manifest.contentSteering=b(o.attributes),this.warnOnMissingAttributes_("#EXT-X-CONTENT-STEERING",o.attributes,["SERVER-URI"])},define:function(){var r=this;this.manifest.definitions=this.manifest.definitions||{};function e(e,t){e in r.manifest.definitions?r.trigger("error",{message:"EXT-X-DEFINE: Duplicate name ".concat(e)}):r.manifest.definitions[e]=t}if("QUERYPARAM"in o.attributes){if("NAME"in o.attributes||"IMPORT"in o.attributes)return void this.trigger("error",{message:"EXT-X-DEFINE: Invalid attributes"});var t=this.params.get(o.attributes.QUERYPARAM);return t?void e(o.attributes.QUERYPARAM,decodeURIComponent(t)):void this.trigger("error",{message:"EXT-X-DEFINE: No query param ".concat(o.attributes.QUERYPARAM)})}return"NAME"in o.attributes?"IMPORT"in o.attributes?void this.trigger("error",{message:"EXT-X-DEFINE: Invalid attributes"}):"VALUE"in o.attributes&&"string"==typeof o.attributes.VALUE?void e(o.attributes.NAME,o.attributes.VALUE):void this.trigger("error",{message:"EXT-X-DEFINE: No value for ".concat(o.attributes.NAME)}):"IMPORT"in o.attributes?this.mainDefinitions[o.attributes.IMPORT]?void e(o.attributes.IMPORT,this.mainDefinitions[o.attributes.IMPORT]):void this.trigger("error",{message:"EXT-X-DEFINE: No value ".concat(o.attributes.IMPORT," to import, or IMPORT used on main playlist")}):void this.trigger("error",{message:"EXT-X-DEFINE: No attribute"})},"i-frame-playlist":function(){this.manifest.iFramePlaylists.push({attributes:o.attributes,uri:o.uri,timeline:p}),this.warnOnMissingAttributes_("#EXT-X-I-FRAME-STREAM-INF",o.attributes,["BANDWIDTH","URI"])}}[o.tagType]||function(){}).call(s)},uri:function(){c.uri=o.uri,u.push(c),!this.manifest.targetDuration||"duration"in c||(this.trigger("warn",{message:"defaulting segment duration to the target duration"}),c.duration=this.manifest.targetDuration),i&&(c.key=i),c.timeline=p,a&&(c.map=a),h=0,null!==this.lastProgramDateTime&&(c.programDateTime=this.lastProgramDateTime,this.lastProgramDateTime+=1e3*c.duration),c={}},comment:function(){},custom:function(){o.segment?(c.custom=c.custom||{},c.custom[o.customType]=o.data):(this.manifest.custom=this.manifest.custom||{},this.manifest.custom[o.customType]=o.data)}})[o.type].call(s)}),e}return _createClass(n,[{key:"requiredCompatibilityversion",value:function(e,t){(e<t||!e)&&this.trigger("warn",{message:"manifest must be at least version ".concat(t)})}},{key:"warnOnMissingAttributes_",value:function(e,t,r){var n=[];r.forEach(function(e){t.hasOwnProperty(e)||n.push(e)}),n.length&&this.trigger("warn",{message:"".concat(e," lacks required attribute(s): ").concat(n.join(", "))})}},{key:"push",value:function(e){this.lineStream.push(e)}},{key:"end",value:function(){this.lineStream.push("\n"),this.manifest.dateRanges.length&&null===this.lastProgramDateTime&&this.trigger("warn",{message:"A playlist with EXT-X-DATERANGE tag must contain atleast one EXT-X-PROGRAM-DATE-TIME tag"}),this.lastProgramDateTime=null,this.trigger("end")}},{key:"addParser",value:function(e){this.parseStream.addParser(e)}},{key:"addTagMapper",value:function(e){this.parseStream.addTagMapper(e)}}]),n}(),Object.defineProperty(e,"__esModule",{value:!0})}),function(e,t){"object"==("undefined"==typeof exports?"undefined":_typeof(exports))&&"object"==("undefined"==typeof module?"undefined":_typeof(module))?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==("undefined"==typeof exports?"undefined":_typeof(exports))?exports.xmldom=t():e.xmldom=t()}(this,function(){"use strict";var r={539:function(e,t){function n(e){return(n="function"==typeof Symbol&&"symbol"==_typeof(Symbol.iterator)?function(e){return _typeof(e)}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":_typeof(e)})(e)}function r(e,t){return void 0===t&&(t=Object),t&&"function"==typeof t.getOwnPropertyDescriptors&&(e=t.create(null,t.getOwnPropertyDescriptors(e))),t&&"function"==typeof t.freeze?t.freeze(e):e}function i(e,t){return Object.prototype.hasOwnProperty.call(e,t)}var a=r({allowfullscreen:!0,async:!0,autofocus:!0,autoplay:!0,checked:!0,controls:!0,default:!0,defer:!0,disabled:!0,formnovalidate:!0,hidden:!0,ismap:!0,itemscope:!0,loop:!0,multiple:!0,muted:!0,nomodule:!0,novalidate:!0,open:!0,playsinline:!0,readonly:!0,required:!0,reversed:!0,selected:!0}),o=r({area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0}),s=r({script:!1,style:!1,textarea:!0,title:!0});function u(e){return e===c.HTML}var c=r({HTML:"text/html",XML_APPLICATION:"application/xml",XML_TEXT:"text/xml",XML_XHTML_APPLICATION:"application/xhtml+xml",XML_SVG_IMAGE:"image/svg+xml"}),l=Object.keys(c).map(function(e){return c[e]}),d=r({HTML:"http://www.w3.org/1999/xhtml",SVG:"http://www.w3.org/2000/svg",XML:"http://www.w3.org/XML/1998/namespace",XMLNS:"http://www.w3.org/2000/xmlns/"});t.assign=function(e,t){if(null===e||"object"!==n(e))throw new TypeError("target is not an object");for(var r in t)i(t,r)&&(e[r]=t[r]);return e},t.find=function(e,t,r){if(void 0===r&&(r=Array.prototype),e&&"function"==typeof r.find)return r.find.call(e,t);for(var n=0;n<e.length;n++)if(i(e,n)){var a=e[n];if(t.call(void 0,a,n,e))return a}},t.freeze=r,t.HTML_BOOLEAN_ATTRIBUTES=a,t.HTML_RAW_TEXT_ELEMENTS=s,t.HTML_VOID_ELEMENTS=o,t.hasDefaultHTMLNamespace=function(e){return u(e)||e===c.XML_XHTML_APPLICATION},t.hasOwn=i,t.isHTMLBooleanAttribute=function(e){return i(a,e.toLowerCase())},t.isHTMLRawTextElement=function(e){e=e.toLowerCase();return i(s,e)&&!s[e]},t.isHTMLEscapableRawTextElement=function(e){e=e.toLowerCase();return i(s,e)&&s[e]},t.isHTMLMimeType=u,t.isHTMLVoidElement=function(e){return i(o,e.toLowerCase())},t.isValidMimeType=function(e){return-1<l.indexOf(e)},t.MIME_TYPE=c,t.NAMESPACE=d},579:function(e,t,r){var o=r(539),n=r(179),a=r(180),s=r(984),r=r(87),i=n.DOMImplementation,u=o.hasDefaultHTMLNamespace,c=o.isHTMLMimeType,l=o.isValidMimeType,d=o.MIME_TYPE,p=o.NAMESPACE,f=a.ParseError,h=r.XMLReader;function m(e){return e.replace(/\r[\n\u0085]/g,"\n").replace(/[\r\u0085\u2028]/g,"\n")}function g(e){if(e=e||{locator:!0},this.assign=e.assign||o.assign,this.domHandler=e.domHandler||v,this.onError=e.onError||e.errorHandler,e.errorHandler&&"function"!=typeof e.errorHandler)throw new TypeError("errorHandler object is no longer supported, switch to onError!");e.errorHandler&&e.errorHandler("warning","The `errorHandler` option has been deprecated, use `onError` instead!",this),this.normalizeLineEndings=e.normalizeLineEndings||m,this.locator=!!e.locator,this.xmlns=this.assign(Object.create(null),e.xmlns)}function v(e){e=e||{};this.mimeType=e.mimeType||d.XML_APPLICATION,this.defaultNamespace=e.defaultNamespace||null,this.cdata=!1,this.currentElement=void 0,this.doc=void 0,this.locator=void 0,this.onError=e.onError}function b(e,t){t.lineNumber=e.lineNumber,t.columnNumber=e.columnNumber}function y(e,t,r){return"string"==typeof e?e.substr(t,r):e.length>=t+r||t?new java.lang.String(e,t,r)+"":e}function _(e,t){(e.currentElement||e.doc).appendChild(t)}g.prototype.parseFromString=function(e,t){if(!l(t))throw new TypeError('DOMParser.parseFromString: the provided mimeType "'+t+'" is not valid.');var r=this.assign(Object.create(null),this.xmlns),n=s.XML_ENTITIES,a=r[""]||null;u(t)?(n=s.HTML_ENTITIES,a=p.HTML):t===d.XML_SVG_IMAGE&&(a=p.SVG),r[""]=a,r.xml=r.xml||p.XML;var i=new this.domHandler({mimeType:t,defaultNamespace:a,onError:this.onError}),a=this.locator?{}:void 0;this.locator&&i.setDocumentLocator(a);a=new h;return a.errorHandler=i,a.domBuilder=i,o.isHTMLMimeType(t)||"string"==typeof e||a.errorHandler.fatalError("source is not a string"),a.parse(this.normalizeLineEndings(String(e)),r,n),i.doc.documentElement||a.errorHandler.fatalError("missing root element"),i.doc},v.prototype={startDocument:function(){var e=new i;this.doc=c(this.mimeType)?e.createHTMLDocument(!1):e.createDocument(this.defaultNamespace,"")},startElement:function(e,t,r,n){var a=this.doc,i=a.createElementNS(e,r||t),o=n.length;_(this,i),this.currentElement=i,this.locator&&b(this.locator,i);for(var s=0;s<o;s++){e=n.getURI(s);var u=n.getValue(s),c=(r=n.getQName(s),a.createAttributeNS(e,r));this.locator&&b(n.getLocator(s),c),c.value=c.nodeValue=u,i.setAttributeNode(c)}},endElement:function(e,t,r){this.currentElement=this.currentElement.parentNode},startPrefixMapping:function(e,t){},endPrefixMapping:function(e){},processingInstruction:function(e,t){t=this.doc.createProcessingInstruction(e,t);this.locator&&b(this.locator,t),_(this,t)},ignorableWhitespace:function(e,t,r){},characters:function(e,t,r){var n;(e=y.apply(this,arguments))&&(n=this.cdata?this.doc.createCDATASection(e):this.doc.createTextNode(e),this.currentElement?this.currentElement.appendChild(n):/^\s*$/.test(e)&&this.doc.appendChild(n),this.locator&&b(this.locator,n))},skippedEntity:function(e){},endDocument:function(){this.doc.normalize()},setDocumentLocator:function(e){e&&(e.lineNumber=0),this.locator=e},comment:function(e,t,r){e=y.apply(this,arguments);e=this.doc.createComment(e);this.locator&&b(this.locator,e),_(this,e)},startCDATA:function(){this.cdata=!0},endCDATA:function(){this.cdata=!1},startDTD:function(e,t,r,n){var a=this.doc.implementation;a&&a.createDocumentType&&(n=a.createDocumentType(e,t,r,n),this.locator&&b(this.locator,n),_(this,n),this.doc.doctype=n)},reportError:function(t,r){if("function"==typeof this.onError)try{this.onError(t,r,this)}catch(e){throw new f("Reporting "+t+' "'+r+'" caused '+e,this.locator)}},warning:function(e){this.reportError("warning",e)},error:function(e){this.reportError("error",e)},fatalError:function(e){throw this.reportError("fatalError",e),new f(e,this.locator)}},"endDTD,startEntity,endEntity,attributeDecl,elementDecl,externalEntityDecl,internalEntityDecl,resolveEntity,getExternalSubset,notationDecl,unparsedEntityDecl".replace(/\w+/g,function(e){v.prototype[e]=function(){return null}}),t.__DOMHandler=v,t.DOMParser=g,t.normalizeLineEndings=m,t.onErrorStopParsing=function(e){if("error"===e)throw"onErrorStopParsing"},t.onWarningStopParsing=function(){throw"onWarningStopParsing"}},179:function(e,t,r){function p(e){return(p="function"==typeof Symbol&&"symbol"==_typeof(Symbol.iterator)?function(e){return _typeof(e)}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":_typeof(e)})(e)}var i=r(539),o=i.find,n=i.hasDefaultHTMLNamespace,f=i.hasOwn,a=i.isHTMLMimeType,_=i.isHTMLRawTextElement,w=i.isHTMLVoidElement,s=i.MIME_TYPE,E=i.NAMESPACE,h=Symbol(),u=r(180).DOMException,T=r(722);function c(e){if(e!==h)throw new TypeError("Illegal constructor")}function l(e){return""!==e}function d(e,t){return f(e,t)||(e[t]=!0),e}function m(e){if(!e)return[];e=e?e.split(/[\t\n\f\r ]+/).filter(l):[];return Object.keys(e.reduce(d,{}))}function g(e){if(!T.QName_exact.test(e))throw new u(u.INVALID_CHARACTER_ERR,'invalid character in qualified name "'+e+'"')}function v(e,t){g(t),e=e||null;var r,n=null,a=t;if(0<=t.indexOf(":")&&(n=(r=t.split(":"))[0],a=r[1]),null!==n&&null===e)throw new u(u.NAMESPACE_ERR,"prefix is non-null and namespace is null");if("xml"===n&&e!==i.NAMESPACE.XML)throw new u(u.NAMESPACE_ERR,'prefix is "xml" and namespace is not the XML namespace');if(("xmlns"===n||"xmlns"===t)&&e!==i.NAMESPACE.XMLNS)throw new u(u.NAMESPACE_ERR,'either qualifiedName or prefix is "xmlns" and namespace is not the XMLNS namespace');if(e===i.NAMESPACE.XMLNS&&"xmlns"!==n&&"xmlns"!==t)throw new u(u.NAMESPACE_ERR,'namespace is the XMLNS namespace and neither qualifiedName nor prefix is "xmlns"');return[e,n,a]}function b(e,t){for(var r in e)f(e,r)&&(t[r]=e[r])}function y(e,t){var r,n=e.prototype;n instanceof t||((r=function(){}).prototype=t.prototype,b(n,r=new r),e.prototype=n=r),n.constructor!=e&&(n.constructor=e)}var S,r={},x=r.ELEMENT_NODE=1,k=r.ATTRIBUTE_NODE=2,R=r.TEXT_NODE=3,I=r.CDATA_SECTION_NODE=4,A=r.ENTITY_REFERENCE_NODE=5,N=(r.ENTITY_NODE=6,r.PROCESSING_INSTRUCTION_NODE=7),D=r.COMMENT_NODE=8,O=r.DOCUMENT_NODE=9,C=r.DOCUMENT_TYPE_NODE=10,L=r.DOCUMENT_FRAGMENT_NODE=11,P=(r.NOTATION_NODE=12,i.freeze({DOCUMENT_POSITION_DISCONNECTED:1,DOCUMENT_POSITION_PRECEDING:2,DOCUMENT_POSITION_FOLLOWING:4,DOCUMENT_POSITION_CONTAINS:8,DOCUMENT_POSITION_CONTAINED_BY:16,DOCUMENT_POSITION_IMPLEMENTATION_SPECIFIC:32}));function M(e){for(var t=[];e.parentNode||e.ownerElement;)e=e.parentNode||e.ownerElement,t.unshift(e);return t}function U(e){return e.guid||(e.guid=Math.random()),e.guid}function j(){}function F(e,t){this._node=e,this._refresh=t,q(this)}function q(e){var t=e._node._inc||e._node.ownerDocument._inc;if(e._inc!==t){var r=e._refresh(e._node);if(Ee(e,"length",r.length),!e.$$length||r.length<e.$$length)for(var n=r.length;n in e;n++)f(e,n)&&delete e[n];b(r,e),e._inc=t}}function H(){}function G(e,t){for(var r=0;r<e.length;){if(e[r]===t)return r;r++}}function B(e,t,r){var n=G(t,r);if(0<=n){for(var a,i=t.length-1;n<=i;)t[n]=t[++n];t.length=i,e&&((a=e.ownerDocument)&&$(a,e,r),r.ownerElement=null)}}function V(){}function X(e){c(e)}function z(e){return("<"==e?"&lt;":">"==e&&"&gt;")||"&"==e&&"&amp;"||'"'==e&&"&quot;"||"&#"+e.charCodeAt()+";"}function W(e,t){if(t(e))return 1;if(e=e.firstChild)do{if(W(e,t))return 1}while(e=e.nextSibling)}function Y(e,t){c(e);t=t||{};(this.ownerDocument=this).contentType=t.contentType||s.XML_APPLICATION,this.type=a(this.contentType)?"html":"xml"}function $(e,t,r){e&&e._inc++,r.namespaceURI===E.XMLNS&&delete t._nsMap[r.prefix?r.localName:""]}function K(e,t,r){if(e&&e._inc){e._inc++;var n=t.childNodes;if(r)n[n.length++]=r;else{for(var a=t.firstChild,i=0;a;)a=(n[i++]=a).nextSibling;n.length=i,delete n[n.length]}}}function Q(e,t){if(e!==t.parentNode)throw new u(u.NOT_FOUND_ERR,"child's parent is not parent");var r=t.previousSibling,n=t.nextSibling;return r?r.nextSibling=n:e.firstChild=n,n?n.previousSibling=r:e.lastChild=r,K(e.ownerDocument,e),t.parentNode=null,t.previousSibling=null,t.nextSibling=null,t}function J(e){return e&&e.nodeType===X.DOCUMENT_TYPE_NODE}function Z(e){return e&&e.nodeType===X.ELEMENT_NODE}function ee(e){return e&&e.nodeType===X.TEXT_NODE}function te(e,t){var r=e.childNodes||[];if(!o(r,Z)&&!J(t)){e=o(r,J);return!(t&&e&&r.indexOf(e)>r.indexOf(t))}}function re(e,t){var r=e.childNodes||[];if(!o(r,function(e){return Z(e)&&e!==t})){e=o(r,J);return!(t&&e&&r.indexOf(e)>r.indexOf(t))}}function ne(e,t,r){var n=e.childNodes||[],a=t.childNodes||[];if(t.nodeType===X.DOCUMENT_FRAGMENT_NODE){var i=a.filter(Z);if(1<i.length||o(a,ee))throw new u(u.HIERARCHY_REQUEST_ERR,"More than one element or text in fragment");if(1===i.length&&!re(e,r))throw new u(u.HIERARCHY_REQUEST_ERR,"Element in fragment can not be inserted before doctype")}if(Z(t)&&!re(e,r))throw new u(u.HIERARCHY_REQUEST_ERR,"Only one element can be added and only after doctype");if(J(t)){if(o(n,function(e){return J(e)&&e!==r}))throw new u(u.HIERARCHY_REQUEST_ERR,"Only one doctype is allowed");t=o(n,Z);if(r&&n.indexOf(t)<n.indexOf(r))throw new u(u.HIERARCHY_REQUEST_ERR,"Doctype can only be inserted before an element")}}function ae(e,t,r,n){(function(e,t,r){if(!e||e.nodeType!==X.DOCUMENT_NODE&&e.nodeType!==X.DOCUMENT_FRAGMENT_NODE&&e.nodeType!==X.ELEMENT_NODE)throw new u(u.HIERARCHY_REQUEST_ERR,"Unexpected parent node type "+e.nodeType);if(r&&r.parentNode!==e)throw new u(u.NOT_FOUND_ERR,"child not in parent");if(!t||!(Z(t)||t instanceof se||J(t)||t.nodeType===X.DOCUMENT_FRAGMENT_NODE||t.nodeType===X.COMMENT_NODE||t.nodeType===X.PROCESSING_INSTRUCTION_NODE)||J(t)&&e.nodeType!==X.DOCUMENT_NODE)throw new u(u.HIERARCHY_REQUEST_ERR,"Unexpected node type "+t.nodeType+" for parent node type "+e.nodeType)})(e,t,r),e.nodeType===X.DOCUMENT_NODE&&(n||function(e,t,r){var n=e.childNodes||[],a=t.childNodes||[];if(t.nodeType===X.DOCUMENT_FRAGMENT_NODE){var i=a.filter(Z);if(1<i.length||o(a,ee))throw new u(u.HIERARCHY_REQUEST_ERR,"More than one element or text in fragment");if(1===i.length&&!te(e,r))throw new u(u.HIERARCHY_REQUEST_ERR,"Element in fragment can not be inserted before doctype")}if(Z(t)&&!te(e,r))throw new u(u.HIERARCHY_REQUEST_ERR,"Only one element can be added and only after doctype");if(J(t)){if(o(n,J))throw new u(u.HIERARCHY_REQUEST_ERR,"Only one doctype is allowed");t=o(n,Z);if(r&&n.indexOf(t)<n.indexOf(r))throw new u(u.HIERARCHY_REQUEST_ERR,"Doctype can only be inserted before an element");if(!r&&t)throw new u(u.HIERARCHY_REQUEST_ERR,"Doctype can not be appended since element is present")}})(e,t,r);n=t.parentNode;if(n&&n.removeChild(t),t.nodeType===L){var a=t.firstChild;if(null==a)return t;var i=t.lastChild}else a=i=t;n=r?r.previousSibling:e.lastChild;for(a.previousSibling=n,i.nextSibling=r,n?n.nextSibling=a:e.firstChild=a,null==r?e.lastChild=i:r.previousSibling=i;a.parentNode=e,a!==i&&(a=a.nextSibling););return K(e.ownerDocument||e,e),t.nodeType==L&&(t.firstChild=t.lastChild=null),t}function ie(e){c(e),this._nsMap=Object.create(null)}function oe(e){c(e),this.namespaceURI=null,this.prefix=null,this.ownerElement=null}function se(e){c(e)}function ue(e){c(e)}function ce(e){c(e)}function le(e){c(e)}function de(e){c(e)}function pe(e){c(e)}function fe(e){c(e)}function he(e){c(e)}function me(e){c(e)}function ge(e){c(e)}function ve(){}function be(e){var t,r=[],n=this.nodeType===O&&this.documentElement||this,a=n.prefix,i=n.namespaceURI;return i&&null==a&&null==(a=n.lookupPrefix(i))&&(t=[{namespace:i,prefix:null}]),we(this,r,e,t),r.join("")}function ye(e,t,r){var n=e.prefix||"",a=e.namespaceURI;if(a&&("xml"!==n||a!==E.XML)&&a!==E.XMLNS){for(var i=r.length;i--;){var o=r[i];if(o.prefix===n)return o.namespace!==a}return 1}}function _e(e,t,r){e.push(" ",t,'="',r.replace(/[<>&"\t\n\r]/g,z),'"')}function we(e,t,r,n){n=n||[];var a="html"===(e.nodeType===O?e:e.ownerDocument).type;if(r){if(!(e=r(e)))return;if("string"==typeof e)return t.push(e),0}switch(e.nodeType){case x:var i=e.attributes,o=i.length,s=e.firstChild,u=e.tagName,c=u;if(!a&&!e.prefix&&e.namespaceURI){for(var l,d,p=0;p<i.length;p++)if("xmlns"===i.item(p).name){l=i.item(p).value;break}if(!l)for(var f=n.length-1;0<=f;f--)if(""===(d=n[f]).prefix&&d.namespace===e.namespaceURI){l=d.namespace;break}if(l!==e.namespaceURI)for(f=n.length-1;0<=f;f--)if((d=n[f]).namespace===e.namespaceURI){d.prefix&&(c=d.prefix+":"+u);break}}t.push("<",c);for(var h,m,g,v=0;v<o;v++)"xmlns"==(h=i.item(v)).prefix?n.push({prefix:h.localName,namespace:h.value}):"xmlns"==h.nodeName&&n.push({prefix:"",namespace:h.value});for(v=0;v<o;v++)ye(h=i.item(v),0,n)&&(_e(t,(m=h.prefix||"")?"xmlns:"+m:"xmlns",g=h.namespaceURI),n.push({prefix:m,namespace:g})),we(h,t,r,n);u===c&&ye(e,0,n)&&(_e(t,(m=e.prefix||"")?"xmlns:"+m:"xmlns",g=e.namespaceURI),n.push({prefix:m,namespace:g}));var b=!s;if(b&&(a||e.namespaceURI===E.HTML)&&(b=w(u)),b)t.push("/>");else{if(t.push(">"),a&&_(u))for(;s;)s.data?t.push(s.data):we(s,t,r,n.slice()),s=s.nextSibling;else for(;s;)we(s,t,r,n.slice()),s=s.nextSibling;t.push("</",c,">")}return;case O:case L:for(s=e.firstChild;s;)we(s,t,r,n.slice()),s=s.nextSibling;return;case k:return _e(t,e.name,e.value),0;case R:return t.push(e.data.replace(/[<&>]/g,z));case I:return t.push(T.CDATA_START,e.data,T.CDATA_END);case D:return t.push(T.COMMENT_START,e.data,T.COMMENT_END);case C:var y=e.publicId,b=e.systemId;return t.push(T.DOCTYPE_DECL_START," ",e.name),y?(t.push(" ",T.PUBLIC," ",y),b&&"."!==b&&t.push(" ",b)):b&&"."!==b&&t.push(" ",T.SYSTEM," ",b),e.internalSubset&&t.push(" [",e.internalSubset,"]"),t.push(">"),0;case N:return t.push("<?",e.target," ",e.data,"?>");case A:return t.push("&",e.nodeName,";");default:t.push("??",e.nodeName)}}function Ee(e,t,r){e[t]=r}(j.prototype={length:0,item:function(e){return 0<=e&&e<this.length?this[e]:null},toString:function(e){for(var t=[],r=0;r<this.length;r++)we(this[r],t,e);return t.join("")},filter:function(e){return Array.prototype.filter.call(this,e)},indexOf:function(e){return Array.prototype.indexOf.call(this,e)}})[Symbol.iterator]=function(){var e=this,t=0;return{next:function(){return t<e.length?{value:e[t++],done:!1}:{done:!0}},return:function(){return{done:!0}}}},F.prototype.item=function(e){return q(this),this[e]||null},y(F,j),(H.prototype={length:0,item:j.prototype.item,getNamedItem:function(e){this._ownerElement&&this._ownerElement._isInHTMLDocumentAndNamespace()&&(e=e.toLowerCase());for(var t=0;t<this.length;){var r=this[t];if(r.nodeName===e)return r;t++}return null},setNamedItem:function(e){var t=e.ownerElement;if(t&&t!==this._ownerElement)throw new u(u.INUSE_ATTRIBUTE_ERR);var r,n,a,i=this.getNamedItemNS(e.namespaceURI,e.localName);return i===e?e:(r=this._ownerElement,n=this,t=e,(e=i)?n[G(n,e)]=t:(n[n.length]=t,n.length++),r&&(a=(t.ownerElement=r).ownerDocument)&&(e&&$(a,r,e),r=r,t=t,a&&a._inc++,t.namespaceURI===E.XMLNS&&(r._nsMap[t.prefix?t.localName:""]=t.value)),i)},setNamedItemNS:function(e){return this.setNamedItem(e)},removeNamedItem:function(e){var t=this.getNamedItem(e);if(!t)throw new u(u.NOT_FOUND_ERR,e);return B(this._ownerElement,this,t),t},removeNamedItemNS:function(e,t){var r=this.getNamedItemNS(e,t);if(!r)throw new u(u.NOT_FOUND_ERR,e?e+" : "+t:t);return B(this._ownerElement,this,r),r},getNamedItemNS:function(e,t){e=e||null;for(var r=0;r<this.length;){var n=this[r];if(n.localName===t&&n.namespaceURI===e)return n;r++}return null}})[Symbol.iterator]=function(){var e=this,t=0;return{next:function(){return t<e.length?{value:e[t++],done:!1}:{done:!0}},return:function(){return{done:!0}}}},V.prototype={hasFeature:function(e,t){return!0},createDocument:function(e,t,r){var n=s.XML_APPLICATION;e===E.HTML?n=s.XML_XHTML_APPLICATION:e===E.SVG&&(n=s.XML_SVG_IMAGE);n=new Y(h,{contentType:n});return n.implementation=this,n.childNodes=new j,n.doctype=r||null,r&&n.appendChild(r),t&&(t=n.createElementNS(e,t),n.appendChild(t)),n},createDocumentType:function(e,t,r,n){g(e);var a=new de(h);return a.name=e,a.nodeName=e,a.publicId=t||"",a.systemId=r||"",a.internalSubset=n||"",a},createHTMLDocument:function(e){var t,r,n,a=new Y(h,{contentType:s.HTML});return a.implementation=this,a.childNodes=new j,!1!==e&&(a.doctype=this.createDocumentType("html"),(a.doctype.ownerDocument=a).appendChild(a.doctype),t=a.createElement("html"),a.appendChild(t),r=a.createElement("head"),t.appendChild(r),"string"==typeof e&&((n=a.createElement("title")).appendChild(a.createTextNode(e)),r.appendChild(n)),t.appendChild(a.createElement("body"))),a}},X.prototype={firstChild:null,lastChild:null,previousSibling:null,nextSibling:null,parentNode:null,childNodes:null,ownerDocument:null,nodeValue:null,namespaceURI:null,prefix:null,localName:null,insertBefore:function(e,t){return ae(this,e,t)},replaceChild:function(e,t){ae(this,e,t,ne),t&&this.removeChild(t)},removeChild:function(e){return Q(this,e)},appendChild:function(e){return this.insertBefore(e,null)},hasChildNodes:function(){return null!=this.firstChild},cloneNode:function(e){return function e(t,r,n){var a,i,o=new r.constructor(h);for(a in r)!f(r,a)||"object"!=p(i=r[a])&&i!=o[a]&&(o[a]=i);switch(r.childNodes&&(o.childNodes=new j),o.ownerDocument=t,o.nodeType){case x:var s=r.attributes,u=o.attributes=new H,c=s.length;u._ownerElement=o;for(var l=0;l<c;l++)o.setAttributeNode(e(t,s.item(l),!0));break;case k:n=!0}if(n)for(var d=r.firstChild;d;)o.appendChild(e(t,d,n)),d=d.nextSibling;return o}(this.ownerDocument||this,this,e)},normalize:function(){for(var e=this.firstChild;e;){var t=e.nextSibling;t&&t.nodeType==R&&e.nodeType==R?(this.removeChild(t),e.appendData(t.data)):(e.normalize(),e=t)}},isSupported:function(e,t){return this.ownerDocument.implementation.hasFeature(e,t)},lookupPrefix:function(e){for(var t=this;t;){var r=t._nsMap;if(r)for(var n in r)if(f(r,n)&&r[n]===e)return n;t=t.nodeType==k?t.ownerDocument:t.parentNode}return null},lookupNamespaceURI:function(e){for(var t=this;t;){var r=t._nsMap;if(r&&f(r,e))return r[e];t=t.nodeType==k?t.ownerDocument:t.parentNode}return null},isDefaultNamespace:function(e){return null==this.lookupPrefix(e)},compareDocumentPosition:function(e){if(this===e)return 0;var t=e,r=this,n=null,a=null;if(t instanceof oe&&(t=(n=t).ownerElement),r instanceof oe&&(r=(a=r).ownerElement,n&&t&&r===t))for(var i,o=0;i=r.attributes[o];o++){if(i===n)return P.DOCUMENT_POSITION_IMPLEMENTATION_SPECIFIC+P.DOCUMENT_POSITION_PRECEDING;if(i===a)return P.DOCUMENT_POSITION_IMPLEMENTATION_SPECIFIC+P.DOCUMENT_POSITION_FOLLOWING}if(!t||!r||r.ownerDocument!==t.ownerDocument)return P.DOCUMENT_POSITION_DISCONNECTED+P.DOCUMENT_POSITION_IMPLEMENTATION_SPECIFIC+(U(r.ownerDocument)>U(t.ownerDocument)?P.DOCUMENT_POSITION_FOLLOWING:P.DOCUMENT_POSITION_PRECEDING);var s=M(t),u=M(r);if(!n&&0<=u.indexOf(t)||a&&t===r)return P.DOCUMENT_POSITION_CONTAINS+P.DOCUMENT_POSITION_PRECEDING;if(!a&&0<=s.indexOf(r)||n&&t===r)return P.DOCUMENT_POSITION_CONTAINED_BY+P.DOCUMENT_POSITION_FOLLOWING;var c,l=function e(t,r){if(r.length<t.length)return e(r,t);var n,a=null;for(n in t){if(t[n]!==r[n])return a;a=t[n]}return a}(u,s);for(c in l.childNodes){var d=l.childNodes[c];if(d===r)return P.DOCUMENT_POSITION_FOLLOWING;if(d===t)return P.DOCUMENT_POSITION_PRECEDING;if(0<=u.indexOf(d))return P.DOCUMENT_POSITION_FOLLOWING;if(0<=s.indexOf(d))return P.DOCUMENT_POSITION_PRECEDING}return 0}},b(r,X),b(r,X.prototype),b(P,X),b(P,X.prototype),Y.prototype={implementation:null,nodeName:"#document",nodeType:O,doctype:null,documentElement:null,_inc:1,insertBefore:function(e,t){if(e.nodeType!==L)return ae(this,e,t),null===(e.ownerDocument=this).documentElement&&e.nodeType===x&&(this.documentElement=e),e;for(var r=e.firstChild;r;){var n=r.nextSibling;this.insertBefore(r,t),r=n}return e},removeChild:function(e){e=Q(this,e);return e===this.documentElement&&(this.documentElement=null),e},replaceChild:function(e,t){ae(this,e,t,ne),e.ownerDocument=this,t&&this.removeChild(t),Z(e)&&(this.documentElement=e)},importNode:function(e,t){return function e(t,r,n){var a;switch(r.nodeType){case x:(a=r.cloneNode(!1)).ownerDocument=t;case L:break;case k:n=!0}if((a=a||r.cloneNode(!1)).ownerDocument=t,a.parentNode=null,n)for(var i=r.firstChild;i;)a.appendChild(e(t,i,n)),i=i.nextSibling;return a}(this,e,t)},getElementById:function(t){var r=null;return W(this.documentElement,function(e){if(e.nodeType==x&&e.getAttribute("id")==t)return r=e,!0}),r},createElement:function(e){var t=new ie(h);return"html"===(t.ownerDocument=this).type&&(e=e.toLowerCase()),n(this.contentType)&&(t.namespaceURI=E.HTML),t.nodeName=e,t.tagName=e,t.localName=e,t.childNodes=new j,(t.attributes=new H)._ownerElement=t},createDocumentFragment:function(){var e=new me(h);return e.ownerDocument=this,e.childNodes=new j,e},createTextNode:function(e){var t=new ue(h);return t.ownerDocument=this,t.appendData(e),t},createComment:function(e){var t=new ce(h);return t.ownerDocument=this,t.appendData(e),t},createCDATASection:function(e){var t=new le(h);return t.ownerDocument=this,t.appendData(e),t},createProcessingInstruction:function(e,t){var r=new ge(h);return r.ownerDocument=this,r.nodeName=r.target=e,r.nodeValue=r.data=t,r},createAttribute:function(e){if(!T.QName_exact.test(e))throw new u(u.INVALID_CHARACTER_ERR,'invalid character in name "'+e+'"');return"html"===this.type&&(e=e.toLowerCase()),this._createAttribute(e)},_createAttribute:function(e){var t=new oe(h);return t.ownerDocument=this,t.name=e,t.nodeName=e,t.localName=e,t.specified=!0,t},createEntityReference:function(e){var t=new he(h);return t.ownerDocument=this,t.nodeName=e,t},createElementNS:function(e,t){var r=v(e,t),n=new ie(h),e=n.attributes=new H;return n.childNodes=new j,n.ownerDocument=this,n.nodeName=t,n.tagName=t,n.namespaceURI=r[0],n.prefix=r[1],n.localName=r[2],e._ownerElement=n},createAttributeNS:function(e,t){var r=v(e,t),e=new oe(h);return e.ownerDocument=this,e.nodeName=t,e.name=t,e.specified=!0,e.namespaceURI=r[0],e.prefix=r[1],e.localName=r[2],e}},y(Y,X),Y.prototype.getElementsByClassName=(ie.prototype={nodeType:x,attributes:null,getQualifiedName:function(){return this.prefix?this.prefix+":"+this.localName:this.localName},_isInHTMLDocumentAndNamespace:function(){return"html"===this.ownerDocument.type&&this.namespaceURI===E.HTML},hasAttribute:function(e){return!!this.getAttributeNode(e)},getAttribute:function(e){e=this.getAttributeNode(e);return e?e.value:null},getAttributeNode:function(e){return this._isInHTMLDocumentAndNamespace()&&(e=e.toLowerCase()),this.attributes.getNamedItem(e)},setAttribute:function(e,t){this._isInHTMLDocumentAndNamespace()&&(e=e.toLowerCase());var r=this.getAttributeNode(e);r?r.value=r.nodeValue=""+t:((r=this.ownerDocument._createAttribute(e)).value=r.nodeValue=""+t,this.setAttributeNode(r))},removeAttribute:function(e){e=this.getAttributeNode(e);e&&this.removeAttributeNode(e)},setAttributeNode:function(e){return this.attributes.setNamedItem(e)},setAttributeNodeNS:function(e){return this.attributes.setNamedItemNS(e)},removeAttributeNode:function(e){return this.attributes.removeNamedItem(e.nodeName)},removeAttributeNS:function(e,t){t=this.getAttributeNodeNS(e,t);t&&this.removeAttributeNode(t)},hasAttributeNS:function(e,t){return null!=this.getAttributeNodeNS(e,t)},getAttributeNS:function(e,t){t=this.getAttributeNodeNS(e,t);return t?t.value:null},setAttributeNS:function(e,t,r){var n=v(e,t)[2],n=this.getAttributeNodeNS(e,n);n?n.value=n.nodeValue=""+r:((n=this.ownerDocument.createAttributeNS(e,t)).value=n.nodeValue=""+r,this.setAttributeNode(n))},getAttributeNodeNS:function(e,t){return this.attributes.getNamedItemNS(e,t)},getElementsByClassName:function(o){var s=m(o);return new F(this,function(a){var i=[];return 0<s.length&&W(a,function(e){var t,r,n;e===a||e.nodeType!==x||(t=e.getAttribute("class"))&&((r=o===t)||(t=m(t),r=s.every((n=t,function(e){return n&&-1!==n.indexOf(e)}))),r&&i.push(e))}),i})},getElementsByTagName:function(n){var a="html"===(this.nodeType===O?this:this.ownerDocument).type,i=n.toLowerCase();return new F(this,function(t){var r=[];return W(t,function(e){e===t||e.nodeType!==x||"*"!==n&&e.getQualifiedName()!==(a&&e.namespaceURI===E.HTML?i:n)||r.push(e)}),r})},getElementsByTagNameNS:function(n,a){return new F(this,function(t){var r=[];return W(t,function(e){e===t||e.nodeType!==x||"*"!==n&&e.namespaceURI!==n||"*"!==a&&e.localName!=a||r.push(e)}),r})}}).getElementsByClassName,Y.prototype.getElementsByTagName=ie.prototype.getElementsByTagName,Y.prototype.getElementsByTagNameNS=ie.prototype.getElementsByTagNameNS,y(ie,X),oe.prototype.nodeType=k,y(oe,X),se.prototype={data:"",substringData:function(e,t){return this.data.substring(e,e+t)},appendData:function(e){e=this.data+e,this.nodeValue=this.data=e,this.length=e.length},insertData:function(e,t){this.replaceData(e,0,t)},deleteData:function(e,t){this.replaceData(e,t,"")},replaceData:function(e,t,r){r=this.data.substring(0,e)+r+this.data.substring(e+t),this.nodeValue=this.data=r,this.length=r.length}},y(se,X),ue.prototype={nodeName:"#text",nodeType:R,splitText:function(e){var t=(r=this.data).substring(e),r=r.substring(0,e);this.data=this.nodeValue=r,this.length=r.length;t=this.ownerDocument.createTextNode(t);return this.parentNode&&this.parentNode.insertBefore(t,this.nextSibling),t}},y(ue,se),ce.prototype={nodeName:"#comment",nodeType:D},y(ce,se),le.prototype={nodeName:"#cdata-section",nodeType:I},y(le,ue),de.prototype.nodeType=C,y(de,X),pe.prototype.nodeType=12,y(pe,X),fe.prototype.nodeType=6,y(fe,X),he.prototype.nodeType=A,y(he,X),me.prototype.nodeName="#document-fragment",me.prototype.nodeType=L,y(me,X),ge.prototype.nodeType=N,y(ge,X),ve.prototype.serializeToString=function(e,t){return be.call(e,t)},X.prototype.toString=be;try{Object.defineProperty&&(S=function e(t){switch(t.nodeType){case x:case L:var r=[];for(t=t.firstChild;t;)7!==t.nodeType&&8!==t.nodeType&&r.push(e(t)),t=t.nextSibling;return r.join("");default:return t.nodeValue}},Object.defineProperty(F.prototype,"length",{get:function(){return q(this),this.$$length}}),Object.defineProperty(X.prototype,"textContent",{get:function(){return S(this)},set:function(e){switch(this.nodeType){case x:case L:for(;this.firstChild;)this.removeChild(this.firstChild);(e||String(e))&&this.appendChild(this.ownerDocument.createTextNode(e));break;default:this.data=e,this.value=e,this.nodeValue=e}}}),Ee=function(e,t,r){e["$$"+t]=r})}catch(e){}t._updateLiveList=q,t.Attr=oe,t.CDATASection=le,t.CharacterData=se,t.Comment=ce,t.Document=Y,t.DocumentFragment=me,t.DocumentType=de,t.DOMImplementation=V,t.Element=ie,t.Entity=fe,t.EntityReference=he,t.LiveNodeList=F,t.NamedNodeMap=H,t.Node=X,t.NodeList=j,t.Notation=pe,t.Text=ue,t.ProcessingInstruction=ge,t.XMLSerializer=ve},984:function(e,t,r){r=r(539).freeze;t.XML_ENTITIES=r({amp:"&",apos:"'",gt:">",lt:"<",quot:'"'}),t.HTML_ENTITIES=r({Aacute:"Á",aacute:"á",Abreve:"Ă",abreve:"ă",ac:"∾",acd:"∿",acE:"∾̳",Acirc:"Â",acirc:"â",acute:"´",Acy:"А",acy:"а",AElig:"Æ",aelig:"æ",af:"⁡",Afr:"𝔄",afr:"𝔞",Agrave:"À",agrave:"à",alefsym:"ℵ",aleph:"ℵ",Alpha:"Α",alpha:"α",Amacr:"Ā",amacr:"ā",amalg:"⨿",AMP:"&",amp:"&",And:"⩓",and:"∧",andand:"⩕",andd:"⩜",andslope:"⩘",andv:"⩚",ang:"∠",ange:"⦤",angle:"∠",angmsd:"∡",angmsdaa:"⦨",angmsdab:"⦩",angmsdac:"⦪",angmsdad:"⦫",angmsdae:"⦬",angmsdaf:"⦭",angmsdag:"⦮",angmsdah:"⦯",angrt:"∟",angrtvb:"⊾",angrtvbd:"⦝",angsph:"∢",angst:"Å",angzarr:"⍼",Aogon:"Ą",aogon:"ą",Aopf:"𝔸",aopf:"𝕒",ap:"≈",apacir:"⩯",apE:"⩰",ape:"≊",apid:"≋",apos:"'",ApplyFunction:"⁡",approx:"≈",approxeq:"≊",Aring:"Å",aring:"å",Ascr:"𝒜",ascr:"𝒶",Assign:"≔",ast:"*",asymp:"≈",asympeq:"≍",Atilde:"Ã",atilde:"ã",Auml:"Ä",auml:"ä",awconint:"∳",awint:"⨑",backcong:"≌",backepsilon:"϶",backprime:"‵",backsim:"∽",backsimeq:"⋍",Backslash:"∖",Barv:"⫧",barvee:"⊽",Barwed:"⌆",barwed:"⌅",barwedge:"⌅",bbrk:"⎵",bbrktbrk:"⎶",bcong:"≌",Bcy:"Б",bcy:"б",bdquo:"„",becaus:"∵",Because:"∵",because:"∵",bemptyv:"⦰",bepsi:"϶",bernou:"ℬ",Bernoullis:"ℬ",Beta:"Β",beta:"β",beth:"ℶ",between:"≬",Bfr:"𝔅",bfr:"𝔟",bigcap:"⋂",bigcirc:"◯",bigcup:"⋃",bigodot:"⨀",bigoplus:"⨁",bigotimes:"⨂",bigsqcup:"⨆",bigstar:"★",bigtriangledown:"▽",bigtriangleup:"△",biguplus:"⨄",bigvee:"⋁",bigwedge:"⋀",bkarow:"⤍",blacklozenge:"⧫",blacksquare:"▪",blacktriangle:"▴",blacktriangledown:"▾",blacktriangleleft:"◂",blacktriangleright:"▸",blank:"␣",blk12:"▒",blk14:"░",blk34:"▓",block:"█",bne:"=⃥",bnequiv:"≡⃥",bNot:"⫭",bnot:"⌐",Bopf:"𝔹",bopf:"𝕓",bot:"⊥",bottom:"⊥",bowtie:"⋈",boxbox:"⧉",boxDL:"╗",boxDl:"╖",boxdL:"╕",boxdl:"┐",boxDR:"╔",boxDr:"╓",boxdR:"╒",boxdr:"┌",boxH:"═",boxh:"─",boxHD:"╦",boxHd:"╤",boxhD:"╥",boxhd:"┬",boxHU:"╩",boxHu:"╧",boxhU:"╨",boxhu:"┴",boxminus:"⊟",boxplus:"⊞",boxtimes:"⊠",boxUL:"╝",boxUl:"╜",boxuL:"╛",boxul:"┘",boxUR:"╚",boxUr:"╙",boxuR:"╘",boxur:"└",boxV:"║",boxv:"│",boxVH:"╬",boxVh:"╫",boxvH:"╪",boxvh:"┼",boxVL:"╣",boxVl:"╢",boxvL:"╡",boxvl:"┤",boxVR:"╠",boxVr:"╟",boxvR:"╞",boxvr:"├",bprime:"‵",Breve:"˘",breve:"˘",brvbar:"¦",Bscr:"ℬ",bscr:"𝒷",bsemi:"⁏",bsim:"∽",bsime:"⋍",bsol:"\\",bsolb:"⧅",bsolhsub:"⟈",bull:"•",bullet:"•",bump:"≎",bumpE:"⪮",bumpe:"≏",Bumpeq:"≎",bumpeq:"≏",Cacute:"Ć",cacute:"ć",Cap:"⋒",cap:"∩",capand:"⩄",capbrcup:"⩉",capcap:"⩋",capcup:"⩇",capdot:"⩀",CapitalDifferentialD:"ⅅ",caps:"∩︀",caret:"⁁",caron:"ˇ",Cayleys:"ℭ",ccaps:"⩍",Ccaron:"Č",ccaron:"č",Ccedil:"Ç",ccedil:"ç",Ccirc:"Ĉ",ccirc:"ĉ",Cconint:"∰",ccups:"⩌",ccupssm:"⩐",Cdot:"Ċ",cdot:"ċ",cedil:"¸",Cedilla:"¸",cemptyv:"⦲",cent:"¢",CenterDot:"·",centerdot:"·",Cfr:"ℭ",cfr:"𝔠",CHcy:"Ч",chcy:"ч",check:"✓",checkmark:"✓",Chi:"Χ",chi:"χ",cir:"○",circ:"ˆ",circeq:"≗",circlearrowleft:"↺",circlearrowright:"↻",circledast:"⊛",circledcirc:"⊚",circleddash:"⊝",CircleDot:"⊙",circledR:"®",circledS:"Ⓢ",CircleMinus:"⊖",CirclePlus:"⊕",CircleTimes:"⊗",cirE:"⧃",cire:"≗",cirfnint:"⨐",cirmid:"⫯",cirscir:"⧂",ClockwiseContourIntegral:"∲",CloseCurlyDoubleQuote:"”",CloseCurlyQuote:"’",clubs:"♣",clubsuit:"♣",Colon:"∷",colon:":",Colone:"⩴",colone:"≔",coloneq:"≔",comma:",",commat:"@",comp:"∁",compfn:"∘",complement:"∁",complexes:"ℂ",cong:"≅",congdot:"⩭",Congruent:"≡",Conint:"∯",conint:"∮",ContourIntegral:"∮",Copf:"ℂ",copf:"𝕔",coprod:"∐",Coproduct:"∐",COPY:"©",copy:"©",copysr:"℗",CounterClockwiseContourIntegral:"∳",crarr:"↵",Cross:"⨯",cross:"✗",Cscr:"𝒞",cscr:"𝒸",csub:"⫏",csube:"⫑",csup:"⫐",csupe:"⫒",ctdot:"⋯",cudarrl:"⤸",cudarrr:"⤵",cuepr:"⋞",cuesc:"⋟",cularr:"↶",cularrp:"⤽",Cup:"⋓",cup:"∪",cupbrcap:"⩈",CupCap:"≍",cupcap:"⩆",cupcup:"⩊",cupdot:"⊍",cupor:"⩅",cups:"∪︀",curarr:"↷",curarrm:"⤼",curlyeqprec:"⋞",curlyeqsucc:"⋟",curlyvee:"⋎",curlywedge:"⋏",curren:"¤",curvearrowleft:"↶",curvearrowright:"↷",cuvee:"⋎",cuwed:"⋏",cwconint:"∲",cwint:"∱",cylcty:"⌭",Dagger:"‡",dagger:"†",daleth:"ℸ",Darr:"↡",dArr:"⇓",darr:"↓",dash:"‐",Dashv:"⫤",dashv:"⊣",dbkarow:"⤏",dblac:"˝",Dcaron:"Ď",dcaron:"ď",Dcy:"Д",dcy:"д",DD:"ⅅ",dd:"ⅆ",ddagger:"‡",ddarr:"⇊",DDotrahd:"⤑",ddotseq:"⩷",deg:"°",Del:"∇",Delta:"Δ",delta:"δ",demptyv:"⦱",dfisht:"⥿",Dfr:"𝔇",dfr:"𝔡",dHar:"⥥",dharl:"⇃",dharr:"⇂",DiacriticalAcute:"´",DiacriticalDot:"˙",DiacriticalDoubleAcute:"˝",DiacriticalGrave:"`",DiacriticalTilde:"˜",diam:"⋄",Diamond:"⋄",diamond:"⋄",diamondsuit:"♦",diams:"♦",die:"¨",DifferentialD:"ⅆ",digamma:"ϝ",disin:"⋲",div:"÷",divide:"÷",divideontimes:"⋇",divonx:"⋇",DJcy:"Ђ",djcy:"ђ",dlcorn:"⌞",dlcrop:"⌍",dollar:"$",Dopf:"𝔻",dopf:"𝕕",Dot:"¨",dot:"˙",DotDot:"⃜",doteq:"≐",doteqdot:"≑",DotEqual:"≐",dotminus:"∸",dotplus:"∔",dotsquare:"⊡",doublebarwedge:"⌆",DoubleContourIntegral:"∯",DoubleDot:"¨",DoubleDownArrow:"⇓",DoubleLeftArrow:"⇐",DoubleLeftRightArrow:"⇔",DoubleLeftTee:"⫤",DoubleLongLeftArrow:"⟸",DoubleLongLeftRightArrow:"⟺",DoubleLongRightArrow:"⟹",DoubleRightArrow:"⇒",DoubleRightTee:"⊨",DoubleUpArrow:"⇑",DoubleUpDownArrow:"⇕",DoubleVerticalBar:"∥",DownArrow:"↓",Downarrow:"⇓",downarrow:"↓",DownArrowBar:"⤓",DownArrowUpArrow:"⇵",DownBreve:"̑",downdownarrows:"⇊",downharpoonleft:"⇃",downharpoonright:"⇂",DownLeftRightVector:"⥐",DownLeftTeeVector:"⥞",DownLeftVector:"↽",DownLeftVectorBar:"⥖",DownRightTeeVector:"⥟",DownRightVector:"⇁",DownRightVectorBar:"⥗",DownTee:"⊤",DownTeeArrow:"↧",drbkarow:"⤐",drcorn:"⌟",drcrop:"⌌",Dscr:"𝒟",dscr:"𝒹",DScy:"Ѕ",dscy:"ѕ",dsol:"⧶",Dstrok:"Đ",dstrok:"đ",dtdot:"⋱",dtri:"▿",dtrif:"▾",duarr:"⇵",duhar:"⥯",dwangle:"⦦",DZcy:"Џ",dzcy:"џ",dzigrarr:"⟿",Eacute:"É",eacute:"é",easter:"⩮",Ecaron:"Ě",ecaron:"ě",ecir:"≖",Ecirc:"Ê",ecirc:"ê",ecolon:"≕",Ecy:"Э",ecy:"э",eDDot:"⩷",Edot:"Ė",eDot:"≑",edot:"ė",ee:"ⅇ",efDot:"≒",Efr:"𝔈",efr:"𝔢",eg:"⪚",Egrave:"È",egrave:"è",egs:"⪖",egsdot:"⪘",el:"⪙",Element:"∈",elinters:"⏧",ell:"ℓ",els:"⪕",elsdot:"⪗",Emacr:"Ē",emacr:"ē",empty:"∅",emptyset:"∅",EmptySmallSquare:"◻",emptyv:"∅",EmptyVerySmallSquare:"▫",emsp:" ",emsp13:" ",emsp14:" ",ENG:"Ŋ",eng:"ŋ",ensp:" ",Eogon:"Ę",eogon:"ę",Eopf:"𝔼",eopf:"𝕖",epar:"⋕",eparsl:"⧣",eplus:"⩱",epsi:"ε",Epsilon:"Ε",epsilon:"ε",epsiv:"ϵ",eqcirc:"≖",eqcolon:"≕",eqsim:"≂",eqslantgtr:"⪖",eqslantless:"⪕",Equal:"⩵",equals:"=",EqualTilde:"≂",equest:"≟",Equilibrium:"⇌",equiv:"≡",equivDD:"⩸",eqvparsl:"⧥",erarr:"⥱",erDot:"≓",Escr:"ℰ",escr:"ℯ",esdot:"≐",Esim:"⩳",esim:"≂",Eta:"Η",eta:"η",ETH:"Ð",eth:"ð",Euml:"Ë",euml:"ë",euro:"€",excl:"!",exist:"∃",Exists:"∃",expectation:"ℰ",ExponentialE:"ⅇ",exponentiale:"ⅇ",fallingdotseq:"≒",Fcy:"Ф",fcy:"ф",female:"♀",ffilig:"ﬃ",fflig:"ﬀ",ffllig:"ﬄ",Ffr:"𝔉",ffr:"𝔣",filig:"ﬁ",FilledSmallSquare:"◼",FilledVerySmallSquare:"▪",fjlig:"fj",flat:"♭",fllig:"ﬂ",fltns:"▱",fnof:"ƒ",Fopf:"𝔽",fopf:"𝕗",ForAll:"∀",forall:"∀",fork:"⋔",forkv:"⫙",Fouriertrf:"ℱ",fpartint:"⨍",frac12:"½",frac13:"⅓",frac14:"¼",frac15:"⅕",frac16:"⅙",frac18:"⅛",frac23:"⅔",frac25:"⅖",frac34:"¾",frac35:"⅗",frac38:"⅜",frac45:"⅘",frac56:"⅚",frac58:"⅝",frac78:"⅞",frasl:"⁄",frown:"⌢",Fscr:"ℱ",fscr:"𝒻",gacute:"ǵ",Gamma:"Γ",gamma:"γ",Gammad:"Ϝ",gammad:"ϝ",gap:"⪆",Gbreve:"Ğ",gbreve:"ğ",Gcedil:"Ģ",Gcirc:"Ĝ",gcirc:"ĝ",Gcy:"Г",gcy:"г",Gdot:"Ġ",gdot:"ġ",gE:"≧",ge:"≥",gEl:"⪌",gel:"⋛",geq:"≥",geqq:"≧",geqslant:"⩾",ges:"⩾",gescc:"⪩",gesdot:"⪀",gesdoto:"⪂",gesdotol:"⪄",gesl:"⋛︀",gesles:"⪔",Gfr:"𝔊",gfr:"𝔤",Gg:"⋙",gg:"≫",ggg:"⋙",gimel:"ℷ",GJcy:"Ѓ",gjcy:"ѓ",gl:"≷",gla:"⪥",glE:"⪒",glj:"⪤",gnap:"⪊",gnapprox:"⪊",gnE:"≩",gne:"⪈",gneq:"⪈",gneqq:"≩",gnsim:"⋧",Gopf:"𝔾",gopf:"𝕘",grave:"`",GreaterEqual:"≥",GreaterEqualLess:"⋛",GreaterFullEqual:"≧",GreaterGreater:"⪢",GreaterLess:"≷",GreaterSlantEqual:"⩾",GreaterTilde:"≳",Gscr:"𝒢",gscr:"ℊ",gsim:"≳",gsime:"⪎",gsiml:"⪐",Gt:"≫",GT:">",gt:">",gtcc:"⪧",gtcir:"⩺",gtdot:"⋗",gtlPar:"⦕",gtquest:"⩼",gtrapprox:"⪆",gtrarr:"⥸",gtrdot:"⋗",gtreqless:"⋛",gtreqqless:"⪌",gtrless:"≷",gtrsim:"≳",gvertneqq:"≩︀",gvnE:"≩︀",Hacek:"ˇ",hairsp:" ",half:"½",hamilt:"ℋ",HARDcy:"Ъ",hardcy:"ъ",hArr:"⇔",harr:"↔",harrcir:"⥈",harrw:"↭",Hat:"^",hbar:"ℏ",Hcirc:"Ĥ",hcirc:"ĥ",hearts:"♥",heartsuit:"♥",hellip:"…",hercon:"⊹",Hfr:"ℌ",hfr:"𝔥",HilbertSpace:"ℋ",hksearow:"⤥",hkswarow:"⤦",hoarr:"⇿",homtht:"∻",hookleftarrow:"↩",hookrightarrow:"↪",Hopf:"ℍ",hopf:"𝕙",horbar:"―",HorizontalLine:"─",Hscr:"ℋ",hscr:"𝒽",hslash:"ℏ",Hstrok:"Ħ",hstrok:"ħ",HumpDownHump:"≎",HumpEqual:"≏",hybull:"⁃",hyphen:"‐",Iacute:"Í",iacute:"í",ic:"⁣",Icirc:"Î",icirc:"î",Icy:"И",icy:"и",Idot:"İ",IEcy:"Е",iecy:"е",iexcl:"¡",iff:"⇔",Ifr:"ℑ",ifr:"𝔦",Igrave:"Ì",igrave:"ì",ii:"ⅈ",iiiint:"⨌",iiint:"∭",iinfin:"⧜",iiota:"℩",IJlig:"Ĳ",ijlig:"ĳ",Im:"ℑ",Imacr:"Ī",imacr:"ī",image:"ℑ",ImaginaryI:"ⅈ",imagline:"ℐ",imagpart:"ℑ",imath:"ı",imof:"⊷",imped:"Ƶ",Implies:"⇒",in:"∈",incare:"℅",infin:"∞",infintie:"⧝",inodot:"ı",Int:"∬",int:"∫",intcal:"⊺",integers:"ℤ",Integral:"∫",intercal:"⊺",Intersection:"⋂",intlarhk:"⨗",intprod:"⨼",InvisibleComma:"⁣",InvisibleTimes:"⁢",IOcy:"Ё",iocy:"ё",Iogon:"Į",iogon:"į",Iopf:"𝕀",iopf:"𝕚",Iota:"Ι",iota:"ι",iprod:"⨼",iquest:"¿",Iscr:"ℐ",iscr:"𝒾",isin:"∈",isindot:"⋵",isinE:"⋹",isins:"⋴",isinsv:"⋳",isinv:"∈",it:"⁢",Itilde:"Ĩ",itilde:"ĩ",Iukcy:"І",iukcy:"і",Iuml:"Ï",iuml:"ï",Jcirc:"Ĵ",jcirc:"ĵ",Jcy:"Й",jcy:"й",Jfr:"𝔍",jfr:"𝔧",jmath:"ȷ",Jopf:"𝕁",jopf:"𝕛",Jscr:"𝒥",jscr:"𝒿",Jsercy:"Ј",jsercy:"ј",Jukcy:"Є",jukcy:"є",Kappa:"Κ",kappa:"κ",kappav:"ϰ",Kcedil:"Ķ",kcedil:"ķ",Kcy:"К",kcy:"к",Kfr:"𝔎",kfr:"𝔨",kgreen:"ĸ",KHcy:"Х",khcy:"х",KJcy:"Ќ",kjcy:"ќ",Kopf:"𝕂",kopf:"𝕜",Kscr:"𝒦",kscr:"𝓀",lAarr:"⇚",Lacute:"Ĺ",lacute:"ĺ",laemptyv:"⦴",lagran:"ℒ",Lambda:"Λ",lambda:"λ",Lang:"⟪",lang:"⟨",langd:"⦑",langle:"⟨",lap:"⪅",Laplacetrf:"ℒ",laquo:"«",Larr:"↞",lArr:"⇐",larr:"←",larrb:"⇤",larrbfs:"⤟",larrfs:"⤝",larrhk:"↩",larrlp:"↫",larrpl:"⤹",larrsim:"⥳",larrtl:"↢",lat:"⪫",lAtail:"⤛",latail:"⤙",late:"⪭",lates:"⪭︀",lBarr:"⤎",lbarr:"⤌",lbbrk:"❲",lbrace:"{",lbrack:"[",lbrke:"⦋",lbrksld:"⦏",lbrkslu:"⦍",Lcaron:"Ľ",lcaron:"ľ",Lcedil:"Ļ",lcedil:"ļ",lceil:"⌈",lcub:"{",Lcy:"Л",lcy:"л",ldca:"⤶",ldquo:"“",ldquor:"„",ldrdhar:"⥧",ldrushar:"⥋",ldsh:"↲",lE:"≦",le:"≤",LeftAngleBracket:"⟨",LeftArrow:"←",Leftarrow:"⇐",leftarrow:"←",LeftArrowBar:"⇤",LeftArrowRightArrow:"⇆",leftarrowtail:"↢",LeftCeiling:"⌈",LeftDoubleBracket:"⟦",LeftDownTeeVector:"⥡",LeftDownVector:"⇃",LeftDownVectorBar:"⥙",LeftFloor:"⌊",leftharpoondown:"↽",leftharpoonup:"↼",leftleftarrows:"⇇",LeftRightArrow:"↔",Leftrightarrow:"⇔",leftrightarrow:"↔",leftrightarrows:"⇆",leftrightharpoons:"⇋",leftrightsquigarrow:"↭",LeftRightVector:"⥎",LeftTee:"⊣",LeftTeeArrow:"↤",LeftTeeVector:"⥚",leftthreetimes:"⋋",LeftTriangle:"⊲",LeftTriangleBar:"⧏",LeftTriangleEqual:"⊴",LeftUpDownVector:"⥑",LeftUpTeeVector:"⥠",LeftUpVector:"↿",LeftUpVectorBar:"⥘",LeftVector:"↼",LeftVectorBar:"⥒",lEg:"⪋",leg:"⋚",leq:"≤",leqq:"≦",leqslant:"⩽",les:"⩽",lescc:"⪨",lesdot:"⩿",lesdoto:"⪁",lesdotor:"⪃",lesg:"⋚︀",lesges:"⪓",lessapprox:"⪅",lessdot:"⋖",lesseqgtr:"⋚",lesseqqgtr:"⪋",LessEqualGreater:"⋚",LessFullEqual:"≦",LessGreater:"≶",lessgtr:"≶",LessLess:"⪡",lesssim:"≲",LessSlantEqual:"⩽",LessTilde:"≲",lfisht:"⥼",lfloor:"⌊",Lfr:"𝔏",lfr:"𝔩",lg:"≶",lgE:"⪑",lHar:"⥢",lhard:"↽",lharu:"↼",lharul:"⥪",lhblk:"▄",LJcy:"Љ",ljcy:"љ",Ll:"⋘",ll:"≪",llarr:"⇇",llcorner:"⌞",Lleftarrow:"⇚",llhard:"⥫",lltri:"◺",Lmidot:"Ŀ",lmidot:"ŀ",lmoust:"⎰",lmoustache:"⎰",lnap:"⪉",lnapprox:"⪉",lnE:"≨",lne:"⪇",lneq:"⪇",lneqq:"≨",lnsim:"⋦",loang:"⟬",loarr:"⇽",lobrk:"⟦",LongLeftArrow:"⟵",Longleftarrow:"⟸",longleftarrow:"⟵",LongLeftRightArrow:"⟷",Longleftrightarrow:"⟺",longleftrightarrow:"⟷",longmapsto:"⟼",LongRightArrow:"⟶",Longrightarrow:"⟹",longrightarrow:"⟶",looparrowleft:"↫",looparrowright:"↬",lopar:"⦅",Lopf:"𝕃",lopf:"𝕝",loplus:"⨭",lotimes:"⨴",lowast:"∗",lowbar:"_",LowerLeftArrow:"↙",LowerRightArrow:"↘",loz:"◊",lozenge:"◊",lozf:"⧫",lpar:"(",lparlt:"⦓",lrarr:"⇆",lrcorner:"⌟",lrhar:"⇋",lrhard:"⥭",lrm:"‎",lrtri:"⊿",lsaquo:"‹",Lscr:"ℒ",lscr:"𝓁",Lsh:"↰",lsh:"↰",lsim:"≲",lsime:"⪍",lsimg:"⪏",lsqb:"[",lsquo:"‘",lsquor:"‚",Lstrok:"Ł",lstrok:"ł",Lt:"≪",LT:"<",lt:"<",ltcc:"⪦",ltcir:"⩹",ltdot:"⋖",lthree:"⋋",ltimes:"⋉",ltlarr:"⥶",ltquest:"⩻",ltri:"◃",ltrie:"⊴",ltrif:"◂",ltrPar:"⦖",lurdshar:"⥊",luruhar:"⥦",lvertneqq:"≨︀",lvnE:"≨︀",macr:"¯",male:"♂",malt:"✠",maltese:"✠",Map:"⤅",map:"↦",mapsto:"↦",mapstodown:"↧",mapstoleft:"↤",mapstoup:"↥",marker:"▮",mcomma:"⨩",Mcy:"М",mcy:"м",mdash:"—",mDDot:"∺",measuredangle:"∡",MediumSpace:" ",Mellintrf:"ℳ",Mfr:"𝔐",mfr:"𝔪",mho:"℧",micro:"µ",mid:"∣",midast:"*",midcir:"⫰",middot:"·",minus:"−",minusb:"⊟",minusd:"∸",minusdu:"⨪",MinusPlus:"∓",mlcp:"⫛",mldr:"…",mnplus:"∓",models:"⊧",Mopf:"𝕄",mopf:"𝕞",mp:"∓",Mscr:"ℳ",mscr:"𝓂",mstpos:"∾",Mu:"Μ",mu:"μ",multimap:"⊸",mumap:"⊸",nabla:"∇",Nacute:"Ń",nacute:"ń",nang:"∠⃒",nap:"≉",napE:"⩰̸",napid:"≋̸",napos:"ŉ",napprox:"≉",natur:"♮",natural:"♮",naturals:"ℕ",nbsp:" ",nbump:"≎̸",nbumpe:"≏̸",ncap:"⩃",Ncaron:"Ň",ncaron:"ň",Ncedil:"Ņ",ncedil:"ņ",ncong:"≇",ncongdot:"⩭̸",ncup:"⩂",Ncy:"Н",ncy:"н",ndash:"–",ne:"≠",nearhk:"⤤",neArr:"⇗",nearr:"↗",nearrow:"↗",nedot:"≐̸",NegativeMediumSpace:"​",NegativeThickSpace:"​",NegativeThinSpace:"​",NegativeVeryThinSpace:"​",nequiv:"≢",nesear:"⤨",nesim:"≂̸",NestedGreaterGreater:"≫",NestedLessLess:"≪",NewLine:"\n",nexist:"∄",nexists:"∄",Nfr:"𝔑",nfr:"𝔫",ngE:"≧̸",nge:"≱",ngeq:"≱",ngeqq:"≧̸",ngeqslant:"⩾̸",nges:"⩾̸",nGg:"⋙̸",ngsim:"≵",nGt:"≫⃒",ngt:"≯",ngtr:"≯",nGtv:"≫̸",nhArr:"⇎",nharr:"↮",nhpar:"⫲",ni:"∋",nis:"⋼",nisd:"⋺",niv:"∋",NJcy:"Њ",njcy:"њ",nlArr:"⇍",nlarr:"↚",nldr:"‥",nlE:"≦̸",nle:"≰",nLeftarrow:"⇍",nleftarrow:"↚",nLeftrightarrow:"⇎",nleftrightarrow:"↮",nleq:"≰",nleqq:"≦̸",nleqslant:"⩽̸",nles:"⩽̸",nless:"≮",nLl:"⋘̸",nlsim:"≴",nLt:"≪⃒",nlt:"≮",nltri:"⋪",nltrie:"⋬",nLtv:"≪̸",nmid:"∤",NoBreak:"⁠",NonBreakingSpace:" ",Nopf:"ℕ",nopf:"𝕟",Not:"⫬",not:"¬",NotCongruent:"≢",NotCupCap:"≭",NotDoubleVerticalBar:"∦",NotElement:"∉",NotEqual:"≠",NotEqualTilde:"≂̸",NotExists:"∄",NotGreater:"≯",NotGreaterEqual:"≱",NotGreaterFullEqual:"≧̸",NotGreaterGreater:"≫̸",NotGreaterLess:"≹",NotGreaterSlantEqual:"⩾̸",NotGreaterTilde:"≵",NotHumpDownHump:"≎̸",NotHumpEqual:"≏̸",notin:"∉",notindot:"⋵̸",notinE:"⋹̸",notinva:"∉",notinvb:"⋷",notinvc:"⋶",NotLeftTriangle:"⋪",NotLeftTriangleBar:"⧏̸",NotLeftTriangleEqual:"⋬",NotLess:"≮",NotLessEqual:"≰",NotLessGreater:"≸",NotLessLess:"≪̸",NotLessSlantEqual:"⩽̸",NotLessTilde:"≴",NotNestedGreaterGreater:"⪢̸",NotNestedLessLess:"⪡̸",notni:"∌",notniva:"∌",notnivb:"⋾",notnivc:"⋽",NotPrecedes:"⊀",NotPrecedesEqual:"⪯̸",NotPrecedesSlantEqual:"⋠",NotReverseElement:"∌",NotRightTriangle:"⋫",NotRightTriangleBar:"⧐̸",NotRightTriangleEqual:"⋭",NotSquareSubset:"⊏̸",NotSquareSubsetEqual:"⋢",NotSquareSuperset:"⊐̸",NotSquareSupersetEqual:"⋣",NotSubset:"⊂⃒",NotSubsetEqual:"⊈",NotSucceeds:"⊁",NotSucceedsEqual:"⪰̸",NotSucceedsSlantEqual:"⋡",NotSucceedsTilde:"≿̸",NotSuperset:"⊃⃒",NotSupersetEqual:"⊉",NotTilde:"≁",NotTildeEqual:"≄",NotTildeFullEqual:"≇",NotTildeTilde:"≉",NotVerticalBar:"∤",npar:"∦",nparallel:"∦",nparsl:"⫽⃥",npart:"∂̸",npolint:"⨔",npr:"⊀",nprcue:"⋠",npre:"⪯̸",nprec:"⊀",npreceq:"⪯̸",nrArr:"⇏",nrarr:"↛",nrarrc:"⤳̸",nrarrw:"↝̸",nRightarrow:"⇏",nrightarrow:"↛",nrtri:"⋫",nrtrie:"⋭",nsc:"⊁",nsccue:"⋡",nsce:"⪰̸",Nscr:"𝒩",nscr:"𝓃",nshortmid:"∤",nshortparallel:"∦",nsim:"≁",nsime:"≄",nsimeq:"≄",nsmid:"∤",nspar:"∦",nsqsube:"⋢",nsqsupe:"⋣",nsub:"⊄",nsubE:"⫅̸",nsube:"⊈",nsubset:"⊂⃒",nsubseteq:"⊈",nsubseteqq:"⫅̸",nsucc:"⊁",nsucceq:"⪰̸",nsup:"⊅",nsupE:"⫆̸",nsupe:"⊉",nsupset:"⊃⃒",nsupseteq:"⊉",nsupseteqq:"⫆̸",ntgl:"≹",Ntilde:"Ñ",ntilde:"ñ",ntlg:"≸",ntriangleleft:"⋪",ntrianglelefteq:"⋬",ntriangleright:"⋫",ntrianglerighteq:"⋭",Nu:"Ν",nu:"ν",num:"#",numero:"№",numsp:" ",nvap:"≍⃒",nVDash:"⊯",nVdash:"⊮",nvDash:"⊭",nvdash:"⊬",nvge:"≥⃒",nvgt:">⃒",nvHarr:"⤄",nvinfin:"⧞",nvlArr:"⤂",nvle:"≤⃒",nvlt:"<⃒",nvltrie:"⊴⃒",nvrArr:"⤃",nvrtrie:"⊵⃒",nvsim:"∼⃒",nwarhk:"⤣",nwArr:"⇖",nwarr:"↖",nwarrow:"↖",nwnear:"⤧",Oacute:"Ó",oacute:"ó",oast:"⊛",ocir:"⊚",Ocirc:"Ô",ocirc:"ô",Ocy:"О",ocy:"о",odash:"⊝",Odblac:"Ő",odblac:"ő",odiv:"⨸",odot:"⊙",odsold:"⦼",OElig:"Œ",oelig:"œ",ofcir:"⦿",Ofr:"𝔒",ofr:"𝔬",ogon:"˛",Ograve:"Ò",ograve:"ò",ogt:"⧁",ohbar:"⦵",ohm:"Ω",oint:"∮",olarr:"↺",olcir:"⦾",olcross:"⦻",oline:"‾",olt:"⧀",Omacr:"Ō",omacr:"ō",Omega:"Ω",omega:"ω",Omicron:"Ο",omicron:"ο",omid:"⦶",ominus:"⊖",Oopf:"𝕆",oopf:"𝕠",opar:"⦷",OpenCurlyDoubleQuote:"“",OpenCurlyQuote:"‘",operp:"⦹",oplus:"⊕",Or:"⩔",or:"∨",orarr:"↻",ord:"⩝",order:"ℴ",orderof:"ℴ",ordf:"ª",ordm:"º",origof:"⊶",oror:"⩖",orslope:"⩗",orv:"⩛",oS:"Ⓢ",Oscr:"𝒪",oscr:"ℴ",Oslash:"Ø",oslash:"ø",osol:"⊘",Otilde:"Õ",otilde:"õ",Otimes:"⨷",otimes:"⊗",otimesas:"⨶",Ouml:"Ö",ouml:"ö",ovbar:"⌽",OverBar:"‾",OverBrace:"⏞",OverBracket:"⎴",OverParenthesis:"⏜",par:"∥",para:"¶",parallel:"∥",parsim:"⫳",parsl:"⫽",part:"∂",PartialD:"∂",Pcy:"П",pcy:"п",percnt:"%",period:".",permil:"‰",perp:"⊥",pertenk:"‱",Pfr:"𝔓",pfr:"𝔭",Phi:"Φ",phi:"φ",phiv:"ϕ",phmmat:"ℳ",phone:"☎",Pi:"Π",pi:"π",pitchfork:"⋔",piv:"ϖ",planck:"ℏ",planckh:"ℎ",plankv:"ℏ",plus:"+",plusacir:"⨣",plusb:"⊞",pluscir:"⨢",plusdo:"∔",plusdu:"⨥",pluse:"⩲",PlusMinus:"±",plusmn:"±",plussim:"⨦",plustwo:"⨧",pm:"±",Poincareplane:"ℌ",pointint:"⨕",Popf:"ℙ",popf:"𝕡",pound:"£",Pr:"⪻",pr:"≺",prap:"⪷",prcue:"≼",prE:"⪳",pre:"⪯",prec:"≺",precapprox:"⪷",preccurlyeq:"≼",Precedes:"≺",PrecedesEqual:"⪯",PrecedesSlantEqual:"≼",PrecedesTilde:"≾",preceq:"⪯",precnapprox:"⪹",precneqq:"⪵",precnsim:"⋨",precsim:"≾",Prime:"″",prime:"′",primes:"ℙ",prnap:"⪹",prnE:"⪵",prnsim:"⋨",prod:"∏",Product:"∏",profalar:"⌮",profline:"⌒",profsurf:"⌓",prop:"∝",Proportion:"∷",Proportional:"∝",propto:"∝",prsim:"≾",prurel:"⊰",Pscr:"𝒫",pscr:"𝓅",Psi:"Ψ",psi:"ψ",puncsp:" ",Qfr:"𝔔",qfr:"𝔮",qint:"⨌",Qopf:"ℚ",qopf:"𝕢",qprime:"⁗",Qscr:"𝒬",qscr:"𝓆",quaternions:"ℍ",quatint:"⨖",quest:"?",questeq:"≟",QUOT:'"',quot:'"',rAarr:"⇛",race:"∽̱",Racute:"Ŕ",racute:"ŕ",radic:"√",raemptyv:"⦳",Rang:"⟫",rang:"⟩",rangd:"⦒",range:"⦥",rangle:"⟩",raquo:"»",Rarr:"↠",rArr:"⇒",rarr:"→",rarrap:"⥵",rarrb:"⇥",rarrbfs:"⤠",rarrc:"⤳",rarrfs:"⤞",rarrhk:"↪",rarrlp:"↬",rarrpl:"⥅",rarrsim:"⥴",Rarrtl:"⤖",rarrtl:"↣",rarrw:"↝",rAtail:"⤜",ratail:"⤚",ratio:"∶",rationals:"ℚ",RBarr:"⤐",rBarr:"⤏",rbarr:"⤍",rbbrk:"❳",rbrace:"}",rbrack:"]",rbrke:"⦌",rbrksld:"⦎",rbrkslu:"⦐",Rcaron:"Ř",rcaron:"ř",Rcedil:"Ŗ",rcedil:"ŗ",rceil:"⌉",rcub:"}",Rcy:"Р",rcy:"р",rdca:"⤷",rdldhar:"⥩",rdquo:"”",rdquor:"”",rdsh:"↳",Re:"ℜ",real:"ℜ",realine:"ℛ",realpart:"ℜ",reals:"ℝ",rect:"▭",REG:"®",reg:"®",ReverseElement:"∋",ReverseEquilibrium:"⇋",ReverseUpEquilibrium:"⥯",rfisht:"⥽",rfloor:"⌋",Rfr:"ℜ",rfr:"𝔯",rHar:"⥤",rhard:"⇁",rharu:"⇀",rharul:"⥬",Rho:"Ρ",rho:"ρ",rhov:"ϱ",RightAngleBracket:"⟩",RightArrow:"→",Rightarrow:"⇒",rightarrow:"→",RightArrowBar:"⇥",RightArrowLeftArrow:"⇄",rightarrowtail:"↣",RightCeiling:"⌉",RightDoubleBracket:"⟧",RightDownTeeVector:"⥝",RightDownVector:"⇂",RightDownVectorBar:"⥕",RightFloor:"⌋",rightharpoondown:"⇁",rightharpoonup:"⇀",rightleftarrows:"⇄",rightleftharpoons:"⇌",rightrightarrows:"⇉",rightsquigarrow:"↝",RightTee:"⊢",RightTeeArrow:"↦",RightTeeVector:"⥛",rightthreetimes:"⋌",RightTriangle:"⊳",RightTriangleBar:"⧐",RightTriangleEqual:"⊵",RightUpDownVector:"⥏",RightUpTeeVector:"⥜",RightUpVector:"↾",RightUpVectorBar:"⥔",RightVector:"⇀",RightVectorBar:"⥓",ring:"˚",risingdotseq:"≓",rlarr:"⇄",rlhar:"⇌",rlm:"‏",rmoust:"⎱",rmoustache:"⎱",rnmid:"⫮",roang:"⟭",roarr:"⇾",robrk:"⟧",ropar:"⦆",Ropf:"ℝ",ropf:"𝕣",roplus:"⨮",rotimes:"⨵",RoundImplies:"⥰",rpar:")",rpargt:"⦔",rppolint:"⨒",rrarr:"⇉",Rrightarrow:"⇛",rsaquo:"›",Rscr:"ℛ",rscr:"𝓇",Rsh:"↱",rsh:"↱",rsqb:"]",rsquo:"’",rsquor:"’",rthree:"⋌",rtimes:"⋊",rtri:"▹",rtrie:"⊵",rtrif:"▸",rtriltri:"⧎",RuleDelayed:"⧴",ruluhar:"⥨",rx:"℞",Sacute:"Ś",sacute:"ś",sbquo:"‚",Sc:"⪼",sc:"≻",scap:"⪸",Scaron:"Š",scaron:"š",sccue:"≽",scE:"⪴",sce:"⪰",Scedil:"Ş",scedil:"ş",Scirc:"Ŝ",scirc:"ŝ",scnap:"⪺",scnE:"⪶",scnsim:"⋩",scpolint:"⨓",scsim:"≿",Scy:"С",scy:"с",sdot:"⋅",sdotb:"⊡",sdote:"⩦",searhk:"⤥",seArr:"⇘",searr:"↘",searrow:"↘",sect:"§",semi:";",seswar:"⤩",setminus:"∖",setmn:"∖",sext:"✶",Sfr:"𝔖",sfr:"𝔰",sfrown:"⌢",sharp:"♯",SHCHcy:"Щ",shchcy:"щ",SHcy:"Ш",shcy:"ш",ShortDownArrow:"↓",ShortLeftArrow:"←",shortmid:"∣",shortparallel:"∥",ShortRightArrow:"→",ShortUpArrow:"↑",shy:"­",Sigma:"Σ",sigma:"σ",sigmaf:"ς",sigmav:"ς",sim:"∼",simdot:"⩪",sime:"≃",simeq:"≃",simg:"⪞",simgE:"⪠",siml:"⪝",simlE:"⪟",simne:"≆",simplus:"⨤",simrarr:"⥲",slarr:"←",SmallCircle:"∘",smallsetminus:"∖",smashp:"⨳",smeparsl:"⧤",smid:"∣",smile:"⌣",smt:"⪪",smte:"⪬",smtes:"⪬︀",SOFTcy:"Ь",softcy:"ь",sol:"/",solb:"⧄",solbar:"⌿",Sopf:"𝕊",sopf:"𝕤",spades:"♠",spadesuit:"♠",spar:"∥",sqcap:"⊓",sqcaps:"⊓︀",sqcup:"⊔",sqcups:"⊔︀",Sqrt:"√",sqsub:"⊏",sqsube:"⊑",sqsubset:"⊏",sqsubseteq:"⊑",sqsup:"⊐",sqsupe:"⊒",sqsupset:"⊐",sqsupseteq:"⊒",squ:"□",Square:"□",square:"□",SquareIntersection:"⊓",SquareSubset:"⊏",SquareSubsetEqual:"⊑",SquareSuperset:"⊐",SquareSupersetEqual:"⊒",SquareUnion:"⊔",squarf:"▪",squf:"▪",srarr:"→",Sscr:"𝒮",sscr:"𝓈",ssetmn:"∖",ssmile:"⌣",sstarf:"⋆",Star:"⋆",star:"☆",starf:"★",straightepsilon:"ϵ",straightphi:"ϕ",strns:"¯",Sub:"⋐",sub:"⊂",subdot:"⪽",subE:"⫅",sube:"⊆",subedot:"⫃",submult:"⫁",subnE:"⫋",subne:"⊊",subplus:"⪿",subrarr:"⥹",Subset:"⋐",subset:"⊂",subseteq:"⊆",subseteqq:"⫅",SubsetEqual:"⊆",subsetneq:"⊊",subsetneqq:"⫋",subsim:"⫇",subsub:"⫕",subsup:"⫓",succ:"≻",succapprox:"⪸",succcurlyeq:"≽",Succeeds:"≻",SucceedsEqual:"⪰",SucceedsSlantEqual:"≽",SucceedsTilde:"≿",succeq:"⪰",succnapprox:"⪺",succneqq:"⪶",succnsim:"⋩",succsim:"≿",SuchThat:"∋",Sum:"∑",sum:"∑",sung:"♪",Sup:"⋑",sup:"⊃",sup1:"¹",sup2:"²",sup3:"³",supdot:"⪾",supdsub:"⫘",supE:"⫆",supe:"⊇",supedot:"⫄",Superset:"⊃",SupersetEqual:"⊇",suphsol:"⟉",suphsub:"⫗",suplarr:"⥻",supmult:"⫂",supnE:"⫌",supne:"⊋",supplus:"⫀",Supset:"⋑",supset:"⊃",supseteq:"⊇",supseteqq:"⫆",supsetneq:"⊋",supsetneqq:"⫌",supsim:"⫈",supsub:"⫔",supsup:"⫖",swarhk:"⤦",swArr:"⇙",swarr:"↙",swarrow:"↙",swnwar:"⤪",szlig:"ß",Tab:"\t",target:"⌖",Tau:"Τ",tau:"τ",tbrk:"⎴",Tcaron:"Ť",tcaron:"ť",Tcedil:"Ţ",tcedil:"ţ",Tcy:"Т",tcy:"т",tdot:"⃛",telrec:"⌕",Tfr:"𝔗",tfr:"𝔱",there4:"∴",Therefore:"∴",therefore:"∴",Theta:"Θ",theta:"θ",thetasym:"ϑ",thetav:"ϑ",thickapprox:"≈",thicksim:"∼",ThickSpace:"  ",thinsp:" ",ThinSpace:" ",thkap:"≈",thksim:"∼",THORN:"Þ",thorn:"þ",Tilde:"∼",tilde:"˜",TildeEqual:"≃",TildeFullEqual:"≅",TildeTilde:"≈",times:"×",timesb:"⊠",timesbar:"⨱",timesd:"⨰",tint:"∭",toea:"⤨",top:"⊤",topbot:"⌶",topcir:"⫱",Topf:"𝕋",topf:"𝕥",topfork:"⫚",tosa:"⤩",tprime:"‴",TRADE:"™",trade:"™",triangle:"▵",triangledown:"▿",triangleleft:"◃",trianglelefteq:"⊴",triangleq:"≜",triangleright:"▹",trianglerighteq:"⊵",tridot:"◬",trie:"≜",triminus:"⨺",TripleDot:"⃛",triplus:"⨹",trisb:"⧍",tritime:"⨻",trpezium:"⏢",Tscr:"𝒯",tscr:"𝓉",TScy:"Ц",tscy:"ц",TSHcy:"Ћ",tshcy:"ћ",Tstrok:"Ŧ",tstrok:"ŧ",twixt:"≬",twoheadleftarrow:"↞",twoheadrightarrow:"↠",Uacute:"Ú",uacute:"ú",Uarr:"↟",uArr:"⇑",uarr:"↑",Uarrocir:"⥉",Ubrcy:"Ў",ubrcy:"ў",Ubreve:"Ŭ",ubreve:"ŭ",Ucirc:"Û",ucirc:"û",Ucy:"У",ucy:"у",udarr:"⇅",Udblac:"Ű",udblac:"ű",udhar:"⥮",ufisht:"⥾",Ufr:"𝔘",ufr:"𝔲",Ugrave:"Ù",ugrave:"ù",uHar:"⥣",uharl:"↿",uharr:"↾",uhblk:"▀",ulcorn:"⌜",ulcorner:"⌜",ulcrop:"⌏",ultri:"◸",Umacr:"Ū",umacr:"ū",uml:"¨",UnderBar:"_",UnderBrace:"⏟",UnderBracket:"⎵",UnderParenthesis:"⏝",Union:"⋃",UnionPlus:"⊎",Uogon:"Ų",uogon:"ų",Uopf:"𝕌",uopf:"𝕦",UpArrow:"↑",Uparrow:"⇑",uparrow:"↑",UpArrowBar:"⤒",UpArrowDownArrow:"⇅",UpDownArrow:"↕",Updownarrow:"⇕",updownarrow:"↕",UpEquilibrium:"⥮",upharpoonleft:"↿",upharpoonright:"↾",uplus:"⊎",UpperLeftArrow:"↖",UpperRightArrow:"↗",Upsi:"ϒ",upsi:"υ",upsih:"ϒ",Upsilon:"Υ",upsilon:"υ",UpTee:"⊥",UpTeeArrow:"↥",upuparrows:"⇈",urcorn:"⌝",urcorner:"⌝",urcrop:"⌎",Uring:"Ů",uring:"ů",urtri:"◹",Uscr:"𝒰",uscr:"𝓊",utdot:"⋰",Utilde:"Ũ",utilde:"ũ",utri:"▵",utrif:"▴",uuarr:"⇈",Uuml:"Ü",uuml:"ü",uwangle:"⦧",vangrt:"⦜",varepsilon:"ϵ",varkappa:"ϰ",varnothing:"∅",varphi:"ϕ",varpi:"ϖ",varpropto:"∝",vArr:"⇕",varr:"↕",varrho:"ϱ",varsigma:"ς",varsubsetneq:"⊊︀",varsubsetneqq:"⫋︀",varsupsetneq:"⊋︀",varsupsetneqq:"⫌︀",vartheta:"ϑ",vartriangleleft:"⊲",vartriangleright:"⊳",Vbar:"⫫",vBar:"⫨",vBarv:"⫩",Vcy:"В",vcy:"в",VDash:"⊫",Vdash:"⊩",vDash:"⊨",vdash:"⊢",Vdashl:"⫦",Vee:"⋁",vee:"∨",veebar:"⊻",veeeq:"≚",vellip:"⋮",Verbar:"‖",verbar:"|",Vert:"‖",vert:"|",VerticalBar:"∣",VerticalLine:"|",VerticalSeparator:"❘",VerticalTilde:"≀",VeryThinSpace:" ",Vfr:"𝔙",vfr:"𝔳",vltri:"⊲",vnsub:"⊂⃒",vnsup:"⊃⃒",Vopf:"𝕍",vopf:"𝕧",vprop:"∝",vrtri:"⊳",Vscr:"𝒱",vscr:"𝓋",vsubnE:"⫋︀",vsubne:"⊊︀",vsupnE:"⫌︀",vsupne:"⊋︀",Vvdash:"⊪",vzigzag:"⦚",Wcirc:"Ŵ",wcirc:"ŵ",wedbar:"⩟",Wedge:"⋀",wedge:"∧",wedgeq:"≙",weierp:"℘",Wfr:"𝔚",wfr:"𝔴",Wopf:"𝕎",wopf:"𝕨",wp:"℘",wr:"≀",wreath:"≀",Wscr:"𝒲",wscr:"𝓌",xcap:"⋂",xcirc:"◯",xcup:"⋃",xdtri:"▽",Xfr:"𝔛",xfr:"𝔵",xhArr:"⟺",xharr:"⟷",Xi:"Ξ",xi:"ξ",xlArr:"⟸",xlarr:"⟵",xmap:"⟼",xnis:"⋻",xodot:"⨀",Xopf:"𝕏",xopf:"𝕩",xoplus:"⨁",xotime:"⨂",xrArr:"⟹",xrarr:"⟶",Xscr:"𝒳",xscr:"𝓍",xsqcup:"⨆",xuplus:"⨄",xutri:"△",xvee:"⋁",xwedge:"⋀",Yacute:"Ý",yacute:"ý",YAcy:"Я",yacy:"я",Ycirc:"Ŷ",ycirc:"ŷ",Ycy:"Ы",ycy:"ы",yen:"¥",Yfr:"𝔜",yfr:"𝔶",YIcy:"Ї",yicy:"ї",Yopf:"𝕐",yopf:"𝕪",Yscr:"𝒴",yscr:"𝓎",YUcy:"Ю",yucy:"ю",Yuml:"Ÿ",yuml:"ÿ",Zacute:"Ź",zacute:"ź",Zcaron:"Ž",zcaron:"ž",Zcy:"З",zcy:"з",Zdot:"Ż",zdot:"ż",zeetrf:"ℨ",ZeroWidthSpace:"​",Zeta:"Ζ",zeta:"ζ",Zfr:"ℨ",zfr:"𝔷",ZHcy:"Ж",zhcy:"ж",zigrarr:"⇝",Zopf:"ℤ",zopf:"𝕫",Zscr:"𝒵",zscr:"𝓏",zwj:"‍",zwnj:"‌"}),t.entityMap=t.HTML_ENTITIES},180:function(e,t,r){function n(e,t){e.prototype=Object.create(Error.prototype,{constructor:{value:e},name:{value:e.name,enumerable:!0,writable:t}})}var a=r(539).freeze({Error:"Error",IndexSizeError:"IndexSizeError",DomstringSizeError:"DomstringSizeError",HierarchyRequestError:"HierarchyRequestError",WrongDocumentError:"WrongDocumentError",InvalidCharacterError:"InvalidCharacterError",NoDataAllowedError:"NoDataAllowedError",NoModificationAllowedError:"NoModificationAllowedError",NotFoundError:"NotFoundError",NotSupportedError:"NotSupportedError",InUseAttributeError:"InUseAttributeError",InvalidStateError:"InvalidStateError",SyntaxError:"SyntaxError",InvalidModificationError:"InvalidModificationError",NamespaceError:"NamespaceError",InvalidAccessError:"InvalidAccessError",ValidationError:"ValidationError",TypeMismatchError:"TypeMismatchError",SecurityError:"SecurityError",NetworkError:"NetworkError",AbortError:"AbortError",URLMismatchError:"URLMismatchError",QuotaExceededError:"QuotaExceededError",TimeoutError:"TimeoutError",InvalidNodeTypeError:"InvalidNodeTypeError",DataCloneError:"DataCloneError",EncodingError:"EncodingError",NotReadableError:"NotReadableError",UnknownError:"UnknownError",ConstraintError:"ConstraintError",DataError:"DataError",TransactionInactiveError:"TransactionInactiveError",ReadOnlyError:"ReadOnlyError",VersionError:"VersionError",OperationError:"OperationError",NotAllowedError:"NotAllowedError",OptOutError:"OptOutError"}),i=Object.keys(a);function o(e){return"number"==typeof e&&1<=e&&e<=25}function s(e,t){o(e)?(this.name=i[e],this.message=t||""):(this.message=e,this.name="string"==typeof t&&t.substring(t.length-a.Error.length)===a.Error?t:a.Error),Error.captureStackTrace&&Error.captureStackTrace(this,s)}n(s,!0),Object.defineProperties(s.prototype,{code:{enumerable:!0,get:function(){var e=i.indexOf(this.name);return o(e)?e:0}}});for(var r={INDEX_SIZE_ERR:1,DOMSTRING_SIZE_ERR:2,HIERARCHY_REQUEST_ERR:3,WRONG_DOCUMENT_ERR:4,INVALID_CHARACTER_ERR:5,NO_DATA_ALLOWED_ERR:6,NO_MODIFICATION_ALLOWED_ERR:7,NOT_FOUND_ERR:8,NOT_SUPPORTED_ERR:9,INUSE_ATTRIBUTE_ERR:10,INVALID_STATE_ERR:11,SYNTAX_ERR:12,INVALID_MODIFICATION_ERR:13,NAMESPACE_ERR:14,INVALID_ACCESS_ERR:15,VALIDATION_ERR:16,TYPE_MISMATCH_ERR:17,SECURITY_ERR:18,NETWORK_ERR:19,ABORT_ERR:20,URL_MISMATCH_ERR:21,QUOTA_EXCEEDED_ERR:22,TIMEOUT_ERR:23,INVALID_NODE_TYPE_ERR:24,DATA_CLONE_ERR:25},u=Object.entries(r),c=0;c<u.length;c++)s[u[c][0]]=u[c][1];function l(e,t){this.message=e,this.locator=t,Error.captureStackTrace&&Error.captureStackTrace(this,l)}n(l),t.DOMException=s,t.DOMExceptionName=a,t.ExceptionCode=r,t.ParseError=l},722:function(e,t){function r(e){try{"function"!=typeof e&&(e=RegExp);var t=new e("𝌆","u").exec("𝌆");return!!t&&2===t[0].length}catch(e){}return!1}var n=r();function a(e){if("["!==e.source[0])throw new Error(e+" can not be used with chars");return e.source.slice(1,e.source.lastIndexOf("]"))}function i(e,t){if("["!==e.source[0])throw new Error("/"+e.source+"/ can not be used with chars_without");if(!t||"string"!=typeof t)throw new Error(JSON.stringify(t)+" is not a valid search");if(-1===e.source.indexOf(t))throw new Error('"'+t+'" is not is /'+e.source+"/");if("-"===t&&1!==e.source.indexOf(t))throw new Error('"'+t+'" is not at the first postion of /'+e.source+"/");return new RegExp(e.source.replace(t,""),n?"u":"")}function o(e){var r=this;return new RegExp(Array.prototype.slice.call(arguments).map(function(e){var t="string"==typeof e;if(t&&void 0===r&&"|"===e)throw new Error("use regg instead of reg to wrap expressions with `|`!");return t?e:e.source}).join(""),n?"mu":"m")}function s(e){if(0===arguments.length)throw new Error("no parameters provided");return o.apply(s,["(?:"].concat(Array.prototype.slice.call(arguments),[")"]))}var u=/[-\x09\x0A\x0D\x20-\x2C\x2E-\uD7FF\uE000-\uFFFD]/;n&&(u=o("[",a(u),"\\u{10000}-\\u{10FFFF}","]"));var c=/[\x20\x09\x0D\x0A]/,l=a(c),d=o(c,"+"),p=o(c,"*"),f=/[:_a-zA-Z\xC0-\xD6\xD8-\xF6\xF8-\u02FF\u0370-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]/;n&&(f=o("[",a(f),"\\u{10000}-\\u{10FFFF}","]"));var h=o("[",a(f),a(/[-.0-9\xB7]/),a(/[\u0300-\u036F\u203F-\u2040]/),"]"),m=o(f,h,"*"),g=o(h,"+"),v=o("&",m,";"),b=s(/&#[0-9]+;|&#x[0-9a-fA-F]+;/),y=s(v,"|",b),_=o("%",m,";"),w=s(o('"',s(/[^%&"]/,"|",_,"|",y),"*",'"'),"|",o("'",s(/[^%&']/,"|",_,"|",y),"*","'")),E=s('"',s(/[^<&"]/,"|",y),"*",'"',"|","'",s(/[^<&']/,"|",y),"*","'"),T=o(i(f,":"),i(h,":"),"*"),S=o(T,s(":",T),"?"),x=o("^",S,"$"),k=o("(",S,")"),R=s(/"[^"]*"|'[^']*'/),I=o(/^<\?/,"(",m,")",s(d,"(",u,"*?)"),"?",/\?>/),A=/[\x20\x0D\x0Aa-zA-Z0-9-'()+,./:=?;!*#@$_%]/,N=s('"',A,'*"',"|","'",i(A,"'"),"*'"),D="\x3c!--",O=o(D,s(i(u,"-"),"|",o("-",i(u,"-"))),"*","--\x3e"),C="#PCDATA",L=s(o(/\(/,p,C,s(p,/\|/,p,S),"*",p,/\)\*/),"|",o(/\(/,p,C,p,/\)/)),c=s("EMPTY","|","ANY","|",L,"|",o(/\([^>]+\)/,/[?*+]?/)),v=o("<!ELEMENT",d,s(S,"|",_),d,s(c,"|",_),p,">"),b=o("NOTATION",d,/\(/,p,m,s(p,/\|/,p,m),"*",p,/\)/),f=o(/\(/,p,g,s(p,/\|/,p,g),"*",p,/\)/),h=s(b,"|",f),T=s(/CDATA|ID|IDREF|IDREFS|ENTITY|ENTITIES|NMTOKEN|NMTOKENS/,"|",h),A=s(/#REQUIRED|#IMPLIED/,"|",s(s("#FIXED",d),"?",E)),C=o("<!ATTLIST",d,m,s(d,m,d,T,d,A),"*",p,">"),L="SYSTEM",c="PUBLIC",g=s(s(L,d,R),"|",s(c,d,N,d,R)),b=o("^",s(s(L,d,"(?<SystemLiteralOnly>",R,")"),"|",s(c,d,"(?<PubidLiteral>",N,")",d,"(?<SystemLiteral>",R,")"))),f=s(d,"NDATA",d,m),h="<!ENTITY",E=o(h,d,m,d,s(w,"|",s(g,f,"?")),p,">"),T=s(w,"|",g),A=s(E,"|",o(h,d,"%",d,m,d,T,p,">")),f=o(c,d,N),E=o("<!NOTATION",d,m,d,s(g,"|",f),p,">"),h=o(p,"=",p),T=/1[.]\d+/,f=o(d,"version",h,s("'",T,"'","|",'"',T,'"')),T=/[A-Za-z][-A-Za-z0-9._]*/,T=o(/^<\?xml/,f,s(d,"encoding",h,s('"',T,'"',"|","'",T,"'")),"?",s(d,"standalone",h,s("'",s("yes","|","no"),"'","|",'"',s("yes","|","no"),'"')),"?",p,/\?>/),h=o(u,"*?",/\]\]>/),h=o(/<!\[CDATA\[/,h);t.chars=a,t.chars_without=i,t.detectUnicodeSupport=r,t.reg=o,t.regg=s,t.AttlistDecl=C,t.CDATA_START="<![CDATA[",t.CDATA_END="]]>",t.CDSect=h,t.Char=u,t.Comment=O,t.COMMENT_START=D,t.COMMENT_END="--\x3e",t.DOCTYPE_DECL_START="<!DOCTYPE",t.elementdecl=v,t.EntityDecl=A,t.EntityValue=w,t.ExternalID=g,t.ExternalID_match=b,t.Name=m,t.NotationDecl=E,t.Reference=y,t.PEReference=_,t.PI=I,t.PUBLIC=c,t.PubidLiteral=N,t.QName=S,t.QName_exact=x,t.QName_group=k,t.S=d,t.SChar_s=l,t.S_OPT=p,t.SYSTEM=L,t.SystemLiteral=R,t.UNICODE_REPLACEMENT_CHARACTER="�",t.UNICODE_SUPPORT=n,t.XMLDecl=T},87:function(e,t,r){var A=r(539),N=r(722),r=r(180),D=A.isHTMLEscapableRawTextElement,O=A.isHTMLMimeType,C=A.isHTMLRawTextElement,L=A.hasOwn,f=A.NAMESPACE,P=r.ParseError,M=r.DOMException;function n(){}n.prototype={parse:function(e,t,r){var n=this.domBuilder;n.startDocument(),h(t,t=Object.create(null)),function(r,e,n,a,i){var o=O(a.mimeType);if(0<=r.indexOf(N.UNICODE_REPLACEMENT_CHARACTER))return i.fatalError("Unicode replacement character detected, source encoding issues?");function s(e){var t=";"===e[e.length-1]?e:e+";";if(!o&&t!==e)return i.error("EntityRef: expecting ;"),e;var r=N.Reference.exec(t);if(!r||r[0].length!==t.length)return i.error("entity not matching Reference production: "+e),e;t=t.slice(1,-1);return L(n,t)?n[t]:"#"===t.charAt(0)?function(e){if(65535<e){var t=55296+((e-=65536)>>10),r=56320+(1023&e);return String.fromCharCode(t,r)}return String.fromCharCode(e)}(parseInt(t.substr(1).replace("x","0x"))):(i.error("entity not found:"+e),e)}function t(e){var t;m<e&&(t=r.substring(m,e).replace(U,s),p&&u(m),a.characters(t,0,e-m),m=e)}function u(e,t){for(;l<=e&&(t=d.exec(r));)c=t.index,l=c+t[0].length,p.lineNumber++;p.columnNumber=e-c+1}for(var c=0,l=0,d=/.*(?:\r\n?|\n)|.*$/g,p=a.locator,f=[{currentNSMap:e}],h=[],m=0;;){try{var g=r.indexOf("<",m);if(g<0){if(!o&&0<h.length)return i.fatalError("unclosed xml tag(s): "+h.join(", "));if(!r.substring(m).match(/^\s*$/)){var v=a.doc,b=v.createTextNode(r.substr(m));if(v.documentElement)return i.error("Extra content at the end of the document");v.appendChild(b),a.currentElement=b}return}switch(m<g&&(b=r.substring(m,g),o||0!==h.length||(b=b.replace(new RegExp(N.S_OPT.source,"g"),""))&&i.error("Unexpected content outside root element: '"+b+"'"),t(g)),r.charAt(g+1)){case"/":var y=r.indexOf(">",g+2),_=r.substring(g+2,0<y?y:void 0);if(!_)return i.fatalError("end tag name missing");var w=0<y&&N.reg("^",N.QName_group,N.S_OPT,"$").exec(_);if(!w)return i.fatalError('end tag name contains invalid characters: "'+_+'"');if(!a.currentElement&&!a.doc.documentElement)return;var E=h[h.length-1]||a.currentElement.tagName||a.doc.documentElement.tagName||"";if(E!==w[1]){w=w[1].toLowerCase();if(!o||E.toLowerCase()!==w)return i.fatalError('Opening and ending tag mismatch: "'+E+'" != "'+_+'"')}_=f.pop();h.pop();var T=_.localNSMap;if(a.endElement(_.uri,_.localName,E),T)for(var S in T)L(T,S)&&a.endPrefixMapping(S);y++;break;case"?":p&&u(g),y=function(e,t,r,n){var a=e.substring(t).match(N.PI);if(!a)return n.fatalError("Invalid processing instruction starting at position "+t);if("xml"===a[1].toLowerCase()){if(0<t)return n.fatalError("processing instruction at position "+t+" is an xml declaration which is only at the start of the document");if(!N.XMLDecl.test(e.substring(t)))return n.fatalError("xml declaration is not well-formed")}return r.processingInstruction(a[1],a[2]),t+a[0].length}(r,g,a,i);break;case"!":p&&u(g),y=q(r,g,a,i,o);break;default:p&&u(g);var x=new H,_=f[f.length-1].currentNSMap,k=(y=function(e,t,n,a,i,o){function r(e,t,r){return L(n.attributeNames,e)?i.fatalError("Attribute "+e+" redefined"):!o&&0<=t.indexOf("<")?i.fatalError("Unescaped '<' not allowed in attributes values"):void n.addValue(e,t.replace(/[\t\n\r]/g," ").replace(U,a),r)}for(var s,u=++t,c=0;;){var l=e.charAt(u);switch(l){case"=":if(1===c)s=e.slice(t,u),c=3;else{if(2!==c)throw new Error("attribute equal must after attrName");c=3}break;case"'":case'"':if(3===c||1===c){if(1===c&&(i.warning('attribute value must after "="'),s=e.slice(t,u)),t=u+1,!(0<(u=e.indexOf(l,t))))throw new Error("attribute value no end '"+l+"' match");r(s,d=e.slice(t,u),t-1),c=5}else{if(4!=c)throw new Error('attribute value must after "="');r(s,d=e.slice(t,u),t),i.warning('attribute "'+s+'" missed start quot('+l+")!!"),t=u+1,c=5}break;case"/":switch(c){case 0:n.setTagName(e.slice(t,u));case 5:case 6:case 7:c=7,n.closed=!0;case 4:case 1:break;case 2:n.closed=!0;break;default:throw new Error("attribute invalid close char('/')")}break;case"":return i.error("unexpected end of input"),0==c&&n.setTagName(e.slice(t,u)),u;case">":switch(c){case 0:n.setTagName(e.slice(t,u));case 5:case 6:case 7:break;case 4:case 1:"/"===(d=e.slice(t,u)).slice(-1)&&(n.closed=!0,d=d.slice(0,-1));case 2:2===c&&(d=s),4==c?(i.warning('attribute "'+d+'" missed quot(")!'),r(s,d,t)):(o||i.warning('attribute "'+d+'" missed value!! "'+d+'" instead!!'),r(d,d,t));break;case 3:if(!o)return i.fatalError("AttValue: ' or \" expected")}return u;case"":l=" ";default:if(l<=" ")switch(c){case 0:n.setTagName(e.slice(t,u)),c=6;break;case 1:s=e.slice(t,u),c=2;break;case 4:var d=e.slice(t,u);i.warning('attribute "'+d+'" missed quot(")!!'),r(s,d,t);case 5:c=6}else switch(c){case 2:o||i.warning('attribute "'+s+'" missed value!! "'+s+'" instead2!!'),r(s,s,t),t=u,c=1;break;case 5:i.warning('attribute space is required"'+s+'"!!');case 6:c=1,t=u;break;case 3:c=4,t=u;break;case 7:throw new Error("elements closed character '/' and '>' must be connected to")}}u++}}(r,g,x,s,i,o),x.length);if(x.closed||(o&&A.isHTMLVoidElement(x.tagName)?x.closed=!0:h.push(x.tagName)),p&&k){for(var E=j(p,{}),R=0;R<k;R++){var I=x[R];u(I.offset),I.locator=j(p,{})}a.locator=E,F(x,a,_)&&f.push(x),a.locator=p}else F(x,a,_)&&f.push(x);o&&!x.closed?y=function(e,t,r,n,a){var i=D(r);if(i||C(r)){r=e.indexOf("</"+r+">",t),e=e.substring(t+1,r);return i&&(e=e.replace(U,n)),a.characters(e,0,e.length),r}return t+1}(r,y,x.tagName,s,a):y++}}catch(r){if(r instanceof P)throw r;if(r instanceof M)throw new P(r.name+": "+r.message,a.locator,r);i.error("element parse error: "+r),y=-1}m<y?m=y:t(Math.max(g,m)+1)}}(e,t,r,n,this.errorHandler),n.endDocument()}};var U=/&#?\w+;?/g;function j(e,t){return t.lineNumber=e.lineNumber,t.columnNumber=e.columnNumber,t}function F(e,t,r){for(var n,a=e.tagName,i=null,o=e.length;o--;){var s,u,c=e[o],l=c.qName,d=c.value,l=0<(n=l.indexOf(":"))?(s=c.prefix=l.slice(0,n),u=l.slice(n+1),"xmlns"===s&&u):(s=null,"xmlns"===(u=l)&&"");c.localName=u,!1!==l&&(null==i&&(i=Object.create(null),h(r,r=Object.create(null))),r[l]=i[l]=d,c.uri=f.XMLNS,t.startPrefixMapping(l,d))}for(o=e.length;o--;)(c=e[o]).prefix&&("xml"===c.prefix&&(c.uri=f.XML),"xmlns"!==c.prefix&&(c.uri=r[c.prefix]));u=0<(n=a.indexOf(":"))?(s=e.prefix=a.slice(0,n),e.localName=a.slice(n+1)):(s=null,e.localName=a);var p=e.uri=r[s||""];if(t.startElement(p,u,a,e),!e.closed)return e.currentNSMap=r,e.localNSMap=i,1;if(t.endElement(p,u,a),i)for(s in i)L(i,s)&&t.endPrefixMapping(s)}function h(e,t){for(var r in e)L(e,r)&&(t[r]=e[r])}function u(r,e){var n=e;function a(e){return e=e||0,r.charAt(n+e)}function i(e){n+=e=e||1}function t(){return r.substring(n)}return{char:a,getIndex:function(){return n},getMatch:function(e){e=N.reg("^",e).exec(t());return e?(i(e[0].length),e[0]):null},getSource:function(){return r},skip:i,skipBlanks:function(){for(var e=0;n<r.length;){var t=a();if(" "!==t&&"\n"!==t&&"\t"!==t&&"\r"!==t)return e;e++,i()}return-1},substringFromIndex:t,substringStartsWith:function(e){return r.substring(n,n+e.length)===e}}}function q(e,t,r,n,a){var i=u(e,t);switch(i.char(2)){case"-":var o=i.getMatch(N.Comment);return o?(r.comment(o,N.COMMENT_START.length,o.length-N.COMMENT_START.length-N.COMMENT_END.length),i.getIndex()):n.fatalError("comment is not well-formed at position "+i.getIndex());case"[":var s=i.getMatch(N.CDSect);return s?a||r.currentElement?(r.startCDATA(),r.characters(s,N.CDATA_START.length,s.length-N.CDATA_START.length-N.CDATA_END.length),r.endCDATA(),i.getIndex()):n.fatalError("CDATA outside of element"):n.fatalError("Invalid CDATA starting at position "+t);case"D":if(r.doc&&r.doc.documentElement)return n.fatalError("Doctype not allowed inside or after documentElement at position "+i.getIndex());if(!i.substringStartsWith(N.DOCTYPE_DECL_START))return n.fatalError("Expected "+N.DOCTYPE_DECL_START+" at position "+i.getIndex());if(i.skip(N.DOCTYPE_DECL_START.length),i.skipBlanks()<1)return n.fatalError("Expected whitespace after "+N.DOCTYPE_DECL_START+" at position "+i.getIndex());o={name:void 0,publicId:void 0,systemId:void 0,internalSubset:void 0};if(o.name=i.getMatch(N.Name),!o.name)return n.fatalError("doctype name missing or contains unexpected characters at position "+i.getIndex());if(i.skipBlanks(),i.substringStartsWith(N.PUBLIC)||i.substringStartsWith(N.SYSTEM)){s=N.ExternalID_match.exec(i.substringFromIndex());if(!s)return n.fatalError("doctype external id is not well-formed at position "+i.getIndex());void 0!==s.groups.SystemLiteralOnly?o.systemId=s.groups.SystemLiteralOnly:(o.systemId=s.groups.SystemLiteral,o.publicId=s.groups.PubidLiteral),i.skip(s[0].length)}return i.skipBlanks(),o.internalSubset=function(e,t){var r,n,a,i=e.getSource();if("["===e.char()){e.skip(1);for(var o=e.getIndex();e.getIndex()<i.length;){if(e.skipBlanks(),"]"===e.char()){var s=i.substring(o,e.getIndex());return e.skip(1),s}var u=null;if("<"===e.char()&&"!"===e.char(1))switch(e.char(2)){case"E":"L"===e.char(3)?u=e.getMatch(N.elementdecl):"N"===e.char(3)&&(u=e.getMatch(N.EntityDecl));break;case"A":u=e.getMatch(N.AttlistDecl);break;case"N":u=e.getMatch(N.NotationDecl);break;case"-":u=e.getMatch(N.Comment)}else if("<"===e.char()&&"?"===e.char(1))r=e,n=t,a=void 0,u=(a=N.PI.exec(r.substringFromIndex()))?"xml"===a[1].toLowerCase()?n.fatalError("xml declaration is only allowed at the start of the document, but found at position "+r.getIndex()):(r.skip(a[0].length),a[0]):n.fatalError("processing instruction is not well-formed at position "+r.getIndex());else{if("%"!==e.char())return t.fatalError("Error detected in Markup declaration");u=e.getMatch(N.PEReference)}if(!u)return t.fatalError("Error in internal subset at position "+e.getIndex())}return t.fatalError("doctype internal subset is not well-formed, missing ]")}}(i,n),i.skipBlanks(),">"!==i.char()?n.fatalError("doctype not terminated with > at position "+i.getIndex()):(i.skip(1),r.startDTD(o.name,o.publicId,o.systemId,o.internalSubset),r.endDTD(),i.getIndex());default:return n.fatalError('Not well-formed XML starting with "<!" at position '+t)}}function H(){this.attributeNames=Object.create(null)}H.prototype={setTagName:function(e){if(!N.QName_exact.test(e))throw new Error("invalid tagName:"+e);this.tagName=e},addValue:function(e,t,r){if(!N.QName_exact.test(e))throw new Error("invalid attribute:"+e);this.attributeNames[e]=this.length,this[this.length++]={qName:e,value:t,offset:r}},length:0,getLocalName:function(e){return this[e].localName},getLocator:function(e){return this[e].locator},getQName:function(e){return this[e].qName},getURI:function(e){return this[e].uri},getValue:function(e){return this[e].value}},t.XMLReader=n,t.parseUtils=u,t.parseDoctypeCommentOrCData=q}},n={};function a(e){var t=n[e];if(void 0!==t)return t.exports;t=n[e]={exports:{}};return r[e](t,t.exports,a),t.exports}var i={};return function(){var e=i,t=a(539);e.assign=t.assign,e.hasDefaultHTMLNamespace=t.hasDefaultHTMLNamespace,e.isHTMLMimeType=t.isHTMLMimeType,e.isValidMimeType=t.isValidMimeType,e.MIME_TYPE=t.MIME_TYPE,e.NAMESPACE=t.NAMESPACE;t=a(180);e.DOMException=t.DOMException,e.DOMExceptionName=t.DOMExceptionName,e.ExceptionCode=t.ExceptionCode,e.ParseError=t.ParseError;t=a(179);e.Attr=t.Attr,e.CDATASection=t.CDATASection,e.CharacterData=t.CharacterData,e.Comment=t.Comment,e.Document=t.Document,e.DocumentFragment=t.DocumentFragment,e.DocumentType=t.DocumentType,e.DOMImplementation=t.DOMImplementation,e.Element=t.Element,e.Entity=t.Entity,e.EntityReference=t.EntityReference,e.LiveNodeList=t.LiveNodeList,e.NamedNodeMap=t.NamedNodeMap,e.Node=t.Node,e.NodeList=t.NodeList,e.Notation=t.Notation,e.ProcessingInstruction=t.ProcessingInstruction,e.Text=t.Text,e.XMLSerializer=t.XMLSerializer;t=a(579);e.DOMParser=t.DOMParser,e.onErrorStopParsing=t.onErrorStopParsing,e.onWarningStopParsing=t.onWarningStopParsing}(),i}),function(e,t){"object"==("undefined"==typeof exports?"undefined":_typeof(exports))&&"undefined"!=typeof module?t(exports,require("@xmldom/xmldom")):"function"==typeof define&&define.amd?define(["exports","@xmldom/xmldom"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).mpdParser={},e.xmldom)}(this,function(e,a){"use strict";function n(e){return!!e&&"object"==_typeof(e)}function d(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.reduce(function(t,r){return"object"!=_typeof(r)||Object.keys(r).forEach(function(e){Array.isArray(t[e])&&Array.isArray(r[e])?t[e]=t[e].concat(r[e]):n(t[e])&&n(r[e])?t[e]=d(t[e],r[e]):t[e]=r[e]}),t},{})}function i(t){return Object.keys(t).map(function(e){return t[e]})}function p(e){return e.reduce(function(e,t){return e.concat(t)},[])}function r(e){if(!e.length)return[];for(var t=[],r=0;r<e.length;r++)t.push(e[r]);return t}var t,o,s,u,c;t=/^(?=((?:[a-zA-Z0-9+\-.]+:)?))\1(?=((?:\/\/[^\/?#]*)?))\2(?=((?:(?:[^?#\/]*\/)*[^;?#\/]*)?))\3((?:;[^?#]*)?)(\?[^#]*)?(#[^]*)?$/,o=/^(?=([^\/?#]*))\1([^]*)$/,s=/(?:\/|^)\.(?=\/)/g,u=/(?:\/|^)\.\.\/(?!\.\.\/)[^\/]*(?=\/)/g;function l(e,t){if(/^[a-z]+:/i.test(t))return t;/^data:/.test(e)&&(e=window.location&&window.location.href||"");var r="function"==typeof window.URL,n=/^\/\//.test(e),a=!window.location&&!/\/\//i.test(e);if(r?e=new window.URL(e,window.location||C):/\/\//i.test(e)||(e=O.buildAbsoluteURL(window.location&&window.location.href||"",e)),r){r=new URL(t,e);return a?r.href.slice(C.length):n?r.href.slice(r.protocol.length):r.href}return O.buildAbsoluteURL(e,t)}function f(e){var t=void 0===(r=e.baseUrl)?"":r,r=void 0===(n=e.source)?"":n,n=void 0===(n=e.range)?"":n,e=void 0===(e=e.indexRange)?"":e,r={uri:r,resolvedUri:l(t||"",r)};return(n||e)&&(n=(n||e).split("-"),e=window.BigInt?window.BigInt(n[0]):parseInt(n[0],10),n=window.BigInt?window.BigInt(n[1]):parseInt(n[1],10),e<Number.MAX_SAFE_INTEGER&&"bigint"==typeof e&&(e=Number(e)),n<Number.MAX_SAFE_INTEGER&&"bigint"==typeof n&&(n=Number(n)),"bigint"==typeof(n="bigint"==typeof n||"bigint"==typeof e?window.BigInt(n)-window.BigInt(e)+window.BigInt(1):n-e+1)&&n<Number.MAX_SAFE_INTEGER&&(n=Number(n)),r.byterange={length:n,offset:e}),r}function h(e){return e&&"number"!=typeof e&&(e=parseInt(e,10)),isNaN(e)?1:e}function m(e){return e&&"number"!=typeof e&&(e=parseInt(e,10)),isNaN(e)?null:e}function g(e){var i,t=e.type,r=e.duration,n=e.timescale,a=void 0===n?1:n,o=e.periodDuration,s=e.sourceDuration,u=(n=L[t](e)).start,e=function(e){for(var t=[],r=u;r<e;r++)t.push(r);return t}(n.end).map((i=e,function(e){var t=i.duration,r=i.timescale,n=void 0===r?1:r,a=i.periodStart,r=i.startNumber;return{number:(void 0===r?1:r)+e,duration:t/n,timeline:a,time:e*t}}));return"static"===t&&(o="number"==typeof o?o:s,e[s=e.length-1].duration=o-r/a*s),e}function y(e){var t=e.baseUrl,r=void 0===(u=e.initialization)?{}:u,n=e.sourceDuration,a=void 0===(s=e.indexRange)?"":s,i=e.periodStart,o=e.presentationTime,s=void 0===(u=e.number)?0:u,u=e.duration;if(!t)throw new Error("NO_BASE_URL");return r=f({baseUrl:t,source:r.sourceURL,range:r.range}),(a=f({baseUrl:t,source:t,indexRange:a})).map=r,u?(e=g(e)).length&&(a.duration=e[0].duration,a.timeline=e[0].timeline):n&&(a.duration=n,a.timeline=i),a.presentationTime=o||i,a.number=s,[a]}function v(e,t,r){for(var n=e.sidx.map||null,a=e.sidx.duration,i=e.timeline||0,o=(o=e.sidx.byterange).offset+o.length,s=t.timescale,u=t.references.filter(function(e){return 1!==e.referenceType}),c=[],l=e.endList?"static":"dynamic",d=e.sidx.timeline,p=d,f=e.mediaSequence||0,h="bigint"==typeof t.firstOffset?window.BigInt(o)+t.firstOffset:o+t.firstOffset,m=0;m<u.length;m++){var g=t.references[m],v=g.referencedSize,b=g.subsegmentDuration,g=void 0,g="bigint"==typeof h?h+window.BigInt(v)-window.BigInt(1):h+v-1,g=y({baseUrl:r,timescale:s,timeline:i,periodStart:d,presentationTime:p,number:f,duration:b,sourceDuration:a,indexRange:"".concat(h,"-").concat(g),type:l})[0];n&&(g.map=n),c.push(g),h+="bigint"==typeof h?window.BigInt(v):v,p+=b/s,f++}return e.segments=c,e}function b(e){return r=function(e){return e.timeline},i(e.reduce(function(t,e){return e.forEach(function(e){t[r(e)]=e}),t},{})).sort(function(e,t){return e.timeline>t.timeline?1:-1});var r}function _(e){var a=[],i=e,o=function(e,t,r,n){a=a.concat(e.playlists||[])};return P.forEach(function(e){for(var t in i.mediaGroups[e])for(var r in i.mediaGroups[e][t]){var n=i.mediaGroups[e][t][r];o(n)}}),a}function w(e){var r=e.playlist,e=e.mediaSequence;r.mediaSequence=e,r.segments.forEach(function(e,t){e.number=r.mediaSequence+t})}function E(e){return e&&e.uri+"-"+(t=e.byterange,e="bigint"==typeof t.offset||"bigint"==typeof t.length?window.BigInt(t.offset)+window.BigInt(t.length)-window.BigInt(1):t.offset+t.length-1,"".concat(t.offset,"-").concat(e));var t}function T(e){var e=e.reduce(function(e,t){return e[t.attributes.baseUrl]||(e[t.attributes.baseUrl]=[]),e[t.attributes.baseUrl].push(t),e},{}),t=[];return Object.values(e).forEach(function(e){e=i(e.reduce(function(e,t){var r,n=t.attributes.id+(t.attributes.lang||"");return e[n]?(t.segments&&(t.segments[0]&&(t.segments[0].discontinuity=!0),(r=e[n].segments).push.apply(r,_toConsumableArray(t.segments))),t.attributes.contentProtection&&(e[n].attributes.contentProtection=t.attributes.contentProtection)):(e[n]=t,e[n].attributes.timelineStarts=[]),e[n].attributes.timelineStarts.push({start:t.attributes.periodStart,timeline:t.attributes.periodStart}),e},{}));t=t.concat(e)}),t.map(function(e){var t;return e.discontinuityStarts=(t=e.segments||[],t.reduce(function(e,t,r){return t.discontinuity&&e.push(r),e},[])),e})}function S(e,t){var r=E(e.sidx);return(r=r&&t[r]&&t[r].sidx)&&v(e,r,e.sidx.resolvedUri),e}function x(e){var t=e.attributes,r=e.segments,n=e.sidx,e=e.discontinuityStarts,r={attributes:{NAME:t.id,AUDIO:"audio",SUBTITLES:"subs",RESOLUTION:{width:t.width,height:t.height},CODECS:t.codecs,BANDWIDTH:t.bandwidth,"PROGRAM-ID":1},uri:"",endList:"static"===t.type,timeline:t.periodStart,resolvedUri:t.baseUrl||"",targetDuration:t.duration,discontinuityStarts:e,timelineStarts:t.timelineStarts,segments:r};return t.frameRate&&(r.attributes["FRAME-RATE"]=t.frameRate),t.contentProtection&&(r.contentProtection=t.contentProtection),t.serviceLocation&&(r.attributes.serviceLocation=t.serviceLocation),n&&(r.sidx=n),r}function k(t){return["mp4a","ac-3","ec-3","opus","vorbis"].some(function(e){return t.startsWith(e)})}function R(e){return"video/mp4"===(e=e.attributes).mimeType||"video/webm"===e.mimeType||"video/mp2t"===e.mimeType&&e.codecs&&!k(e.codecs)||"video"===e.contentType}function I(e){return"audio/mp4"===(e=e.attributes).mimeType||"audio/webm"===e.mimeType||"video/mp2t"===e.mimeType&&e.codecs&&k(e.codecs)||"audio"===e.contentType}function A(e){return"text/vtt"===(e=e.attributes).mimeType||"text"===e.contentType}function N(r){return r?Object.keys(r).reduce(function(e,t){t=r[t];return e.concat(t.playlists)},[]):[]}function D(e){var t=e.dashPlaylists,r=e.locations,n=e.contentSteering,a=void 0===(d=e.sidxMapping)?{}:d,i=e.previousManifest,o=e.eventStream;if(!t.length)return{};var s=(p=t[0].attributes).sourceDuration,u=p.type,c=p.suggestedPresentationDelay,l=p.minimumUpdatePeriod,d=T(t.filter(R)).map(x),e=T(t.filter(I)),p=T(t.filter(A)),t=t.map(function(e){return e.attributes.captionServices}).filter(Boolean),s={allowCache:!0,discontinuityStarts:[],segments:[],endList:!0,mediaGroups:{AUDIO:{},VIDEO:{},"CLOSED-CAPTIONS":{},SUBTITLES:{}},uri:"",duration:s,playlists:function(e){var t,r=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};if(!Object.keys(r).length)return e;for(t in e)e[t]=S(e[t],r);return e}(d,a)};0<=l&&(s.minimumUpdatePeriod=1e3*l),r&&(s.locations=r),n&&(s.contentSteering=n),"dynamic"===u&&(s.suggestedPresentationDelay=c),o&&0<o.length&&(s.eventStream=o);var f,h,m,o=0===s.playlists.length,o=e.length?function(e,t,r){var d,p=1<arguments.length&&void 0!==t?t:{},f=2<arguments.length&&void 0!==r&&r,e=e.reduce(function(e,t){var r=t.attributes.role&&t.attributes.role.value||"",n=t.attributes.lang||"",a=t.attributes.label||"main";n&&!t.attributes.label&&(l=r?" (".concat(r,")"):"",a="".concat(t.attributes.lang).concat(l)),e[a]||(e[a]={language:n,autoselect:!0,default:"main"===r,playlists:[],uri:""});var i,o,s,u,c,l,s=S((i=f,o=t.attributes,s=t.segments,u=t.sidx,c=t.mediaSequence,l=t.discontinuitySequence,n=t.discontinuityStarts,s={attributes:{NAME:o.id,BANDWIDTH:o.bandwidth,CODECS:o.codecs,"PROGRAM-ID":1},uri:"",endList:"static"===o.type,timeline:o.periodStart,resolvedUri:o.baseUrl||"",targetDuration:o.duration,discontinuitySequence:l,discontinuityStarts:n,timelineStarts:o.timelineStarts,mediaSequence:c,segments:s},o.contentProtection&&(s.contentProtection=o.contentProtection),o.serviceLocation&&(s.attributes.serviceLocation=o.serviceLocation),u&&(s.sidx=u),i&&(s.attributes.AUDIO="audio",s.attributes.SUBTITLES="subs"),s),p);return e[a].playlists.push(s),void 0===d&&"main"===r&&((d=t).default=!0),e},{});return d||(e[Object.keys(e)[0]].default=!0),e}(e,a,o):null,p=p.length?function(e,t){var r=1<arguments.length&&void 0!==t?t:{};return e.reduce(function(e,o){var t=o.attributes.label||o.attributes.lang||"text";return e[t]||(e[t]={language:t,default:!1,autoselect:!1,playlists:[],uri:""}),e[t].playlists.push(S(function(){var e=o.attributes,t=o.segments,r=o.mediaSequence,n=o.discontinuityStarts,a=o.discontinuitySequence;void 0===t&&(t=[{uri:e.baseUrl,timeline:e.periodStart,resolvedUri:e.baseUrl||"",duration:e.sourceDuration,number:0}],e.duration=e.sourceDuration);var i={NAME:e.id,BANDWIDTH:e.bandwidth,"PROGRAM-ID":1};e.codecs&&(i.CODECS=e.codecs);t={attributes:i,uri:"",endList:"static"===e.type,timeline:e.periodStart,resolvedUri:e.baseUrl||"",targetDuration:e.duration,timelineStarts:e.timelineStarts,discontinuityStarts:n,discontinuitySequence:a,mediaSequence:r,segments:t};return e.serviceLocation&&(t.attributes.serviceLocation=e.serviceLocation),t}(),r)),e},{})}(p,a):null,d=(a=d.concat(N(o),N(p))).map(function(e){return e.timelineStarts});return s.timelineStarts=b(d),f=s.timelineStarts,a.forEach(function(t){t.mediaSequence=0,t.discontinuitySequence=f.findIndex(function(e){return e.timeline===t.timeline}),t.segments&&t.segments.forEach(function(e,t){e.number=t})}),o&&(s.mediaGroups.AUDIO.audio=o),p&&(s.mediaGroups.SUBTITLES.subs=p),t.length&&(s.mediaGroups["CLOSED-CAPTIONS"].cc=t.reduce(function(n,e){return e&&e.forEach(function(e){var t=e.channel,r=e.language;n[r]={autoselect:!1,default:!1,instreamId:t,language:r},e.hasOwnProperty("aspectRatio")&&(n[r].aspectRatio=e.aspectRatio),e.hasOwnProperty("easyReader")&&(n[r].easyReader=e.easyReader),e.hasOwnProperty("3D")&&(n[r]["3D"]=e["3D"])}),n},{})),i?(p=(o={oldManifest:i,newManifest:s}).oldManifest,t=o.newManifest,i=p.playlists.concat(_(p)),o=t.playlists.concat(_(t)),t.timelineStarts=b([p.timelineStarts,t.timelineStarts]),o={oldPlaylists:i,newPlaylists:o,timelineStarts:t.timelineStarts},h=o.oldPlaylists,m=o.timelineStarts,o.newPlaylists.forEach(function(t){t.discontinuitySequence=m.findIndex(function(e){return e.timeline===t.timeline});var e=function(e,t){for(var r=0;r<e.length;r++)if(e[r].attributes.NAME===t)return e[r];return null}(h,t.attributes.NAME);if(e&&!t.sidx){var r=t.segments[0],n=e.segments.findIndex(function(e){return Math.abs(e.presentationTime-r.presentationTime)<.016666666666666666});if(-1===n)return w({playlist:t,mediaSequence:e.mediaSequence+e.segments.length}),t.segments[0].discontinuity=!0,t.discontinuityStarts.unshift(0),void((!e.segments.length&&t.timeline>e.timeline||e.segments.length&&t.timeline>e.segments[e.segments.length-1].timeline)&&t.discontinuitySequence--);e.segments[n].discontinuity&&!r.discontinuity&&(r.discontinuity=!0,t.discontinuityStarts.unshift(0),t.discontinuitySequence--),w({playlist:t,mediaSequence:e.segments[n].number})}}),t):s}var O=c={buildAbsoluteURL:function(e,t,r){if(r=r||{},e=e.trim(),!(t=t.trim())){if(!r.alwaysNormalize)return e;var n=c.parseURL(e);if(!n)throw new Error("Error trying to parse base URL.");return n.path=c.normalizePath(n.path),c.buildURLFromParts(n)}n=c.parseURL(t);if(!n)throw new Error("Error trying to parse relative URL.");if(n.scheme)return r.alwaysNormalize?(n.path=c.normalizePath(n.path),c.buildURLFromParts(n)):t;t=c.parseURL(e);if(!t)throw new Error("Error trying to parse base URL.");!t.netLoc&&t.path&&"/"!==t.path[0]&&(a=o.exec(t.path),t.netLoc=a[1],t.path=a[2]),t.netLoc&&!t.path&&(t.path="/");var a,e={scheme:t.scheme,netLoc:n.netLoc,path:null,params:n.params,query:n.query,fragment:n.fragment};return n.netLoc||(e.netLoc=t.netLoc,"/"===n.path[0])||(n.path?(a=(a=t.path).substring(0,a.lastIndexOf("/")+1)+n.path,e.path=c.normalizePath(a)):(e.path=t.path,n.params||(e.params=t.params,n.query||(e.query=t.query)))),null===e.path&&(e.path=r.alwaysNormalize?c.normalizePath(n.path):n.path),c.buildURLFromParts(e)},parseURL:function(e){e=t.exec(e);return e?{scheme:e[1]||"",netLoc:e[2]||"",path:e[3]||"",params:e[4]||"",query:e[5]||"",fragment:e[6]||""}:null},normalizePath:function(e){for(e=e.split("").reverse().join("").replace(s,"");e.length!==(e=e.replace(u,"")).length;);return e.split("").reverse().join("")},buildURLFromParts:function(e){return e.scheme+e.netLoc+e.path+e.params+e.query+e.fragment}},C="http://example.com",L={static:function(e){var t=e.duration,r=e.timescale,n=void 0===r?1:r,a=e.sourceDuration,i=e.periodDuration,r=h(e.startNumber)-1,e=m(e.endNumber),n=t/n;return"number"==typeof e?{start:r,end:e}:"number"==typeof i?{start:r,end:r+Math.ceil(i/n)}:{start:r,end:r+Math.ceil(a/n)}},dynamic:function(e){var t=e.NOW,r=e.clientOffset,n=e.availabilityStartTime,a=e.timescale,i=void 0===a?1:a,o=e.duration,s=e.periodStart,u=void 0===s?0:s,c=e.minimumUpdatePeriod,a=void 0===c?0:c,s=e.timeShiftBufferDepth,c=void 0===s?1/0:s,s=h(e.startNumber)-1,e=m(e.endNumber),r=(t+r)/1e3,u=n+u,a=r+a-u,a=Math.ceil(a*i/o),c=Math.floor((r-u-c)*i/o),o=Math.floor((r-u)*i/o);return{start:s+Math.max(0,c),end:"number"==typeof e?e:s+Math.min(a,o)}}},P=["AUDIO","SUBTITLES"];function M(){return(M=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r,n=arguments[t];for(r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function U(e,t){for(var r,n,a,i,o,s,u=e.type,c=e.minimumUpdatePeriod,l=void 0===c?0:c,d=void 0===(c=e.media)?"":c,p=e.sourceDuration,f=void 0===(c=e.timescale)?1:c,h=void 0===(c=e.startNumber)?1:c,m=e.periodStart,g=[],v=-1,b=0;b<t.length;b++){var y,_=t[b],w=_.d,E=_.r||0,T=_.t||0,S=void 0;v<0&&(v=T),T&&v<T&&(v=T),S=E<0?(y=b+1)===t.length?"dynamic"===u&&0<l&&0<d.indexOf("$Number$")?(r=v,n=w,s=s=s=_=s=o=i=a=void 0,a=(T=e).NOW,i=T.clientOffset,o=T.availabilityStartTime,s=T.timescale,_=void 0===s?1:s,s=void 0===(s=T.periodStart)?0:s,T=T.minimumUpdatePeriod,s=(a+i)/1e3+(void 0===T?0:T)-(o+s),Math.ceil((s*_-r)/n)):(p*f-v)/w:(t[y].t-v)/w:E+1;for(var x=h+g.length+S,k=h+g.length;k<x;)g.push({number:k,duration:w/f,time:v,timeline:m}),v+=w,k++}return g}function j(e,t){return e.replace(W,(a=t,function(e,t,r,n){if("$$"===e)return"$";if(void 0===a[t])return e;e=""+a[t];return"RepresentationID"===t?e:(n=r?parseInt(n,10):1)<=e.length?e:"".concat(new Array(n-e.length+1).join("0")).concat(e)}));var a}function F(a,e){var i={RepresentationID:a.id,Bandwidth:a.bandwidth||0},t=void 0===(t=a.initialization)?{sourceURL:"",range:""}:t,o="video/mp2t"===a.mimeType&&0===t.sourceURL.length?null:f({baseUrl:a.baseUrl,source:j(t.sourceURL,i),range:t.range});return(t=e,(e=a).duration||t?e.duration?g(e):U(e,t):[{number:e.startNumber||1,duration:e.sourceDuration,time:0,timeline:e.periodStart}]).map(function(e){i.Number=e.number,i.Time=e.time;var t=j(a.media||"",i),r=a.timescale||1,n=a.presentationTimeOffset||0,r=a.periodStart+(e.time-n)/r;return M({uri:t,timeline:e.timeline,duration:e.duration,resolvedUri:l(a.baseUrl||"",t),number:e.number,presentationTime:r},o?{map:o}:{})})}function q(a,e){var t=a.duration,r=void 0===(r=a.segmentUrls)?[]:r,i=a.periodStart;if(!t&&!e||t&&e)throw new Error("SEGMENT_TIME_UNSPECIFIED");var n,o=r.map(function(e){return t=e,r=a.baseUrl,e=a.initialization,e=f({baseUrl:r,source:(e=void 0===e?{}:e).sourceURL,range:e.range}),(t=f({baseUrl:r,source:t.media,range:t.mediaRange})).map=e,t;var t,r});return t&&(n=g(a)),e&&(n=U(a,e)),n.map(function(e,t){if(o[t]){var r=o[t],n=a.timescale||1,t=a.presentationTimeOffset||0;return r.timeline=e.timeline,r.duration=e.duration,r.number=e.number,r.presentationTime=i+(e.time-t)/n,r}}).filter(function(e){return e})}function H(e){var t,r=e.attributes,n=e.segmentInfo;n.template?(i=F,t=d(r,n.template)):n.base?(i=y,t=d(r,n.base)):n.list&&(i=q,t=d(r,n.list));var a={attributes:r};if(!i)return a;var i,o=i(t,n.segmentTimeline);return r.key&&o.map(function(e){return e.key=r.key,e.map&&(e.map.key=r.key),e}),t.duration?(e=t.duration,i=void 0===(i=t.timescale)?1:i,t.duration=e/i):o.length?t.duration=o.reduce(function(e,t){return Math.max(e,Math.ceil(t.duration))},0):t.duration=0,a.attributes=t,a.segments=o,n.base&&t.indexRange&&(a.sidx=o[0],a.segments=[]),a}function G(e){return e.map(H)}function B(e,t){return r(e.childNodes).filter(function(e){return e.tagName===t})}function V(e){return e.textContent.trim()}function X(e){if(!(a=/P(?:(\d*)Y)?(?:(\d*)M)?(?:(\d*)D)?(?:T(?:(\d*)H)?(?:(\d*)M)?(?:([\d.]*)S)?)?/.exec(e)))return 0;var t=(i=_slicedToArray(a.slice(1),6))[0],r=i[1],n=i[2],e=i[3],a=i[4],i=i[5];return 31536e3*parseFloat(t||0)+2592e3*parseFloat(r||0)+86400*parseFloat(n||0)+3600*parseFloat(e||0)+60*parseFloat(a||0)+parseFloat(i||0)}function z(e){return e&&e.attributes?r(e.attributes).reduce(function(e,t){var r=Y[t.name]||Y.DEFAULT;return e[t.name]=r(t.value),e},{}):{}}var W=/\$([A-z]*)(?:(%0)([0-9]+)d)?\$/g,Y={mediaPresentationDuration:X,availabilityStartTime:function(e){return/^\d+-\d+-\d+T\d+:\d+:\d+(\.\d+)?$/.test(e=e)&&(e+="Z"),Date.parse(e)/1e3},minimumUpdatePeriod:X,suggestedPresentationDelay:X,type:function(e){return e},timeShiftBufferDepth:X,start:X,width:function(e){return parseInt(e,10)},height:function(e){return parseInt(e,10)},bandwidth:function(e){return parseInt(e,10)},frameRate:function(e){return parseFloat(e.split("/").reduce(function(e,t){return e/t}))},startNumber:function(e){return parseInt(e,10)},timescale:function(e){return parseInt(e,10)},presentationTimeOffset:function(e){return parseInt(e,10)},duration:function(e){var t=parseInt(e,10);return isNaN(t)?X(e):t},d:function(e){return parseInt(e,10)},t:function(e){return parseInt(e,10)},r:function(e){return parseInt(e,10)},presentationTime:function(e){return parseInt(e,10)},DEFAULT:function(e){return e}};function $(e,t){return t.length?p(e.map(function(n){return t.map(function(e){var t=V(e),r=l(n.baseUrl,t),e=d(z(e),{baseUrl:r});return r!==t&&!e.serviceLocation&&n.serviceLocation&&(e.serviceLocation=n.serviceLocation),e})})):e}function K(e){var t=B(e,"SegmentTemplate")[0],r=B(e,"SegmentList")[0],n=r&&B(r,"SegmentURL").map(function(e){return d({tag:"SegmentURL"},z(e))}),a=B(e,"SegmentBase")[0],e=(i=r||t)&&B(i,"SegmentTimeline")[0],i=(i=r||a||t)&&B(i,"Initialization")[0];(t=t&&z(t))&&i?t.initialization=i&&z(i):t&&t.initialization&&(t.initialization={sourceURL:t.initialization});var o={template:t,segmentTimeline:e&&B(e,"S").map(z),list:r&&d(z(r),{segmentUrls:n,initialization:z(i)}),base:a&&d(z(a),{initialization:z(i)})};return Object.keys(o).forEach(function(e){o[e]||delete o[e]}),o}function Q(s){return p(B(s.node,"EventStream").map(function(e){var i=z(e),o=i.schemeIdUri;return B(e,"Event").map(function(e){var t=z(e),r=t.presentationTime||0,n=i.timescale||1,a=t.duration||0,r=r/n+s.attributes.start;return{schemeIdUri:o,value:i.value,id:t.id,start:r,end:r+a/n,messageData:V(e)||t.messageData,contentEncoding:i.contentEncoding,presentationTimeOffset:i.presentationTimeOffset||0}})}))}function J(u,c,l){return function(e){var t=z(e),r=$(c,B(e,"BaseURL")),n=B(e,"Role")[0],n={role:z(n)},t=d(u,t,n),n=B(e,"Accessibility")[0],n="urn:scte:dash:cc:cea-608:2015"===(n=z(n)).schemeIdUri?("string"!=typeof n.value?[]:n.value.split(";")).map(function(e){var t,r,n=e;return/^CC\d=/.test(e)?(r=(t=_slicedToArray(e.split("="),2))[0],n=t[1]):/^CC\d$/.test(e)&&(r=e),{channel:r,language:n}}):"urn:scte:dash:cc:cea-708:2015"===n.schemeIdUri?("string"!=typeof n.value?[]:n.value.split(";")).map(function(e){var t,r,n={channel:void 0,language:void 0,aspectRatio:1,easyReader:0,"3D":0};return/=/.test(e)?(t=(r=_slicedToArray(e.split("="),2))[0],r=void 0===(r=r[1])?"":r,n.channel=t,n.language=e,r.split(",").forEach(function(e){var t=_slicedToArray(e.split(":"),2),e=t[0],t=t[1];"lang"===e?n.language=t:"er"===e?n.easyReader=Number(t):"war"===e?n.aspectRatio=Number(t):"3D"===e&&(n["3D"]=Number(t))})):n.language=e,n.channel&&(n.channel="SERVICE"+n.channel),n}):void 0;n&&(t=d(t,{captionServices:n}));n=B(e,"Label")[0];n&&n.childNodes.length&&(a=n.childNodes[0].nodeValue.trim(),t=d(t,{label:a}));var a=B(e,"ContentProtection").reduce(function(e,t){var r=z(t);r.schemeIdUri&&(r.schemeIdUri=r.schemeIdUri.toLowerCase());var n=te[r.schemeIdUri];return n&&(e[n]={attributes:r},(t=B(t,"cenc:pssh")[0])&&(t=V(t),e[n].pssh=t&&function(e){for(var t=(e=e,window.atob?window.atob(e):Buffer.from(e,"base64").toString("binary")),r=new Uint8Array(t.length),n=0;n<t.length;n++)r[n]=t.charCodeAt(n);return r}(t))),e},{});Object.keys(a).length&&(t=d(t,{contentProtection:a}));a=B(e,"ContentProtection").reduce(function(e,t){var r=z(t);if(r.schemeIdUri&&(r.schemeIdUri=r.schemeIdUri.toLowerCase()),"urn:mpeg:dash:sea:2012"===r.schemeIdUri){r=B(t,"sea:SegmentEncryption")[0];if(r&&"urn:mpeg:dash:sea:aes128-cbc:2013"!==z(r).schemeIdUri)return e;t=B(t,"sea:CryptoPeriod")[0];!t||(t=z(t)).IV&&t.keyUriTemplate&&("0x"===t.IV.substring(0,2).toLowerCase()&&(t.IV=t.IV.substring(2)),t.IV=t.IV.match(/.{8}/g),t.IV[0]=parseInt(t.IV[0],16),t.IV[1]=parseInt(t.IV[1],16),t.IV[2]=parseInt(t.IV[2],16),t.IV[3]=parseInt(t.IV[3],16),t.IV=new Uint32Array(t.IV),e={method:"AES-128",iv:t.IV,uri:t.keyUriTemplate})}return e},{});Object.keys(a).length&&(t=d(t,{key:a}));var i,o,s,a=K(e),e=B(e,"Representation"),a=d(l,a);return p(e.map((i=t,o=r,s=a,function(e){var t=B(e,"BaseURL"),t=$(o,t),r=d(i,z(e)),n=K(e);return t.map(function(e){return{segmentInfo:d(s,n),attributes:d(r,e)}})})))}}function Z(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},r=void 0===(a=t.manifestUri)?"":a,n=void 0===(o=t.NOW)?Date.now():o,a=void 0===(i=t.clientOffset)?0:i,i=void 0===(o=t.eventHandler)?function(){}:o;if(!(t=B(e,"Period")).length)throw new Error("INVALID_NUMBER_OF_PERIOD");var o=B(e,"Location"),s=z(e),r=$([{baseUrl:r}],B(e,"BaseURL")),e=B(e,"ContentSteering");s.type=s.type||"static",s.sourceDuration=s.mediaPresentationDuration||0,s.NOW=n,s.clientOffset=a,o.length&&(s.locations=o.map(V));var u,c,l=[];return t.forEach(function(e,t){var r,n=z(e),a=l[t-1];n.start=(r={attributes:n,priorPeriodAttributes:a?a.attributes:null,mpdType:s.type},t=r.attributes,a=r.priorPeriodAttributes,r=r.mpdType,"number"==typeof t.start?t.start:a&&"number"==typeof a.start&&"number"==typeof a.duration?a.start+a.duration:a||"static"!==r?null:0),l.push({node:e,attributes:n})}),{locations:s.locations,contentSteeringInfo:function(e,t){if(1<e.length&&t({type:"warn",message:"The MPD manifest should contain no more than one ContentSteering tag"}),!e.length)return null;e=d({serverURL:V(e[0])},z(e[0]));return e.queryBeforeStart="true"===e.queryBeforeStart,e}(e,i),representationInfo:p(l.map((u=s,c=r,function(e,t){var r=$(c,B(e.node,"BaseURL")),n=d(u,{periodStart:e.attributes.start});"number"==typeof e.attributes.duration&&(n.periodDuration=e.attributes.duration);var a=B(e.node,"AdaptationSet"),e=K(e.node);return p(a.map(J(n,r,e)))}))),eventStream:p(l.map(Q))}}function ee(e){if(""===e)throw new Error("DASH_EMPTY_MANIFEST");var t,r,n=new a.DOMParser;try{r=(t=n.parseFromString(e,"application/xml"))&&"MPD"===t.documentElement.tagName?t.documentElement:null}catch(e){}if(!r||r&&0<r.getElementsByTagName("parsererror").length)throw new Error("DASH_INVALID_XML");return r}var te={"urn:uuid:1077efec-c0b2-4d02-ace3-3c1e52e2fb4b":"org.w3.clearkey","urn:uuid:edef8ba9-79d6-4ace-a3c8-27dcd51d21ed":"com.widevine.alpha","urn:uuid:9a04f079-9840-4286-ab92-e65be0885f95":"com.microsoft.playready","urn:uuid:f239e769-efa3-4850-9c16-a903c6932efb":"com.adobe.primetime","urn:mpeg:dash:mp4protection:2011":"mp4protection"};e.VERSION="1.3.0",e.addSidxSegmentsToPlaylist=v,e.generateSidxKey=E,e.inheritAttributes=Z,e.parse=function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},r=Z(ee(e),t),e=G(r.representationInfo);return D({dashPlaylists:e,locations:r.locations,contentSteering:r.contentSteeringInfo,sidxMapping:t.sidxMapping,previousManifest:t.previousManifest,eventStream:r.eventStream})},e.parseUTCTiming=function(e){return function(e){e=B(e,"UTCTiming")[0];if(!e)return null;var t=z(e);switch(t.schemeIdUri){case"urn:mpeg:dash:utc:http-head:2014":case"urn:mpeg:dash:utc:http-head:2012":t.method="HEAD";break;case"urn:mpeg:dash:utc:http-xsdate:2014":case"urn:mpeg:dash:utc:http-iso:2014":case"urn:mpeg:dash:utc:http-xsdate:2012":case"urn:mpeg:dash:utc:http-iso:2012":t.method="GET";break;case"urn:mpeg:dash:utc:direct:2014":case"urn:mpeg:dash:utc:direct:2012":t.method="DIRECT",t.value=Date.parse(t.value);break;default:throw new Error("UNSUPPORTED_UTC_TIMING_SCHEME")}return t}(ee(e))},e.stringToMpdXml=ee,e.toM3u8=D,e.toPlaylists=G,Object.defineProperty(e,"__esModule",{value:!0})});var patternValidationRegex=/^<all_urls>$|^(https?|wss?|file|ftp|\*):\/\/(\*|\*\.[^*/]+|[^*/]+)\/.*$|^file:\/\/\/.*$|^resource:\/\/(\*|\*\.[^*/]+|[^*/]+)\/.*$|^about:/;function getRawRegex(e){if(!patternValidationRegex.test(e))throw new Error(e+" is an invalid pattern, it must match "+String(patternValidationRegex));"<all_urls>"==e&&(e="*://*/*");var t=_slicedToArray(e.split(/(^[^:]+:[/][/])([^/]+)?/),4),r=t[1],e=t[2],t=t[3];return"^"+(r=r.replace("*","https?").replace(/[/]/g,"[/]"))+(e=(null!=e?e:"").replace(/[.]/g,"[.]").replace(/^[*]/,"[^/]+").replace(/[*]$/g,"[^.]+"))+"("+(t=t.replace(/[/]/g,"[/]").replace(/[.]/g,"[.]").replace(/[*]/g,".*"))+")?$"}function patternToRegex(e){return new RegExp(e.map(getRawRegex).join("|"))}function isOriginPermitted(e){return _isOriginPermitted.apply(this,arguments)}function _isOriginPermitted(){return(_isOriginPermitted=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",p(browser.permissions.contains,{origins:[new URL(t).origin+"/*"]}));case 1:case"end":return e.stop()}},e)}))).apply(this,arguments)}function wasPreviouslyLoaded(e,t,r){return _wasPreviouslyLoaded.apply(this,arguments)}function _wasPreviouslyLoaded(){return(_wasPreviouslyLoaded=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,r,n){var a;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return a=browser.runtime.id,e.next=3,browser.scripting.executeScript({target:{tabId:t,allFrames:n},args:[a,r],func:function(e,t){var r=void 0!==window[e]&&window[e]==t;return window[e]=t,r}});case 3:return a=e.sent,ret=null==a?void 0:1==a[0].result,e.abrupt("return",ret);case 7:case"end":return e.stop()}},e)}))).apply(this,arguments)}function injectScript(e,t,r,n){return _injectScript.apply(this,arguments)}function _injectScript(){return(_injectScript=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,r,n,a){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,r.length)return e.next=4,browser.scripting.insertCSS({target:t,files:r});e.next=4;break;case 4:if(n.length)return e.prev=5,e.next=8,browser.scripting.executeScript({target:t,files:n});e.next=13;break;case 8:e.next=13;break;case 10:throw e.prev=10,e.t0=e.catch(5),new Error("browser.runtime.lastError ".concat(e.t0.message));case 13:e.next=18;break;case 15:e.prev=15,e.t1=e.catch(0);case 18:case"end":return e.stop()}},e,null,[[0,15],[5,10]])}))).apply(this,arguments)}function is_exclude_urls(e){return e.match(/^(?:chrome(|-\w+)?:\/\/)|(?:(?:https?:)?\/\/chrome(?:webstore)?.google.com)/)||e.match(/^https?:\/\/addons.opera.com\//)||e.match(/^https?:\/\/addons.mozilla.org\//)||e.match(/^https?:\/\/microsoftedge.microsoft.com\/addons/)}function matchsCheck(t,e,r){try{if(!t.url||is_exclude_urls(t.url)||!e.test(t.url)||r&&r.test(t.url))return!1}catch(e){if(e.message.includes("chrome-error://chromewebdata/"))return!1;throw url=t.url||"",new Error("matchsCheck error ".concat(e.message," url: ").concat(url))}return!0}function _installScripts2(){return _installScripts.apply(this,arguments)}function _installScripts(){return(_installScripts=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t,c,r=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(c=1<r.length&&void 0!==r[1]?r[1]:null,t=(t=0<r.length&&void 0!==r[0]?r[0]:null)||browser.runtime.getManifest().content_scripts,c){e.next=14;break}return e.prev=4,e.next=7,p(browser.tabs.query,{});case 7:c=(c=e.sent).filter(function(e){return!(is_exclude_urls(e.url)||"unloaded"==e.status)}),e.next=14;break;case 11:throw e.prev=11,e.t0=e.catch(4),new Error("browser.runtime.lastError ".concat(e.t0.message));case 14:t.forEach(function(e){var t=e.js,r=void 0===t?[]:t,n=e.css,a=void 0===n?[]:n,t=e.matches,n=e.excludeMatches,i="document[".concat(JSON.stringify(JSON.stringify({js:r,css:a})),"]"),o=patternToRegex(t),s=n?patternToRegex(n):null,u={};"allFrames"in e?u.allFrames=e.allFrames:"frameIds"in e?u.frameIds=e.frameIds:"documentIds"in e&&(u.documentIds=e.documentIds),c.forEach(function(){var t=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:matchsCheck(t,o,s)&&(u.tabId=t.id,injectScript(u,a,r,i));case 1:case"end":return e.stop()}},e)}));return function(e){return t.apply(this,arguments)}}())});case 15:case"end":return e.stop()}},e,null,[[4,11]])}))).apply(this,arguments)}browser.contentScripts||(browser.contentScripts={register:function(d,f){return _asyncToGenerator(_regeneratorRuntime().mark(function e(){var a,i,o,t,s,u,c,r,n,l;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t=d.js,a=void 0===t?[]:t,l=d.css,i=void 0===l?[]:l,o=d.allFrames,t=d.matches,l=d.excludeMatches,s="document[".concat(JSON.stringify(JSON.stringify({js:a,css:i})),"]"),u=patternToRegex(t),c=l?patternToRegex(l):null,r={},"allFrames"in d?r.allFrames=d.allFrames:"frameIds"in d?r.frameIds=d.frameIds:"documentIds"in d&&(r.documentIds=d.documentIds),e.next=8,p(browser.tabs.query,{});case 8:return e.sent.filter(function(e){return!(is_exclude_urls(e.url)||"unloaded"==e.status)}).forEach(function(){var t=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:matchsCheck(t,u,c)&&(r.tabId=t.id,injectScript(r,i,a,s));case 1:case"end":return e.stop()}},e)}));return function(e){return t.apply(this,arguments)}}()),n=function(){var t=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var r,n=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(r=1<n.length&&void 0!==n[1]?n[1]:changeInfo,r.status,is_exclude_urls((r=2<n.length?n[2]:void 0).url)||"unloaded"==changeInfo.status)return e.abrupt("return");e.next=4;break;case 4:matchsCheck(r,u,c)&&injectScript(r,i,a,o,s);case 5:case"end":return e.stop()}},e)}));return function(e){return t.apply(this,arguments)}}(),browser.tabs.onUpdated.addListener(n),l={unregister:function(){return _asyncToGenerator(_regeneratorRuntime().mark(function e(){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",p(browser.tabs.onUpdated.removeListener.bind(browser.tabs.onUpdated),n));case 1:case"end":return e.stop()}},e)}))()}},"function"==typeof f&&f(l),e.abrupt("return",Promise.resolve(l));case 16:case"end":return e.stop()}},e)}))()}});var Sentry=function(e,t){e=window.Sentry||{};var r=Object.prototype.toString;function u(e){switch(r.call(e)){case"[object Error]":case"[object Exception]":case"[object DOMException]":return 1;default:return b(e,Error)}}function c(e,t){return r.call(e)==="[object ".concat(t,"]")}function l(e){return c(e,"ErrorEvent")}function d(e){return c(e,"DOMError")}function p(e){return c(e,"String")}function o(e){return"object"==_typeof(e)&&null!==e&&"__sentry_template_string__"in e&&"__sentry_template_values__"in e}function f(e){return null===e||o(e)||"object"!=_typeof(e)&&"function"!=typeof e}function g(e){return c(e,"Object")}function h(e){return"undefined"!=typeof Event&&b(e,Event)}function v(e){return Boolean(e&&e.then&&"function"==typeof e.then)}function b(e,t){try{return e instanceof t}catch(e){return!1}}function y(e){return"object"==_typeof(e)&&null!==e&&(e.__isVue||e.t)}function _(e,t){var r=1<arguments.length&&void 0!==t?t:0;return"string"!=typeof e||0===r||e.length<=r?e:"".concat(e.slice(0,r),"...")}function n(e,t){if(!Array.isArray(e))return"";for(var r=[],n=0;n<e.length;n++){var a=e[n];try{y(a)?r.push("[VueViewModel]"):r.push(String(a))}catch(e){r.push("[value cannot be serialized]")}}return r.join(t)}function m(t,e,r){var n=2<arguments.length&&void 0!==r&&r;return(1<arguments.length&&void 0!==e?e:[]).some(function(e){return function(e,t,r){var n=2<arguments.length&&void 0!==r&&r;return!!p(e)&&(c(t,"RegExp")?t.test(e):!!p(t)&&(n?e===t:e.includes(t)))}(t,e,n)})}function i(e,t,r,n,a,i,o){var s,u,c=2<arguments.length&&void 0!==r?r:250,l=3<arguments.length?n:void 0,d=4<arguments.length?a:void 0,p=5<arguments.length?i:void 0,f=6<arguments.length?o:void 0;p.exception&&p.exception.values&&f&&b(f.originalException,Error)&&((s=0<p.exception.values.length?p.exception.values[p.exception.values.length-1]:void 0)&&(p.exception.values=(s=function a(i,o,s,e,u,t,c,l){if(t.length>=s+1)return t;var d=_toConsumableArray(t);{var r;b(e[u],Error)&&(w(c,l),r=i(o,e[u]),t=d.length,E(r,u,t,l),d=a(i,o,s,e[u],u,[r].concat(_toConsumableArray(d)),r,t))}return Array.isArray(e.errors)&&e.errors.forEach(function(e,t){var r,n;b(e,Error)&&(w(c,l),r=i(o,e),n=d.length,E(r,"errors[".concat(t,"]"),n,l),d=a(i,o,s,e,u,[r].concat(_toConsumableArray(d)),r,n))}),d}(e,t,d,f.originalException,l,p.exception.values,s,0),u=c,s.map(function(e){return e.value&&(e.value=_(e.value,u)),e}))))}function w(e,t){e.mechanism=e.mechanism||{type:"generic",handled:!0},e.mechanism=_objectSpread(_objectSpread(_objectSpread({},e.mechanism),"AggregateError"===e.type&&{is_exception_group:!0}),{},{exception_id:t})}function E(e,t,r,n){e.mechanism=e.mechanism||{type:"generic",handled:!0},e.mechanism=_objectSpread(_objectSpread({},e.mechanism),{},{type:"chained",source:t,exception_id:r,parent_id:n})}var s="8.30.0",S=globalThis;function T(e,t,r){r=r||S,r=r.__SENTRY__=r.__SENTRY__||{},r=r[s]=r[s]||{};return r[e]||(r[e]=t())}var x=S,k=80;function R(e,t){var r=1<arguments.length&&void 0!==t?t:{};if(!e)return"<unknown>";try{for(var n,a=e,i=[],o=0,s=0,u=" > ".length,c=Array.isArray(r)?r:r.keyAttrs,l=!Array.isArray(r)&&r.maxStringLength||k;a&&o++<5&&!("html"===(n=function(e,t){var r=e,n=[];if(!r||!r.tagName)return"";if(x.HTMLElement&&r instanceof HTMLElement&&r.dataset){if(r.dataset.sentryComponent)return r.dataset.sentryComponent;if(r.dataset.sentryElement)return r.dataset.sentryElement}n.push(r.tagName.toLowerCase());t=t&&t.length?t.filter(function(e){return r.getAttribute(e)}).map(function(e){return[e,r.getAttribute(e)]}):null;if(t&&t.length)t.forEach(function(e){n.push("[".concat(e[0],'="').concat(e[1],'"]'))});else{r.id&&n.push("#".concat(r.id));t=r.className;if(t&&p(t)){var a=_createForOfIteratorHelper(t.split(/\s+/));try{for(a.s();!(i=a.n()).done;){var i=i.value;n.push(".".concat(i))}}catch(e){a.e(e)}finally{a.f()}}}for(var o=0,s=["aria-label","type","name","title","alt"];o<s.length;o++){var u=s[o],c=r.getAttribute(u);c&&n.push("[".concat(u,'="').concat(c,'"]'))}return n.join("")}(a,c))||1<o&&s+i.length*u+n.length>=l);)i.push(n),s+=n.length,a=a.parentNode;return i.reverse().join(" > ")}catch(e){return"<unknown>"}}function I(e){if(!x.HTMLElement)return null;for(var t=e,r=0;r<5;r++){if(!t)return null;if(t instanceof HTMLElement){if(t.dataset.sentryComponent)return t.dataset.sentryComponent;if(t.dataset.sentryElement)return t.dataset.sentryElement}t=t.parentNode}return null}var a=["debug","info","warn","error","log","assert","trace"],A={};function N(e){if(!("console"in S))return e();var r=S.console,n={},t=Object.keys(A);t.forEach(function(e){var t=A[e];n[e]=r[e],r[e]=t});try{return e()}finally{t.forEach(function(e){r[e]=n[e]})}}var D=T("logger",function(){var e=!1,t={enable:function(){e=!0},disable:function(){e=!1},isEnabled:function(){return e}};return a.forEach(function(e){t[e]=function(){}}),t}),O=/^(?:(\w+):)\/\/(?:(\w+)(?::(\w+)?)?@)([\w.-]+)(?::(\d+))?\/(.+)/;function C(e,t){var r=1<arguments.length&&void 0!==t&&t,n=e.host,a=e.path,i=e.pass,o=e.port,s=e.projectId,u=e.protocol,c=e.publicKey;return"".concat(u,"://").concat(c).concat(r&&i?":".concat(i):"","@").concat(n).concat(o?":".concat(o):"","/").concat(a&&"".concat(a,"/")).concat(s)}function L(e){return{protocol:e.protocol,publicKey:e.publicKey||"",pass:e.pass||"",host:e.host,port:e.port||"",path:e.path||"",projectId:e.projectId}}function P(e){e=("string"==typeof e?function(e){var t=O.exec(e);if(t){var r,n=_slicedToArray(t.slice(1),6),a=n[0],i=n[1],o=n[2],s=void 0===o?"":o,u=n[3],e=void 0===u?"":u,t=n[4],o=void 0===t?"":t,u=n[5],t="",n=void 0===u?"":u,u=n.split("/");return 1<u.length&&(t=u.slice(0,-1).join("/"),n=u.pop()),n&&(r=n.match(/^\d+/))&&(n=r[0]),L({host:e,pass:s,path:t,projectId:n,port:o,protocol:a,publicKey:i})}N(function(){})}:L)(e);if(e)return e}var M=function(){"use strict";_inherits(a,_wrapNativeSuper(Error));var n=_createSuper(a);function a(e){var t,r=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"warn";return _classCallCheck(this,a),(t=n.call(this,e)).message=e,t.name=(this instanceof a?this.constructor:void 0).prototype.constructor.name,Object.setPrototypeOf(_assertThisInitialized(t),(this instanceof a?this.constructor:void 0).prototype),t.logLevel=r,t}return _createClass(a)}();function U(e,t,r){var n;t in e&&("function"==typeof(r=r(n=e[t]))&&F(r,n),e[t]=r)}function j(e,t,r){try{Object.defineProperty(e,t,{value:r,writable:!0,configurable:!0})}catch(e){}}function F(e,t){try{var r=t.prototype||{};e.prototype=t.prototype=r,j(e,"__sentry_original__",t)}catch(e){}}function q(e){return e.__sentry_original__}function H(e){if(u(e))return _objectSpread({message:e.message,name:e.name,stack:e.stack},B(e));if(h(e)){var t=_objectSpread({type:e.type,target:G(e.target),currentTarget:G(e.currentTarget)},B(e));return"undefined"!=typeof CustomEvent&&b(e,CustomEvent)&&(t.detail=e.detail),t}return e}function G(e){try{return"undefined"!=typeof Element&&b(e,Element)?R(e):Object.prototype.toString.call(e)}catch(e){return"<unknown>"}}function B(e){if("object"!=_typeof(e)||null===e)return{};var t,r={};for(t in e)Object.prototype.hasOwnProperty.call(e,t)&&(r[t]=e[t]);return r}function V(e){return function t(e,r){if(function(e){if(g(e))try{var t=Object.getPrototypeOf(e).constructor.name;return!t||"Object"===t}catch(e){return 1}}(e)){var n=r.get(e);if(void 0!==n)return n;var a={};r.set(e,a);for(var i=0,o=Object.keys(e);i<o.length;i++){var s=o[i];void 0!==e[s]&&(a[s]=t(e[s],r))}return a}if(Array.isArray(e)){var n=r.get(e);if(void 0!==n)return n;var u=[];return r.set(e,u),e.forEach(function(e){u.push(t(e,r))}),u}return e}(e,new Map)}var X="?",z=/\(error: (.*)\)/,W=/captureMessage|captureException/;function Y(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var l=t.sort(function(e,t){return e[0]-t[0]}).map(function(e){return e[1]});return function(e){for(var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:0,r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:0,n=[],a=e.split("\n"),i=t;i<a.length;i++){var o=a[i];if(!(1024<o.length)){var s=z.test(o)?o.replace(z,"$1"):o;if(!s.match(/\S*Error: /)){var u=_createForOfIteratorHelper(l);try{for(u.s();!(c=u.n()).done;){var c=(0,c.value)(s);if(c){n.push(c);break}}}catch(e){u.e(e)}finally{u.f()}if(n.length>=50+r)break}}}return function(e){if(!e.length)return[];var t=Array.from(e);return/sentryWrapped/.test($(t).function||"")&&t.pop(),t.reverse(),W.test($(t).function||"")&&(t.pop(),W.test($(t).function||"")&&t.pop()),t.slice(0,50).map(function(e){return _objectSpread(_objectSpread({},e),{},{filename:e.filename||$(t).filename,function:e.function||X})})}(n.slice(r))}}function $(e){return e[e.length-1]||{}}var K="<anonymous>";function Q(e){try{return e&&"function"==typeof e&&e.name||K}catch(e){return K}}function J(e){var t=e.exception;if(t){var r=[];try{return t.values.forEach(function(e){e.stacktrace.frames&&r.push.apply(r,_toConsumableArray(e.stacktrace.frames))}),r}catch(e){return}}}var Z={},ee={};function te(e,t){Z[e]=Z[e]||[],Z[e].push(t)}function re(e,t){ee[e]||(t(),ee[e]=!0)}function ne(e,t){var r=e&&Z[e];if(r){var n,a=_createForOfIteratorHelper(r);try{for(a.s();!(n=a.n()).done;){var i=n.value;try{i(t)}catch(e){}}}catch(e){a.e(e)}finally{a.f()}}}function ae(){"console"in S&&a.forEach(function(a){a in S.console&&U(S.console,a,function(e){return A[a]=e,function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];ne("console",{args:t,level:a});var n=A[a];n&&n.apply(S.console,t)}})})}var ie=S;function oe(e){return e&&/^function\s+\w+\(\)\s+\{\s+\[native code\]\s+\}$/.test(e.toString())}var se=1e3;function ue(){return Date.now()/se}var ce=function(){var e=S.performance;if(!e||!e.now)return ue;var t=Date.now()-e.now(),r=null==e.timeOrigin?t:e.timeOrigin;return function(){return(r+e.now())/se}}(),le=function(){var e=S.performance;if(e&&e.now){var t=e.now(),r=Date.now(),n=e.timeOrigin?Math.abs(e.timeOrigin+t-r):36e5,a=n<36e5,i=e.timing&&e.timing.navigationStart,t="number"==typeof i?Math.abs(i+t-r):36e5;return a||t<36e5?n<=t?e.timeOrigin:i:r}}();function de(e,t){te("fetch",e),re("fetch",function(){return pe(void 0,t),0})}function pe(s,e){1<arguments.length&&void 0!==e&&e&&!function(){if("string"==typeof EdgeRuntime)return 1;if(function(){if("fetch"in ie)try{return new Headers,new Request("http://www.example.com"),new Response,1}catch(e){return}}()){if(oe(ie.fetch))return 1;var e=!1,t=ie.document;if(t&&"function"==typeof t.createElement)try{var r=t.createElement("iframe");r.hidden=!0,t.head.appendChild(r),r.contentWindow&&r.contentWindow.fetch&&(e=oe(r.contentWindow.fetch)),t.head.removeChild(r)}catch(e){}return e}}()||U(S,"fetch",function(o){return function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=function(e){if(0===e.length)return{method:"GET",url:""};if(2===e.length){var t=_slicedToArray(e,2),r=t[0],t=t[1];return{url:ge(r),method:me(t,"method")?String(t.method).toUpperCase():"GET"}}e=e[0];return{url:ge(e),method:me(e,"method")?String(e.method).toUpperCase():"GET"}}(t),a={args:t,fetchData:{method:n.method,url:n.url},startTimestamp:1e3*ce()};s||ne("fetch",_objectSpread({},a));var i=(new Error).stack;return o.apply(S,t).then(function(){var t=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(s?s(t):ne("fetch",_objectSpread(_objectSpread({},a),{},{endTimestamp:1e3*ce(),response:t})),t));case 1:case"end":return e.stop()}},e)}));return function(e){return t.apply(this,arguments)}}(),function(e){throw ne("fetch",_objectSpread(_objectSpread({},a),{},{endTimestamp:1e3*ce(),error:e})),u(e)&&void 0===e.stack&&(e.stack=i,j(e,"framesToPop",1)),e})}})}function fe(e){return he.apply(this,arguments)}function he(){return(he=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var r;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:e.prev=0,r=t.clone(),e.next=7;break;case 4:return e.prev=4,e.t0=e.catch(0),e.abrupt("return");case 7:return e.next=9,function(){var r=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,r){var a;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(t&&t.body&&t.body.getReader)return a=t.body.getReader(),e.abrupt("return",a.read().then(function(){var t=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var r;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(t.done)return e.abrupt("return",Promise.resolve());e.next=3;break;case 3:return e.prev=3,e.next=6,Promise.race([a.read(),new Promise(function(e){setTimeout(function(){e({done:!0})},5e3)})]);case 6:return r=e.sent,e.next=9,n(r);case 9:e.next=13;break;case 11:e.prev=11,e.t0=e.catch(3);case 13:case"end":return e.stop()}},e,null,[[3,11]])}));function n(e){return t.apply(this,arguments)}return n}()).then(r).catch(function(){}));e.next=3;break;case 3:case"end":return e.stop()}},e)}));return function(e,t){return r.apply(this,arguments)}}()(r,function(){ne("fetch-body-resolved",{endTimestamp:1e3*ce(),response:t})});case 9:case"end":return e.stop()}},e,null,[[0,4]])}))).apply(this,arguments)}function me(e,t){return e&&"object"==_typeof(e)&&e[t]}function ge(e){return"string"==typeof e?e:e?me(e,"url")?e.url:e.toString?e.toString():"":""}var ve=null;function be(e){te("error",e),re("error",ye)}function ye(){ve=S.onerror,S.onerror=function(e,t,r,n,a){return ne("error",{column:n,error:a,line:r,msg:e,url:t}),!(!ve||ve.__SENTRY_LOADER__)&&ve.apply(this,arguments)},S.onerror.__SENTRY_INSTRUMENTED__=!0}var _e,we=null;function Ee(e){var t="unhandledrejection";te(t,e),re(t,Te)}function Te(){we=S.onunhandledrejection,S.onunhandledrejection=function(e){return ne("unhandledrejection",e),!(we&&!we.__SENTRY_LOADER__)||we.apply(this,arguments)},S.onunhandledrejection.__SENTRY_INSTRUMENTED__=!0}function Se(){var t=S.crypto||S.msCrypto,r=function(){return 16*Math.random()};try{if(t&&t.randomUUID)return t.randomUUID().replace(/-/g,"");t&&t.getRandomValues&&(r=function(){var e=new Uint8Array(1);return t.getRandomValues(e),e[0]})}catch(e){}return([1e7]+1e3+4e3+8e3+1e11).replace(/[018]/g,function(e){return(e^(15&r())>>e/4).toString(16)})}function xe(e){return e.exception&&e.exception.values?e.exception.values[0]:void 0}function ke(e,t,r){e=e.exception=e.exception||{},e=e.values=e.values||[],e=e[0]=e[0]||{};e.value||(e.value=t||""),e.type||(e.type=r||"Error")}function Re(e,t){var r=xe(e);r&&(e=r.mechanism,r.mechanism=_objectSpread(_objectSpread({type:"generic",handled:!0},e),t),t&&"data"in t&&(t=_objectSpread(_objectSpread({},e&&e.data),t.data),r.mechanism.data=t))}function Ie(e){if(e&&e.__sentry_captured__)return 1;try{j(e,"__sentry_captured__",!0)}catch(e){}}function Ae(e){return Array.isArray(e)?e:[e]}function Ne(e,t,r){var n=1<arguments.length&&void 0!==t?t:100,a=2<arguments.length&&void 0!==r?r:1/0;try{return function e(t,r){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:1/0;var a=3<arguments.length&&void 0!==arguments[3]?arguments[3]:1/0;var i=4<arguments.length&&void 0!==arguments[4]?arguments[4]:function(){var r="function"==typeof WeakSet,n=r?new WeakSet:[];return[function(e){if(r)return!!n.has(e)||(n.add(e),!1);for(var t=0;t<n.length;t++)if(n[t]===e)return!0;return n.push(e),!1},function(e){if(r)n.delete(e);else for(var t=0;t<n.length;t++)if(n[t]===e){n.splice(t,1);break}}]}();var o=_slicedToArray(i,2),s=o[0],u=o[1];if(null==r||["number","boolean","string"].includes(_typeof(r))&&!Number.isNaN(r))return r;var o=function(e,t){try{if("domain"===e&&t&&"object"==_typeof(t)&&t.o)return"[Domain]";if("domainEmitter"===e)return"[DomainEmitter]";if("undefined"!=typeof global&&t===global)return"[Global]";if("undefined"!=typeof window&&t===window)return"[Window]";if("undefined"!=typeof document&&t===document)return"[Document]";if(y(t))return"[VueViewModel]";if(g(r=t)&&"nativeEvent"in r&&"preventDefault"in r&&"stopPropagation"in r)return"[SyntheticEvent]";if("number"==typeof t&&t!=t)return"[NaN]";if("function"==typeof t)return"[Function: ".concat(Q(t),"]");if("symbol"==_typeof(t))return"[".concat(String(t),"]");if("bigint"==typeof t)return"[BigInt: ".concat(String(t),"]");var r=function(e){var e=Object.getPrototypeOf(e);return e?e.constructor.name:"null prototype"}(t);return(/^HTML(\w*)Element$/.test(r)?"[HTMLElement: ":"[object ").concat(r,"]")}catch(e){return"**non-serializable** (".concat(e,")")}}(t,r);if(!o.startsWith("[object "))return o;if(r.__sentry_skip_normalization__)return r;var c="number"==typeof r.__sentry_override_normalization_depth__?r.__sentry_override_normalization_depth__:n;if(0===c)return o.replace("object ","");if(s(r))return"[Circular ~]";var l=r;if(l&&"function"==typeof l.toJSON)try{return e("",l.toJSON(),c-1,a,i)}catch(t){}var d=Array.isArray(r)?[]:{};var p=0;var f=H(r);for(var h in f)if(Object.prototype.hasOwnProperty.call(f,h)){if(a<=p){d[h]="[MaxProperties ~]";break}var m=f[h];d[h]=e(h,m,c-1,a,i),p++}return u(r),d}("",e,n,a)}catch(e){return{ERROR:"**non-serializable** (".concat(e,")")}}}function De(t){return new Ce(function(e){e(t)})}function Oe(r){return new Ce(function(e,t){t(r)})}(qi=_e=_e||{})[qi.PENDING=0]="PENDING",qi[qi.RESOLVED=1]="RESOLVED",qi[qi.REJECTED=2]="REJECTED";var Ce=function(){"use strict";function t(e){_classCallCheck(this,t),t.prototype.__init.call(this),t.prototype.__init2.call(this),t.prototype.__init3.call(this),t.prototype.__init4.call(this),this.i=_e.PENDING,this.u=[];try{e(this.l,this.h)}catch(e){this.h(e)}}return _createClass(t,[{key:"then",value:function(n,a){var e=this;return new t(function(t,r){e.u.push([!1,function(e){if(n)try{t(n(e))}catch(e){r(e)}else t(e)},function(e){if(a)try{t(a(e))}catch(e){r(e)}else r(e)}]),e.p()})}},{key:"catch",value:function(e){return this.then(function(e){return e},e)}},{key:"finally",value:function(a){var i=this;return new t(function(e,t){var r,n;return i.then(function(e){n=!1,r=e,a&&a()},function(e){n=!0,r=e,a&&a()}).then(function(){(n?t:e)(r)})})}},{key:"__init",value:function(){var t=this;this.l=function(e){t.m(_e.RESOLVED,e)}}},{key:"__init2",value:function(){var t=this;this.h=function(e){t.m(_e.REJECTED,e)}}},{key:"__init3",value:function(){var r=this;this.m=function(e,t){r.i===_e.PENDING&&(v(t)?t.then(r.l,r.h):(r.i=e,r.v=t,r.p()))}}},{key:"__init4",value:function(){var t=this;this.p=function(){var e;t.i!==_e.PENDING&&(e=t.u.slice(),t.u=[],e.forEach(function(e){e[0]||(t.i===_e.RESOLVED&&e[1](t.v),t.i===_e.REJECTED&&e[2](t.v),e[0]=!0)}))}}}]),t}();function Le(e){if(!e)return{};var t=e.match(/^(([^:/?#]+):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?$/);if(!t)return{};var r=t[6]||"",e=t[8]||"";return{host:t[4],path:t[5],protocol:t[2],search:r,hash:e,relative:t[5]+r+e}}var Pe=["fatal","error","warning","log","info","debug"];function Me(e){return"warn"===e?"warning":Pe.includes(e)?e:"log"}var Ue="baggage",je="sentry-",Fe=/^sentry-/,qe=8192;function He(e){e=function(e){if(e&&(p(e)||Array.isArray(e)))return Array.isArray(e)?e.reduce(function(r,e){e=Be(e);return Object.entries(e).forEach(function(e){var t=_slicedToArray(e,2),e=t[0],t=t[1];r[e]=t}),r},{}):Be(e)}(e);if(e){e=Object.entries(e).reduce(function(e,t){var r=_slicedToArray(t,2),t=r[0],r=r[1];return t.match(Fe)&&(e[t.slice(je.length)]=r),e},{});return 0<Object.keys(e).length?e:void 0}}function Ge(e){if(e)return function(e){if(0!==Object.keys(e).length)return Object.entries(e).reduce(function(e,t,r){var n=_slicedToArray(t,2),t=n[0],n=n[1],n="".concat(encodeURIComponent(t),"=").concat(encodeURIComponent(n)),n=0===r?n:"".concat(e,",").concat(n);return n.length>qe?e:n},"")}(Object.entries(e).reduce(function(e,t){var r=_slicedToArray(t,2),t=r[0],r=r[1];return r&&(e["".concat(je).concat(t)]=r),e},{}))}function Be(e){return e.split(",").map(function(e){return e.split("=").map(function(e){return decodeURIComponent(e.trim())})}).reduce(function(e,t){var r=_slicedToArray(t,2),t=r[0],r=r[1];return t&&r&&(e[t]=r),e},{})}var Ve=new RegExp("^[ \\t]*([0-9a-f]{32})?-?([0-9a-f]{16})?-?([01])?[ \\t]*$");function Xe(e,t){var r=function(e){if(e){var t,e=e.match(Ve);if(e)return"1"===e[3]?t=!0:"0"===e[3]&&(t=!1),{traceId:e[1],parentSampled:t,parentSpanId:e[2]}}}(e),n=He(t),a=r||{},e=a.traceId,t=a.parentSpanId,a=a.parentSampled;return r?{traceId:e||Se(),parentSpanId:t||Se().substring(16),spanId:Se().substring(16),sampled:a,dsc:n||{}}:{traceId:e||Se(),spanId:Se().substring(16)}}function ze(e,t,r){var n=0<arguments.length&&void 0!==e?e:Se(),a=1<arguments.length&&void 0!==t?t:Se().substring(16),i=2<arguments.length?r:void 0,i=void 0!==i?i?"-1":"-0":"";return"".concat(n,"-").concat(a).concat(i)}function We(e,t){return[e,1<arguments.length&&void 0!==t?t:[]]}function Ye(e,t){var r=_createForOfIteratorHelper(e[1]);try{for(r.s();!(n=r.n()).done;){var n=n.value;if(t(n,n[0].type))return 1}}catch(e){r.e(e)}finally{r.f()}}function $e(e){return S.__SENTRY__&&S.__SENTRY__.encodePolyfill?S.__SENTRY__.encodePolyfill(e):(new TextEncoder).encode(e)}var Ke={session:"session",sessions:"session",attachment:"attachment",transaction:"transaction",event:"error",client_report:"internal",user_report:"default",profile:"profile",profile_chunk:"profile",replay_event:"replay",replay_recording:"replay",check_in:"monitor",feedback:"feedback",span:"span",statsd:"metric_bucket"};function Qe(e){return Ke[e]}function Je(e){if(e&&e.sdk){e=e.sdk;return{name:e.name,version:e.version}}}var Ze=6e4;function et(e){for(var n,a=e[0],t=1;t<e.length;){var r=e[t],i=e[t+1];if(t+=2,("optionalAccess"===r||"optionalCall"===r)&&null==a)return;"access"===r||"optionalAccess"===r?a=i(n=a):"call"!==r&&"optionalCall"!==r||(a=i(function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return a.call.apply(a,[n].concat(t))}),n=void 0)}return a}function tt(){return{traceId:Se(),spanId:Se().substring(16)}}function rt(){}var nt=S,at=["attachTo","createForm","createWidget","remove"],it=Object.assign(function(e){return N(function(){}),_objectSpread({name:"Feedback"},at.reduce(function(e,t){return e[t]=rt,e},{}))},{_isShim:!0}),ot=["start","stop","flush"];function st(){return ut(S),S}function ut(e){e=e.__SENTRY__=e.__SENTRY__||{};return e.version=e.version||s,e[s]=e[s]||{}}function ct(e){var t=ce(),r={sid:Se(),init:!0,timestamp:t,started:t,duration:0,status:"ok",errors:0,ignoreDuration:!1,toJSON:function(){return V({sid:"".concat((e=r).sid),init:e.init,started:new Date(1e3*e.started).toISOString(),timestamp:new Date(1e3*e.timestamp).toISOString(),status:e.status,errors:e.errors,did:"number"==typeof e.did||"string"==typeof e.did?"".concat(e.did):void 0,duration:e.duration,abnormal_mechanism:e.abnormal_mechanism,attrs:{release:e.release,environment:e.environment,ip_address:e.ipAddress,user_agent:e.userAgent}});var e}};return e&&lt(r,e),r}function lt(e,t){var r,n=1<arguments.length&&void 0!==t?t:{};n.user&&(!e.ipAddress&&n.user.ip_address&&(e.ipAddress=n.user.ip_address),e.did||n.did||(e.did=n.user.id||n.user.email||n.user.username)),e.timestamp=n.timestamp||ce(),n.abnormal_mechanism&&(e.abnormal_mechanism=n.abnormal_mechanism),n.ignoreDuration&&(e.ignoreDuration=n.ignoreDuration),n.sid&&(e.sid=32===n.sid.length?n.sid:Se()),void 0!==n.init&&(e.init=n.init),!e.did&&n.did&&(e.did="".concat(n.did)),"number"==typeof n.started&&(e.started=n.started),e.ignoreDuration?e.duration=void 0:"number"==typeof n.duration?e.duration=n.duration:(r=e.timestamp-e.started,e.duration=0<=r?r:0),n.release&&(e.release=n.release),n.environment&&(e.environment=n.environment),!e.ipAddress&&n.ipAddress&&(e.ipAddress=n.ipAddress),!e.userAgent&&n.userAgent&&(e.userAgent=n.userAgent),"number"==typeof n.errors&&(e.errors=n.errors),n.status&&(e.status=n.status)}var dt="_sentrySpan";function pt(e,t){t?j(e,dt,t):delete e[dt]}function ft(e){return e[dt]}var ht=function(){"use strict";function t(){_classCallCheck(this,t),this._=!1,this.S=[],this.T=[],this.k=[],this.I=[],this.j={},this.C={},this.O={},this.M={},this.R={},this.A=tt()}return _createClass(t,[{key:"clone",value:function(){var e=new t;return e.k=_toConsumableArray(this.k),e.C=_objectSpread({},this.C),e.O=_objectSpread({},this.O),e.M=_objectSpread({},this.M),e.j=this.j,e.L=this.L,e.P=this.P,e.N=this.N,e.D=this.D,e.T=_toConsumableArray(this.T),e.F=this.F,e.I=_toConsumableArray(this.I),e.R=_objectSpread({},this.R),e.A=_objectSpread({},this.A),e.q=this.q,e.U=this.U,pt(e,ft(this)),e}},{key:"setClient",value:function(e){this.q=e}},{key:"setLastEventId",value:function(e){this.U=e}},{key:"getClient",value:function(){return this.q}},{key:"lastEventId",value:function(){return this.U}},{key:"addScopeListener",value:function(e){this.S.push(e)}},{key:"addEventProcessor",value:function(e){return this.T.push(e),this}},{key:"setUser",value:function(e){return this.j=e||{email:void 0,id:void 0,ip_address:void 0,username:void 0},this.P&&lt(this.P,{user:e}),this.H(),this}},{key:"getUser",value:function(){return this.j}},{key:"getRequestSession",value:function(){return this.F}},{key:"setRequestSession",value:function(e){return this.F=e,this}},{key:"setTags",value:function(e){return this.C=_objectSpread(_objectSpread({},this.C),e),this.H(),this}},{key:"setTag",value:function(e,t){return this.C=_objectSpread(_objectSpread({},this.C),{},_defineProperty({},e,t)),this.H(),this}},{key:"setExtras",value:function(e){return this.O=_objectSpread(_objectSpread({},this.O),e),this.H(),this}},{key:"setExtra",value:function(e,t){return this.O=_objectSpread(_objectSpread({},this.O),{},_defineProperty({},e,t)),this.H(),this}},{key:"setFingerprint",value:function(e){return this.D=e,this.H(),this}},{key:"setLevel",value:function(e){return this.L=e,this.H(),this}},{key:"setTransactionName",value:function(e){return this.N=e,this.H(),this}},{key:"setContext",value:function(e,t){return null===t?delete this.M[e]:this.M[e]=t,this.H(),this}},{key:"setSession",value:function(e){return e?this.P=e:delete this.P,this.H(),this}},{key:"getSession",value:function(){return this.P}},{key:"update",value:function(e){if(!e)return this;var t="function"==typeof e?e(this):e,r=_slicedToArray(t instanceof ht?[t.getScopeData(),t.getRequestSession()]:g(t)?[e,e.requestSession]:[],2),n=r[0],a=r[1],i=n||{},o=i.tags,s=i.extra,t=i.user,e=i.contexts,r=i.level,n=i.fingerprint,n=void 0===n?[]:n,i=i.propagationContext;return this.C=_objectSpread(_objectSpread({},this.C),o),this.O=_objectSpread(_objectSpread({},this.O),s),this.M=_objectSpread(_objectSpread({},this.M),e),t&&Object.keys(t).length&&(this.j=t),r&&(this.L=r),n.length&&(this.D=n),i&&(this.A=i),a&&(this.F=a),this}},{key:"clear",value:function(){return this.k=[],this.C={},this.O={},this.j={},this.M={},this.L=void 0,this.N=void 0,this.D=void 0,this.F=void 0,this.P=void 0,pt(this,void 0),this.I=[],this.A=tt(),this.H(),this}},{key:"addBreadcrumb",value:function(e,t){var r="number"==typeof t?t:100;if(r<=0)return this;t=_objectSpread({timestamp:ue()},e),e=this.k;return e.push(t),this.k=e.length>r?e.slice(-r):e,this.H(),this}},{key:"getLastBreadcrumb",value:function(){return this.k[this.k.length-1]}},{key:"clearBreadcrumbs",value:function(){return this.k=[],this.H(),this}},{key:"addAttachment",value:function(e){return this.I.push(e),this}},{key:"clearAttachments",value:function(){return this.I=[],this}},{key:"getScopeData",value:function(){return{breadcrumbs:this.k,attachments:this.I,contexts:this.M,tags:this.C,extra:this.O,user:this.j,level:this.L,fingerprint:this.D||[],eventProcessors:this.T,propagationContext:this.A,sdkProcessingMetadata:this.R,transactionName:this.N,span:ft(this)}}},{key:"setSDKProcessingMetadata",value:function(e){return this.R=_objectSpread(_objectSpread({},this.R),e),this}},{key:"setPropagationContext",value:function(e){return this.A=e,this}},{key:"getPropagationContext",value:function(){return this.A}},{key:"captureException",value:function(e,t){var r=t&&t.event_id?t.event_id:Se();if(!this.q)return D.warn("No client configured on scope - will not capture exception!"),r;var n=new Error("Sentry syntheticException");return this.q.captureException(e,_objectSpread(_objectSpread({originalException:e,syntheticException:n},t),{},{event_id:r}),this),r}},{key:"captureMessage",value:function(e,t,r){var n=r&&r.event_id?r.event_id:Se();if(!this.q)return D.warn("No client configured on scope - will not capture message!"),n;var a=new Error(e);return this.q.captureMessage(e,t,_objectSpread(_objectSpread({originalException:e,syntheticException:a},r),{},{event_id:n}),this),n}},{key:"captureEvent",value:function(e,t){var r=t&&t.event_id?t.event_id:Se();return this.q?this.q.captureEvent(e,_objectSpread(_objectSpread({},t),{},{event_id:r}),this):D.warn("No client configured on scope - will not capture event!"),r}},{key:"H",value:function(){var t=this;this._||(this._=!0,this.S.forEach(function(e){e(t)}),this._=!1)}}]),t}(),mt=function(){"use strict";function r(e,t){_classCallCheck(this,r),e=e||new ht,t=t||new ht,this.B=[{scope:e}],this.W=t}return _createClass(r,[{key:"withScope",value:function(e){var t,r=this,n=this.X();try{t=e(n)}catch(e){throw this.G(),e}return v(t)?t.then(function(e){return r.G(),e},function(e){throw r.G(),e}):(this.G(),t)}},{key:"getClient",value:function(){return this.getStackTop().client}},{key:"getScope",value:function(){return this.getStackTop().scope}},{key:"getIsolationScope",value:function(){return this.W}},{key:"getStackTop",value:function(){return this.B[this.B.length-1]}},{key:"X",value:function(){var e=this.getScope().clone();return this.B.push({client:this.getClient(),scope:e}),e}},{key:"G",value:function(){return!(this.B.length<=1||!this.B.pop())}}]),r}();function gt(){var e=ut(st());return e.stack=e.stack||new mt(T("defaultCurrentScope",function(){return new ht}),T("defaultIsolationScope",function(){return new ht}))}function vt(e){return gt().withScope(e)}function bt(e,t){var r=gt();return r.withScope(function(){return r.getStackTop().scope=e,t(e)})}function yt(e){return gt().withScope(function(){return e(gt().getIsolationScope())})}function _t(e){e=ut(e);return e.acs||{withIsolationScope:yt,withScope:vt,withSetScope:bt,withSetIsolationScope:function(e,t){return yt(t)},getCurrentScope:function(){return gt().getScope()},getIsolationScope:function(){return gt().getIsolationScope()}}}function wt(){return _t(st()).getCurrentScope()}function Et(){return _t(st()).getIsolationScope()}function Tt(){return T("globalScope",function(){return new ht})}function St(){for(var e=_t(st()),t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];if(2!==r.length)return e.withScope(r[0]);var a=r[0],i=r[1];return a?e.withSetScope(a,i):e.withScope(i)}function xt(){return wt().getClient()}var kt="_sentryMetrics";function Rt(e){e=e[kt];if(e){var t={},r=_createForOfIteratorHelper(e);try{for(r.s();!(a=r.n()).done;){var n=_slicedToArray(a.value,2),a=_slicedToArray(n[1],2),n=a[0],a=a[1];(t[n]||(t[n]=[])).push(V(a))}}catch(e){r.e(e)}finally{r.f()}return t}}var It="sentry.source",At="sentry.sample_rate",Nt="sentry.op",Dt="sentry.origin",Ot="sentry.idle_span_finish_reason",Ct="sentry.measurement_unit",Lt="sentry.measurement_value",Pt="sentry.exclusive_time",Mt=0,Ut=1,jt=2;function Ft(e,t){e.setAttribute("http.response.status_code",t);t=function(e){if(e<400&&100<=e)return{code:Ut};if(400<=e&&e<500)switch(e){case 401:return{code:jt,message:"unauthenticated"};case 403:return{code:jt,message:"permission_denied"};case 404:return{code:jt,message:"not_found"};case 409:return{code:jt,message:"already_exists"};case 413:return{code:jt,message:"failed_precondition"};case 429:return{code:jt,message:"resource_exhausted"};case 499:return{code:jt,message:"cancelled"};default:return{code:jt,message:"invalid_argument"}}if(500<=e&&e<600)switch(e){case 501:return{code:jt,message:"unimplemented"};case 503:return{code:jt,message:"unavailable"};case 504:return{code:jt,message:"deadline_exceeded"};default:return{code:jt,message:"internal_error"}}return{code:jt,message:"unknown_error"}}(t);"unknown_error"!==t.message&&e.setStatus(t)}var qt=1;function Ht(e){var t=e.spanContext();return ze(t.traceId,t.spanId,Xt(e))}function Gt(e){return"number"==typeof e?Bt(e):Array.isArray(e)?e[0]+e[1]/1e9:e instanceof Date?Bt(e.getTime()):ce()}function Bt(e){return 9999999999<e?e/1e3:e}function Vt(e){if("function"==typeof e.getSpanJSON)return e.getSpanJSON();try{var t=e.spanContext(),r=t.spanId,n=t.traceId;if(e.attributes&&e.startTime&&e.name&&e.endTime&&e.status){var a=e.attributes,i=e.startTime,o=e.name,s=e.endTime,u=e.parentSpanId,t=e.status;return V({span_id:r,trace_id:n,data:a,description:o,parent_span_id:u,start_timestamp:Gt(i),timestamp:Gt(s)||void 0,status:zt(t),op:a[Nt],origin:a[Dt],_metrics_summary:Rt(e)})}return{span_id:r,trace_id:n}}catch(e){return{}}}function Xt(e){return e.spanContext().traceFlags===qt}function zt(e){if(e&&e.code!==Mt)return e.code===Ut?"ok":e.message||"unknown_error"}var Wt="_sentryChildSpans",Yt="_sentryRootSpan";function $t(e,t){var r=e[Yt]||e;j(t,Yt,r),e[Wt]?e[Wt].add(t):j(e,Wt,new Set([t]))}function Kt(e){var a=new Set;return function e(t){if(!a.has(t)&&Xt(t)){a.add(t);var r,n=_createForOfIteratorHelper(t[Wt]?Array.from(t[Wt]):[]);try{for(n.s();!(r=n.n()).done;)e(r.value)}catch(e){n.e(e)}finally{n.f()}}}(e),Array.from(a)}function Qt(e){return e[Yt]||e}function Jt(){var e=_t(st());return e.getActiveSpan?e.getActiveSpan():ft(wt())}var Zt=!1;function er(){Zt||(Zt=!0,be(tr),Ee(tr))}function tr(){var e=Jt(),e=e&&Qt(e);e&&e.setStatus({code:jt,message:"internal_error"})}tr.tag="sentry_tracingErrorCallback";var rr="_sentryScope",nr="_sentryIsolationScope";function ar(e){return{scope:e[rr],isolationScope:e[nr]}}function ir(e){if("boolean"==typeof __SENTRY_TRACING__&&!__SENTRY_TRACING__)return!1;var t=xt(),t=e||t&&t.getOptions();return!!t&&(t.enableTracing||"tracesSampleRate"in t||"tracesSampler"in t)}var or=function(){"use strict";function t(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};_classCallCheck(this,t),this.J=e.traceId||Se(),this.Y=e.spanId||Se().substring(16)}return _createClass(t,[{key:"spanContext",value:function(){return{spanId:this.Y,traceId:this.J,traceFlags:0}}},{key:"end",value:function(e){}},{key:"setAttribute",value:function(e,t){return this}},{key:"setAttributes",value:function(e){return this}},{key:"setStatus",value:function(e){return this}},{key:"updateName",value:function(e){return this}},{key:"isRecording",value:function(){return!1}},{key:"addEvent",value:function(e,t,r){return this}},{key:"addLink",value:function(e){return this}},{key:"addLinks",value:function(e){return this}},{key:"recordException",value:function(e,t){}}]),t}();function sr(e,t,r){var n,a,i,o=2<arguments.length&&void 0!==r?r:function(){};try{n=e()}catch(e){throw t(e),o(),e}return a=t,i=o,v(o=n)?o.then(function(e){return i(),e},function(e){throw a(e),i(),e}):(i(),o)}var ur="production",cr="_frozenDsc";function lr(e,t){j(e,cr,t)}function dr(e,t){var r=t.getOptions(),n=(t.getDsn()||{}).publicKey,e=V({environment:r.environment||ur,release:r.release,public_key:n,trace_id:e});return t.emit("createDsc",e),e}function pr(e){var t=xt();if(!t)return{};var r=dr(Vt(e).trace_id||"",t),n=Qt(e),a=n[cr];if(a)return a;var i=n.spanContext().traceState,e=i&&i.get("sentry.dsc"),a=e&&He(e);if(a)return a;i=Vt(n),e=i.data||{},a=e[At];null!=a&&(r.sample_rate="".concat(a));e=e[It],i=i.description;return"url"!==e&&i&&(r.transaction=i),r.sampled=String(Xt(n)),t.emit("createDsc",r,n),r}function fr(e){if("boolean"==typeof e)return Number(e);e="string"==typeof e?parseFloat(e):e;return"number"!=typeof e||isNaN(e)||e<0||1<e?void 0:e}function hr(e,t,r){var n=3<arguments.length&&void 0!==arguments[3]?arguments[3]:Jt(),n=n&&Qt(n);n&&n.addEvent(e,(_defineProperty(n={},Lt,t),_defineProperty(n,Ct,r),n))}function mr(e){if(e&&0!==e.length){var n={};return e.forEach(function(e){var t=e.attributes||{},r=t[Ct],t=t[Lt];"string"==typeof r&&"number"==typeof t&&(n[e.name]={value:t,unit:r})}),n}}var gr=function(){"use strict";function d(){var e,t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};_classCallCheck(this,d),this.J=t.traceId||Se(),this.Y=t.spanId||Se().substring(16),this.K=t.startTimestamp||ce(),this.V={},this.setAttributes(_objectSpread((_defineProperty(e={},Dt,"manual"),_defineProperty(e,Nt,t.op),e),t.attributes)),this.Z=t.name,t.parentSpanId&&(this.tt=t.parentSpanId),"sampled"in t&&(this.nt=t.sampled),t.endTimestamp&&(this.et=t.endTimestamp),this.o=[],this.rt=t.isStandalone,this.et&&this.ot()}return _createClass(d,[{key:"addLink",value:function(e){return this}},{key:"addLinks",value:function(e){return this}},{key:"recordException",value:function(e,t){}},{key:"spanContext",value:function(){return{spanId:this.Y,traceId:this.J,traceFlags:this.nt?qt:0}}},{key:"setAttribute",value:function(e,t){return void 0===t?delete this.V[e]:this.V[e]=t,this}},{key:"setAttributes",value:function(t){var r=this;return Object.keys(t).forEach(function(e){return r.setAttribute(e,t[e])}),this}},{key:"updateStartTime",value:function(e){this.K=Gt(e)}},{key:"setStatus",value:function(e){return this.it=e,this}},{key:"updateName",value:function(e){return this.Z=e,this}},{key:"end",value:function(e){this.et||(this.et=Gt(e),this.ot())}},{key:"getSpanJSON",value:function(){return V({data:this.V,description:this.Z,op:this.V[Nt],parent_span_id:this.tt,span_id:this.Y,start_timestamp:this.K,status:zt(this.it),timestamp:this.et,trace_id:this.J,origin:this.V[Dt],_metrics_summary:Rt(this),profile_id:this.V["sentry.profile_id"],exclusive_time:this.V[Pt],measurements:mr(this.o),is_segment:this.rt&&Qt(this)===this||void 0,segment_id:this.rt?Qt(this).spanContext().spanId:void 0})}},{key:"isRecording",value:function(){return!this.et&&!!this.nt}},{key:"addEvent",value:function(e,t,r){r=vr(t)?t:r||ce(),t=!vr(t)&&t||{},t={name:e,time:Gt(r),attributes:t};return this.o.push(t),this}},{key:"isStandaloneSpan",value:function(){return!!this.rt}},{key:"ot",value:function(){var e,t,r,n=xt();n&&n.emit("spanEnd",this),!this.rt&&this!==Qt(this)||(this.rt?this.nt?(e=function(e,t){var r=pr(e[0]),n=t&&t.getDsn(),a=t&&t.getOptions().tunnel,n=_objectSpread(_objectSpread({sent_at:(new Date).toISOString()},!!r.trace_id&&!!r.public_key&&{trace:r}),!!a&&n&&{dsn:C(n)}),i=t&&t.getOptions().beforeSendSpan,o=i?function(e){return i(Vt(e))}:Vt,s=[],u=_createForOfIteratorHelper(e);try{for(u.s();!(c=u.n()).done;){var c=o(c.value);c&&s.push([{type:"span"},c])}}catch(e){u.e(e)}finally{u.f()}return We(n,s)}([this],n),(r=xt())&&((t=e[1])&&0!==t.length?(t=r.getTransport())&&t.send(e).then(null,function(e){}):r.recordDroppedEvent("before_send","span"))):n&&n.recordDroppedEvent("sample_rate","span"):(n=this.st())&&(ar(this).scope||wt()).captureEvent(n))}},{key:"st",value:function(){var t=this;if(br(Vt(this))){this.Z||(this.Z="<unlabeled transaction>");var e=ar(this),r=e.scope,n=e.isolationScope,a=(r||wt()).getClient()||xt();if(!0===this.nt){var i,o,s,u,c=Kt(this).filter(function(e){return e!==t&&!(e instanceof d&&e.isStandaloneSpan())}).map(Vt).filter(br),l=this.V[It],n=_objectSpread({contexts:{trace:(o=(u=(i=this).spanContext()).spanId,s=u.traceId,e=Vt(i),u=e.data,i=e.op,V({parent_span_id:e.parent_span_id,span_id:o,trace_id:s,data:u,op:i,status:e.status,origin:e.origin}))},spans:1e3<c.length?c.sort(function(e,t){return e.start_timestamp-t.start_timestamp}).slice(0,1e3):c,start_timestamp:this.K,timestamp:this.et,transaction:this.Z,type:"transaction",sdkProcessingMetadata:_objectSpread({capturedSpanScope:r,capturedSpanIsolationScope:n},V({dynamicSamplingContext:pr(this)})),_metrics_summary:Rt(this)},l&&{transaction_info:{source:l}}),l=mr(this.o);return l&&Object.keys(l).length&&(n.measurements=l),n}a&&a.recordDroppedEvent("sample_rate","transaction")}}}]),d}();function vr(e){return e&&"number"==typeof e||e instanceof Date||Array.isArray(e)}function br(e){return!!(e.start_timestamp&&e.timestamp&&e.span_id&&e.trace_id)}var yr="__SENTRY_SUPPRESS_TRACING__";function _r(a,i){var e=xr();if(e.startSpanManual)return e.startSpanManual(a,i);var o=Sr(a),s=a.forceTransaction,t=a.parentSpan;return St(a.scope,function(){return Ir(t)(function(){var e=wt(),t=Rr(e),r=a.onlyIfParent&&!t?new or:Tr({parentSpan:t,spanArguments:o,forceTransaction:s,scope:e});function n(){r.end()}return pt(e,r),sr(function(){return i(r,n)},function(){var e=Vt(r).status;!r.isRecording()||e&&"ok"!==e||r.setStatus({code:jt,message:"internal_error"})})})})}function wr(r){var e=xr();if(e.startInactiveSpan)return e.startInactiveSpan(r);var n=Sr(r),a=r.forceTransaction,t=r.parentSpan;return(r.scope?function(e){return St(r.scope,e)}:void 0!==t?function(e){return Er(t,e)}:function(e){return e()})(function(){var e=wt(),t=Rr(e);return r.onlyIfParent&&!t?new or:Tr({parentSpan:t,spanArguments:n,forceTransaction:a,scope:e})})}function Er(t,r){var e=xr();return e.withActiveSpan?e.withActiveSpan(t,r):St(function(e){return pt(e,t||void 0),r(e)})}function Tr(e){var t=e.parentSpan,r=e.spanArguments,n=e.forceTransaction,a=e.scope;if(!ir())return new or;var i,o,s,u,c,e=Et();return t&&!n?(i=function(e,t,r){var n=e.spanContext(),a=n.spanId,n=n.traceId,t=!t.getScopeData().sdkProcessingMetadata[yr]&&Xt(e),n=t?new gr(_objectSpread(_objectSpread({},r),{},{parentSpanId:a,traceId:n,sampled:t})):new or({traceId:n});$t(e,n);e=xt();return e&&(e.emit("spanStart",n),r.endTimestamp&&e.emit("spanEnd",n)),n}(t,a,r),$t(t,i)):t?(s=pr(t),n=(o=t.spanContext()).traceId,u=o.spanId,o=Xt(t),lr(i=kr(_objectSpread({traceId:n,parentSpanId:u},r),a,o),s)):(o=(u=_objectSpread(_objectSpread({},e.getPropagationContext()),a.getPropagationContext())).traceId,c=u.dsc,s=u.parentSpanId,u=u.sampled,i=kr(_objectSpread({traceId:o,parentSpanId:s},r),a,u),c&&lr(i,c)),c=a,(a=i)&&(j(a,nr,e),j(a,rr,c)),i}function Sr(e){var t=_objectSpread({isStandalone:(e.experimental||{}).standalone},e);if(e.startTime){var r=_objectSpread({},t);return r.startTimestamp=Gt(e.startTime),delete r.startTime,r}return t}function xr(){return _t(st())}function kr(e,t,r){var n=xt(),a=n&&n.getOptions()||{},i=e.name,o=void 0===i?"":i,i=e.attributes,o=_slicedToArray(t.getScopeData().sdkProcessingMetadata[yr]?[!1]:function(e,t){if(!ir(e))return[!1];e=fr("function"==typeof e.tracesSampler?e.tracesSampler(t):void 0!==t.parentSampled?t.parentSampled:void 0!==e.tracesSampleRate?e.tracesSampleRate:1);return void 0===e?[!1]:e&&Math.random()<e?[!0,e]:[!1,e]}(a,{name:o,parentSampled:r,attributes:i,transactionContext:{name:o,parentSampled:r}}),2),r=o[0],o=o[1],r=new gr(_objectSpread(_objectSpread({},e),{},{attributes:_objectSpread(_defineProperty({},It,"custom"),e.attributes),sampled:r}));return void 0!==o&&r.setAttribute(At,o),n&&n.emit("spanStart",r),r}function Rr(e){var t=ft(e);if(t){e=xt();return(e?e.getOptions():{}).parentSpanIsAlwaysRootSpan?Qt(t):t}}function Ir(t){return void 0!==t?function(e){return Er(t,e)}:function(e){return e()}}var Ar={idleTimeout:1e3,finalTimeout:3e4,childSpanTimeout:15e3},Nr="heartbeatFailed",Dr="idleTimeout",Or="finalTimeout",Cr="externalFinish";function Lr(e,t){var r,n=1<arguments.length&&void 0!==t?t:{},i=new Map,o=!1,s=Cr,a=!n.disableAutoFinish,u=[],c=n.idleTimeout,l=void 0===c?Ar.idleTimeout:c,d=n.finalTimeout,p=void 0===d?Ar.finalTimeout:d,c=n.childSpanTimeout,f=void 0===c?Ar.childSpanTimeout:c,h=n.beforeSpanEnd,d=xt();if(!d||!ir())return new or;var m=wt(),g=Jt(),v=(c=wr(e),pt(wt(),c),c);function b(){r&&(clearTimeout(r),r=void 0)}function y(e){b(),r=setTimeout(function(){!o&&0===i.size&&a&&(s=Dr,v.end(e))},l)}function _(e){r=setTimeout(function(){!o&&a&&(s=Nr,v.end(e))},f)}function w(n){o=!0,i.clear(),u.forEach(function(e){return e()}),pt(m,g);var a,e=Vt(v);e.start_timestamp&&((e.data||{})[Ot]||v.setAttribute(Ot,s),D.log('[Tracing] Idle span "'.concat(e.op,'" finished')),e=Kt(v).filter(function(e){return e!==v}),a=0,e.forEach(function(e){e.isRecording()&&(e.setStatus({code:jt,message:"cancelled"}),e.end(n));var t=Vt(e),r=t.timestamp,r=void 0===r?0:r,t=t.start_timestamp,t=void 0===t?0:t;r-t<=(p+l)/1e3&&t<=n||(v[Wt]&&v[Wt].delete(e),a++)}),0<a&&v.setAttribute("sentry.idle_span_discarded_spans",a))}return v.end=new Proxy(v.end,{apply:function(e,t,r){h&&h(v);var n=_toArray(r),a=n[0],i=n.slice(1),r=Gt(a||ce()),n=Kt(v).filter(function(e){return e!==v});if(!n.length)return w(r),Reflect.apply(e,t,[r].concat(_toConsumableArray(i)));a=n.map(function(e){return Vt(e).timestamp}).filter(function(e){return!!e}),n=a.length?Math.max.apply(Math,_toConsumableArray(a)):void 0,a=Vt(v).start_timestamp,n=Math.min(a?a+p/1e3:1/0,Math.max(a||-1/0,Math.min(r,n||1/0)));return w(n),Reflect.apply(e,t,[n].concat(_toConsumableArray(i)))}}),u.push(d.on("spanStart",function(e){o||e===v||Vt(e).timestamp||Kt(v).includes(e)&&(e=e.spanContext().spanId,b(),i.set(e,!0),_(ce()+f/1e3))})),u.push(d.on("spanEnd",function(e){o||(e=e.spanContext().spanId,i.has(e)&&i.delete(e),0===i.size&&y(ce()+l/1e3))})),u.push(d.on("idleSpanEnableAutoFinish",function(e){e===v&&(a=!0,y(),i.size&&_())})),n.disableAutoFinish||y(),setTimeout(function(){o||(v.setStatus({code:jt,message:"deadline_exceeded"}),s=Or,v.end())},p),v}function Pr(e,t){var r,n,a,i,o,s,u,c=t.fingerprint,l=t.span,d=t.breadcrumbs,p=t.sdkProcessingMetadata;r=e,n=t.extra,a=t.tags,i=t.user,o=t.contexts,s=t.level,t=t.transactionName,(n=V(n))&&Object.keys(n).length&&(r.extra=_objectSpread(_objectSpread({},n),r.extra)),(a=V(a))&&Object.keys(a).length&&(r.tags=_objectSpread(_objectSpread({},a),r.tags)),(i=V(i))&&Object.keys(i).length&&(r.user=_objectSpread(_objectSpread({},i),r.user)),(o=V(o))&&Object.keys(o).length&&(r.contexts=_objectSpread(_objectSpread({},o),r.contexts)),s&&(r.level=s),t&&"transaction"!==r.type&&(r.transaction=t),l&&(u=l,(s=e).contexts=_objectSpread({trace:(l=(t=(r=u).spanContext()).spanId,t=t.traceId,V({parent_span_id:Vt(r).parent_span_id,span_id:l,trace_id:t}))},s.contexts),s.sdkProcessingMetadata=_objectSpread({dynamicSamplingContext:pr(u)},s.sdkProcessingMetadata),(u=Vt(Qt(u)).description)&&!s.transaction&&"transaction"===s.type&&(s.transaction=u)),u=c,(c=e).fingerprint=c.fingerprint?Ae(c.fingerprint):[],u&&(c.fingerprint=c.fingerprint.concat(u)),c.fingerprint&&!c.fingerprint.length&&delete c.fingerprint,c=d,c=[].concat(_toConsumableArray((d=e).breadcrumbs||[]),_toConsumableArray(c)),d.breadcrumbs=c.length?c:void 0,p=p,e.sdkProcessingMetadata=_objectSpread(_objectSpread({},e.sdkProcessingMetadata),p)}function Mr(e,t){var r=t.extra,n=t.tags,a=t.user,i=t.contexts,o=t.level,s=t.sdkProcessingMetadata,u=t.breadcrumbs,c=t.fingerprint,l=t.eventProcessors,d=t.attachments,p=t.propagationContext,f=t.transactionName,t=t.span;Ur(e,"extra",r),Ur(e,"tags",n),Ur(e,"user",a),Ur(e,"contexts",i),Ur(e,"sdkProcessingMetadata",s),o&&(e.level=o),f&&(e.transactionName=f),t&&(e.span=t),u.length&&(e.breadcrumbs=[].concat(_toConsumableArray(e.breadcrumbs),_toConsumableArray(u))),c.length&&(e.fingerprint=[].concat(_toConsumableArray(e.fingerprint),_toConsumableArray(c))),l.length&&(e.eventProcessors=[].concat(_toConsumableArray(e.eventProcessors),_toConsumableArray(l))),d.length&&(e.attachments=[].concat(_toConsumableArray(e.attachments),_toConsumableArray(d))),e.propagationContext=_objectSpread(_objectSpread({},e.propagationContext),p)}function Ur(e,t,r){if(r&&Object.keys(r).length)for(var n in e[t]=_objectSpread({},e[t]),r)Object.prototype.hasOwnProperty.call(r,n)&&(e[t][n]=r[n])}function jr(e,t,r,n,a,i){var o,s,u,c,l=e.normalizeDepth,d=void 0===l?3:l,p=e.normalizeMaxBreadth,f=void 0===p?1e3:p,h=_objectSpread(_objectSpread({},t),{},{event_id:t.event_id||r.event_id||Se(),timestamp:t.timestamp||ue()}),m=r.integrations||e.integrations.map(function(e){return e.name});o=h,u=(s=e).environment,c=s.release,l=s.dist,p=void 0===(p=s.maxValueLength)?250:p,"environment"in o||(o.environment="environment"in s?u:ur),void 0===o.release&&void 0!==c&&(o.release=c),void 0===o.dist&&void 0!==l&&(o.dist=l),o.message&&(o.message=_(o.message,p)),(l=o.exception&&o.exception.values&&o.exception.values[0])&&l.value&&(l.value=_(l.value,p)),(o=o.request)&&o.url&&(o.url=_(o.url,p)),p=h,0<(m=m).length&&(p.sdk=p.sdk||{},p.sdk.integrations=[].concat(_toConsumableArray(p.sdk.integrations||[]),_toConsumableArray(m))),a&&a.emit("applyFrameMetadata",t),void 0===t.type&&function(e,s){var t=S._sentryDebugIds;if(t){var u,r=Fr.get(s);r?u=r:(u=new Map,Fr.set(s,u));var n=Object.entries(t).reduce(function(e,t){var r,n=_slicedToArray(t,2),t=n[0],a=n[1],n=u.get(t);n?r=n:(r=s(t),u.set(t,r));for(var i=r.length-1;0<=i;i--){var o=r[i];if(o.filename){e[o.filename]=a;break}}return e},{});try{e.exception.values.forEach(function(e){e.stacktrace.frames.forEach(function(e){e.filename&&(e.debug_id=n[e.filename])})})}catch(e){}}}(h,e.stackParser);t=function(e){if(!e)return n;var t=n?n.clone():new ht;return t.update(e),t}(r.captureContext);r.mechanism&&Re(h,r.mechanism);e=a?a.getEventProcessors():[],a=Tt().getScopeData();i&&Mr(a,i.getScopeData()),t&&Mr(a,t.getScopeData());t=[].concat(_toConsumableArray(r.attachments||[]),_toConsumableArray(a.attachments));return t.length&&(r.attachments=t),Pr(h,a),function n(a,i,o,e){var s=3<arguments.length&&void 0!==e?e:0;return new Ce(function(t,e){var r=a[s];null===i||"function"!=typeof r?t(i):(v(r=r(_objectSpread({},i),o))?r.then(function(e){return n(a,e,o,s+1).then(t)}):n(a,r,o,s+1).then(t)).then(null,e)})}([].concat(_toConsumableArray(e),_toConsumableArray(a.eventProcessors)),h,r).then(function(e){return e&&function(e){var r,t={};try{e.exception.values.forEach(function(e){e.stacktrace.frames.forEach(function(e){e.debug_id&&(e.abs_path?t[e.abs_path]=e.debug_id:e.filename&&(t[e.filename]=e.debug_id),delete e.debug_id)})})}catch(e){}0!==Object.keys(t).length&&(e.debug_meta=e.debug_meta||{},e.debug_meta.images=e.debug_meta.images||[],r=e.debug_meta.images,Object.entries(t).forEach(function(e){var t=_slicedToArray(e,2),e=t[0],t=t[1];r.push({type:"sourcemap",code_file:e,debug_id:t})}))}(e),"number"==typeof d&&0<d?function(e,t,r){if(!e)return null;var n=_objectSpread(_objectSpread(_objectSpread(_objectSpread(_objectSpread({},e),e.breadcrumbs&&{breadcrumbs:e.breadcrumbs.map(function(e){return _objectSpread(_objectSpread({},e),e.data&&{data:Ne(e.data,t,r)})})}),e.user&&{user:Ne(e.user,t,r)}),e.contexts&&{contexts:Ne(e.contexts,t,r)}),e.extra&&{extra:Ne(e.extra,t,r)});return e.contexts&&e.contexts.trace&&n.contexts&&(n.contexts.trace=e.contexts.trace,e.contexts.trace.data&&(n.contexts.trace.data=Ne(e.contexts.trace.data,t,r))),e.spans&&(n.spans=e.spans.map(function(e){return _objectSpread(_objectSpread({},e),e.data&&{data:Ne(e.data,t,r)})})),n}(e,d,f):e})}var Fr=new WeakMap;var qr=["user","level","extra","contexts","tags","fingerprint","requestSession","propagationContext"];function Hr(e,t){return wt().captureException(e,function(e){if(e)return e instanceof ht||"function"==typeof e||Object.keys(e).some(function(e){return qr.includes(e)})?{captureContext:e}:e}(t))}function Gr(e,t){return wt().captureEvent(e,t)}function Br(e,t){Et().setContext(e,t)}function Vr(e){Et().setExtras(e)}function Xr(e,t){Et().setExtra(e,t)}function zr(e){Et().setTags(e)}function Wr(e,t){Et().setTag(e,t)}function Yr(e){Et().setUser(e)}function $r(){return Et().lastEventId()}function Kr(e){var t=xt(),r=Et(),n=wt(),a=t&&t.getOptions()||{},i=a.release,t=a.environment,a=void 0===t?ur:t,t=(S.navigator||{}).userAgent,t=ct(_objectSpread(_objectSpread({release:i,environment:a,user:n.getUser()||r.getUser()},t&&{userAgent:t}),e)),e=r.getSession();return e&&"ok"===e.status&&lt(e,{status:"exited"}),Qr(),r.setSession(t),n.setSession(t),t}function Qr(){var e,t=Et(),r=wt(),n=r.getSession()||t.getSession();n&&(e={},"ok"===n.status&&(e={status:"exited"}),lt(n,e)),Jr(),t.setSession(),r.setSession()}function Jr(){var e=Et(),t=wt(),r=xt(),e=t.getSession()||e.getSession();e&&r&&r.captureSession(e)}function Zr(){(0<arguments.length&&void 0!==arguments[0]&&arguments[0]?Qr:Jr)()}var en="7";function tn(e){var t=e.protocol?"".concat(e.protocol,":"):"",r=e.port?":".concat(e.port):"";return"".concat(t,"//").concat(e.host).concat(r).concat(e.path?"/".concat(e.path):"","/api/")}function rn(e,t,r){return t||"".concat("".concat(tn(t=e)).concat(t.projectId,"/envelope/"),"?").concat((r=r,n=_objectSpread({sentry_key:e.publicKey,sentry_version:en},r&&{sentry_client:"".concat(r.name,"/").concat(r.version)}),Object.keys(n).map(function(e){return"".concat(encodeURIComponent(e),"=").concat(encodeURIComponent(n[e]))}).join("&")));var n}var nn=[];function an(e,t){var r=_createForOfIteratorHelper(t);try{for(r.s();!(n=r.n()).done;){var n=n.value;n&&n.afterAllSetup&&n.afterAllSetup(e)}}catch(e){r.e(e)}finally{r.f()}}function on(r,e,t){var n,a;t[e.name]||(t[e.name]=e,-1===nn.indexOf(e.name)&&"function"==typeof e.setupOnce&&(e.setupOnce(),nn.push(e.name)),e.setup&&"function"==typeof e.setup&&e.setup(r),"function"==typeof e.preprocessEvent&&(n=e.preprocessEvent.bind(e),r.on("preprocessEvent",function(e,t){return n(e,t,r)})),"function"==typeof e.processEvent&&(a=e.processEvent.bind(e),e=Object.assign(function(e,t){return a(e,t,r)},{id:e.name}),r.addEventProcessor(e)))}var sn=function(){"use strict";function r(e){var t;_classCallCheck(this,r),this.ct=e,this._integrations={},this.ut=0,this.ft={},this.dt={},this.T=[],e.dsn&&(this.lt=P(e.dsn)),this.lt&&(t=rn(this.lt,e.tunnel,e._metadata?e._metadata.sdk:void 0),this.ht=e.transport(_objectSpread(_objectSpread({tunnel:this.ct.tunnel,recordDroppedEvent:this.recordDroppedEvent.bind(this)},e.transportOptions),{},{url:t})))}return _createClass(r,[{key:"captureException",value:function(e,t,r){var n=this,a=Se();if(Ie(e))return a;var i=_objectSpread({event_id:a},t);return this.gt(this.eventFromException(e,i).then(function(e){return n.vt(e,i,r)})),i.event_id}},{key:"captureMessage",value:function(e,t,r,n){var a=this,i=_objectSpread({event_id:Se()},r),r=o(e)?e:String(e),e=f(e)?this.eventFromMessage(r,t,i):this.eventFromException(e,i);return this.gt(e.then(function(e){return a.vt(e,i,n)})),i.event_id}},{key:"captureEvent",value:function(e,t,r){var n=Se();if(t&&t.originalException&&Ie(t.originalException))return n;n=_objectSpread({event_id:n},t),t=(e.sdkProcessingMetadata||{}).capturedSpanScope;return this.gt(this.vt(e,n,t||r)),n.event_id}},{key:"captureSession",value:function(e){"string"!=typeof e.release||(this.sendSession(e),lt(e,{init:!1}))}},{key:"getDsn",value:function(){return this.lt}},{key:"getOptions",value:function(){return this.ct}},{key:"getSdkMetadata",value:function(){return this.ct._metadata}},{key:"getTransport",value:function(){return this.ht}},{key:"flush",value:function(e){var r=this.ht;return r?(this.emit("flush"),this.yt(e).then(function(t){return r.flush(e).then(function(e){return t&&e})})):De(!0)}},{key:"close",value:function(e){var t=this;return this.flush(e).then(function(e){return t.getOptions().enabled=!1,t.emit("close"),e})}},{key:"getEventProcessors",value:function(){return this.T}},{key:"addEventProcessor",value:function(e){this.T.push(e)}},{key:"init",value:function(){(this.bt()||this.ct.integrations.some(function(e){return e.name.startsWith("Spotlight")}))&&this._t()}},{key:"getIntegrationByName",value:function(e){return this._integrations[e]}},{key:"addIntegration",value:function(e){var t=this._integrations[e.name];on(this,e,this._integrations),t||an(this,[e])}},{key:"sendEvent",value:function(t){var r=this,e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};this.emit("beforeSendEvent",t,e);var n,a,i,o,s,u,c,l,d,p=(n=t,a=this.lt,i=this.ct._metadata,o=this.ct.tunnel,u=Je(i),c=n.type&&"replay_event"!==n.type?n.type:"event",s=n,(i=i&&i.sdk)&&(s.sdk=s.sdk||{},s.sdk.name=s.sdk.name||i.name,s.sdk.version=s.sdk.version||i.version,s.sdk.integrations=[].concat(_toConsumableArray(s.sdk.integrations||[]),_toConsumableArray(i.integrations||[])),s.sdk.packages=[].concat(_toConsumableArray(s.sdk.packages||[]),_toConsumableArray(i.packages||[]))),i=u,u=o,o=a,a=n.sdkProcessingMetadata&&n.sdkProcessingMetadata.dynamicSamplingContext,a=_objectSpread(_objectSpread(_objectSpread({event_id:n.event_id,sent_at:(new Date).toISOString()},i&&{sdk:i}),!!u&&o&&{dsn:C(o)}),a&&{trace:V(_objectSpread({},a))}),delete n.sdkProcessingMetadata,We(a,[[{type:c},n]])),f=_createForOfIteratorHelper(e.attachments||[]);try{for(f.s();!(d=f.n()).done;){var h=d.value;l=p,d=void 0,d="string"==typeof(h=h).data?$e(h.data):h.data,h=[V({type:"attachment",length:d.length,filename:h.filename,content_type:h.contentType,attachment_type:h.attachmentType}),d],d=void 0,l=(d=_slicedToArray(l,2))[0],d=d[1],p=[l,[].concat(_toConsumableArray(d),[h])]}}catch(e){f.e(e)}finally{f.f()}e=this.sendEnvelope(p);e&&e.then(function(e){return r.emit("afterSendEvent",t,e)},null)}},{key:"sendSession",value:function(e){e=function(e,t,r,n){r=Je(r);return We(_objectSpread(_objectSpread({sent_at:(new Date).toISOString()},r&&{sdk:r}),!!n&&t&&{dsn:C(t)}),["aggregates"in e?[{type:"sessions"},e]:[{type:"session"},e.toJSON()]])}(e,this.lt,this.ct._metadata,this.ct.tunnel);this.sendEnvelope(e)}},{key:"recordDroppedEvent",value:function(e,t,r){this.ct.sendClientReports&&(r="number"==typeof r?r:1,t="".concat(e,":").concat(t),this.ft[t]=(this.ft[t]||0)+r)}},{key:"on",value:function(e,t){var r=this.dt[e]=this.dt[e]||[];return r.push(t),function(){var e=r.indexOf(t);-1<e&&r.splice(e,1)}}},{key:"emit",value:function(e){for(var t=arguments.length,r=new Array(1<t?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];e=this.dt[e];e&&e.forEach(function(e){return e.apply(void 0,r)})}},{key:"sendEnvelope",value:function(e){return this.emit("beforeEnvelope",e),this.bt()&&this.ht?this.ht.send(e).then(null,function(e){return e}):De({})}},{key:"_t",value:function(){var t,r,e=this.ct.integrations;this._integrations=(t=this,r={},e.forEach(function(e){e&&on(t,e,r)}),r),an(this,e)}},{key:"wt",value:function(e,t){var r=!1,n=!1,t=t.exception&&t.exception.values;if(t){n=!0;var a=_createForOfIteratorHelper(t);try{for(a.s();!(i=a.n()).done;){var i=i.value.mechanism;if(i&&!1===i.handled){r=!0;break}}}catch(e){a.e(e)}finally{a.f()}}t="ok"===e.status;(t&&0===e.errors||t&&r)&&(lt(e,_objectSpread(_objectSpread({},r&&{status:"crashed"}),{},{errors:e.errors||Number(n||r)})),this.captureSession(e))}},{key:"yt",value:function(n){var a=this;return new Ce(function(e){var t=0,r=setInterval(function(){0==a.ut?(clearInterval(r),e(!0)):(t+=1,n&&n<=t&&(clearInterval(r),e(!1)))},1)})}},{key:"bt",value:function(){return!1!==this.getOptions().enabled&&void 0!==this.ht}},{key:"St",value:function(e,t,i){var o=this,s=3<arguments.length&&void 0!==arguments[3]?arguments[3]:Et(),r=this.getOptions(),n=Object.keys(this._integrations);return!t.integrations&&0<n.length&&(t.integrations=n),this.emit("preprocessEvent",e,t),e.type||s.setLastEventId(e.event_id||t.event_id),jr(r,e,t,i,this,s).then(function(e){if(null===e)return e;var t,r,n,a=_objectSpread(_objectSpread({},s.getPropagationContext()),i?i.getPropagationContext():void 0);return e.contexts&&e.contexts.trace||!a||(n=a.traceId,t=a.spanId,r=a.parentSpanId,a=a.dsc,e.contexts=_objectSpread({trace:V({trace_id:n,span_id:t,parent_span_id:r})},e.contexts),n=a||dr(n,o),e.sdkProcessingMetadata=_objectSpread({dynamicSamplingContext:n},e.sdkProcessingMetadata)),e})}},{key:"vt",value:function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},r=2<arguments.length?arguments[2]:void 0;return this.Et(e,t,r).then(function(e){return e.event_id},function(e){})}},{key:"Et",value:function(n,a,i){var o=this,c=this.getOptions(),e=c.sampleRate,s=cn(n),t=un(n),r=n.type||"error",u="before send for type `".concat(r,"`"),l=void 0===e?void 0:fr(e);if(t&&"number"==typeof l&&Math.random()>l)return this.recordDroppedEvent("sample_rate","error",n),Oe(new M("Discarding event because it's not included in the random sample (sampling rate = ".concat(e,")"),"log"));var d="replay_event"===r?"replay":r,r=(n.sdkProcessingMetadata||{}).capturedSpanIsolationScope;return this.St(n,a,i,r).then(function(e){if(null===e)throw o.recordDroppedEvent("event_processor",d,n),new M("An event processor returned `null`, will not send event.","log");return a.data&&!0===a.data.__sentry__?e:function(e,t){var r="".concat(t," must return `null` or a valid event.");if(v(e))return e.then(function(e){if(!g(e)&&null!==e)throw new M(r);return e},function(e){throw new M("".concat(t," rejected with ").concat(e))});if(!g(e)&&null!==e)throw new M(r);return e}(function(e,t,r){var n=c.beforeSend,a=c.beforeSendTransaction,i=c.beforeSendSpan;if(un(t)&&n)return n(t,r);if(cn(t)){if(t.spans&&i){var o=[],s=_createForOfIteratorHelper(t.spans);try{for(s.s();!(u=s.n()).done;){var u=i(u.value);u?o.push(u):e.recordDroppedEvent("before_send","span")}}catch(e){s.e(e)}finally{s.f()}t.spans=o}if(a)return t.spans&&(n=t.spans.length,t.sdkProcessingMetadata=_objectSpread(_objectSpread({},t.sdkProcessingMetadata),{},{spanCountBeforeProcessing:n})),a(t,r)}return t}(o,e,a),u)}).then(function(e){if(null===e)throw o.recordDroppedEvent("before_send",d,n),s&&(t=1+(n.spans||[]).length,o.recordDroppedEvent("before_send","span",t)),new M("".concat(u," returned `null`, will not send event."),"log");var t=i&&i.getSession();!s&&t&&o.wt(t,e),s&&0<(r=(e.sdkProcessingMetadata&&e.sdkProcessingMetadata.spanCountBeforeProcessing||0)-(e.spans?e.spans.length:0))&&o.recordDroppedEvent("before_send","span",r);var r=e.transaction_info;return s&&r&&e.transaction!==n.transaction&&(e.transaction_info=_objectSpread(_objectSpread({},r),{},{source:"custom"})),o.sendEvent(e,a),e}).then(null,function(e){if(e instanceof M)throw e;throw o.captureException(e,{data:{__sentry__:!0},originalException:e}),new M("Event processing pipeline threw an error, original event will not be sent. Details have been sent as a new event.\nReason: ".concat(e))})}},{key:"gt",value:function(e){var t=this;this.ut++,e.then(function(e){return t.ut--,e},function(e){return t.ut--,e})}},{key:"Tt",value:function(){var e=this.ft;return this.ft={},Object.entries(e).map(function(e){var t=_slicedToArray(e,2),e=t[0],t=t[1],e=_slicedToArray(e.split(":"),2);return{reason:e[0],category:e[1],quantity:t}})}},{key:"$t",value:function(){var e,t=this.Tt();0!==t.length&&this.lt&&(e=t,e=We((t=this.ct.tunnel&&C(this.lt))?{dsn:t}:{},[[{type:"client_report"},{timestamp:ue(),discarded_events:e}]]),this.sendEnvelope(e))}}]),r}();function un(e){return void 0===e.type}function cn(e){return"transaction"===e.type}function ln(e){wt().setClient(e)}var dn=64;function pn(i,r){var n,o,s=2<arguments.length&&void 0!==arguments[2]?arguments[2]:(n=i.bufferSize||dn,{$:o=[],add:function(e){if(!(void 0===n||o.length<n))return Oe(new M("Not adding Promise because buffer limit was reached."));var t=e();return-1===o.indexOf(t)&&o.push(t),t.then(function(){return a(t)}).then(null,function(){return a(t).then(null,function(){})}),t},drain:function(e){return new Ce(function(t,r){var n=o.length;if(!n)return t(!0);var a=setTimeout(function(){e&&0<e&&t(!1)},e);o.forEach(function(e){De(e).then(function(){--n||(clearTimeout(a),t(!0))},r)})})}});function a(e){return o.splice(o.indexOf(e),1)[0]||Promise.resolve(void 0)}var u={};return{send:function(e){var n=[];if(Ye(e,function(e,t){var r=Qe(t);!function(e,t,r){var n=2<arguments.length&&void 0!==r?r:Date.now();return(e[t]||e.all||0)>n}(u,r)?n.push(e):(t=fn(e,t),i.recordDroppedEvent("ratelimit_backoff",r,t))}),0===n.length)return De({});function t(r){Ye(a,function(e,t){e=fn(e,t);i.recordDroppedEvent(r,Qe(t),e)})}var a=We(e[0],n);return s.add(function(){return r({body:function(e){var t=(e=_slicedToArray(e,2))[0],e=e[1],r=JSON.stringify(t);function n(e){"string"==typeof r?r="string"==typeof e?r+e:[$e(r),e]:r.push("string"==typeof e?$e(e):e)}var a=_createForOfIteratorHelper(e);try{for(a.s();!(o=a.n()).done;){var i=_slicedToArray(o.value,2),o=i[0],s=i[1];if(n("\n".concat(JSON.stringify(o),"\n")),"string"==typeof s||s instanceof Uint8Array)n(s);else{var u=void 0;try{u=JSON.stringify(s)}catch(t){u=JSON.stringify(Ne(s))}n(u)}}}catch(e){a.e(e)}finally{a.f()}return"string"==typeof r?r:function(e){var t=e.reduce(function(e,t){return e+t.length},0),r=new Uint8Array(t),n=0,a=_createForOfIteratorHelper(e);try{for(a.s();!(i=a.n()).done;){var i=i.value;r.set(i,n),n+=i.length}}catch(e){a.e(e)}finally{a.f()}return r}(r)}(a)}).then(function(e){return u=function(e,t,r){var n=t.statusCode,a=t.headers,i=2<arguments.length&&void 0!==r?r:Date.now(),o=_objectSpread({},e),s=a&&a["x-sentry-rate-limits"],a=a&&a["retry-after"];if(s){var u=_createForOfIteratorHelper(s.trim().split(","));try{for(u.s();!(d=u.n()).done;){var c=_slicedToArray(d.value.split(":",5),5),l=c[0],d=c[1],p=c[4],l=parseInt(l,10),f=1e3*(isNaN(l)?60:l);if(d){var h=_createForOfIteratorHelper(d.split(";"));try{for(h.s();!(m=h.n()).done;){var m=m.value;"metric_bucket"===m&&p&&!p.split(";").includes("custom")||(o[m]=i+f)}}catch(e){h.e(e)}finally{h.f()}}else o.all=i+f}}catch(e){u.e(e)}finally{u.f()}}else a?o.all=i+function(e,t){var r=1<arguments.length&&void 0!==t?t:Date.now(),n=parseInt("".concat(e),10);if(!isNaN(n))return 1e3*n;n=Date.parse("".concat(e));return isNaN(n)?Ze:n-r}(a,i):429===n&&(o.all=i+6e4);return o}(u,e),e},function(e){throw t("network_error"),e})}).then(function(e){return e},function(e){if(e instanceof M)return t("queue_overflow"),De({});throw e})},flush:function(e){return s.drain(e)}}}function fn(e,t){if("event"===t||"transaction"===t)return Array.isArray(e)?e[1]:void 0}var hn;function mn(e,t){var r,n,a,i,o=xt(),s=Et();o&&(r=(a=o.getOptions()).beforeBreadcrumb,n=void 0===r?null:r,(a=void 0===(a=a.maxBreadcrumbs)?100:a)<=0||(i=_objectSpread({timestamp:ue()},e),null!==(e=n?N(function(){return n(i,t)}):i)&&(o.emit&&o.emit("beforeAddBreadcrumb",e,t),s.addBreadcrumb(e,a))))}function gn(){return{name:"FunctionToString",setupOnce:function(){hn=Function.prototype.toString;try{Function.prototype.toString=function(){for(var e=q(this),e=bn.has(xt())&&void 0!==e?e:this,t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];return hn.apply(e,r)}}catch(e){}},setup:function(e){bn.set(e,!0)}}}function vn(){var i=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};return{name:"InboundFilters",processEvent:function(e,t,r){var n,a,r=r.getOptions(),r=function(e,t){var r=0<arguments.length&&void 0!==e?e:{},n=1<arguments.length&&void 0!==t?t:{};return{allowUrls:[].concat(_toConsumableArray(r.allowUrls||[]),_toConsumableArray(n.allowUrls||[])),denyUrls:[].concat(_toConsumableArray(r.denyUrls||[]),_toConsumableArray(n.denyUrls||[])),ignoreErrors:[].concat(_toConsumableArray(r.ignoreErrors||[]),_toConsumableArray(n.ignoreErrors||[]),_toConsumableArray(r.disableErrorDefaults?[]:yn)),ignoreTransactions:[].concat(_toConsumableArray(r.ignoreTransactions||[]),_toConsumableArray(n.ignoreTransactions||[])),ignoreInternal:void 0===r.ignoreInternal||r.ignoreInternal}}(i,r);return n=e,(r=r).ignoreInternal&&function(e){try{return"SentryError"===e.exception.values[0].type}catch(e){}}(n)||(a=r.ignoreErrors,!n.type&&a&&a.length&&function(e){var t,r=[];e.message&&r.push(e.message);try{t=e.exception.values[e.exception.values.length-1]}catch(e){}return t&&t.value&&(r.push(t.value),t.type&&r.push("".concat(t.type,": ").concat(t.value))),r}(n).some(function(e){return m(e,a)})||(!n.type&&n.exception&&n.exception.values&&0!==n.exception.values.length&&!n.message&&!n.exception.values.some(function(e){return e.stacktrace||e.type&&"Error"!==e.type||e.value})||(function(e,t){if("transaction"===e.type&&t&&t.length){e=e.transaction;return e&&m(e,t)}}(n,r.ignoreTransactions)||(function(e,t){if(t&&t.length){e=_n(e);return e&&m(e,t)}}(n,r.denyUrls)||!function(e){if(!e||!e.length)return 1;var t=_n(n);return!t||m(t,e)}(r.allowUrls)))))?null:e}}}var bn=new WeakMap,yn=[/^Script error\.?$/,/^Javascript error: Script error\.? on line 0$/,/^ResizeObserver loop completed with undelivered notifications.$/,/^Cannot redefine property: googletag$/,"undefined is not an object (evaluating 'a.L')",'can\'t redefine non-configurable property "solana"',"vv().getRestrictions is not a function. (In 'vv().getRestrictions(1,a)', 'vv().getRestrictions' is undefined)","Can't find variable: _AutofillCallbackHandler"];function _n(e){try{var t;try{t=e.exception.values[0].stacktrace.frames}catch(e){}return t?function(e){for(var t=0<arguments.length&&void 0!==e?e:[],r=t.length-1;0<=r;r--){var n=t[r];if(n&&"<anonymous>"!==n.filename&&"[native code]"!==n.filename)return n.filename||null}return null}(t):null}catch(e){return null}}function wn(){var s;return{name:"Dedupe",processEvent:function(e){if(e.type)return e;try{if(t=e,(r=s)&&(o=r,n=(i=t).message,a=o.message,!(!n&&!a||n&&!a||!n&&a||n!==a)&&Tn(i,o)&&En(i,o)||(i=t,t=Sn(o=r),r=Sn(i),!!(t&&r&&t.type===r.type&&t.value===r.value&&Tn(i,o)&&En(i,o)))))return null}catch(e){}var t,r,n,a,i,o;return s=e}}}function En(e,t){var r=J(e),n=J(t);if(!r&&!n)return 1;if(!(r&&!n||!r&&n)&&n.length===r.length){for(var a=0;a<n.length;a++){var i=n[a],o=r[a];if(i.filename!==o.filename||i.lineno!==o.lineno||i.colno!==o.colno||i.function!==o.function)return}return 1}}function Tn(e,t){var r=e.fingerprint,n=t.fingerprint;if(!r&&!n)return 1;if(!(r&&!n||!r&&n))try{return r.join("")===n.join("")}catch(e){return}}function Sn(e){return e.exception&&e.exception.values&&e.exception.values[0]}function xn(e,t){var r=T("globalMetricsAggregators",function(){return new WeakMap}),n=r.get(e);if(n)return n;var a=new t(e);return e.on("flush",function(){return a.flush()}),e.on("close",function(){return a.close()}),r.set(e,a),a}function kn(e,t,r,n,a){var i,o,s,u,c,l,d=4<arguments.length&&void 0!==a?a:{},p=d.client||xt();p&&(i=(c=(l=Jt())?Qt(l):void 0)&&Vt(c).description,o=d.unit,s=d.tags,u=d.timestamp,c=(l=p.getOptions()).release,d=l.environment,l={},c&&(l.release=c),d&&(l.environment=d),i&&(l.transaction=i),xn(p,e).add(t,r,n,o,_objectSpread(_objectSpread({},l),s),u))}function Rn(e,t,r,n){kn(e,"d",t,An(r),n)}var In={increment:function(e,t){var r=3<arguments.length?arguments[3]:void 0;kn(e,"c",t,An(2<arguments.length&&void 0!==arguments[2]?arguments[2]:1),r)},distribution:Rn,set:function(e,t,r,n){kn(e,"s",t,r,n)},gauge:function(e,t,r,n){kn(e,"g",t,An(r),n)},timing:function(r,n,e){var t=3<arguments.length&&void 0!==arguments[3]?arguments[3]:"second",a=4<arguments.length?arguments[4]:void 0;if("function"==typeof e){var i=ce();return _r({op:"metrics.timing",name:n,startTime:i,onlyIfParent:!0},function(t){return sr(function(){return e()},function(){},function(){var e=ce();Rn(r,n,e-i,_objectSpread(_objectSpread({},a),{},{unit:"second"})),t.end(e)})})}Rn(r,n,e,_objectSpread(_objectSpread({},a),{},{unit:t}))},getMetricsAggregatorForClient:xn};function An(e){return"string"==typeof e?parseInt(e):e}var Nn=[["\n","\\n"],["\r","\\r"],["\t","\\t"],["\\","\\\\"],["|","\\u{7c}"],[",","\\u{2c}"]];var Dn=(_defineProperty(t={c:function(){"use strict";function t(e){_classCallCheck(this,t),this.v=e}return _createClass(t,[{key:"weight",get:function(){return 1}},{key:"add",value:function(e){this.v+=e}},{key:"toString",value:function(){return"".concat(this.v)}}]),t}(),g:function(){"use strict";function t(e){_classCallCheck(this,t),this.kt=e,this.It=e,this.xt=e,this.jt=e,this.Ct=1}return _createClass(t,[{key:"weight",get:function(){return 5}},{key:"add",value:function(e){(this.kt=e)<this.It&&(this.It=e),e>this.xt&&(this.xt=e),this.jt+=e,this.Ct++}},{key:"toString",value:function(){return"".concat(this.kt,":").concat(this.It,":").concat(this.xt,":").concat(this.jt,":").concat(this.Ct)}}]),t}()},"d",function(){"use strict";function t(e){_classCallCheck(this,t),this.v=[e]}return _createClass(t,[{key:"weight",get:function(){return this.v.length}},{key:"add",value:function(e){this.v.push(e)}},{key:"toString",value:function(){return this.v.join(":")}}]),t}()),_defineProperty(t,"s",function(){"use strict";function t(e){_classCallCheck(this,t),this.first=e,this.v=new Set([e])}return _createClass(t,[{key:"weight",get:function(){return this.v.size}},{key:"add",value:function(e){this.v.add(e)}},{key:"toString",value:function(){return Array.from(this.v).map(function(e){return"string"==typeof e?function(e){for(var t=0,r=0;r<e.length;r++)t=(t<<5)-t+e.charCodeAt(r),t&=t;return t>>>0}(e):e}).join(":")}}]),t}()),t),On=function(){"use strict";function r(e){var t=this;_classCallCheck(this,r),this.q=e,this.Ot=new Map,this.Mt=setInterval(function(){return t.flush()},5e3)}return _createClass(r,[{key:"add",value:function(e,t,r){var n=3<arguments.length&&void 0!==arguments[3]?arguments[3]:"none",a=4<arguments.length&&void 0!==arguments[4]?arguments[4]:{},i=5<arguments.length&&void 0!==arguments[5]?arguments[5]:ce(),o=Math.floor(i),s=t.replace(/[^\w\-.]+/gi,"_"),u=function(e){var t,r,n={};for(t in e)Object.prototype.hasOwnProperty.call(e,t)&&(n[t.replace(/[^\w\-./]+/gi,"")]=(r=String(e[t]),_toConsumableArray(r).reduce(function(e,t){return e+function(e){for(var t=0,r=Nn;t<r.length;t++){var n=_slicedToArray(r[t],2),a=n[0],n=n[1];if(e===a)return n}return e}(t)},"")));return n}(a),c=n.replace(/[^\w]+/gi,"_"),i=(i=s,t=c,n=u,"".concat(e).concat(i).concat(t).concat(Object.entries(V(n)).sort(function(e,t){return e[0].localeCompare(t[0])}))),t=this.Ot.get(i),n=t&&"s"===e?t.metric.weight:0;t?(t.metric.add(r),t.timestamp<o&&(t.timestamp=o)):(t={metric:new Dn[e](r),timestamp:o,metricType:e,name:s,unit:c,tags:u},this.Ot.set(i,t)),e=e,s=s,n="string"==typeof r?t.metric.weight-n:r,r=c,c=a,a=i,(i=Jt())&&(e=e,s=s,n=n,r=r,c=c,a=a,i=i[kt]||(i[kt]=new Map),s="".concat(e,":").concat(s,"@").concat(r),(r=i.get(a))?(r=_slicedToArray(r,2)[1],i.set(a,[s,{min:Math.min(r.min,n),max:Math.max(r.max,n),count:r.count+=1,sum:r.sum+=n,tags:r.tags}])):i.set(a,[s,{min:n,max:n,count:1,sum:n,tags:c}]))}},{key:"flush",value:function(){var e;0!==this.Ot.size&&(e=Array.from(this.Ot.values()),function(e,t){D.log("Flushing aggregated metrics, number of metrics: ".concat(t.length));var r,n,a,i,o=(o=t,r=e.getDsn(),n=e.getSdkMetadata(),a=e.getOptions().tunnel,t={sent_at:(new Date).toISOString()},n&&n.sdk&&(t.sdk={name:n.sdk.name,version:n.sdk.version}),a&&r&&(t.dsn=C(r)),We(t,[(i=o,[{type:"statsd",length:(o=function(){var e="",t=_createForOfIteratorHelper(i);try{for(t.s();!(n=t.n()).done;){var r=n.value,n=Object.entries(r.tags),n=0<n.length?"|#".concat(n.map(function(e){var t=_slicedToArray(e,2),e=t[0],t=t[1];return"".concat(e,":").concat(t)}).join(",")):"";e+="".concat(r.name,"@").concat(r.unit,":").concat(r.metric,"|").concat(r.metricType).concat(n,"|T").concat(r.timestamp,"\n")}}catch(e){t.e(e)}finally{t.f()}return e}()).length},o])]));e.sendEnvelope(o)}(this.q,e),this.Ot.clear())}},{key:"close",value:function(){clearInterval(this.Mt),this.flush()}}]),r}();var Cn=S,Ln=0;function Pn(t,e,r){var n=1<arguments.length&&void 0!==e?e:{},a=2<arguments.length?r:void 0;if("function"!=typeof t)return t;try{var i=t.__sentry_wrapped__;if(i)return i;if(q(t))return t}catch(n){return t}function o(){var r=Array.prototype.slice.call(arguments);try{a&&"function"==typeof a&&a.apply(this,arguments);var e=r.map(function(e){return Pn(e,n)});return t.apply(this,e)}catch(t){throw Ln++,setTimeout(function(){Ln--}),St(function(e){e.addEventProcessor(function(e){return n.mechanism&&(ke(e,void 0,void 0),Re(e,n.mechanism)),e.extra=_objectSpread(_objectSpread({},e.extra),{},{arguments:r}),e}),Hr(t)}),t}}try{for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&(o[s]=t[s])}catch(t){}F(o,t),j(t,"__sentry_wrapped__",o);try{Object.getOwnPropertyDescriptor(o,"name").configurable&&Object.defineProperty(o,"name",{get:function(){return t.name}})}catch(t){}return o}function Mn(e,t){e=Fn(e,t),t={type:t&&t.name,value:function(e){e=e&&e.message;return e?e.error&&"string"==typeof e.error.message?e.error.message:e:"No error message"}(t)};return e.length&&(t.stacktrace={frames:e}),void 0===t.type&&""===t.value&&(t.value="Unrecoverable error caught"),t}function Un(e,t,r,n){var a=xt(),i=a&&a.getOptions().normalizeDepth,a=function(e){for(var t in e)if(Object.prototype.hasOwnProperty.call(e,t)){var r=e[t];if(r instanceof Error)return r}}(t),i={__serialized__:function e(t,r,n){var a,i=1<arguments.length&&void 0!==r?r:3,o=2<arguments.length&&void 0!==n?n:102400,s=Ne(t,i);return a=JSON.stringify(s),~-encodeURI(a).split(/%..|./).length>o?e(t,i-1,o):s}(t,i)};if(a)return{exception:{values:[Mn(e,a)]},extra:i};i={exception:{values:[{type:h(t)?t.constructor.name:n?"UnhandledRejection":"Error",value:function(e,t){var r=t.isUnhandledRejection,t=function(e,t){var r=1<arguments.length&&void 0!==t?t:40,n=Object.keys(H(e));n.sort();var a=n[0];if(!a)return"[object has no keys]";if(a.length>=r)return _(a,r);for(var i=n.length;0<i;i--){var o=n.slice(0,i).join(", ");if(!(o.length>r))return i===n.length?o:_(o,r)}return""}(e),r=r?"promise rejection":"exception";if(l(e))return"Event `ErrorEvent` captured as ".concat(r," with message `").concat(e.message,"`");if(h(e))return"Event `".concat(function(e){try{var t=Object.getPrototypeOf(e);return t?t.constructor.name:void 0}catch(e){}}(e),"` (type=").concat(e.type,") captured as ").concat(r);return"Object captured as ".concat(r," with keys: ").concat(t)}(t,{isUnhandledRejection:n})}]},extra:i};return!r||(r=Fn(e,r)).length&&(i.exception.values[0].stacktrace={frames:r}),i}function jn(e,t){return{exception:{values:[Mn(e,t)]}}}function Fn(e,t){var r=t.stacktrace||t.stack||"",n=t&&qn.test(t.message)?1:0,a="number"==typeof t.framesToPop?t.framesToPop:0;try{return e(r,n,a)}catch(e){}return[]}var qn=/Minified React error #\d+;/i;function Hn(e,t,r,n){n=Bn(e,t,r&&r.syntheticException||void 0,n);return Re(n),n.level="error",r&&r.event_id&&(n.event_id=r.event_id),De(n)}function Gn(e,t){var r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:"info",n=3<arguments.length?arguments[3]:void 0,a=4<arguments.length?arguments[4]:void 0,a=Vn(e,t,n&&n.syntheticException||void 0,a);return a.level=r,n&&n.event_id&&(a.event_id=n.event_id),De(a)}function Bn(e,t,r,n,a){if(l(t)&&t.error)return jn(e,t.error);if(d(t)||c(t,"DOMException")){var i,o,s=t;return"stack"in t?o=jn(e,t):(i=s.name||(d(s)?"DOMError":"DOMException"),ke(o=Vn(e,i=s.message?"".concat(i,": ").concat(s.message):i,r,n),i)),"code"in s&&(o.tags=_objectSpread(_objectSpread({},o.tags),{},{"DOMException.code":"".concat(s.code)})),o}return u(t)?jn(e,t):(g(t)||h(t)?Re(o=Un(e,t,r,a),{synthetic:!0}):(ke(o=Vn(e,t,r,n),"".concat(t),void 0),Re(o,{synthetic:!0})),o)}function Vn(e,t,r,n){var a={};if(n&&r&&((i=Fn(e,r)).length&&(a.exception={values:[{value:t,stacktrace:{frames:i}}]})),o(t)){var r=t.__sentry_template_string__,i=t.__sentry_template_values__;return a.logentry={message:r,params:i},a}return a.message=t,a}function Xn(e,t){var r=t.metadata,n=t.tunnel,t=t.dsn;return We(_objectSpread(_objectSpread({event_id:e.event_id,sent_at:(new Date).toISOString()},r&&r.sdk&&{sdk:{name:r.sdk.name,version:r.sdk.version}}),!!n&&!!t&&{dsn:C(t)}),[[{type:"user_report"},e]])}function zn(t,r,n,a){var i,o;return function(e){0<=r.value&&(e||a)&&(!(o=r.value-(i||0))&&void 0!==i||(i=r.value,r.delta=o,r.rating=(e=r.value)>n[1]?"poor":e>n[0]?"needs-improvement":"good",t(r)))}}function Wn(){var e=la();return e&&e.activationStart||0}function Yn(e,t){var r=la(),n="navigate";return r&&(ca.document&&ca.document.prerendering||0<Wn()?n="prerender":ca.document&&ca.document.wasDiscarded?n="restore":r.type&&(n=r.type.replace(/_/g,"-"))),{name:e,value:void 0===t?-1:t,rating:"good",delta:0,entries:[],id:"v3-".concat(Date.now(),"-").concat(Math.floor(8999999999999*Math.random())+1e12),navigationType:n}}function $n(t){var r=!1;return function(e){r||(t(e),r=!0)}}function Kn(e){"hidden"===ca.document.visibilityState&&-1<fa&&(fa="visibilitychange"===e.type?e.timeStamp:0,removeEventListener("visibilitychange",Kn,!0),removeEventListener("prerenderingchange",Kn,!0))}function Qn(e){ca.document&&ca.document.prerendering?addEventListener("prerenderingchange",function(){return e()},!0):e()}function Jn(e){e.forEach(function(e){e.interactionId&&(wa=Math.min(wa,e.interactionId),Ea=Math.max(Ea,e.interactionId),_a=Ea?(Ea-wa)/7+1:0)})}function Zn(){return+(ra?_a:performance.interactionCount||0)}function ea(e){var t=Sa[Sa.length-1],r=xa[e.interactionId];(r||Sa.length<10||t&&e.duration>t.latency)&&(r?(r.entries.push(e),r.latency=Math.max(r.latency,e.duration)):(e={id:e.interactionId,latency:e.duration,entries:[e]},xa[e.id]=e,Sa.push(e)),Sa.sort(function(e,t){return t.latency-e.latency}),Sa.splice(10).forEach(function(e){delete xa[e.id]}))}function ta(e){ca.document&&ca.document.prerendering?Qn(function(){return ta(e)}):ca.document&&"complete"!==ca.document.readyState?addEventListener("load",function(){return ta(e)},!0):setTimeout(e,0)}var ra,na,aa,ia,oa,sa,ua=function(){"use strict";_inherits(n,sn);var r=_createSuper(n);function n(e){var t;_classCallCheck(this,n);e=_objectSpread({parentSpanIsAlwaysRootSpan:!0},e);return function(e,t,r,n){var r=2<arguments.length&&void 0!==r?r:["browser"],a=3<arguments.length&&void 0!==n?n:"npm";(n=e._metadata||{}).sdk||(n.sdk={name:"sentry.javascript.".concat("browser"),packages:r.map(function(e){return{name:"".concat(a,":@sentry/").concat(e),version:s}}),version:s}),e._metadata=n}(e,"browser",["browser"],Cn.SENTRY_SDK_SOURCE||"cdn"),t=r.call(this,e),e.sendClientReports&&Cn.document&&Cn.document.addEventListener("visibilitychange",function(){"hidden"===Cn.document.visibilityState&&t.$t()}),t}return _createClass(n,[{key:"eventFromException",value:function(e,t){return Hn(this.ct.stackParser,e,t,this.ct.attachStacktrace)}},{key:"eventFromMessage",value:function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"info",r=2<arguments.length?arguments[2]:void 0;return Gn(this.ct.stackParser,e,t,r,this.ct.attachStacktrace)}},{key:"captureUserFeedback",value:function(e){this.bt()&&(e=Xn(e,{metadata:this.getSdkMetadata(),dsn:this.getDsn(),tunnel:this.getOptions().tunnel}),this.sendEnvelope(e))}},{key:"St",value:function(e,t,r){return e.platform=e.platform||"javascript",_get(_getPrototypeOf(n.prototype),"St",this).call(this,e,t,r)}}]),n}(),ca=S,la=function(){return ca.performance&&performance.getEntriesByType&&performance.getEntriesByType("navigation")[0]},da=function(e,t,r){try{if(PerformanceObserver.supportedEntryTypes.includes(e)){var n=new PerformanceObserver(function(e){Promise.resolve().then(function(){t(e.getEntries())})});return n.observe(Object.assign({type:e,buffered:!0},r||{})),n}}catch(e){}},pa=function(t){function e(e){("pagehide"===e.type||ca.document&&"hidden"===ca.document.visibilityState)&&t(e)}ca.document&&(addEventListener("visibilitychange",e,!0),addEventListener("pagehide",e,!0))},fa=-1,ha=function(){return ca.document&&fa<0&&(fa="hidden"!==ca.document.visibilityState||ca.document.prerendering?1/0:0,addEventListener("visibilitychange",Kn,!0),addEventListener("prerenderingchange",Kn,!0)),{get firstHiddenTime(){return fa}}},ma=[1800,3e3],ga=[.1,.25],va=function(o){var s=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};!function(e,t){var i=1<arguments.length&&void 0!==t?t:{};Qn(function(){var t,r=ha(),n=Yn("FCP"),a=da("paint",function(e){e.forEach(function(e){"first-contentful-paint"===e.name&&(a.disconnect(),e.startTime<r.firstHiddenTime&&(n.value=Math.max(e.startTime-Wn(),0),n.entries.push(e),t(!0)))})});a&&(t=zn(e,n,ma,i.reportAllChanges))})}($n(function(){function e(e){e.forEach(function(e){var t,r;e.hadRecentInput||(t=a[0],r=a[a.length-1],n&&t&&r&&e.startTime-r.startTime<1e3&&e.startTime-t.startTime<5e3?(n+=e.value,a.push(e)):(n=e.value,a=[e]))}),n>r.value&&(r.value=n,r.entries=a,t())}var t,r=Yn("CLS",0),n=0,a=[],i=da("layout-shift",e);i&&(t=zn(o,r,ga,s.reportAllChanges),pa(function(){e(i.takeRecords()),t(!0)}),setTimeout(t,0))}))},ba=[100,300],ya=function(o){var s=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};Qn(function(){function t(e){e.startTime<r.firstHiddenTime&&(n.value=e.processingStart-e.startTime,n.entries.push(e),i(!0))}function e(e){e.forEach(t)}var r=ha(),n=Yn("FID"),a=da("first-input",e),i=zn(o,n,ba,s.reportAllChanges);a&&pa($n(function(){e(a.takeRecords()),a.disconnect()}))})},_a=0,wa=1/0,Ea=0,Ta=[200,500],Sa=[],xa={},ka=function(a){var i=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};Qn(function(){"interactionCount"in performance||ra||(ra=da("event",Jn,{type:"event",buffered:!0,durationThreshold:0}));function e(e){e.forEach(function(t){t.interactionId&&ea(t),"first-input"===t.entryType&&(Sa.some(function(e){return e.entries.some(function(e){return t.duration===e.duration&&t.startTime===e.startTime})})||ea(t))}),e=Math.min(Sa.length-1,Math.floor(Zn()/50)),(e=Sa[e])&&e.latency!==t.value&&(t.value=e.latency,t.entries=e.entries,n())}var t=Yn("INP"),r=da("event",e,{durationThreshold:null!=i.durationThreshold?i.durationThreshold:40}),n=zn(a,t,Ta,i.reportAllChanges);r&&("PerformanceEventTiming"in ca&&"interactionId"in PerformanceEventTiming.prototype&&r.observe({type:"first-input",buffered:!0}),pa(function(){e(r.takeRecords()),t.value<0&&0<Zn()&&(t.value=0,t.entries=[]),n(!0)}))})},Ra=[2500,4e3],Ia={},Aa=function(o){var s=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};Qn(function(){function e(e){(e=e[e.length-1])&&e.startTime<n.firstHiddenTime&&(a.value=Math.max(e.startTime-Wn(),0),a.entries=[e],t())}var t,r,n=ha(),a=Yn("LCP"),i=da("largest-contentful-paint",e);i&&(t=zn(o,a,Ra,s.reportAllChanges),r=$n(function(){Ia[a.id]||(e(i.takeRecords()),i.disconnect(),Ia[a.id]=!0,t(!0))}),["keydown","click"].forEach(function(e){ca.document&&addEventListener(e,function(){return setTimeout(r,0)},!0)}),pa(r))})},Na=[800,1800],Da=function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},r=Yn("TTFB"),n=zn(e,r,Na,t.reportAllChanges);ta(function(){var e,t=la();t&&((e=t.responseStart)<=0||e>performance.now()||(r.value=Math.max(e-Wn(),0),r.entries=[t],n(!0)))})},Oa={},Ca={};function La(e,t){return Ga("cls",e,Ua,na,1<arguments.length&&void 0!==t&&t)}function Pa(e,t){return Ba(e,t),Ca[e]||(n={},"event"===(r=e)&&(n.durationThreshold=0),da(r,function(e){Ma(r,{entries:e})},n),Ca[e]=!0),Va(e,t);var r,n}function Ma(e,t){var r=Oa[e];if(r&&r.length){var n,a=_createForOfIteratorHelper(r);try{for(a.s();!(n=a.n()).done;){var i=n.value;try{i(t)}catch(e){}}}catch(e){a.e(e)}finally{a.f()}}}function Ua(){return va(function(e){Ma("cls",{metric:e}),na=e},{reportAllChanges:!0})}function ja(){return ya(function(e){Ma("fid",{metric:e}),aa=e})}function Fa(){return Aa(function(e){Ma("lcp",{metric:e}),ia=e},{reportAllChanges:!0})}function qa(){return Da(function(e){Ma("ttfb",{metric:e}),oa=e})}function Ha(){return ka(function(e){Ma("inp",{metric:e}),sa=e})}function Ga(e,t,r,n,a){var i,o=4<arguments.length&&void 0!==a&&a;return Ba(e,t),Ca[e]||(i=r(),Ca[e]=!0),n&&t({metric:n}),Va(e,t,o?i:void 0)}function Ba(e,t){Oa[e]=Oa[e]||[],Oa[e].push(t)}function Va(r,n,a){return function(){a&&a();var e,t=Oa[r];!t||-1!==(e=t.indexOf(n))&&t.splice(e,1)}}function Xa(e){return"number"==typeof e&&isFinite(e)}function za(e,t,r,n){var a=_extends({},(_objectDestructuringEmpty(n),n)),n=Vt(e).start_timestamp;return n&&t<n&&"function"==typeof e.updateStartTime&&e.updateStartTime(t),Er(e,function(){var e=wr(_objectSpread({startTime:t},a));return e&&e.end(r),e})}function Wa(e){var t=xt();if(t){var r,n=e.name,a=e.transaction,i=e.attributes,o=e.startTime,s=t.getOptions(),u=s.release,c=s.environment,s=t.getIntegrationByName("Replay"),t=s&&s.getReplayId(),l=wt(),s=l.getUser(),s=void 0!==s?s.email||s.id||s.ip_address:void 0;try{r=l.getScopeData().contexts.profile.profile_id}catch(e){}return wr({name:n,attributes:_objectSpread({release:u,environment:c,user:s||void 0,profile_id:r||void 0,replay_id:t||void 0,transaction:a,"user_agent.original":ca.navigator&&ca.navigator.userAgent},i),startTime:o,experimental:{standalone:!0}})}}function Ya(){return ca&&ca.addEventListener&&ca.performance}function $a(e){return e/1e3}function Ka(){var i,o,s,u,c=0;function n(){var t,e,r,n,a;s||(s=!0,o&&(t=c,r=o,n=$a((le||0)+(et([e=i,"optionalAccess",function(e){return e.startTime}])||0)),a=wt().getScopeData().transactionName,a=Wa({name:e?R(et([e,"access",function(e){return e.sources},"access",function(e){return e[0]},"optionalAccess",function(e){return e.node}])):"Layout shift",transaction:a,attributes:V((_defineProperty(a={},Dt,"auto.http.browser.cls"),_defineProperty(a,Nt,"ui.webvital.cls"),_defineProperty(a,Pt,et([e,"optionalAccess",function(e){return e.duration}])||0),_defineProperty(a,"sentry.pageload.span_id",r),a)),startTime:n}),et([a,"optionalAccess",function(e){return e.addEvent},"call",function(e){return e("cls",(_defineProperty(e={},Ct,""),_defineProperty(e,Lt,t),e))}]),et([a,"optionalAccess",function(e){return e.end},"call",function(e){return e(n)}])),u())}!function(){try{return et([PerformanceObserver,"access",function(e){return e.supportedEntryTypes},"optionalAccess",function(e){return e.includes},"call",function(e){return e("layout-shift")}])}catch(e){return}}()||(u=La(function(e){var t=e.metric,e=t.entries[t.entries.length-1];e&&(c=t.value,i=e)},!(s=!1)),pa(function(){n()}),setTimeout(function(){var t=et([xt(),"optionalAccess",function(e){return e.on},"call",function(e){return e("startNavigationSpan",function(){n(),t&&t()})}]),e=Jt(),r=e&&Qt(e),e=r&&Vt(r);e&&"pageload"===e.op&&(o=r.spanContext().spanId)},0))}var Qa,Ja,Za=2147483647,ei=0,ti={};function ri(e){var t=e.recordClsStandaloneSpans,e=Ya();if(e&&le){e.mark&&ca.performance.mark("sentry-tracing-init");var r=Ga("fid",function(e){var t=e.metric,r=t.entries[t.entries.length-1];r&&(e=le/1e3,r=r.startTime/1e3,ti.fid={value:t.value,unit:"millisecond"},ti["mark.fid"]={value:e+r,unit:"second"})},ja,aa),n=function(e,t){return Ga("lcp",e,Fa,ia,1<arguments.length&&void 0!==t&&t)}(function(e){var t=e.metric,e=t.entries[t.entries.length-1];e&&(ti.lcp={value:t.value,unit:"millisecond"},Qa=e)},!0),a=Ga("ttfb",function(e){var t=e.metric;t.entries[t.entries.length-1]&&(ti.ttfb={value:t.value,unit:"millisecond"})},qa,oa),i=t?Ka():La(function(e){var t=e.metric,e=t.entries[t.entries.length-1];e&&(ti.cls={value:t.value,unit:""},Ja=e)},!0);return function(){r(),n(),a(),i&&i()}}return function(){}}function ni(b,e){var y,_,w,r,t,n,a=Ya();a&&ca.performance.getEntries&&le&&(y=le/1e3,n=a.getEntries(),a=Vt(b),_=a.op,w=a.start_timestamp,n.slice(ei).forEach(function(e){var t,r,n,a,i,o,s,u,c,l,d,p,f,h=e.startTime/1e3,m=Math.max(0,e.duration)/1e3;if(!("navigation"===_&&w&&y+h<w))switch(e.entryType){case"navigation":s=b,u=e,c=y,["unloadEvent","redirect","domContentLoadedEvent","loadEvent","connect"].forEach(function(e){ai(s,u,e,c)}),ai(s,u,"secureConnection",c,"TLS/SSL","connectEnd"),ai(s,u,"fetch",c,"cache","domainLookupStart"),ai(s,u,"domainLookup",c,"DNS"),l=s,d=c+u.requestStart/1e3,p=c+u.responseEnd/1e3,f=c+u.responseStart/1e3,u.responseEnd&&(za(l,d,p,{op:"browser",name:"request",attributes:_defineProperty({},Dt,"auto.ui.browser.metrics")}),za(l,f,p,{op:"browser",name:"response",attributes:_defineProperty({},Dt,"auto.ui.browser.metrics")}));break;case"mark":case"paint":case"measure":t=b,r=e,n=h,a=m,i=y,o=$a((o=la())?o.requestStart:0),o=i+Math.max(n,o),n=(i+=n)+a,a=_defineProperty({},Dt,"auto.resource.browser.metrics"),o!==i&&(a["sentry.browser.measure_happened_before_request"]=!0,a["sentry.browser.measure_start_time"]=o),za(t,o,n,{name:r.name,op:r.entryType,attributes:a});var g=ha(),v=e.startTime<g.firstHiddenTime;"first-paint"===e.name&&v&&(ti.fp={value:e.startTime,unit:"millisecond"}),"first-contentful-paint"===e.name&&v&&(ti.fcp={value:e.startTime,unit:"millisecond"});break;case"resource":i=b,o=(t=e).name,n=h,r=m,a=y,"xmlhttprequest"!==t.initiatorType&&"fetch"!==t.initiatorType&&(g=Le(o),ii(v=_defineProperty({},Dt,"auto.resource.browser.metrics"),t,"transferSize","http.response_transfer_size"),ii(v,t,"encodedBodySize","http.response_content_length"),ii(v,t,"decodedBodySize","http.decoded_response_content_length"),"renderBlockingStatus"in t&&(v["resource.render_blocking_status"]=t.renderBlockingStatus),g.protocol&&(v["url.scheme"]=g.protocol.split(":").pop()),g.host&&(v["server.address"]=g.host),v["url.same_origin"]=o.includes(ca.location.origin),za(i,n=a+n,n+r,{name:o.replace(ca.location.origin,""),op:t.initiatorType?"resource.".concat(t.initiatorType):"resource.other",attributes:v}))}}),ei=Math.max(n.length-1,0),a=b,(n=ca.navigator)&&((t=n.connection)&&(t.effectiveType&&a.setAttribute("effectiveConnectionType",t.effectiveType),t.type&&a.setAttribute("connectionType",t.type),Xa(t.rtt)&&(ti["connection.rtt"]={value:t.rtt,unit:"millisecond"})),Xa(n.deviceMemory)&&a.setAttribute("deviceMemory","".concat(n.deviceMemory," GB")),Xa(n.hardwareConcurrency)&&a.setAttribute("hardwareConcurrency",String(n.hardwareConcurrency))),"pageload"===_&&(t=ti,(a=la())&&(n=a.responseStart,(a=a.requestStart)<=n&&(t["ttfb.requestTime"]={value:n-a,unit:"millisecond"})),(a=ti["mark.fid"])&&ti.fid&&(za(b,a.value,a.value+ti.fid.value/1e3,{name:"first input delay",op:"ui.action",attributes:_defineProperty({},Dt,"auto.ui.browser.metrics")}),delete ti["mark.fid"]),"fcp"in ti&&e.recordClsOnPageloadSpan||delete ti.cls,Object.entries(ti).forEach(function(e){var t=_slicedToArray(e,2),e=t[0],t=t[1];hr(e,t.value,t.unit)}),b.setAttribute("performance.timeOrigin",y),r=b,Qa&&(Qa.element&&r.setAttribute("lcp.element",R(Qa.element)),Qa.id&&r.setAttribute("lcp.id",Qa.id),Qa.url&&r.setAttribute("lcp.url",Qa.url.trim().slice(0,200)),r.setAttribute("lcp.size",Qa.size)),Ja&&Ja.sources&&Ja.sources.forEach(function(e,t){return r.setAttribute("cls.source.".concat(t+1),R(e.node))})),Ja=Qa=void 0,ti={})}function ai(e,t,r,n,a,i){i=i?t[i]:t["".concat(r,"End")],t=t["".concat(r,"Start")];t&&i&&za(e,n+t/1e3,n+i/1e3,{op:"browser",name:a||r,attributes:_defineProperty({},Dt,"auto.ui.browser.metrics")})}function ii(e,t,r,n){r=t[r];null!=r&&r<Za&&(e[n]=r)}var oi,si,ui,ci,li=1e3;function di(){var o,e;ca.document&&(e=pi(o=ne.bind(null,"dom"),!0),ca.document.addEventListener("click",e,!1),ca.document.addEventListener("keypress",e,!1),["EventTarget","Node"].forEach(function(e){e=ca[e]&&ca[e].prototype;e&&e.hasOwnProperty&&e.hasOwnProperty("addEventListener")&&(U(e,"addEventListener",function(i){return function(e,t,r){if("click"===e||"keypress"==e)try{var n=this.__sentry_instrumentation_handlers__=this.__sentry_instrumentation_handlers__||{},a=n[e]=n[e]||{refCount:0};a.handler||(n=pi(o),a.handler=n,i.call(this,e,n,r)),a.refCount++}catch(e){}return i.call(this,e,t,r)}}),U(e,"removeEventListener",function(i){return function(e,t,r){if("click"===e||"keypress"==e)try{var n=this.__sentry_instrumentation_handlers__||{},a=n[e];a&&(a.refCount--,a.refCount<=0&&(i.call(this,e,a.handler,r),a.handler=void 0,delete n[e]),0===Object.keys(n).length&&delete this.__sentry_instrumentation_handlers__)}catch(e){}return i.call(this,e,t,r)}}))}))}function pi(n,e){var a=1<arguments.length&&void 0!==e&&e;return function(e){var t,r;e&&!e._sentryCaptured&&(t=function(e){try{return e.target}catch(e){return null}}(e),("keypress"!==e.type||t&&t.tagName&&("INPUT"===t.tagName||"TEXTAREA"===t.tagName||t.isContentEditable))&&(j(e,"_sentryCaptured",!0),t&&!t._sentryId&&j(t,"_sentryId",Se()),r="keypress"===e.type?"input":e.type,function(e){if(e.type===si){try{if(!e.target||e.target._sentryId!==ui)return}catch(e){}return 1}}(e)||(n({event:e,name:r,global:a}),si=e.type,ui=t?t._sentryId:void 0),clearTimeout(oi),oi=ca.setTimeout(function(){si=ui=void 0},li)))}}function fi(e){te("history",e),re("history",hi)}function hi(){var e,t,a;function r(i){return function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var n,a=2<t.length?t[2]:void 0;return a&&(n=ci,a=String(a),ne("history",{from:n,to:ci=a})),i.apply(this,t)}}e=nt.chrome,t=e&&e.app&&e.app.runtime,e="history"in nt&&!!nt.history.pushState&&!!nt.history.replaceState,!t&&e&&(a=ca.onpopstate,ca.onpopstate=function(){var e=ca.location.href;if(ne("history",{from:ci,to:ci=e}),a)try{for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];return a.apply(this,r)}catch(e){}},U(ca.history,"pushState",r),U(ca.history,"replaceState",r))}var mi={};function gi(e){mi[e]=void 0}var vi="__sentry_xhr_v3__";function bi(e){te("xhr",e),re("xhr",yi)}function yi(){var e;ca.XMLHttpRequest&&((e=XMLHttpRequest.prototype).open=new Proxy(e.open,{apply:function(e,t,r){var n=1e3*ce(),a=p(r[0])?r[0].toUpperCase():void 0,i=function(e){if(p(e))return e;try{return e.toString()}catch(e){}}(r[1]);if(!a||!i)return e.apply(t,r);t[vi]={method:a,url:i,request_headers:{}},"POST"===a&&i.match(/sentry_key/)&&(t.__sentry_own_request__=!0);function o(){var e=t[vi];if(e&&4===t.readyState){try{e.status_code=t.status}catch(e){}ne("xhr",{endTimestamp:1e3*ce(),startTimestamp:n,xhr:t})}}return"onreadystatechange"in t&&"function"==typeof t.onreadystatechange?t.onreadystatechange=new Proxy(t.onreadystatechange,{apply:function(e,t,r){return o(),e.apply(t,r)}}):t.addEventListener("readystatechange",o),t.setRequestHeader=new Proxy(t.setRequestHeader,{apply:function(e,t,r){var n=_slicedToArray(r,2),a=n[0],i=n[1],n=t[vi];return n&&p(a)&&p(i)&&(n.request_headers[a.toLowerCase()]=i),e.apply(t,r)}}),e.apply(t,r)}}),e.send=new Proxy(e.send,{apply:function(e,t,r){var n=t[vi];return n&&(void 0!==r[0]&&(n.body=r[0]),ne("xhr",{startTimestamp:1e3*ce(),xhr:t})),e.apply(t,r)}}))}var _i=[],wi=new Map;var Ei={click:"click",pointerdown:"click",pointerup:"click",mousedown:"click",mouseup:"click",touchstart:"click",touchend:"click",mouseover:"hover",mouseout:"hover",mouseenter:"hover",mouseleave:"hover",pointerover:"hover",pointerout:"hover",pointerenter:"hover",pointerleave:"hover",dragstart:"drag",dragend:"drag",drag:"drag",dragenter:"drag",dragleave:"drag",dragover:"drag",drop:"drag",keydown:"press",keyup:"press",keypress:"press",input:"press"};function Ti(n){var a=1<arguments.length&&void 0!==arguments[1]?arguments[1]:function(e){var t=mi[e];if(t)return t;var r=ca[e];if(oe(r))return mi[e]=r.bind(ca);var n=ca.document;if(n&&"function"==typeof n.createElement)try{var a=n.createElement("iframe");a.hidden=!0,n.head.appendChild(a);var i=a.contentWindow;i&&i[e]&&(r=i[e]),n.head.removeChild(a)}catch(e){}return r&&(mi[e]=r.bind(ca))}("fetch"),i=0,o=0;return pn(n,function(e){var t=e.body.length;i+=t,o++;var r=_objectSpread({body:e.body,method:"POST",referrerPolicy:"origin",headers:n.headers,keepalive:i<=6e4&&o<15},n.fetchOptions);if(!a)return gi("fetch"),Oe("No fetch implementation available");try{return a(n.url,r).then(function(e){return i-=t,o--,{statusCode:e.status,headers:{"x-sentry-rate-limits":e.headers.get("X-Sentry-Rate-Limits"),"retry-after":e.headers.get("Retry-After")}}})}catch(e){return gi("fetch"),i-=t,o--,Oe(e)}})}function Si(e,t,r,n){t={filename:e,function:"<anonymous>"===t?X:t,in_app:!0};return void 0!==r&&(t.lineno=r),void 0!==n&&(t.colno=n),t}function xi(){var t=_objectSpread({console:!0,dom:!0,fetch:!0,history:!0,sentry:!0,xhr:!0},0<arguments.length&&void 0!==arguments[0]?arguments[0]:{});return{name:"Breadcrumbs",setup:function(e){var a,i,o,s,u,c;t.console&&(te("console",function(r){return function(e){if(xt()!==r)return;var t={category:"console",data:{arguments:e.args,logger:"console"},level:Me(e.level),message:n(e.args," ")};if("assert"===e.level){if(!1!==e.args[0])return;t.message="Assertion failed: ".concat(n(e.args.slice(1)," ")||"console.assert"),t.data.arguments=e.args.slice(1)}mn(t,{input:e.args,level:e.level})}}(e)),re("console",ae)),t.dom&&(u=e,c=t.dom,te("dom",function(e){if(xt()===u){var t,r="object"==_typeof(c)?c.serializeAttribute:void 0,n="object"==_typeof(c)&&"number"==typeof c.maxStringLength?c.maxStringLength:void 0;n&&1024<n&&(n=1024),"string"==typeof r&&(r=[r]);try{var a=e.event,a=a&&a.target?a.target:a,i=R(a,{keyAttrs:r,maxStringLength:n}),o=I(a)}catch(e){i="<unknown>"}0!==i.length&&(t={category:"ui.".concat(e.name),message:i},o&&(t.data={"ui.component_name":o}),mn(t,{event:e.event,name:e.name,global:e.global}))}}),re("dom",di)),t.xhr&&bi((s=e,function(e){var t,r,n,a,i,o;xt()===s&&(t=e.startTimestamp,r=e.endTimestamp,o=e.xhr[vi],t&&r&&o&&(n=o.method,a=o.url,i=o.status_code,o=o.body,mn({category:"xhr",data:{method:n,url:a,status_code:i},type:"http"},{xhr:e.xhr,input:o,startTimestamp:t,endTimestamp:r})))})),t.fetch&&de((o=e,function(e){var t,r,n;xt()===o&&(t=e.startTimestamp,!(r=e.endTimestamp)||e.fetchData.url.match(/sentry_key/)&&"POST"===e.fetchData.method||(e.error?mn({category:"fetch",data:e.fetchData,level:"error",type:"http"},{data:e.error,input:e.args,startTimestamp:t,endTimestamp:r}):(n=e.response,mn({category:"fetch",data:_objectSpread(_objectSpread({},e.fetchData),{},{status_code:n&&n.status}),type:"http"},{input:e.args,response:n,startTimestamp:t,endTimestamp:r}))))})),t.history&&fi((i=e,function(e){var t,r,n,a;xt()===i&&(t=e.from,r=e.to,n=Le(Cn.location.href),a=t?Le(t):void 0,e=Le(r),a&&a.path||(a=n),n.protocol===e.protocol&&n.host===e.host&&(r=e.relative),n.protocol===a.protocol&&n.host===a.host&&(t=a.relative),mn({category:"navigation",data:{from:t,to:r}}))})),t.sentry&&e.on("beforeSendEvent",(a=e,function(e){var t,r,n;xt()===a&&mn({category:"sentry."+("transaction"===e.type?"transaction":"event"),event_id:e.event_id,level:e.level,message:(r=(t=e).message,n=t.event_id,r||((t=xe(t))?t.type&&t.value?"".concat(t.type,": ").concat(t.value):t.type||t.value||n||"<unknown>":n||"<unknown>"))},{event:e})}))}}}function ki(){var t=_objectSpread({XMLHttpRequest:!0,eventTarget:!0,requestAnimationFrame:!0,setInterval:!0,setTimeout:!0},0<arguments.length&&void 0!==arguments[0]?arguments[0]:{});return{name:"BrowserApiErrors",setupOnce:function(){t.setTimeout&&U(Cn,"setTimeout",Vi),t.setInterval&&U(Cn,"setInterval",Vi),t.requestAnimationFrame&&U(Cn,"requestAnimationFrame",Xi),t.XMLHttpRequest&&"XMLHttpRequest"in Cn&&U(XMLHttpRequest.prototype,"send",zi);var e=t.eventTarget;e&&(Array.isArray(e)?e:Bi).forEach(Wi)}}}var Ri=/^\s*at (\S+?)(?::(\d+))(?::(\d+))\s*$/i,Ii=/^\s*at (?:(.+?\)(?: \[.+\])?|.*?) ?\((?:address at )?)?(?:async )?((?:<anonymous>|[-a-z]+:|.*bundle|\/)?.*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,Ai=/\((\S*)(?::(\d+))(?::(\d+))\)/,Ni=[30,function(e){var t=Ri.exec(e);if(t){var r=_slicedToArray(t,4),n=r[1],t=r[2],r=r[3];return Si(n,X,+t,+r)}r=Ii.exec(e);if(r){!r[2]||0!==r[2].indexOf("eval")||(a=Ai.exec(r[2]))&&(r[2]=a[1],r[3]=a[2],r[4]=a[3]);var e=_slicedToArray(Gi(r[1]||X,r[2]),2),a=e[0];return Si(e[1],a,r[3]?+r[3]:void 0,r[4]?+r[4]:void 0)}}],Di=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)?((?:[-a-z]+)?:\/.*?|\[native code\]|[^@]*(?:bundle|\d+\.js)|\/[\w\-. /=]+)(?::(\d+))?(?::(\d+))?\s*$/i,Oi=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,Ci=[50,function(e){var t=Di.exec(e);if(t){t[3]&&-1<t[3].indexOf(" > eval")&&((r=Oi.exec(t[3]))&&(t[1]=t[1]||"eval",t[3]=r[1],t[4]=r[2],t[5]=""));var r,n=t[3],e=t[1]||X,e=(r=_slicedToArray(Gi(e,n),2))[0];return Si(n=r[1],e,t[4]?+t[4]:void 0,t[5]?+t[5]:void 0)}}],Li=/^\s*at (?:((?:\[object object\])?.+) )?\(?((?:[-a-z]+):.*?):(\d+)(?::(\d+))?\)?\s*$/i,Pi=[40,function(e){e=Li.exec(e);return e?Si(e[2],e[1]||X,+e[3],e[4]?+e[4]:void 0):void 0}],Mi=/ line (\d+).*script (?:in )?(\S+)(?:: in function (\S+))?$/i,Ui=[10,function(e){e=Mi.exec(e);return e?Si(e[2],e[3]||X,+e[1]):void 0}],ji=/ line (\d+), column (\d+)\s*(?:in (?:<anonymous function: ([^>]+)>|([^)]+))\(.*\))? in (.*):\s*$/i,Fi=[20,function(e){e=ji.exec(e);return e?Si(e[5],e[3]||e[4]||X,+e[1],+e[2]):void 0}],qi=[Ni,Ci],Hi=Y.apply(void 0,qi),Gi=function(e,t){var r=-1!==e.indexOf("safari-extension"),n=-1!==e.indexOf("safari-web-extension");return r||n?[-1!==e.indexOf("@")?e.split("@")[0]:X,(r?"safari-extension:":"safari-web-extension:").concat(t)]:[e,t]},Bi=["EventTarget","Window","Node","ApplicationCache","AudioTrackList","BroadcastChannel","ChannelMergerNode","CryptoOperation","EventSource","FileReader","HTMLUnknownElement","IDBDatabase","IDBRequest","IDBTransaction","KeyOperation","MediaController","MessagePort","ModalWindow","Notification","SVGElementInstance","Screen","SharedWorker","TextTrack","TextTrackCue","TextTrackList","WebSocket","WebSocketWorker","Worker","XMLHttpRequest","XMLHttpRequestEventTarget","XMLHttpRequestUpload"];function Vi(a){return function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=t[0];return t[0]=Pn(n,{mechanism:{data:{function:Q(a)},handled:!1,type:"instrument"}}),a.apply(this,t)}}function Xi(t){return function(e){return t.apply(this,[Pn(e,{mechanism:{data:{function:"requestAnimationFrame",handler:Q(t)},handled:!1,type:"instrument"}})])}}function zi(a){return function(){for(var e=this,t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];return["onload","onerror","onprogress","onreadystatechange"].forEach(function(n){n in e&&"function"==typeof e[n]&&U(e,n,function(e){var t={mechanism:{data:{function:n,handler:Q(e)},handled:!1,type:"instrument"}},r=q(e);return r&&(t.mechanism.data.handler=Q(r)),Pn(e,t)})}),a.apply(this,r)}}function Wi(a){var e=Cn[a]&&Cn[a].prototype;e&&e.hasOwnProperty&&e.hasOwnProperty("addEventListener")&&(U(e,"addEventListener",function(n){return function(e,t,r){try{"function"==typeof t.handleEvent&&(t.handleEvent=Pn(t.handleEvent,{mechanism:{data:{function:"handleEvent",handler:Q(t),target:a},handled:!1,type:"instrument"}}))}catch(e){}return n.apply(this,[e,Pn(t,{mechanism:{data:{function:"addEventListener",handler:Q(t),target:a},handled:!1,type:"instrument"}}),r])}}),U(e,"removeEventListener",function(i){return function(e,t,r){var n=t;try{var a=n&&n.__sentry_wrapped__;a&&i.call(this,e,a,r)}catch(e){}return i.call(this,e,n,r)}}))}function Yi(){var t=_objectSpread({onerror:!0,onunhandledrejection:!0},0<arguments.length&&void 0!==arguments[0]?arguments[0]:{});return{name:"GlobalHandlers",setupOnce:function(){Error.stackTraceLimit=50},setup:function(e){var n,s;t.onerror&&(s=e,be(function(e){var t,r,n,a=$i(),i=a.stackParser,o=a.attachStacktrace;xt()!==s||0<Ln||(t=e.msg,r=e.url,n=e.line,a=e.column,e=e.error,t=Bn(i,e||t,void 0,o,!1),o=r,r=n,n=a,a=(a=(a=(a=(a=t.exception=t.exception||{}).values=a.values||[])[0]=a[0]||{}).stacktrace=a.stacktrace||{}).frames=a.frames||[],n=isNaN(parseInt(n,10))?void 0:n,r=isNaN(parseInt(r,10))?void 0:r,o=p(o)&&0<o.length?o:function(){try{return x.document.location.href}catch(e){return""}}(),0===a.length&&a.push({colno:n,filename:o,function:X,in_app:!0,lineno:r}),(t=t).level="error",Gr(t,{originalException:e,mechanism:{handled:!1,type:"onerror"}}))})),t.onunhandledrejection&&(n=e,Ee(function(e){var t=$i(),r=t.stackParser,t=t.attachStacktrace;xt()!==n||0<Ln||(e=function(e){if(f(e))return e;try{if("reason"in e)return e.reason;if("detail"in e&&"reason"in e.detail)return e.detail.reason}catch(e){}return e}(e),(t=f(e)?{exception:{values:[{type:"UnhandledRejection",value:"Non-Error promise rejection captured with value: ".concat(String(e))}]}}:Bn(r,e,void 0,t,!0)).level="error",Gr(t,{originalException:e,mechanism:{handled:!1,type:"onunhandledrejection"}}))}))}}}function $i(){var e=xt();return e&&e.getOptions()||{stackParser:function(){return[]},attachStacktrace:!1}}function Ki(){return{name:"HttpContext",preprocessEvent:function(e){var t,r,n;(Cn.navigator||Cn.location||Cn.document)&&(t=e.request&&e.request.url||Cn.location&&Cn.location.href,r=(Cn.document||{}).referrer,n=(Cn.navigator||{}).userAgent,n=_objectSpread(_objectSpread(_objectSpread({},e.request&&e.request.headers),r&&{Referer:r}),n&&{"User-Agent":n}),n=_objectSpread(_objectSpread(_objectSpread({},e.request),t&&{url:t}),{},{headers:n}),e.request=n)}}}function Qi(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},n=e.limit||5,a=e.key||"cause";return{name:"LinkedErrors",preprocessEvent:function(e,t,r){r=r.getOptions();i(Mn,r.stackParser,r.maxValueLength,a,n,e,t)}}}function Ji(e){return[vn(),gn(),ki(),xi(),Yi(),Qi(),wn(),Ki()]}var t={increment:function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:1,r=2<arguments.length?arguments[2]:void 0;In.increment(On,e,t,r)},distribution:function(e,t,r){In.distribution(On,e,t,r)},set:function(e,t,r){In.set(On,e,t,r)},gauge:function(e,t,r){In.gauge(On,e,t,r)},timing:function(e,t){var r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:"second",n=3<arguments.length?arguments[3]:void 0;return In.timing(On,e,t,r,n)}},Zi=new WeakMap,eo=new Map,to={traceFetch:!0,traceXHR:!0,enableHTTPTimings:!0};function ro(e,t){function n(e){return function(e,t){var r,n,a=Cn.location&&Cn.location.href;if(a){try{r=new URL(e,a),n=new URL(a).origin}catch(e){return!1}var i=r.origin===n;return t?m(r.toString(),t)||i&&m(r.pathname,t):i}i=!!e.match(/^\/(?!\/)/);return t?m(e,t):i}(e,s)}var r=_objectSpread({traceFetch:to.traceFetch,traceXHR:to.traceXHR},t),a=r.traceFetch,i=r.traceXHR,t=r.shouldCreateSpanForRequest,o=r.enableHTTPTimings,s=r.tracePropagationTargets,u="function"==typeof t?t:function(e){return!0},c={};a&&(e.addEventProcessor(function(e){return"transaction"===e.type&&e.spans&&e.spans.forEach(function(e){var t;"http.client"!==e.op||(t=eo.get(e.span_id))&&(e.timestamp=t/1e3,eo.delete(e.span_id))}),e}),te(e="fetch-body-resolved",function(e){if(e.response){var t=Zi.get(e.response);t&&e.endTimestamp&&eo.set(t,e.endTimestamp)}}),re(e,function(){return pe(fe),0}),de(function(e){var t,r=function(e,t,r,n,a){var i=4<arguments.length&&void 0!==a?a:"auto.http.browser";if(e.fetchData){var o=ir()&&t(e.fetchData.url);if(!e.endTimestamp||!o){var s,u=wt(),c=xt(),l=e.fetchData,d=l.method,p=l.url,f=function(e){try{return new URL(e).href}catch(e){return}}(p),h=f?Le(f).host:void 0,l=!!Jt(),h=o&&l?wr({name:"".concat(d," ").concat(p),attributes:(_defineProperty(s={url:p,type:"fetch","http.method":d,"http.url":f,"server.address":h},Dt,i),_defineProperty(s,Nt,"http.client"),s)}):new or;return e.fetchData.__span=h.spanContext().spanId,n[h.spanContext().spanId]=h,r(e.fetchData.url)&&c&&(s=e.args[0],e.args[1]=e.args[1]||{},(m=e.args[1]).headers=function(e,t,r,n,a){var i=_objectSpread(_objectSpread({},Et().getPropagationContext()),r.getPropagationContext()),o=i.traceId,s=i.spanId,r=i.sampled,i=i.dsc,r=a?Ht(a):ze(o,s,r),t=Ge(i||(a?pr(a):dr(o,t))),e=n.headers||("undefined"!=typeof Request&&b(e,Request)?e.headers:void 0);if(e){if("undefined"!=typeof Headers&&b(e,Headers)){var u=new Headers(e);return u.append("sentry-trace",r),t&&u.append(Ue,t),u}if(Array.isArray(e)){var c=[].concat(_toConsumableArray(e),[["sentry-trace",r]]);return t&&c.push([Ue,t]),c}u="baggage"in e?e.baggage:void 0,c=[];return Array.isArray(u)?c.push.apply(c,_toConsumableArray(u)):u&&c.push(u),t&&c.push(t),_objectSpread(_objectSpread({},e),{},{"sentry-trace":r,baggage:0<c.length?c.join(","):void 0})}return{"sentry-trace":r,baggage:t}}(s,c,u,m,ir()&&l?h:void 0)),h}var m,u=e.fetchData.__span;!u||(m=n[u])&&(l=m,(h=e).response?(Ft(l,h.response.status),!(m=h.response&&h.response.headers&&h.response.headers.get("content-length"))||0<(m=parseInt(m))&&l.setAttribute("http.response_content_length",m)):h.error&&l.setStatus({code:jt,message:"internal_error"}),l.end(),delete n[u])}}(e,u,n,c);e.response&&e.fetchData.__span&&Zi.set(e.response,e.fetchData.__span),r&&(e=(t=io(e.fetchData.url))?Le(t).host:void 0,r.setAttributes({"http.url":t,"server.address":e})),o&&r&&no(r)})),i&&bi(function(e){e=function(e,t,r,n,a){var i=e.xhr,o=i&&i[vi];if(i&&!i.__sentry_own_request__&&o){var s=ir()&&t(o.url);if(!e.endTimestamp||!s){var u=io(o.url),c=u?Le(u).host:void 0,t=!!Jt(),l=s&&t?wr({name:"".concat(o.method," ").concat(o.url),attributes:(_defineProperty(a={type:"xhr","http.method":o.method,"http.url":u,url:o.url,"server.address":c},Dt,"auto.http.browser"),_defineProperty(a,Nt,"http.client"),a)}):new or;i.__sentry_xhr_span_id__=l.spanContext().spanId,n[i.__sentry_xhr_span_id__]=l;e=xt();return i.setRequestHeader&&r(o.url)&&e&&(s=i,u=e,c=ir()&&t?l:void 0,a=wt(),r=_objectSpread(_objectSpread({},Et().getPropagationContext()),a.getPropagationContext()),e=r.traceId,t=r.spanId,a=r.sampled,r=r.dsc,function(e,t,r){try{e.setRequestHeader("sentry-trace",t),r&&e.setRequestHeader(Ue,r)}catch(e){}}(s,c&&ir()?Ht(c):ze(e,t,a),Ge(r||(c?pr(c):dr(e,u))))),l}l=i.__sentry_xhr_span_id__;!l||(i=n[l])&&void 0!==o.status_code&&(Ft(i,o.status_code),i.end(),delete n[l])}}(e,u,n,c);o&&e&&no(e)})}function no(a){var i,o=(Vt(a).data||{}).url;o&&"string"==typeof o&&(i=Pa("resource",function(e){e.entries.forEach(function(e){var t,r,n;"resource"===(n=e).entryType&&"initiatorType"in n&&"string"==typeof n.nextHopProtocol&&("fetch"===n.initiatorType||"xmlhttprequest"===n.initiatorType)&&e.name.endsWith(o)&&(r=function(e){var t="unknown",r="unknown",n="",a=_createForOfIteratorHelper(e);try{for(a.s();!(o=a.n()).done;){var i=o.value;if("/"===i){var o=_slicedToArray(e.split("/"),2),t=o[0],r=o[1];break}if(!isNaN(Number(i))){t="h"===n?"http":n,r=e.split(n)[1];break}n+=i}}catch(e){a.e(e)}finally{a.f()}return n===e&&(t=n),{name:t,version:r}}((t=e).nextHopProtocol),n=r.name,e=r.version,(r=[]).push(["network.protocol.version",e],["network.protocol.name",n]),(le?[].concat(r,[["http.request.redirect_start",ao(t.redirectStart)],["http.request.fetch_start",ao(t.fetchStart)],["http.request.domain_lookup_start",ao(t.domainLookupStart)],["http.request.domain_lookup_end",ao(t.domainLookupEnd)],["http.request.connect_start",ao(t.connectStart)],["http.request.secure_connection_start",ao(t.secureConnectionStart)],["http.request.connection_end",ao(t.connectEnd)],["http.request.request_start",ao(t.requestStart)],["http.request.response_start",ao(t.responseStart)],["http.request.response_end",ao(t.responseEnd)]]):r).forEach(function(e){return a.setAttribute.apply(a,_toConsumableArray(e))}),setTimeout(i))})}))}function ao(e){var t=0<arguments.length&&void 0!==e?e:0;return((le||performance.timeOrigin)+t)/1e3}function io(e){try{return new URL(e,Cn.location.origin).href}catch(e){return}}var oo=_objectSpread(_objectSpread({},Ar),{},{instrumentNavigation:!0,instrumentPageLoad:!0,markBackgroundSpan:!0,enableLongTask:!0,enableLongAnimationFrame:!0,enableInp:!0,_experiments:{}},to);function so(e,t,r){e.emit("startPageLoadSpan",t,r),wt().setTransactionName(t.name);t=Jt();return"pageload"===(t&&Vt(t).op)?t:void 0}function uo(e,t){Et().setPropagationContext(tt()),wt().setPropagationContext(tt()),e.emit("startNavigationSpan",t),wt().setTransactionName(t.name);t=Jt();return"navigation"===(t&&Vt(t).op)?t:void 0}function co(e){e="meta[name=".concat(e,"]"),e=x.document&&x.document.querySelector?x.document.querySelector(e):null;return e?e.getAttribute("content"):void 0}return er(),e.BrowserClient=ua,e.SDK_VERSION=s,e.SEMANTIC_ATTRIBUTE_SENTRY_OP=Nt,e.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN=Dt,e.SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE=At,e.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE=It,e.Scope=ht,e.WINDOW=Cn,e.addBreadcrumb=mn,e.addEventProcessor=function(e){Et().addEventProcessor(e)},e.addIntegration=function(e){var t=xt();t&&t.addIntegration(e)},e.breadcrumbsIntegration=xi,e.browserApiErrorsIntegration=ki,e.browserTracingIntegration=function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};er();var t=_objectSpread(_objectSpread({},oo),e),l=t.enableInp,r=t.enableLongTask,n=t.enableLongAnimationFrame,e=t._experiments,d=e.enableInteractions,s=e.enableStandaloneClsSpans,u=t.beforeStartSpan,p=t.idleTimeout,f=t.finalTimeout,h=t.childSpanTimeout,m=t.markBackgroundSpan,g=t.traceFetch,v=t.traceXHR,b=t.shouldCreateSpanForRequest,y=t.enableHTTPTimings,_=t.instrumentPageLoad,w=t.instrumentNavigation,c=ri({recordClsStandaloneSpans:s||!1});!l||Ya()&&le&&Ga("inp",function(e){var t,r,n,a,i,o=e.metric;null==o.value||(t=o.entries.find(function(e){return e.duration===o.value&&Ei[e.name]}))&&(r=t.interactionId,n=Ei[t.name],a=$a(le+t.startTime),i=o.value/1e3,e=(e=Jt())?Qt(e):void 0,e=(e=(null!=r?wi.get(r):void 0)||e)?Vt(e).description:wt().getScopeData().transactionName,et([e=Wa({name:R(t.target),transaction:e,attributes:V((_defineProperty(e={},Dt,"auto.http.browser.inp"),_defineProperty(e,Nt,"ui.interaction.".concat(n)),_defineProperty(e,Pt,t.duration),e)),startTime:a}),"optionalAccess",function(e){return e.addEvent},"call",function(e){return e("inp",(_defineProperty(e={},Ct,"millisecond"),_defineProperty(e,Lt,o.value),e))}]),et([e,"optionalAccess",function(e){return e.end},"call",function(e){return e(a+i)}]))},Ha,sa),n&&S.PerformanceObserver&&PerformanceObserver.supportedEntryTypes&&PerformanceObserver.supportedEntryTypes.includes("long-animation-frame")?new PerformanceObserver(function(e){if(Jt()){var t=_createForOfIteratorHelper(e.getEntries());try{for(t.s();!(o=t.n()).done;){var r,n,a,i,o,s,u,c=o.value;c.scripts[0]&&(r=$a(le+c.startTime),n=c.duration/1e3,u=_defineProperty({},Dt,"auto.ui.browser.metrics"),a=(s=c.scripts[0]).invoker,i=s.invokerType,o=s.sourceURL,c=s.sourceFunctionName,s=s.sourceCharPosition,u["browser.script.invoker"]=a,u["browser.script.invoker_type"]=i,o&&(u["code.filepath"]=o),c&&(u["code.function"]=c),-1!==s&&(u["browser.script.source_char_position"]=s),(u=wr({name:"Main UI thread blocked",op:"ui.long-animation-frame",startTime:r,attributes:u}))&&u.end(r+n))}}catch(e){t.e(e)}finally{t.f()}}}).observe({type:"long-animation-frame",buffered:!0}):r&&Pa("longtask",function(e){e=e.entries;if(Jt()){var t=_createForOfIteratorHelper(e);try{for(t.s();!(a=t.n()).done;){var r=a.value,n=$a(le+r.startTime),a=r.duration/1e3,r=wr({name:"Main UI thread blocked",op:"ui.long-task",startTime:n,attributes:_defineProperty({},Dt,"auto.ui.browser.metrics")});r&&r.end(n+a)}}catch(e){t.e(e)}finally{t.f()}}}),d&&Pa("event",function(e){e=e.entries;if(Jt()){var t=_createForOfIteratorHelper(e);try{for(t.s();!(a=t.n()).done;){var r,n,a,i=a.value;"click"===i.name&&(r=$a(le+i.startTime),n=i.duration/1e3,a={name:R(i.target),op:"ui.interaction.".concat(i.name),startTime:r,attributes:_defineProperty({},Dt,"auto.ui.browser.metrics")},(i=I(i.target))&&(a.attributes["ui.component_name"]=i),(a=wr(a))&&a.end(r+n))}}catch(e){t.e(e)}finally{t.f()}}});var E={name:void 0,source:void 0};function T(e,t){var r="pageload"===t.op,n=u?u(t):t,a=n.attributes||{};t.name!==n.name&&(a[It]="custom",n.attributes=a),E.name=n.name,E.source=a[It];var i=Lr(n,{idleTimeout:p,finalTimeout:f,childSpanTimeout:h,disableAutoFinish:r,beforeSpanEnd:function(e){c(),ni(e,{recordClsOnPageloadSpan:!s})}});function o(){["interactive","complete"].includes(Cn.document.readyState)&&e.emit("idleSpanEnableAutoFinish",i)}return r&&Cn.document&&(Cn.document.addEventListener("readystatechange",function(){o()}),o()),i}return{name:"BrowserTracing",afterAllSetup:function(r){var e,n,t,a,i,o,s,u=Cn.location&&Cn.location.href;function c(e){var e=e.entries,t=Jt(),r=t&&Qt(t);e.forEach(function(e){var t;"duration"in e&&r&&(null==(t=e.interactionId)||wi.has(t)||(10<_i.length&&(e=_i.shift(),wi.delete(e)),_i.push(t),wi.set(t,r)))})}r.on("startNavigationSpan",function(e){xt()===r&&(n&&!Vt(n).timestamp&&n.end(),n=T(r,_objectSpread({op:"navigation"},e)))}),r.on("startPageLoadSpan",function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};xt()===r&&(n&&!Vt(n).timestamp&&n.end(),t=Xe(t.sentryTrace||co("sentry-trace"),t.baggage||co("baggage")),wt().setPropagationContext(t),n=T(r,_objectSpread({op:"pageload"},e)))}),r.on("spanEnd",function(e){var t,r=Vt(e).op;e!==Qt(e)||"navigation"!==r&&"pageload"!==r||(r=(t=wt()).getPropagationContext(),t.setPropagationContext(_objectSpread(_objectSpread({},r),{},{sampled:void 0!==r.sampled?r.sampled:Xt(e),dsc:r.dsc||pr(e)})))}),Cn.location&&(_&&so(r,{name:Cn.location.pathname,startTime:le?le/1e3:void 0,attributes:(_defineProperty(e={},It,"url"),_defineProperty(e,Dt,"auto.pageload.browser"),e)}),w&&fi(function(e){var t=e.to,e=e.from;void 0===e&&u&&-1!==u.indexOf(t)?u=void 0:e!==t&&(u=void 0,uo(r,{name:Cn.location.pathname,attributes:(_defineProperty(t={},It,"url"),_defineProperty(t,Dt,"auto.navigation.browser"),t)}))})),m&&Cn&&Cn.document&&Cn.document.addEventListener("visibilitychange",function(){var e,t=Jt();t&&(e=Qt(t),Cn.document.hidden&&e&&((t=Vt(e)).op,t.status||e.setStatus({code:jt,message:"cancelled"}),e.setAttribute("sentry.cancellation_reason","document.hidden"),e.end()))}),d&&(t=p,a=f,i=h,o=E,Cn.document&&addEventListener("click",function(){var e=Jt(),e=e&&Qt(e);if(e){e=Vt(e).op;if(["navigation","pageload"].includes(e))return}s&&(s.setAttribute(Ot,"interactionInterrupted"),s.end(),s=void 0),o.name&&(s=Lr({name:o.name,op:"ui.action.click",attributes:_defineProperty({},It,o.source||"url")},{idleTimeout:t,finalTimeout:a,childSpanTimeout:i}))},{once:!1,capture:!0})),l&&(Pa("event",c),Pa("first-input",c)),ro(r,{traceFetch:g,traceXHR:v,tracePropagationTargets:r.getOptions().tracePropagationTargets,shouldCreateSpanForRequest:b,enableHTTPTimings:y})}}},e.captureEvent=Gr,e.captureException=Hr,e.captureMessage=function(e,t){var r="string"==typeof t?t:void 0,t="string"!=typeof t?{captureContext:t}:void 0;return wt().captureMessage(e,r,t)},e.captureSession=Zr,e.captureUserFeedback=function(e){var t=xt();t&&t.captureUserFeedback(e)},e.chromeStackLineParser=Ni,e.close=function(){var t=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var r;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return r=xt(),e.abrupt("return",r?r.close(t):Promise.resolve(!1));case 2:case"end":return e.stop()}},e)}));return function(e){return t.apply(this,arguments)}}(),e.continueTrace=function(e,r){var n=e.sentryTrace,a=e.baggage;return St(function(e){var t=Xe(n,a);return e.setPropagationContext(t),r()})},e.createTransport=pn,e.createUserFeedbackEnvelope=Xn,e.dedupeIntegration=wn,e.defaultStackLineParsers=qi,e.defaultStackParser=Hi,e.endSession=Qr,e.eventFromException=Hn,e.eventFromMessage=Gn,e.exceptionFromError=Mn,e.feedbackAsyncIntegration=it,e.feedbackIntegration=it,e.flush=function(){var t=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var r;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return r=xt(),e.abrupt("return",r?r.flush(t):Promise.resolve(!1));case 2:case"end":return e.stop()}},e)}));return function(e){return t.apply(this,arguments)}}(),e.forceLoad=function(){},e.functionToStringIntegration=gn,e.geckoStackLineParser=Ci,e.getActiveSpan=Jt,e.getClient=xt,e.getCurrentHub=function(){return{bindClient:function(e){wt().setClient(e)},withScope:St,getClient:xt,getScope:wt,getIsolationScope:Et,captureException:function(e,t){return wt().captureException(e,t)},captureMessage:function(e,t,r){return wt().captureMessage(e,t,r)},captureEvent:Gr,addBreadcrumb:mn,setUser:Yr,setTags:zr,setTag:Wr,setExtra:Xr,setExtras:Vr,setContext:Br,getIntegration:function(e){var t=xt();return t&&t.getIntegrationByName(e.id)||null},startSession:Kr,endSession:Qr,captureSession:function(e){if(e)return Qr();var t;t=wt(),e=xt(),t=t.getSession(),e&&t&&e.captureSession(t)}}},e.getCurrentScope=wt,e.getDefaultIntegrations=Ji,e.getGlobalScope=Tt,e.getIsolationScope=Et,e.getRootSpan=Qt,e.getSpanDescendants=Kt,e.globalHandlersIntegration=Yi,e.httpContextIntegration=Ki,e.inboundFiltersIntegration=vn,e.init=function(){var e=function(e){var t=0<arguments.length&&void 0!==e?e:{},r={defaultIntegrations:Ji(),release:"string"==typeof __SENTRY_RELEASE__?__SENTRY_RELEASE__:Cn.SENTRY_RELEASE&&Cn.SENTRY_RELEASE.id?Cn.SENTRY_RELEASE.id:void 0,autoSessionTracking:!0,sendClientReports:!0};return null==t.defaultIntegrations&&delete t.defaultIntegrations,_objectSpread(_objectSpread({},r),t)}(0<arguments.length&&void 0!==arguments[0]?arguments[0]:{});if(!function(){var e=void 0!==Cn.window&&Cn;if(e){var t=e[e.chrome?"chrome":"browser"],r=t&&t.runtime&&t.runtime.id,n=Cn.location&&Cn.location.href||"",t=!!r&&Cn===Cn.top&&["chrome-extension:","moz-extension:","ms-browser-extension:","safari-web-extension:"].some(function(e){return n.startsWith("".concat(e,"//"))}),e=void 0!==e.nw;return r&&!t&&!e}}()){var t,n,r=_objectSpread(_objectSpread({},e),{},{stackParser:(r=e.stackParser||Hi,Array.isArray(r)?Y.apply(void 0,_toConsumableArray(r)):r),integrations:(r=(t=e).defaultIntegrations||[],t=t.integrations,r.forEach(function(e){e.isDefaultInstance=!0}),t=Array.isArray(t)?[].concat(_toConsumableArray(r),_toConsumableArray(t)):"function"==typeof t?Ae(t(r)):r,-1<(t=(r=(n={},t.forEach(function(e){var t=e.name,r=n[t];r&&!r.isDefaultInstance&&e.isDefaultInstance||(n[t]=e)}),Object.values(n))).findIndex(function(e){return"Debug"===e.name}))&&(t=_slicedToArray(r.splice(t,1),1)[0],r.push(t)),r),transport:e.transport||Ti}),r=function(e,t){!0===t.debug&&N(function(){}),wt().update(t.initialScope);t=new e(t);return ln(t),t.init(),t}(ua,r);return!e.autoSessionTracking||void 0!==Cn.document&&(Kr({ignoreDuration:!0}),Zr(),fi(function(e){var t=e.from,e=e.to;void 0!==t&&t!==e&&(Kr({ignoreDuration:!0}),Zr())})),r}N(function(){})},e.isInitialized=function(){return!!xt()},e.lastEventId=$r,e.linkedErrorsIntegration=Qi,e.makeFetchTransport=Ti,e.metrics=t,e.onLoad=function(e){e()},e.opera10StackLineParser=Ui,e.opera11StackLineParser=Fi,e.parameterize=function(e){for(var t=arguments.length,r=new Array(1<t?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];var a=new String(String.raw.apply(String,[e].concat(r)));return a.__sentry_template_string__=e.join("\0").replace(/%/g,"%%").replace(/\0/g,"%s"),a.__sentry_template_values__=r,a},e.replayIntegration=function(e){return N(function(){}),_objectSpread({name:"Replay"},ot.reduce(function(e,t){return e[t]=rt,e},{}))},e.setContext=Br,e.setCurrentClient=ln,e.setExtra=Xr,e.setExtras=Vr,e.setMeasurement=hr,e.setTag=Wr,e.setTags=zr,e.setUser=Yr,e.showReportDialog=function(){var e,t,o,r,n,a,i=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};!Cn.document||(o=(t=(e=wt()).getClient())&&t.getDsn())&&(e&&(i.user=_objectSpread(_objectSpread({},e.getUser()),i.user)),i.eventId||(r=$r())&&(i.eventId=r),(r=Cn.document.createElement("script")).async=!0,r.crossOrigin="anonymous",r.src=function(e){var t=P(o);if(!t)return"";var r,n="".concat(tn(t),"embed/error-page/"),a="dsn=".concat(C(t));for(r in e)if("dsn"!==r&&"onClose"!==r)if("user"===r){var i=e.user;if(!i)continue;i.name&&(a+="&name=".concat(encodeURIComponent(i.name))),i.email&&(a+="&email=".concat(encodeURIComponent(i.email)))}else a+="&".concat(encodeURIComponent(r),"=").concat(encodeURIComponent(e[r]));return"".concat(n,"?").concat(a)}(i),i.onLoad&&(r.onload=i.onLoad),(n=i.onClose)&&(a=function e(t){if("__sentry_reportdialog_closed__"===t.data)try{n()}finally{Cn.removeEventListener("message",e)}},Cn.addEventListener("message",a)),(a=Cn.document.head||Cn.document.body)&&a.appendChild(r))},e.spanToBaggageHeader=function(e){return Ge(pr(e))},e.spanToJSON=Vt,e.spanToTraceHeader=Ht,e.startBrowserTracingNavigationSpan=uo,e.startBrowserTracingPageLoadSpan=so,e.startInactiveSpan=wr,e.startNewTrace=function(t){return St(function(e){return e.setPropagationContext(tt()),Er(null,t)})},e.startSession=Kr,e.startSpan=function(n,a){var e=xr();if(e.startSpan)return e.startSpan(n,a);var i=Sr(n),o=n.forceTransaction,t=n.parentSpan;return St(n.scope,function(){return Ir(t)(function(){var e=wt(),t=Rr(e),r=n.onlyIfParent&&!t?new or:Tr({parentSpan:t,spanArguments:i,forceTransaction:o,scope:e});return pt(e,r),sr(function(){return a(r)},function(){var e=Vt(r).status;!r.isRecording()||e&&"ok"!==e||r.setStatus({code:jt,message:"internal_error"})},function(){return r.end()})})})},e.startSpanManual=_r,e.winjsStackLineParser=Pi,e.withActiveSpan=Er,e.withIsolationScope=function(){for(var e=_t(st()),t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];if(2!==r.length)return e.withIsolationScope(r[0]);var a=r[0],i=r[1];return a?e.withSetIsolationScope(a,i):e.withIsolationScope(i)},e.withScope=St,e}({}),GA_ENDPOINT="https://www.google-analytics.com/mp/collect",GA_DEBUG_ENDPOINT="https://www.google-analytics.com/debug/mp/collect",DEFAULT_ENGAGEMENT_TIME_MSEC=100,SESSION_EXPIRATION_IN_MIN=30,Analytics=function(){"use strict";function n(e,t){var r=2<arguments.length&&void 0!==arguments[2]&&arguments[2];_classCallCheck(this,n),this.MEASUREMENT_ID=e,this.API_SECRET=t,this.debug=r}var t,r,a,e,i;return _createClass(n,[{key:"getOrCreateClientId",value:(i=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,chrome.storage.local.get("clientId");case 2:if(t=e.sent,t=t.clientId){e.next=8;break}return t=self.crypto.randomUUID(),e.next=8,chrome.storage.local.set({clientId:t});case 8:return e.abrupt("return",t);case 9:case"end":return e.stop()}},e)})),function(){return i.apply(this,arguments)})},{key:"getOrCreateSessionId",value:(e=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t,r,n;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,chrome.storage.session.get("sessionData");case 2:if(n=e.sent,t=n.sessionData,r=Date.now(),!t||!t.timestamp){e.next=14;break}if(n=(r-t.timestamp)/6e4,!(SESSION_EXPIRATION_IN_MIN<n)){e.next=11;break}t=null,e.next=14;break;case 11:return t.timestamp=r,e.next=14,chrome.storage.session.set({sessionData:t});case 14:if(t){e.next=18;break}return t={session_id:r.toString(),timestamp:r.toString()},e.next=18,chrome.storage.session.set({sessionData:t});case 18:return e.abrupt("return",t.session_id);case 19:case"end":return e.stop()}},e)})),function(){return e.apply(this,arguments)})},{key:"fireEvent",value:(a=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var r,n,a=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(r=1<a.length&&void 0!==a[1]?a[1]:{},t=t.replace("-","_"),r.session_id){e.next=7;break}return e.next=6,this.getOrCreateSessionId();case 6:r.session_id=e.sent;case 7:return r.engagement_time_msec||(r.engagement_time_msec=DEFAULT_ENGAGEMENT_TIME_MSEC),e.prev=8,e.t0=fetch,e.t1="".concat(this.debug?GA_DEBUG_ENDPOINT:GA_ENDPOINT,"?measurement_id=").concat(this.MEASUREMENT_ID,"&api_secret=").concat(this.API_SECRET),e.t2=JSON,e.next=14,this.getOrCreateClientId();case 14:return e.t3=e.sent,e.t4=[{name:t,params:r}],e.t5={client_id:e.t3,events:e.t4},e.t6=e.t2.stringify.call(e.t2,e.t5),e.t7={method:"POST",body:e.t6},e.next=21,(0,e.t0)(e.t1,e.t7);case 21:if(n=e.sent,this.debug){e.next=24;break}return e.abrupt("return");case 24:return e.t8=console,e.next=27,n.text();case 27:e.t9=e.sent,e.t8.log.call(e.t8,e.t9),e.next=34;break;case 31:e.prev=31,e.t10=e.catch(8);case 34:case"end":return e.stop()}},e,this,[[8,31]])})),function(e){return a.apply(this,arguments)})},{key:"firePageViewEvent",value:(r=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,r){var n,a=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return n=2<a.length&&void 0!==a[2]?a[2]:{},e.abrupt("return",this.fireEvent("page_view",_objectSpread({page_title:t,page_location:r},n)));case 2:case"end":return e.stop()}},e,this)})),function(e,t){return r.apply(this,arguments)})},{key:"fireErrorEvent",value:(t=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var r,n=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return r=1<n.length&&void 0!==n[1]?n[1]:{},e.abrupt("return",this.fireEvent("extension_error",_objectSpread(_objectSpread({},t),r)));case 2:case"end":return e.stop()}},e,this)})),function(e){return t.apply(this,arguments)})}]),n}(),Ga=function(){"use strict";function r(e,t){_classCallCheck(this,r),this.analytics=new Analytics(e,t),this.initListeners(),this.extraParamsCallbcak=null}return _createClass(r,[{key:"initListeners",value:function(){var i=this;browser.runtime.onMessage.addListener(function(e,t,r){if("ga-msg"==e.message){var n,a=(null==t||null===(n=t.tab)||void 0===n?void 0:n.url)||"";switch(e.type){case"event":i.sendGaEvent(e.name,a,e.params);break;case"exception":i.sendGaException(e.error,a,e.params);break;case"pageview":i.sendGaPreview(e.title,e.location,e.params)}r()}})}},{key:"setExtraParamsCallbcak",value:function(e){this.extraParamsCallbcak=e}},{key:"getEextraParams",value:function(){return this.extraParamsCallbcak?this.extraParamsCallbcak():{}}},{key:"sendGaPreview",value:function(e,t){var r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{};return this.analytics.firePageViewEvent(e,t,r)}},{key:"sendGaEvent",value:function(e,t){var r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{},n=this.getEextraParams();return this.analytics.fireEvent(e,_objectSpread(_objectSpread(_objectSpread({tabUrl:t},r),n),{},{version:chrome.runtime.getManifest().version}))}},{key:"sendGaException",value:function(e,t){var r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{},n=this.getEextraParams();return this.analytics.fireErrorEvent({error:e},_objectSpread(_objectSpread(_objectSpread({tabUrl:t},r),n),{},{version:chrome.runtime.getManifest().version}))}}]),r}();window.DownloadBuffers=(_class4=function(){"use strict";function c(e){_classCallCheck(this,c),_defineProperty(this,"buffers",[]),_defineProperty(this,"bufferSize",0),_defineProperty(this,"inited",!1);var t=e.directory,r=e.tabId,n=e.tabUrl,a=e.uniqueId,i=e.title,e=e.extension;t?(this.directory=t,this.bufferType=c.BUFFER_TYPE.USER_DIR):this.bufferType=c.BUFFER_TYPE.BUFFER_TYPE,this.tabId=r,this.uniqueId=a,this.tabUrl=n||location.href,this.title=middleTruncate(sanitizeFilename(i)),this.extension=e,this.dirName=".t".concat(this.tabId,"-e").concat(browser.runtime.id.substr(0,8),"-u").concat(this.uniqueId,"-d").concat(Date.now())}var e,t,r,n,a,i,o,s,u,l,d,p,f;return _createClass(c,[{key:"tempFileName",value:function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:null,t=1<arguments.length&&void 0!==arguments[1]&&arguments[1];return(null==e?"".concat(this.title,"."):"".concat(this.title,"-").concat(e,".")).concat(t?"raw.":"").concat(this.extension)}},{key:"opfsHsEnoughSpace",value:(f=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var r,n;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,navigator.storage.estimate();case 3:if(n=e.sent,r=n.quota,r=void 0===r?0:r,n=n.usage,1.5*t<r-(void 0===n?0:n))return e.abrupt("return",!0);e.next=10;break;case 10:e.next=15;break;case 12:e.prev=12,e.t0=e.catch(0);case 15:return e.abrupt("return",!1);case 16:case"end":return e.stop()}},e,null,[[0,12]])})),function(e){return f.apply(this,arguments)})},{key:"init",value:(p=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t,r,n=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(t=0<n.length&&void 0!==n[0]?n[0]:0,this.inited)return e.abrupt("return");e.next=4;break;case 4:if(this.inited=!0,!(0<(this.estimatedSize=t)&&t<524288e3)){e.next=10;break}0==this.bufferSize&&(this.bufferType=c.BUFFER_TYPE.BUFFERS),e.next=44;break;case 10:if(this.directory)return e.next=13,this.directory.getDirectoryHandle(this.dirName,{create:!0});e.next=16;break;case 13:this.subDirectory=e.sent,e.next=44;break;case 16:return e.next=18,this.opfsHsEnoughSpace(t);case 18:if(r=e.sent){e.next=25;break}return e.next=22,c.clearOldTempFiles(this.directory,2);case 22:return e.next=24,this.opfsHsEnoughSpace(t);case 24:r=e.sent;case 25:if(r)return e.prev=26,e.next=29,navigator.storage.getDirectory();e.next=42;break;case 29:return this.directory=e.sent,e.next=32,this.directory.getDirectoryHandle(this.dirName,{create:!0});case 32:this.subDirectory=e.sent,this.bufferType=c.BUFFER_TYPE.OPFS_DIR,e.next=40;break;case 36:e.prev=36,e.t0=e.catch(26),this.bufferType=c.BUFFER_TYPE.BUFFERS;case 40:e.next=44;break;case 42:this.bufferType=c.BUFFER_TYPE.BUFFERS;case 44:case"end":return e.stop()}},e,this,[[26,36]])})),function(){return p.apply(this,arguments)})},{key:"writeFile",value:(d=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,r){var n,a,i=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return n=2<i.length&&void 0!==i[2]&&i[2],e.next=3,this.subDirectory.getFileHandle(this.tempFileName(t,n),{create:!0});case 3:return n=e.sent,e.next=6,n.createWritable();case 6:return a=e.sent,e.next=9,a.write(r);case 9:return e.next=11,a.close();case 11:case"end":return e.stop()}},e,this)})),function(e,t){return d.apply(this,arguments)})},{key:"push",value:(l=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,r){var n,a=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(n=2<a.length&&void 0!==a[2]&&a[2],this.bufferType!=c.BUFFER_TYPE.BUFFERS){e.next=5;break}this.buffers[t]=r,e.next=12;break;case 5:if(this.directory)return e.next=8,this.writeFile(t,r,n);e.next=11;break;case 8:this.buffers[t]=!0,e.next=12;break;case 11:this.buffers[t]=r;case 12:this.bufferSize+=r.byteLength;case 13:case"end":return e.stop()}},e,this)})),function(e,t){return l.apply(this,arguments)})},{key:"count",value:function(){return this.buffers.filter(function(e){return null!==e}).length}},{key:"check",value:function(e){return!!this.buffers[e]}},{key:"readFilesTo",value:(u=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var r,n,a,i,o=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:r=1<o.length&&void 0!==o[1]?o[1]:0,n=!(2<o.length&&void 0!==o[2])||o[2];case 2:if(r<this.buffers.length)return e.next=5,this.subDirectory.getFileHandle(this.tempFileName(r));e.next=20;break;case 5:return a=e.sent,e.next=8,a.getFile();case 8:return i=e.sent,e.next=11,i.arrayBuffer();case 11:return i=e.sent,e.next=14,t(i,r);case 14:if(n)return e.next=17,this.subDirectory.removeEntry(this.tempFileName(r));e.next=17;break;case 17:r++,e.next=2;break;case 20:case"end":return e.stop()}},e,this)})),function(e){return u.apply(this,arguments)})},{key:"getFile",value:(s=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var r,n,a,i,o,s,u=this;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(r=t?{type:t}:void 0,this.bufferType==c.BUFFER_TYPE.BUFFERS)return e.abrupt("return",new File(this.buffers,this.tempFileName(),r));e.next=3;break;case 3:if(!this.directory){e.next=68;break}if(e.prev=4,1==this.buffers.length)return e.next=8,this.subDirectory.getFileHandle(this.tempFileName(0));e.next=10;break;case 8:return o=e.sent,e.abrupt("return",o.getFile());case 10:if(this.targetFile)return e.abrupt("return",this.targetFile.getFile());e.next=12;break;case 12:return e.next=14,this.ensureStorageSpace(Math.max(524288e3,.5*this.bufferSize));case 14:if(e.sent){e.next=26;break}if(this.bufferSize<1932735283.2)return n=[],e.next=22,this.readFilesTo(function(){var r=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,r){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:n[r]=t;case 1:case"end":return e.stop()}},e)}));return function(e,t){return r.apply(this,arguments)}}());e.next=23;break;case 22:return e.abrupt("return",new File(n,this.tempFileName(),r));case 23:return e.next=25,this.ensureStorageSpace(Math.max(524288e3,.5*this.bufferSize),2);case 25:e.sent;case 26:return e.next=28,this.subDirectory.getFileHandle(this.tempFileName(),{create:!0});case 28:return this.targetFile=e.sent,e.next=31,this.targetFile.createWritable();case 31:return a=e.sent,e.prev=32,e.next=35,this.readFilesTo(function(){var r=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,r){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,a.write(t);case 3:e.next=11;break;case 5:return e.prev=5,e.t0=e.catch(0),e.next=9,c.clearOldTempFiles(u.directory,1);case 9:return e.next=11,a.write(t);case 11:case"end":return e.stop()}},e,null,[[0,5]])}));return function(e,t){return r.apply(this,arguments)}}());case 35:return e.next=37,a.close();case 37:return e.abrupt("return",this.targetFile.getFile());case 40:if(e.prev=40,e.t0=e.catch(32),a.locked)return e.next=45,a.abort();e.next=45;break;case 45:throw e.t0;case 46:e.next=66;break;case 48:return e.prev=48,e.t1=e.catch(4),e.prev=50,e.next=53,navigator.storage.estimate();case 53:s=e.sent,i=s.quota,o=s.usage,s=s.usageDetails,o=i-o,s=(null==s?void 0:s.fileSystem)||0,_reportMsg("getFile ".concat(formatBytes(this.bufferSize)," ").concat(formatBytes(s)).concat(formatBytes(i)," ").concat(formatBytes(o)," error: ").concat(null===e.t1||void 0===e.t1?void 0:e.t1.message," ").concat(this.tabUrl," ").concat(this.bufferType,".")),e.next=65;break;case 62:e.prev=62,e.t2=e.catch(50),_reportMsg("getFile error: ".concat(null===e.t1||void 0===e.t1?void 0:e.t1.message," ").concat(this.tabUrl," ").concat(this.bufferType));case 65:throw e.t1;case 66:e.next=69;break;case 68:return e.abrupt("return",new File(this.buffers,this.tempFileName(),r));case 69:case"end":return e.stop()}},e,this,[[4,48],[32,40],[50,62]])})),function(e){return s.apply(this,arguments)})},{key:"deleteFiles",value:(o=_asyncToGenerator(_regeneratorRuntime().mark(function e(){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,this.directory)return e.next=4,this.directory.removeEntry(this.dirName,{recursive:!0});e.next=4;break;case 4:e.next=9;break;case 6:e.prev=6,e.t0=e.catch(0);case 9:case"end":return e.stop()}},e,this,[[0,6]])})),function(){return o.apply(this,arguments)})},{key:"getSize",value:function(){return this.bufferSize}},{key:"ensureStorageSpace",value:(i=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var r,n,a,i=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(r=1<i.length&&void 0!==i[1]?i[1]:3,this.directory){e.next=3;break}return e.abrupt("return",!0);case 3:return e.prev=3,e.next=6,navigator.storage.estimate();case 6:if(n=e.sent,a=n.quota,n=n.usage,a-n<t)return e.next=14,c.clearOldTempFiles(this.directory,r);e.next=23;break;case 14:return e.next=16,navigator.storage.estimate();case 16:if(a=e.sent,n=a.quota,a=a.usage,n-a<t)return e.abrupt("return",!1);e.next=23;break;case 23:return e.abrupt("return",!0);case 26:return e.prev=26,e.t0=e.catch(3),e.abrupt("return",!1);case 30:case"end":return e.stop()}},e,this,[[3,26]])})),function(e){return i.apply(this,arguments)})}],[{key:"clearTabTempFiles",value:(a=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var r,n,a,i,o,s,u,c,l=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(r=1<l.length&&void 0!==l[1]?l[1]:null,e.prev=1,r){e.next=14;break}return e.next=5,navigator.storage.estimate();case 5:if(c=e.sent,null!=(u=c.usageDetails)&&u.fileSystem)return e.next=10,navigator.storage.getDirectory();e.next=13;break;case 10:r=e.sent,e.next=14;break;case 13:return e.abrupt("return");case 14:i=a=!(n=[]),e.prev=17,s=_asyncIterator(r.entries());case 19:return e.next=21,s.next();case 21:if(!(a=!(c=e.sent).done)){e.next=29;break}if(u=_slicedToArray(c.value,2),c=u[0],u[1],c.startsWith(".t".concat(t,"-e").concat(browser.runtime.id.substr(0,8),"-")))return e.next=26,r.removeEntry(c,{recursive:!0});e.next=26;break;case 26:a=!1,e.next=19;break;case 29:e.next=35;break;case 31:e.prev=31,e.t0=e.catch(17),i=!0,o=e.t0;case 35:if(e.prev=35,e.prev=36,a&&null!=s.return)return e.next=40,s.return();e.next=40;break;case 40:if(e.prev=40,i)throw o;e.next=43;break;case 43:return e.finish(40);case 44:return e.finish(35);case 45:return e.next=47,Promise.all(n);case 47:n.length,e.next=53;break;case 50:e.prev=50,e.t1=e.catch(1),null===e.t1||void 0===e.t1||e.t1.message.includes("estimate");case 53:case"end":return e.stop()}},e,null,[[1,50],[17,31,35,45],[36,,40,44]])})),function(e){return a.apply(this,arguments)})},{key:"clearTempFilesByUniqueId",value:(n=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var r,n,a,i,o,s,u,c,l=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(r=1<l.length&&void 0!==l[1]?l[1]:null,e.prev=1,r){e.next=14;break}return e.next=5,navigator.storage.estimate();case 5:if(c=e.sent,null!=(u=c.usageDetails)&&u.fileSystem)return e.next=10,navigator.storage.getDirectory();e.next=13;break;case 10:r=e.sent,e.next=14;break;case 13:return e.abrupt("return");case 14:i=a=!(n=[]),e.prev=17,s=_asyncIterator(r.entries());case 19:return e.next=21,s.next();case 21:if(!(a=!(c=e.sent).done)){e.next=29;break}if(u=_slicedToArray(c.value,2),c=u[0],u[1],c.includes("-u".concat(t,"-")))return e.next=26,r.removeEntry(c,{recursive:!0});e.next=26;break;case 26:a=!1,e.next=19;break;case 29:e.next=35;break;case 31:e.prev=31,e.t0=e.catch(17),i=!0,o=e.t0;case 35:if(e.prev=35,e.prev=36,a&&null!=s.return)return e.next=40,s.return();e.next=40;break;case 40:if(e.prev=40,i)throw o;e.next=43;break;case 43:return e.finish(40);case 44:return e.finish(35);case 45:return e.next=47,Promise.all(n);case 47:n.length,e.next=53;break;case 50:e.prev=50,e.t1=e.catch(1),null===e.t1||void 0===e.t1||e.t1.message.includes("estimate");case 53:case"end":return e.stop()}},e,null,[[1,50],[17,31,35,45],[36,,40,44]])})),function(e){return n.apply(this,arguments)})},{key:"clearOldTempFiles",value:(r=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t,r,n,a,i,o,s,u,c,l,d,p=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(t=0<p.length&&void 0!==p[0]?p[0]:null,r=1<p.length&&void 0!==p[1]?p[1]:6,e.prev=2,t){e.next=15;break}return e.next=6,navigator.storage.estimate();case 6:if(u=e.sent,null!=(u=u.usageDetails)&&u.fileSystem)return e.next=11,navigator.storage.getDirectory();e.next=14;break;case 11:t=e.sent,e.next=15;break;case 14:return e.abrupt("return");case 15:i=a=!(n=[]),e.prev=18,s=_asyncIterator(t.entries());case 20:return e.next=22,s.next();case 22:if(!(a=!(u=e.sent).done)){e.next=33;break}if(d=_slicedToArray(u.value,2),c=d[0],d[1],null==(d=c.match("-e".concat(browser.runtime.id.substr(0,8),"-.*-d(?<date>\\d{13})")))||null===(l=d.groups)||void 0===l||!l.date){e.next=30;break}if(new Date(Number(d.groups.date)).getTime()<Date.now()-36e5*r)return e.next=30,t.removeEntry(c,{recursive:!0});e.next=30;break;case 30:a=!1,e.next=20;break;case 33:e.next=39;break;case 35:e.prev=35,e.t0=e.catch(18),i=!0,o=e.t0;case 39:if(e.prev=39,e.prev=40,a&&null!=s.return)return e.next=44,s.return();e.next=44;break;case 44:if(e.prev=44,i)throw o;e.next=47;break;case 47:return e.finish(44);case 48:return e.finish(39);case 49:return e.next=51,Promise.all(n);case 51:n.length,e.next=57;break;case 54:e.prev=54,e.t1=e.catch(2),null===e.t1||void 0===e.t1||e.t1.message.includes("estimate");case 57:case"end":return e.stop()}},e,null,[[2,54],[18,35,39,49],[40,,44,48]])})),function(){return r.apply(this,arguments)})},{key:"clearFilesExceptActiveTabs",value:(t=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var r,n,a,i,o,s,u,c,l=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(r=1<l.length&&void 0!==l[1]?l[1]:null,e.prev=1,r){e.next=14;break}return e.next=5,navigator.storage.estimate();case 5:if(u=e.sent,null!=(c=u.usageDetails)&&c.fileSystem)return e.next=10,navigator.storage.getDirectory();e.next=13;break;case 10:r=e.sent,e.next=14;break;case 13:return e.abrupt("return");case 14:i=a=!(n=[]),e.prev=17,s=_asyncIterator(r.entries());case 19:return e.next=21,s.next();case 21:if(!(a=!(u=e.sent).done)){e.next=28;break}c=_slicedToArray(u.value,2),u=c[0],c[1],(c=u.match(/^\.t(\d+)-/))&&(c=parseInt(c[1],10),t.includes(c)||n.push(r.removeEntry(u,{recursive:!0})));case 25:a=!1,e.next=19;break;case 28:e.next=34;break;case 30:e.prev=30,e.t0=e.catch(17),i=!0,o=e.t0;case 34:if(e.prev=34,e.prev=35,a&&null!=s.return)return e.next=39,s.return();e.next=39;break;case 39:if(e.prev=39,i)throw o;e.next=42;break;case 42:return e.finish(39);case 43:return e.finish(34);case 44:return e.next=46,Promise.all(n);case 46:n.length,e.next=52;break;case 49:e.prev=49,e.t1=e.catch(1);case 52:case"end":return e.stop()}},e,null,[[1,49],[17,30,34,44],[35,,39,43]])})),function(e){return t.apply(this,arguments)})},{key:"printFiles",value:(e=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t,r,n,a,i,o,s=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(t=0<s.length&&void 0!==s[0]?s[0]:null,e.prev=1,t){e.next=14;break}return e.next=5,navigator.storage.estimate();case 5:if(o=e.sent,null!=(o=o.usageDetails)&&o.fileSystem)return e.next=10,navigator.storage.getDirectory();e.next=13;break;case 10:t=e.sent,e.next=14;break;case 13:return e.abrupt("return");case 14:n=r=!1,e.prev=16,i=_asyncIterator(t.entries());case 18:return e.next=20,i.next();case 20:if(!(r=!(o=e.sent).done)){e.next=26;break}o=_slicedToArray(o.value,2),o[0],o[1];case 23:r=!1,e.next=18;break;case 26:e.next=32;break;case 28:e.prev=28,e.t0=e.catch(16),n=!0,a=e.t0;case 32:if(e.prev=32,e.prev=33,r&&null!=i.return)return e.next=37,i.return();e.next=37;break;case 37:if(e.prev=37,n)throw a;e.next=40;break;case 40:return e.finish(37);case 41:return e.finish(32);case 42:e.next=47;break;case 44:e.prev=44,e.t1=e.catch(1);case 47:case"end":return e.stop()}},e,null,[[1,44],[16,28,32,42],[33,,37,41]])})),function(){return e.apply(this,arguments)})}]),c}(),_defineProperty(_class4,"BUFFER_TYPE",{BUFFERS:"buffers",USER_DIR:"userDir",OPFS_DIR:"opfsDir"}),_class4);var LsLicenseAPI=function(){"use strict";function n(e,t,r){_classCallCheck(this,n),this.baseUrl=e,this.storeId=t,this.productIds=r}var t,r,a;return _createClass(n,[{key:"activate",value:(a=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,r){var n,a,i,o;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return i={method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},body:JSON.stringify({license_key:t,instance_name:r})},n="".concat(this.baseUrl,"/v1/licenses/activate"),a={activated:!1,licenseInfo:null},e.prev=3,e.next=6,XMLRequest(n,i);case 6:return i=e.sent,e.next=9,i.json();case 9:if(o=e.sent,a.activated=o.activated,a.error=o.error,1==o.activated)if(o.meta.store_id==this.storeId&&this.productIds.includes(o.meta.product_id))a.licenseInfo={name:o.instance.name,license_key:o.license_key.key,instance_id:o.instance.id,customer_name:o.meta.customer_name,customer_email:o.meta.customer_email,product_name:o.meta.product_name};else{try{this.deactivate({license_key:o.license_key.key,instance_id:o.instance.id})}catch(e){}a.activated=!1,a.error="The license key is not for our product."}e.next=19;break;case 16:e.prev=16,e.t0=e.catch(3),a.error=e.t0.message;case 19:return e.abrupt("return",a);case 20:case"end":return e.stop()}},e,this,[[3,16]])})),function(e,t){return a.apply(this,arguments)})},{key:"deactivate",value:(r=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var r,n,a,i;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return n=t.license_key,a=t.instance_id,r={method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},body:JSON.stringify({license_key:n,instance_id:a})},n="".concat(this.baseUrl,"/v1/licenses/deactivate"),a={deactivated:!1},e.prev=4,e.next=7,XMLRequest(n,r);case 7:return i=e.sent,e.next=10,i.json();case 10:i=e.sent,a.deactivated=i.deactivated,a.error=i.error,e.next=19;break;case 16:e.prev=16,e.t0=e.catch(4),a.error=e.t0.message;case 19:return e.abrupt("return",a);case 20:case"end":return e.stop()}},e,this,[[4,16]])})),function(e){return r.apply(this,arguments)})},{key:"validate",value:(t=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var r,n,a,i,o;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return n=t.license_key,a=t.instance_id,r="".concat(this.baseUrl,"/v1/licenses/validate"),n={method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},body:JSON.stringify({license_key:n,instance_id:a})},a={valid:!1},e.prev=4,e.next=7,XMLRequest(r,n);case 7:return i=e.sent,e.next=10,i.json();case 10:i=e.sent,a.valid=i.valid,a.error=i.error,1==a.valid?i.meta.store_id==this.storeId&&this.productIds.includes(i.meta.product_id)||(a.valid=!1,a.error="The license key is not for this product."):a.status=null==i||null===(o=i.license_key)||void 0===o?void 0:o.status,e.next=20;break;case 17:e.prev=17,e.t0=e.catch(4),a.error=e.t0.message;case 20:return e.abrupt("return",a);case 21:case"end":return e.stop()}},e,this,[[4,17]])})),function(e){return t.apply(this,arguments)})}]),n}(),License=function(){"use strict";function i(e,t,r){var n=3<arguments.length&&void 0!==arguments[3]?arguments[3]:86400,a=4<arguments.length&&void 0!==arguments[4]&&arguments[4];_classCallCheck(this,i),this.api=new LsLicenseAPI(e,t,r),this.maxAgeSecs=n,this.verbose=!!a,this.status=i.STATUS.UNCHECKED,this.licenseStoreKey="".concat(browser.runtime.id,"_license")}var e,t,r,n;return _createClass(i,[{key:"isCacheExpired",value:function(e){return!e||e+1e3*this.maxAgeSecs<Date.now()||e>Date.now()}},{key:"updateCacheTime",value:function(e){var t=Date.now();browser.storage.sync.set(_defineProperty({},this.licenseStoreKey,_objectSpread(_objectSpread({},e),{},{cacheTime:t})))}},{key:"activate",value:(n=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,r){var n;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.api.activate(t,r);case 2:return(n=e.sent).activated&&(this.updateCacheTime(n),this.status=i.STATUS.ACTIVATED),e.abrupt("return",n);case 5:case"end":return e.stop()}},e,this)})),function(e,t){return n.apply(this,arguments)})},{key:"getLicenseInfoFromStorage",value:(r=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,browser.storage.sync.get([this.licenseStoreKey]);case 2:if(null!=(t=e.sent)&&t[this.licenseStoreKey])return e.abrupt("return",t[this.licenseStoreKey]);e.next=5;break;case 5:return e.abrupt("return",null);case 6:case"end":return e.stop()}},e,this)})),function(){return r.apply(this,arguments)})},{key:"deactivate",value:(t=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t,r;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,this.getLicenseInfoFromStorage();case 3:if(null==(t=e.sent))return e.abrupt("return",{deactivated:!1,error:"Deactivate failed, please deactivate it from your order link."});e.next=6;break;case 6:return e.next=8,this.api.deactivate(t.licenseInfo);case 8:return(r=e.sent).deactivated&&browser.storage.sync.remove(this.licenseStoreKey),e.abrupt("return",r);case 13:return e.prev=13,e.t0=e.catch(0),this.status=i.STATUS.ACTIVATED,e.abrupt("return",{deactivated:!1,error:e.t0.message});case 17:case"end":return e.stop()}},e,this,[[0,13]])})),function(){return t.apply(this,arguments)})},{key:"checkLicense",value:(e=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t,r;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,this.getLicenseInfoFromStorage();case 3:if(null==(t=e.sent))return e.abrupt("return",{valid:!1,error:null});e.next=6;break;case 6:if(this.isCacheExpired(t.cacheTime)){e.next=8;break}return e.abrupt("return",{valid:t.activated||!1,licenseInfo:t.licenseInfo,error:t.error});case 8:return e.next=10,this.api.validate(t.licenseInfo);case 10:return 1==(r=e.sent).valid?(r.licenseInfo=t.licenseInfo,this.updateCacheTime(t)):"expired"==(null==r?void 0:r.status)?browser.storage.sync.remove(this.licenseStoreKey):"Failed to fetch"===r.error&&(r.valid=!0,r.licenseInfo=t.licenseInfo),e.abrupt("return",r);case 16:return e.prev=16,e.t0=e.catch(0),e.abrupt("return",{valid:!1,error:e.t0.message});case 19:case"end":return e.stop()}},e,this,[[0,16]])})),function(){return e.apply(this,arguments)})}]),i}();_defineProperty(License,"STATUS",{UNCHECKED:"unchecked",INACTIVATED:"inactivated",ACTIVATED:"activated"});var Stream=function(){"use strict";function e(){_classCallCheck(this,e),"undefined"==typeof window&&(globalThis.window=globalThis)}var n,r,a,i,t,o,s,u;return _createClass(e,[{key:"isVideoPlaylist",value:function(e){if(e.attributes.RESOLUTION)return!0;if(e.attributes.CODECS){var t=/^(av0?1|avc0?[1234]|vp0?[89]|hvc1|hev1|theora|mp4v)/,r=_createForOfIteratorHelper(e.attributes.CODECS.split(","));try{for(r.s();!(n=r.n()).done;){var n=n.value;if(t.test(n))return!0}}catch(e){r.e(e)}finally{r.f()}}return!1}},{key:"getMediaInfoFromReceivedHeader",value:function(e,t){var r=null,n={},a=e.get("content-type",null),i=e.get("content-length",null),o=e.get("content-range",null),s=e.get("icy-br",null),u=e.get("icy-name",null),e=e.get("content-disposition",null);if(o?(o=getRangeInfo(o))&&(n.size=o.total):i&&(i=parseInt(i),n.size=i),u?n.title=u:!e||(e=getFileNameFromcontentDisposition(e))&&(e=getTitleExtFromFileName(e),n.title=e.title,r=e.ext),null==r&&(t=getTitleExtFromUrl(t),n.title||(n.title=t.title),r=t.ext),a){a=getTypeFormatFromExtMime(r,a);if(!a.type){if(!streamExts.includes(r))return null;a.type="stream",a.ext=r}return s&&(a.type,n.bitrate=s.trim().split(",")[0]),n.type=a.type,n.ext=a.ext,n}return null}},{key:"determineMediaType",value:function(e){var t=null===(t=e.match(/\.(\w{2,4})(?:$|\?|#)/))||void 0===t?void 0:t[1],t=null==t?void 0:t.toLowerCase();if(t){if(["m4v","mp4","webm"].includes(t))return"video";if(["aac","m4a","mp3","ogg"].includes(t))return"audio";if(["srt","vtt","sub"].includes(t))return"subtitle"}for(var r=0,n=[{type:"video",regex:/\/(video|vod)\//i},{type:"audio",regex:/\/(audio)\//i},{type:"subtitle",regex:/\/(subtitles|subs)\//i}];r<n.length;r++){var a=n[r];if(a.regex.test(e))return a.type}return"unknown"}},{key:"isHlsMasterManifest",value:function(e){if(e.includes("#EXT-X-STREAM-INF"))return!0;for(var t=0,r=["#EXT-X-TARGETDURATION","#EXT-X-MEDIA-SEQUENCE","#EXTINF"];t<r.length;t++){var n=r[t];if(e.includes(n))return!1}return!1}},{key:"parseManifest",value:function(e,t){var r,n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:"",a=null;try{e=e.replace(/\uFFFD/g,""),"hls"==t?((r=new m3u8Parser.Parser).push(e),r.end(),a=r.manifest):"dash"==t&&(r=n?{manifestUri:n}:{},a=mpdParser.parse(e,r))}catch(e){throw new Error("parseManifest ".concat(e.message))}return a}},{key:"getFullUrl",value:function(e,t){try{return new URL(e,t).toString()}catch(e){return null}}},{key:"resolveUrl",value:function(e,t,r){var n=null;return null!=t&&t.length&&t.startsWith("http")?n=t:null!=e&&e.length&&(n=e),n?(null!==n&&void 0!==n&&n.startsWith("http")||null==r||!r.length||(n=this.getFullUrl(n,r)),n):null}},{key:"getPlaylistType",value:function(e){var t,r=/^(av0?1|avc0?[1234]|vp0?[89]|hvc1|hev1|theora|mp4v)/,n=/^(mp4a|flac|vorbis|opus|ac-[34]|ec-3|alac|mp3|speex|aac)/,a={hasVideo:!1,hasAudio:!1};null!==(t=e.attributes)&&void 0!==t&&t.RESOLUTION&&(a.hasVideo=!0);var i=_createForOfIteratorHelper((null===(e=e.attributes)||void 0===e||null===(e=e.CODECS)||void 0===e?void 0:e.split(","))||[]);try{for(i.s();!(o=i.n()).done;){var o=o.value;r.test(o)&&(a.hasVideo=!0),n.test(o)&&(a.hasAudio=!0)}}catch(e){i.e(e)}finally{i.f()}return a}},{key:"hlsDashSegmentParse",value:function(e,i){var o=this,s=[];return e.forEach(function(e){var t=o.resolveUrl(null==e?void 0:e.uri,null==e?void 0:e.resolvedUri,i);if(null!=t){var r=null;if(e.map)if(null==(r=o.resolveUrl(null===(a=e.map)||void 0===a?void 0:a.uri,null===(n=e.map)||void 0===n?void 0:n.resolvedUri,i)))return;var n,a=null;if(e.key)if(null==(a=o.resolveUrl(null===(n=e.key)||void 0===n?void 0:n.uri,null===(n=e.key)||void 0===n?void 0:n.resolvedUri,i)))return;s.push({url:t,duration:e.duration,map:e.map?{url:r,byterange:e.map.byterange||null}:null,byterange:e.byterange||null,key:e.key?{url:a,method:e.key.method,iv:e.key.iv?Array.from(e.key.iv):null}:null})}}),null!==(e=s[0])&&void 0!==e&&e.map&&(e={url:s[0].url},s[0].map&&(e.url=s[0].map.url,s[0].map.byterange&&(e.byterange={offset:s[0].map.byterange.offset,length:s[0].map.byterange.length}),s[0].key&&!s[0].map.key&&(s[0].map.key=s[0].key),s[0].map.key&&(e.key=s[0].map.key)),!e.byterange&&s[0].byterange&&(e.byterange={offset:0,length:s[0].byterange.offset}),s.unshift(e)),s}},{key:"hlsDashMediaGroupsParse",value:function(e,t){for(var r={},n=0,a=Object.entries(e);n<a.length;n++){for(var i=_slicedToArray(a[n],2),o=i[0],i=i[1],s=[],u=0,c=Object.entries(i);u<c.length;u++)for(var l=_slicedToArray(c[u],2),d=l[0],l=l[1],p=0,f=Object.entries(l);p<f.length;p++){var h=_slicedToArray(f[p],2),m=h[0],g=h[1],v=g.language,b=void 0===v?"N/A":v,y=g.playlists,h=void 0===y?[]:y,v=g.uri,y=void 0===v?"":v,v=g.resolvedUri,v=void 0===v?"":v,m={type:o.toLowerCase(),groupName:d,language:b,name:m,default:null!==(m=null==g?void 0:g.default)&&void 0!==m&&m,mediaSequence:g.mediaSequence||0,discontinuitySequence:g.discontinuitySequence||0,discontinuityStarts:g.discontinuityStarts||[]};if(h.length){g=this.hlsDashPlaylistParseHelper(h[0],g.mediaGroups,t,"audio"==o.toLowerCase());g&&(m=_objectSpread(_objectSpread({},m),g))}else{v=this.resolveUrl(y,v,t);if(!v)continue;m.url=v}s.push(m)}0<s.length&&(r[o]=s)}return r}},{key:"hlsDashPlaylistParseHelper",value:function(e,t,r){var n,a,i=3<arguments.length&&void 0!==arguments[3]&&arguments[3],o={name:null===(n=e.attributes)||void 0===n?void 0:n.NAME,bandwidth:(null===(n=e.attributes)||void 0===n?void 0:n["AVERAGE-BANDWIDTH"])||(null===(n=e.attributes)||void 0===n?void 0:n.BANDWIDTH)||null};if(null!=e&&null!==(a=e.segments)&&void 0!==a&&a.length)o.discontinuitySequence=e.discontinuitySequence||0,o.discontinuityStarts=e.discontinuityStarts||[],o.segments=this.hlsDashSegmentParse(e.segments,r);else{var s=this.resolveUrl(null==e?void 0:e.uri,null==e?void 0:e.resolvedUri,r);if(null==s)return null;o.url=s}var u,c,l,d,p,s=i?{hasAudio:!0,hasVideo:!1}:this.getPlaylistType(e);if(s.hasVideo?(e.attributes&&(null!==(i=e.attributes)&&void 0!==i&&null!==(i=i.RESOLUTION)&&void 0!==i&&i.width&&null!==(u=e.attributes)&&void 0!==u&&null!==(u=u.RESOLUTION)&&void 0!==u&&u.height?(l="".concat(e.attributes.RESOLUTION.width,"x").concat(e.attributes.RESOLUTION.height),o.width=e.attributes.RESOLUTION.width,o.height=e.attributes.RESOLUTION.height,o.resolution=l):null!==(l=e.attributes)&&void 0!==l&&null!==(l=l.RESOLUTION)&&void 0!==l&&l.width?o.width=e.attributes.RESOLUTION.width:null!==(c=e.attributes)&&void 0!==c&&null!==(c=c.RESOLUTION)&&void 0!==c&&c.height&&(o.height=e.attributes.RESOLUTION.height),o.codecs=e.attributes.CODECS||null,o.frameRate=(null===(c=e.attributes)||void 0===c?void 0:c["FRAME-RATE"])||null),o.type="video"):s.hasAudio&&(o.type="audio"),null!==(s=e.attributes)&&void 0!==s&&s["PROGRESSIVE-URI"]&&(o.progressiveUrl=e.attributes["PROGRESSIVE-URI"]),null!==(s=e.attributes)&&void 0!==s&&s.SUBTITLES&&t&&t.SUBTITLES&&(d=e.attributes.SUBTITLES,t.SUBTITLES[d]&&(o.subtitles=d)),null!==(d=e.attributes)&&void 0!==d&&d["CLOSED-CAPTIONS"]&&t&&t["CLOSED-CAPTIONS"]&&(p=e.attributes["CLOSED-CAPTIONS"],t["CLOSED-CAPTIONS"][p]&&(o.captions=p)),null!==(p=e.attributes)&&void 0!==p&&p.AUDIO&&t&&t.AUDIO&&(p=e.attributes.AUDIO,t.AUDIO[p]&&(o.audio=p)),null!=e&&e.contentProtection)for(var f in o.contentProtection={},e.contentProtection)o.contentProtection[f]=_objectSpread({},e.contentProtection[f]),o.contentProtection[f].pssh&&(o.contentProtection[f].pssh=Array.from(o.contentProtection[f].pssh));return o}},{key:"hlsDashParserHelper",value:function(t,e,r,n){var a,i,o=this,n={streamType:e,manifestUrl:r,title:n};if(t.duration&&(n.duration=t.duration),t.playlists&&(i=[],t.playlists.forEach(function(e){e=o.hlsDashPlaylistParseHelper(e,t.mediaGroups,r);e&&i.push(e)}),n.playlist=i),0<(null==t||null===(a=t.segments)||void 0===a?void 0:a.length)&&(n.mediaSequence=t.mediaSequence||0,n.discontinuitySequence=t.discontinuitySequence||0,n.discontinuityStarts=t.discontinuityStarts||[],n.segments=this.hlsDashSegmentParse(t.segments,r),n.duration=t.segments.reduce(function(e,t){return e+t.duration},0)),t.mediaGroups&&(n.mediaGroups=this.hlsDashMediaGroupsParse(t.mediaGroups,r)),t.contentProtection){var s,u={};for(s in t.contentProtection)u[s]=_objectSpread({},t.contentProtection[s]),u[s].pssh&&(u[s].pssh=Array.from(t.contentProtection[s].pssh));n.contentProtection=u}return n}},{key:"buildMainPlaylistName",value:function(e,t){return t.name&&t.name.includes(e)&&(e=t.name),t.frameRate&&!e.includes(t.frameRate)&&(e+="_".concat(t.frameRate)),null!=t&&t.bandwidth&&!e.includes(t.bandwidth)&&(e+="_".concat(Math.floor(t.bandwidth/1e3),"kbps")),null!=t&&t.resolution&&!e.includes(t.resolution)&&(e+="(".concat(null==t?void 0:t.resolution,")")),e}},{key:"checkGetManifestDataFromUrl",value:(u=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var r,n,a,i,o,s,u,c=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return r=1<c.length&&void 0!==c[1]?c[1]:"",n=2<c.length&&void 0!==c[2]?c[2]:{},a=new AbortController,e.next=5,this.requestUrl(t,n=_objectSpread(_objectSpread({},n),{},{signal:a.signal}));case 5:if(null!=(i=e.sent)&&i.ok){e.next=9;break}return[404,403,451].includes(null==i?void 0:i.status),e.abrupt("return",null);case 9:if((o=this.getMediaInfoFromReceivedHeader(i.headers,i.url))&&"stream"!=(null==o?void 0:o.type)){e.next=22;break}return e.next=13,i.text();case 13:if(s=e.sent,"hls"==(u=determineStreamType(s))||"dash"==u)return e.abrupt("return",this.getManifestDataFromText(s,u,r||o.title,t));e.next=19;break;case 19:case 20:e.next=24;break;case 22:return a.abort(),e.abrupt("return",_objectSpread(_objectSpread({},o),{},{init:n,download_url:i.url}));case 24:return e.abrupt("return",null);case 25:case"end":return e.stop()}},e,this)})),function(e){return u.apply(this,arguments)})},{key:"getManifestDataFromText",value:(s=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,r,n){var a,i,o=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(a=3<o.length&&void 0!==o[3]?o[3]:"",(i=determineStreamType(t))&&i!=r&&(r=i),i=this.parseManifest(t,r,a))return e.abrupt("return",this.hlsDashParserHelper(i,r,a,n));e.next=6;break;case 6:return e.abrupt("return",null);case 7:case"end":return e.stop()}},e,this)})),function(e,t,r){return s.apply(this,arguments)})},{key:"containsStreamUrls",value:function(e){return/['"]([^'"]*?\.(mpd|m3u8)(\?[^"'\r\n]*)?)['"]/g.test(e)}},{key:"requestUrl",value:(o=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var r,n,a,i=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(r=1<i.length&&void 0!==i[1]?i[1]:{},n=[],e.prev=2,null==r||!r.headers){e.next=10;break}if(r.headers=updatePrivacyCombrHeaders(t,r.headers),0<(a=["referer","origin"].filter(function(e){return r.headers[e]}).map(function(e){return{header:e,value:r.headers[e]}})).length)return e.next=9,updateSessionRules([t],r.headers.referer,{attrs:a});e.next=10;break;case 9:n=e.sent;case 10:return e.next=12,XMLRequest(t,r).catch(function(e){return null});case 12:return a=e.sent,e.abrupt("return",a);case 16:return e.prev=16,e.t0=e.catch(2),e.abrupt("return",null);case 20:if(e.prev=20,n.length)return e.next=24,browser.declarativeNetRequest.updateSessionRules({removeRuleIds:n});e.next=24;break;case 24:return e.finish(20);case 25:case"end":return e.stop()}},e,null,[[2,16,20,25]])})),function(e){return o.apply(this,arguments)})},{key:"requestUrlOld",value:(t=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var r,n,a,i,o,s=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return r=1<s.length&&void 0!==s[1]?s[1]:{},e.next=3,XMLRequest(t,r).catch(function(e){return null});case 3:if(null!=(n=e.sent)&&n.ok){e.next=16;break}return i=null==r||null===(a=r.headers)||void 0===a?void 0:a.referer,e.next=8,updateSessionRules([t],i);case 8:return o=e.sent,e.next=11,XMLRequest(t,_objectSpread(_objectSpread({},r),{},{cache:"no-store"})).catch(function(e){return null});case 11:if(null!=(n=e.sent)&&n.ok){e.next=16;break}return o.length&&browser.declarativeNetRequest.updateSessionRules({removeRuleIds:o}),e.abrupt("return",null);case 16:return e.abrupt("return",n);case 17:case"end":return e.stop()}},e)})),function(e){return t.apply(this,arguments)})},{key:"getManifestUrlsFromResponseText",value:function(i,t,e,r){var o=this,s=[],n=null==r||null===(f=r.get("Content-Type"))||void 0===f||null===(f=f.toLowerCase())||void 0===f||null===(f=f.split(";"))||void 0===f?void 0:f[0];if(["application/ttml+xml","application/x-subrip","text/vtt"].includes(n))return s;if(/^[\r\n\s]*(?:<!(?:doctype|DOCTYPE)\s+(?:html|HTML)[^>]*>|<html\b[^>]*>)/i.test(t))return s;if(0==t.length)return s;if("application/json"==n){try{var a=JSON.parse(t),u=/(m3u8|mpd|hls|dash)/i,c=/\.(m3u8|mpd)(\?[^"'\r\n]*)?$/,l=/^(?:(?:https?:)?\/\/[^\s/$.?#].[^\s]*|\/[^\s]*|[^<>"'`\s{}|\^[\]]+)/;(function e(t){if(Array.isArray(t))t.forEach(e);else if("object"===_typeof(t)&&null!==t)for(var r in t){var n,a=t[r];"string"==typeof a&&(u.test(r)&&l.test(a)?(n=o.getFullUrl(a,i))&&s.push(n):l.test(a)&&c.test(a)&&((n=o.getFullUrl(a,i))&&s.push(n))),e(a)}})(a)}catch(e){if(this.containsStreamUrls(t))for(var d=_wrapRegExp(/(?:['"]([^'"\d\W]*(?:m3u8?|mpd|hls|dash)[^'"\d\w]*)['"]\s*:\s*['"]([^'"]+)['"])|(?:((?:(?:https?:)?\/\/[^\s/$.?#].[^\s]*|\/[^\s]*|[^<>"'`\s{}|\^[\]]+)\.(m3u8?|mpd)(?:\?[^"'\r\n]*)?)['"])/gi,{key:1,keyValue:2,url:3});null!==(p=d.exec(t));){var p=p.groups.keyValue||p.groups.url;!p||(p=this.getFullUrl(p,i))&&s.push(p)}}return s}if("application/xml"==n)return s;if("text/javascript"==n)return s;if(1<n.split(",").length)return s;if(imageMimeTypes.includes(n)||"video/mp2t"==n)return s;if("text/xml"==n)return s;if(streamMimeTypes.includes(n))return s;if(t.startsWith("http")){var f=t.split("\n").filter(function(e){return e.startsWith("http")});if(f.length)return s.push.apply(s,_toConsumableArray(f)),s}r=r.get("Content-length");if(102400<r||102400<(null==t?void 0:t.length))return s;if("text/plain"===n&&/^https?:\/\/[^/]*twinkaboo\.com/.test(i))return s;throw new Error("getManifestUrlsFromManifestText ".concat(n,",").concat(e," ").concat(i))}},{key:"checkManifestText",value:function(e,t,r,n){if(determineStreamType(t))return!0;var a=null===(a=n.get("Content-Type"))||void 0===a||null===(a=a.toLowerCase())||void 0===a||null===(a=a.split(";"))||void 0===a?void 0:a[0];if(["application/ttml+xml","application/x-subrip","text/vtt"].includes(a))return!1;if(/^[\r\n\s]*(?:<!(?:doctype|DOCTYPE)\s+(?:html|HTML)[^>]*>|<html\b[^>]*>)/i.test(t))return!1;if(0==t.length)return!1;if("application/json"==a)return this.containsStreamUrls(t),!1;if("application/xml"==a)return!1;if("text/javascript"==a)return!1;if(1<a.split(",").length)return!1;if(imageMimeTypes.includes(a)||"video/mp2t"==a)return!1;if("text/xml"==a)return!1;if(streamMimeTypes.includes(a))return!1;if(t.startsWith("http")&&t.split("\n").filter(function(e){return e.startsWith("http")}).length)return!1;n=n.get("Content-length");if(102400<n||102400<(null==t?void 0:t.length))return!1;throw new Error("getManifestDataFromUrl ".concat(a,",").concat(r," ").concat(e))}},{key:"validateAndApplyManifestParams",value:(i=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,r,n,a){var i,o,s;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.checkGetManifestDataFromUrl(t,n,a);case 2:if(i=e.sent){e.next=14;break}if(o=new URL(r),s=new URL(t),o.search&&!s.search)return s.search=o.search,e.next=10,this.checkGetManifestDataFromUrl(s.toString(),n,a);e.next=13;break;case 10:if(i=e.sent)return e.abrupt("return",{manifestResult:i,searchParams:o.search});e.next=13;break;case 13:return e.abrupt("return",null);case 14:return e.abrupt("return",{manifestResult:i});case 15:case"end":return e.stop()}},e,this)})),function(e,t,r,n){return i.apply(this,arguments)})},{key:"validateMediaUrl",value:(a=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,r){var n,a,i,o,s,u=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return n=2<u.length&&void 0!==u[2]?u[2]:{},a=3<u.length&&void 0!==u[3]&&u[3]?n:_objectSpread(_objectSpread({},n),{},{signal:(new AbortController).signal}),e.next=5,this.requestUrl(t,a);case 5:if(i=e.sent,null!==(n=i)&&void 0!==n&&n.ok)return e.abrupt("return",{valid:!0});e.next=8;break;case 8:if(o=new URL(r),s=new URL(t),o.search&&!s.search)return s.search=o.search,e.next=14,this.requestUrl(s.toString(),a);e.next=17;break;case 14:if(i=e.sent,null!==(s=i)&&void 0!==s&&s.ok)return e.abrupt("return",{valid:!0,searchParams:o.search});e.next=17;break;case 17:return e.abrupt("return",{valid:!1});case 18:case"end":return e.stop()}},e,this)})),function(e,t){return a.apply(this,arguments)})},{key:"applySearchParamsToUrl",value:function(e,t){if(!t)return e;var r=new URL(e);return r.search?e:(r.search=t,r.toString())}},{key:"applySearchParamsToSegments",value:function(e,t){var r=this;return t&&e?e.map(function(e){return _objectSpread(_objectSpread({},e),{},{url:r.applySearchParamsToUrl(e.url,t)})}):e}},{key:"manifestToVideoLinks",value:(r=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,r){var n,a,i,o,s,u,c,l,d,f,h,m,g,v,b,y,_,w,E,T,S,x,k,R,I,A,N,D,O,C,L,P,M,U=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(n=2<U.length&&void 0!==U[2]&&U[2],a=3<U.length&&void 0!==U[3]&&U[3],i=4<U.length&&void 0!==U[4]&&U[4],s=!(o=[]),l=c=!(u=["doppiocdn.live","doppiocdn.net","sacdnssedge.com","sagcoreedge.com"]),d=function(e){try{var t=new URL(e).hostname.split(".").slice(-2).join(".");return u.includes(t)}catch(e){return!1}},e.prev=9,f=t.streamType,t.init=r,h=null,m=function(e){return!e||!/vp09\.(?!00\b)\d{2}/i.test(e)},!t.playlist){e.next=54;break}g=0;case 16:if(!(g<t.playlist.length)){e.next=54;break}if(v=t.playlist[g],m(v.codecs)){e.next=21;break}return s=!0,e.abrupt("continue",51);case 21:if(b={streamType:f,manifestUrl:t.manifestUrl,init:r},t.duration&&(b.duration=t.duration),v.title=this.buildMainPlaylistName(t.title,v),!v.segments){e.next=35;break}if(h||a||l){e.next=31;break}return e.next=28,this.validateMediaUrl(v.segments[0].url,t.manifestUrl,r);case 28:null!=(w=e.sent)&&w.searchParams&&(h=w.searchParams),w.valid&&(l=!0);case 31:h&&(v.segments=this.applySearchParamsToSegments(v.segments,h)),o.push(_objectSpread(_objectSpread({},b),v)),e.next=50;break;case 35:if(!v.url){e.next=50;break}if(y=null,h&&!i||a||l){e.next=48;break}return e.next=40,this.validateAndApplyManifestParams(v.url,t.manifestUrl,v.title,r);case 40:if(_=e.sent){e.next=45;break}return!c&&d(v.url)&&(c=!0),e.abrupt("continue",51);case 45:y=_.manifestResult,_.searchParams&&(h=_.searchParams),l=!0;case 48:v.url=this.applySearchParamsToUrl(v.url,h),i?null!==(w=y)&&void 0!==w&&w.streamType?(v.mediaSequence=(null===(x=y)||void 0===x?void 0:x.mediaSequence)||0,v.segments=y.segments,o.push(_objectSpread(_objectSpread({},b),v))):o.push(b):("hls"===f&&(0===g?null!==(S=y)&&void 0!==S&&S.contentProtection&&(v.contentProtection=y.contentProtection):t.playlist[0].contentProtection&&(v.contentProtection=t.playlist[0].contentProtection)),o.push(_objectSpread(_objectSpread({},b),v)));case 50:v.progressiveUrl&&(x=t.manifestUrl.match(_wrapRegExp(/https?:\/\/[^.]+\.dailymotion\.com\/.*\/([^.]+)\.m3u8/,{id:1})),S=null!=x&&null!==(E=x.groups)&&void 0!==E&&E.id?"https://www.dailymotion.com/video/".concat(null==x||null===(T=x.groups)||void 0===T?void 0:T.id):t.manifestUrl,v.progressiveUrl,v.url,(x=_objectWithoutProperties(v,_excluded)).download_url=v.progressiveUrl,x.webpage_url=S,x.init=r,t.duration&&(x.duration=t.duration),o.push(x));case 51:g++,e.next=16;break;case 54:if(!t.mediaGroups){e.next=112;break}k=0,R=Object.entries(t.mediaGroups);case 56:if(!(k<R.length)){e.next=112;break}if(A=_slicedToArray(R[k],2),I=A[0],A=A[1],!n&&"audio"!=I.toLowerCase()){e.next=109;break}N=_createForOfIteratorHelper(A),e.prev=60,N.s();case 62:if((O=N.n()).done){e.next=101;break}if(D=O.value,Object.assign(D,{title:t.title+"-"+D.groupName+"-"+D.name,type:I.toLowerCase(),streamType:f,manifestUrl:t.manifestUrl,init:r,duration:t.duration}),t.duration&&(D.duration=t.duration),!D.segments){e.next=77;break}if(h||a||l){e.next=73;break}return e.next=70,this.validateMediaUrl(D.segments[0].url,t.manifestUrl,r);case 70:null!=(O=e.sent)&&O.searchParams&&(h=O.searchParams),O.valid&&(l=!0);case 73:D.segments=this.applySearchParamsToSegments(D.segments,h),o.push(D),e.next=99;break;case 77:if(!D.url){e.next=99;break}if(L=null,h&&!i||a||l){e.next=86;break}return e.next=82,this.validateAndApplyManifestParams(D.url,t.manifestUrl,t.title,r);case 82:(P=e.sent)||!c&&d(p.url)&&(c=!0),L=P.manifestResult,null!=P&&P.searchParams&&(h=P.searchParams);case 86:if(D.url=this.applySearchParamsToUrl(D.url,h),!i){e.next=97;break}if(null===(P=L)||void 0===P||!P.streamType){e.next=97;break}if(!L.segments){e.next=95;break}D.mediaSequence=(null===(M=L)||void 0===M?void 0:M.mediaSequence)||0,D.segments=L.segments,delete D.url,e.next=97;break;case 95:return e.abrupt("return");case 97:"hls"===f&&null!=t&&null!==(C=t.playlist)&&void 0!==C&&null!==(C=C[0])&&void 0!==C&&C.contentProtection&&(D.contentProtection=t.playlist[0].contentProtection),o.push(D);case 99:e.next=62;break;case 101:e.next=106;break;case 103:e.prev=103,e.t0=e.catch(60),N.e(e.t0);case 106:return e.prev=106,N.f(),e.finish(106);case 109:k++,e.next=56;break;case 112:if(!t.segments){e.next=122;break}if(t.type=this.determineMediaType(t.segments[0].url),h||a||l){e.next=120;break}return e.next=118,this.validateMediaUrl(t.segments[0].url,t.manifestUrl,r);case 118:null!=(M=e.sent)&&M.searchParams&&(h=M.searchParams);case 120:t.segments=this.applySearchParamsToSegments(t.segments,h),o.push(t);case 122:e.next=128;break;case 124:throw e.prev=124,e.t1=e.catch(9),e.t1;case 128:if(s||0!=o.length||0!=c){e.next=132;break}throw new Error("manifestToVideoLinks no videoLinks");case 132:return e.abrupt("return",o);case 133:case"end":return e.stop()}},e,this,[[9,124],[60,103,106,109]])})),function(e,t){return r.apply(this,arguments)})},{key:"getVideoLinksFromManifestText",value:(n=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,r,n){var a,i,o,s=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return o=3<s.length&&void 0!==s[3]?s[3]:"",a=4<s.length&&void 0!==s[4]?s[4]:{},i=5<s.length&&void 0!==s[5]&&s[5],e.next=5,this.getManifestDataFromText(t,r,n,o);case 5:return o=e.sent,e.abrupt("return",o?this.manifestToVideoLinks(o,a,!1,i,!1):[]);case 7:case"end":return e.stop()}},e,this)})),function(e,t,r){return n.apply(this,arguments)})}]),e}(),Downloader=function(){"use strict";function e(){_classCallCheck(this,e),this.downloadInfo={},this.progressInterval=null,this.initListeners()}var n,t;return _createClass(e,[{key:"initListeners",value:function(){var o=this;browser.runtime.onInstalled.addListener(function(e){"install"===e.reason&&o.clearDownloadCount()}),browser.downloads.onChanged.addListener(function(e){var t,r,n=e.id,a=null,i="";n in o.downloadInfo&&(t=o.downloadInfo[n],"paused"in e?(a=e.paused.current?"paused":"downloading",e.paused.current||o.downloading()):"state"in e&&(a=e.state.current,"in_progress"===e.state.current?o.downloading():"complete"===e.state.current?o.completeDownload(n):"interrupted"===e.state.current&&null!=e&&null!==(r=e.error)&&void 0!==r&&r.current&&(i=e.error.current)),a&&(o.handleStateChange(t,a,i),null==t||!t.sendResponse||"in_progress"!==a&&"complete"!==a&&"interrupted"!==a||(t.sendResponse({result:0<i.length?-1:n,downloadId:n,msg:"complete"===a?"success":i||a}),delete t.sendResponse)))})}},{key:"handleStateChange",value:function(e,t){var r=e.tabId,t={message:"download-changed",key:e.key,state:t,errmsg:2<arguments.length&&void 0!==arguments[2]?arguments[2]:""};r&&-1!==r?browser.tabs.sendMessage(r,t):browser.runtime.sendMessage(t)}},{key:"handleProgress",value:function(e,t){var r=e.tabId,t=_objectSpread({message:"progress-changed",key:e.key,progress:t},2<arguments.length&&void 0!==arguments[2]?arguments[2]:null);browser.runtime.sendMessage(t),r&&-1!==r&&browser.tabs.sendMessage(r,t)}},{key:"completeDownload",value:(t=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var r,n,a,i;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,p(browser.downloads.search,{id:t});case 2:r=e.sent,n=_createForOfIteratorHelper(r);try{for(n.s();!(a=n.n()).done;)i=a.value,a=-1!=i.totalBytes?Math.floor(i.bytesReceived/i.totalBytes*100):-1,i={bytesReceived:i.bytesReceived,totalBytes:i.totalBytes},this.handleProgress(this.downloadInfo[t],a,i)}catch(e){n.e(e)}finally{n.f()}delete this.downloadInfo[t];case 6:case"end":return e.stop()}},e,this)})),function(e){return t.apply(this,arguments)})},{key:"downloading",value:function(){var u=this;null==this.progressInterval&&(this.progressInterval=setInterval(_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t,r,n,a,i,o,s;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,p(browser.downloads.search,{state:"in_progress"});case 2:t=e.sent,r=!1,n=Object.keys(u.downloadInfo),a=_createForOfIteratorHelper(t);try{for(a.s();!(s=a.n()).done;)(i=s.value).byExtensionId===browser.runtime.id&&n.includes(i.id.toString())&&(r=!0,o=-1!=i.totalBytes?Math.floor(i.bytesReceived/i.totalBytes*100):-1,s={bytesReceived:i.bytesReceived,totalBytes:i.totalBytes},u.handleProgress(u.downloadInfo[i.id],o,s))}catch(e){a.e(e)}finally{a.f()}0==r&&u.progressInterval&&(clearInterval(u.progressInterval),u.progressInterval=null);case 8:case"end":return e.stop()}},e)})),1e3))}},{key:"downloadVideoLink",value:(n=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,r,n){var a,i,o,s=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(a=!(3<s.length&&void 0!==s[3])||s[3],t.hasOwnProperty("url")){e.next=3;break}return e.abrupt("return",n({result:-1,msg:"Invalid argument"}));case 3:return i={url:t.url},t.hasOwnProperty("fileName")&&(i.filename=configuration.BRAND+"/"+t.fileName),t.hasOwnProperty("saveAs")&&(i.saveAs=t.saveAs),t.hasOwnProperty("headers")&&("object"==_typeof(t.headers)?i.headers=Object.entries(t.headers).map(function(e){e=_slicedToArray(e,2);return{name:e[0],value:e[1]}}):i.headers=t.headers),t.hasOwnProperty("method")&&(i.method=t.method),t.hasOwnProperty("body")&&(i.body=t.body),e.next=11,p(browser.downloads.download,i).catch(function(e){return n({result:-1,msg:"Invalid argument"}),null});case 11:(i=e.sent)&&(this.downloadInfo[i]={tabId:(null==t?void 0:t.tabId)||(null==r||null===(o=r.tab)||void 0===o?void 0:o.id)||-1,key:(null==t?void 0:t.key)||t.url,fileName:(null==t?void 0:t.fileName)||null,sendResponse:a?n:null},this.downloading(),0==a&&n({result:i,downloadId:i,msg:i?"success":"fail"}));case 13:case"end":return e.stop()}},e,this)})),function(e,t,r){return n.apply(this,arguments)})},{key:"incrementDownloadCount",value:function(){browser.storage.local.get(["total_number_of_downloads"],function(e){e=e.total_number_of_downloads+1;browser.storage.local.set({total_number_of_downloads:e}),3===e&&confirm("You have downloaded multiple videos with Video Downloader professional. Please share your experience with others and make a review for us.")&&browser.tabs.create({url:"http://smarturl.it/vdm-reviews",selected:!0},function(e){}),7===e&&confirm("if you like what we do and can support our work, please make a donation so we can keep on making it even better.")&&browser.tabs.create({url:"http://smarturl.it/vdm-contribute",selected:!0},function(e){})})}},{key:"clearDownloadCount",value:function(){browser.storage.local.set({total_number_of_downloads:0})}}]),e}(),Recorder=function(){"use strict";function e(){_classCallCheck(this,e),_defineProperty(this,"config",configuration),_defineProperty(this,"general",null),_defineProperty(this,"tabs",{}),_defineProperty(this,"tabsKey","recordTabs-".concat(browser.runtime.id.substr(0,8))),_defineProperty(this,"getActiveTab",_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t={active:!0,lastFocusedWindow:!0},e.next=3,chrome.tabs.query(t);case 3:return t=e.sent,t=_slicedToArray(t,1),t=t[0],e.abrupt("return",t);case 7:case"end":return e.stop()}},e)}))),_defineProperty(this,"setTimerContent",function(t,i){return new Promise(function(){var r=_asyncToGenerator(_regeneratorRuntime().mark(function e(r,n){var a;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:chrome.tabs.sendMessage(t.id,{operation:"content_start_timer",countdown:i},{frameId:0}),a=setTimeout(function(){r(!1)},1e3*i+500),chrome.runtime.onMessage.addListener(function e(t){switch(t.operation){case"content_end_timer":chrome.runtime.onMessage.removeListener(e),clearInterval(a),r(t.type);break;case"content_stop_timer":chrome.runtime.onMessage.removeListener(e),clearInterval(a),n(t.type)}});case 3:case"end":return e.stop()}},e)}));return function(e,t){return r.apply(this,arguments)}}())}),this.progressInterval=null,this.initListeners(),this.getRecordTabs()}var n,t,a,i,r,o,s,u;return _createClass(e,[{key:"getRecordTabs",value:(u=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,localStorageGet(this.tabsKey);case 2:null!=(t=e.sent)&&t.tabId&&(this.tabs=t[String(tabId)]);case 4:case"end":return e.stop()}},e,this)})),function(){return u.apply(this,arguments)})},{key:"storeRecordTabs",value:function(){localStorageSet(_defineProperty({},this.tabsKey,this.tabs))}},{key:"initListeners",value:function(){var i=this;browser.runtime.onMessage.addListener(function(e,t,r){switch(e.message){case"get-tab-status":return i.getRecordStatus(null===(a=e.tab)||void 0===a?void 0:a.id).then(function(e){r(e)}),!0;case"start-record":i.startRecord(e.tab,e.mode,e.options),r();break;case"cancel-record":i.cancelRecord(e.tab,null==e?void 0:e.mode),r();break;case"stop-record":i.stopRecord(e.tab,null==e?void 0:e.mode),r();break;case"get-target-tab":var n,a=null;a="contentIframe"==i.config.GENERAL.tabRecordType?null===(n=i.tabs)||void 0===n?void 0:n[t.tab.id]:i.findTabById("recordTabId",null===(n=t.tab)||void 0===n?void 0:n.id),r(a);break;case"update-status":i.updateStatus((null==e?void 0:e.tab)||t.tab,e.status,null==e?void 0:e.mode),r();break;case"check-capture-permissions":return chrome.permissions.contains({permissions:["desktopCapture","alarms","offscreen"]},function(e){e?r({status:"ok"}):chrome.permissions.request({permissions:["desktopCapture","alarms","offscreen"]},function(e){e?(addAlarmListener(),r({status:"ok"})):r({status:"error"})})}),!0}}),browser.commands.onCommand.addListener(function(){var t=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var r;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if("stop-recording"===t)return e.next=3,i.getActiveTab();e.next=7;break;case 3:r=e.sent,i.stopRecord(r),e.next=12;break;case 7:if("cancel-recording"===t)return e.next=10,i.getActiveTab();e.next=12;break;case 10:r=e.sent,i.cancelRecord(r);case 12:case"end":return e.stop()}},e)}));return function(e){return t.apply(this,arguments)}}()),browser.tabs.onRemoved.addListener(function(e,t){var r,n;null!==(r=i.tabs)&&void 0!==r&&null!==(r=r[e])&&void 0!==r&&r.targetTabId?(n=i.tabs[e].targetTabId,null!==(r=i.tabs)&&void 0!==r&&null!==(r=r[n])&&void 0!==r&&r.recordTabId&&(i.tabs[n].recordTabId=null,i.tabs[n].status={})):null!==(n=i.tabs)&&void 0!==n&&null!==(n=n[e])&&void 0!==n&&n.recordTabId,i.tabs[e]&&(delete i.tabs[e],i.storeRecordTabs())})}},{key:"createTab",value:function(e,t,r,n){var a=t.url,i=t.targetTabId,i=void 0===i?null:i,t=t.recordTabId,t=void 0===t?null:t;return this.tabs[e]={id:e,url:a,targetTabId:i,recordTabId:t,mode:r,options:n},this.tabs[e]}},{key:"findTabById",value:function(e,t){for(var r=0,n=Object.values(this.tabs);r<n.length;r++){var a=n[r];if((null==a?void 0:a[e])===t)return a}return null}},{key:"getRecordStatus",value:(s=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var r,n,a;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if("Video"==(null==(r=null===(r=this.tabs)||void 0===r?void 0:r[t])?void 0:r.mode))return a={frameId:(null==r||null===(n=r.options)||void 0===n?void 0:n.frameId)||0},e.next=5,p(browser.tabs.sendMessage,t,{message:"get-videoRecord-status",element:r.options},a).catch(function(e){return null});e.next=7;break;case 5:a=e.sent,r.status=a;case 7:return e.abrupt("return",r);case 8:case"end":return e.stop()}},e,this)})),function(e){return s.apply(this,arguments)})},{key:"cancelRecord",value:(o=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,r){var n,a,i,o,s;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(e.t0=t,e.t0){e.next=5;break}return e.next=4,this.getActiveTab();case 4:e.t0=e.sent;case 5:if(t=e.t0,n=t.id,a=null===(o=this.tabs)||void 0===o?void 0:o[n],"Video"==(r=r||a.mode))return s={frameId:(null==a||null===(i=a.options)||void 0===i?void 0:i.frameId)||0},e.next=13,browser.tabs.sendMessage(n,{message:"cancel-videoRecord",element:a.options},s);e.next=15;break;case 13:e.next=19;break;case 15:return(o=null==a?void 0:a.recordTabId)||(s=this.findTabById("targetTabId",n),o=null==s?void 0:s.id),e.next=19,browser.tabs.sendMessage(o,{message:"do-cancel-record"});case 19:case"end":return e.stop()}},e,this)})),function(e,t){return o.apply(this,arguments)})},{key:"stopRecord",value:(r=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,r){var n,a,i,o,s;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(e.t0=t,e.t0){e.next=5;break}return e.next=4,this.getActiveTab();case 4:e.t0=e.sent;case 5:if(t=e.t0,n=t.id,null!=(a=null===(o=this.tabs)||void 0===o?void 0:o[n])&&a.recordDurationTimer&&(clearTimeout(a.recordDurationTimer),a.recordDurationTimer=null),"Video"==(r=r||a.mode))return s={frameId:(null==a||null===(i=a.options)||void 0===i?void 0:i.frameId)||0},e.next=14,browser.tabs.sendMessage(n,{message:"stop-videoRecord",element:a.options},s);e.next=16;break;case 14:e.next=25;break;case 16:if("Tab"==r&&"contentIframe"==this.config.GENERAL.tabRecordType)return e.next=19,browser.tabs.sendMessage(n,{message:"do-stop-record"});e.next=21;break;case 19:e.next=25;break;case 21:return(o=null==a?void 0:a.recordTabId)||(s=this.findTabById("targetTabId",n),o=null==s?void 0:s.id),e.next=25,browser.tabs.sendMessage(o,{message:"do-stop-record"});case 25:case"end":return e.stop()}},e,this)})),function(e,t){return r.apply(this,arguments)})},{key:"startVideoElRecord",value:function(e,t,r){browser.tabs.sendMessage(e.id,{message:"request-videoRecord",element:r},{frameId:r.frameId})}},{key:"startTabRecord",value:(i=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,r,n,a){var i;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if("Tab"==n&&a.crop||"Audio"!==n&&"contentIframe"==this.config.GENERAL.tabRecordType)return t.recordTabId=t.id,t.targetTabId=t.id,e.next=5,browser.tabs.sendMessage(t.id,{message:"do-start-record",tab:t});e.next=6;break;case 5:return e.abrupt("return",e.sent);case 6:if(!t.recordTabId){e.next=10;break}browser.tabs.update(t.recordTabId,{active:!0}),e.next=19;break;case 10:return e.next=12,browser.tabs.create({url:chrome.runtime.getURL("html/recording.html"),index:r+1,active:!1,pinned:!0});case 12:return i=e.sent,this.createTab(i.id,{url:i.url||i.pendingUrl,targetTabId:t.id},n,a),t.recordTabId=i.id,e.next=17,new Promise(function(r){function n(e,t){e===i.id&&"complete"===t.status&&(browser.tabs.onUpdated.removeListener(n),r())}browser.tabs.onUpdated.addListener(n)});case 17:return e.next=19,browser.tabs.sendMessage(t.recordTabId,{message:"do-start-record",tab:t});case 19:case"end":return e.stop()}},e,this)})),function(e,t,r,n){return i.apply(this,arguments)})},{key:"startScreenRecord",value:(a=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,r,n){var a,i;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,browser.tabs.create({url:chrome.runtime.getURL("html/recording.html"),index:t+1,active:!0,pinned:!0});case 2:return a=e.sent,i=this.createTab(a.id,{url:a.url||a.pendingUrl,targetTabId:a.id,recordTabId:a.id},r,n),e.next=6,new Promise(function(r){function n(e,t){e===a.id&&"complete"===t.status&&(browser.tabs.onUpdated.removeListener(n),r())}browser.tabs.onUpdated.addListener(n)});case 6:return e.next=8,browser.tabs.sendMessage(a.id,{message:"do-start-record",tab:i});case 8:case"end":return e.stop()}},e,this)})),function(e,t,r){return a.apply(this,arguments)})},{key:"getRecordCountDownload",value:(t=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,getStoredGeneral();case 2:return t=e.sent,t=_objectSpread(_objectSpread({},this.config.GENERAL),t),e.abrupt("return",(null==t?void 0:t.recordCountDown)||0);case 5:case"end":return e.stop()}},e,this)})),function(){return t.apply(this,arguments)})},{key:"startRecord",value:(n=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,r,n){var a,i,o,s,u;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(i=t.id,o=t.index,u=t.url,s=this.tabs[i],"recording"==(null===(a=s)||void 0===a||null===(a=a.status)||void 0===a?void 0:a.status)&&this.stopRecord(t,s.mode),s=this.createTab(i,{url:u},r,n),e.prev=4,"Video"!=r&&"Tab"!=r){e.next=15;break}if("Tab"!=r||!n.crop){e.next=10;break}s.crop=n.crop,e.next=15;break;case 10:return e.next=12,this.getRecordCountDownload();case 12:return u=e.sent,e.next=15,this.setTimerContent(s,u);case 15:if("Video"!=r){e.next=19;break}this.startVideoElRecord(s,r,n),e.next=26;break;case 19:if("Screen"==r)return e.next=22,this.startScreenRecord(o,r,n);e.next=24;break;case 22:e.next=26;break;case 24:return e.next=26,this.startTabRecord(s,o,r,n);case 26:e.next=31;break;case 28:e.prev=28,e.t0=e.catch(4);case 31:return e.prev=31,s.waiting=!1,this.storeRecordTabs(),e.finish(31);case 35:case"end":return e.stop()}},e,this,[[4,28,31,35]])})),function(e,t,r){return n.apply(this,arguments)})},{key:"startRecordDurationTimer",value:function(e,t,r){var n,a=this,i=e.id;null!==(n=this.tabs[i])&&void 0!==n&&n.recordDurationTimer&&clearTimeout(this.tabs[i].recordDurationTimer),this.tabs[i].recordDurationTimer=setTimeout(function(){a.stopRecord(e,t)},1e3*r)}},{key:"updateStatus",value:function(e,t,r){var n,a,i=e.id,i=(e.index,e.url,null===(n=this.tabs)||void 0===n?void 0:n[i]);r=r||i.mode,"recording"==t.status&&null!=i&&null!==(a=i.options)&&void 0!==a&&a.enableRecordDuration&&this.startRecordDurationTimer(e,r,i.options.recordDuration),i.status=t,sendMessage({message:"record-status-update",tab:i})}}]),e}(),recorder=new Recorder;String.prototype.htmlSymDecode=function(){var e=document.createElement("div");return e.innerHTML=this,e.innerText};var Sentrybrowser=function(){"use strict";function t(e){_classCallCheck(this,t),_defineProperty(this,"client",null),this.init(e)}return _createClass(t,[{key:"init",value:function(e){var t=Sentry.getDefaultIntegrations({}).filter(function(e){return!["BrowserApiErrors","Breadcrumbs","GlobalHandlers"].includes(e.name)});this.client=new Sentry.BrowserClient({dsn:configuration.sentryDSN,release:chrome.runtime.getManifest().version,environment:e?"Prod":"Dev",transport:Sentry.makeFetchTransport,stackParser:Sentry.defaultStackParser,integrations:t}),(new Sentry.Scope).setClient(this.client),this.client.init()}},{key:"reportMsg",value:function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"error",r=2<arguments.length?arguments[2]:void 0;this.client&&(r=r?"user: ".concat(r," "):"",this.client.captureMessage("".concat(configuration.BRAND,": ").concat(r," ").concat(e),t)),is_release||(e={type:"basic",title:"show reportMsg for dev",message:e,iconUrl:"images/logo.png"},browser.notifications.create(e))}}]),t}(),Background=function(){"use strict";function e(){_classCallCheck(this,e),this.config=configuration,this.init()}var t,r,n,a,i,o,s,u,c,l,d,f,h,m,g,v,b,y,_,w,E,T,S,x,k,R,I,A,N,D,O,C,L;return _createClass(e,[{key:"init",value:(L=_asyncToGenerator(_regeneratorRuntime().mark(function e(){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:this.firstInitListeners(),this.isrelease=is_release,this.sentry=new Sentrybrowser(is_release),this.ga=new Ga(this.config.MEASUREMENT_ID,this.config.API_SECRET),this.ga.setExtraParamsCallbcak(this.getUserInfo.bind(this)),this.activated=!1,this.uaInfo=parseUserAgent(navigator.userAgent),this.license=new License(this.config.LICENSE_BASEURL,this.config.STORE_ID,this.config.PRODUCT_IDS,3600),this.downloader=new Downloader,this.stream=new Stream,this.tabsData={},this.streamReload={},this.requestHeaders={},this.polyvNet={manifestIds:{},pandingStreams:new Map},this.boomstream={info:new Map,mediaLinks:new Map,pandingStreams:new Map},this.initFilter(),this.initCspList(),this.initListeners(),this.initContextMenu();case 19:case"end":return e.stop()}},e,this)})),function(){return L.apply(this,arguments)})},{key:"getUserInfo",value:function(){try{if(this.licenseInfo)return _objectSpread(_objectSpread({custom:this.licenseInfo.customer_name,key:this.licenseInfo.license_key},this.uaInfo),{},{extension_source:extension_source})}catch(e){}return _objectSpread(_objectSpread({},this.uaInfo),{},{extension_source:extension_source})}},{key:"reportMsg",value:function(e,t){this.sentry.reportMsg(e,t,null===(t=this.licenseInfo)||void 0===t?void 0:t.customer_email)}},{key:"activate",value:(C=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,r){var n;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.license.activate(t,r);case 2:return(n=e.sent).activated&&(this.activated=!0,this.licenseInfo=n.licenseInfo),e.abrupt("return",n);case 5:case"end":return e.stop()}},e,this)})),function(e,t){return C.apply(this,arguments)})},{key:"deactivate",value:(O=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.license.deactivate();case 2:return(t=e.sent).deactivated&&(this.activated=!1,this.licenseInfo=null),e.abrupt("return",t);case 5:case"end":return e.stop()}},e,this)})),function(){return O.apply(this,arguments)})},{key:"checkLicense",value:(D=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.license.checkLicense();case 2:return(t=e.sent).valid&&(this.activated=!0,this.licenseInfo=t.licenseInfo),e.abrupt("return",t);case 5:case"end":return e.stop()}},e,this)})),function(){return D.apply(this,arguments)})},{key:"initFilter",value:(N=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,getStoredFilter();case 2:return t=e.sent,e.next=5,updateFilter(t,this.config.MEDIA_FILTER);case 5:if(this.filter=e.sent,areObjectsEqual(this.filter,t)){e.next=9;break}return e.next=9,storeFilter(this.filter);case 9:chrome.storage.onChanged.addListener(this.filterListener.bind(this));case 10:case"end":return e.stop()}},e,this)})),function(){return N.apply(this,arguments)})},{key:"filterListener",value:function(e,t){var r="".concat(browser.runtime.id,"_filter");"sync"===t&&e[r]&&(this.filter=e[r].newValue)}},{key:"initCspList",value:(A=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,getStoredCspList();case 2:t=e.sent,this.cspDomainlist=null==t?this.config.CSP_LIST:t.filter(function(e){return e.enable}).map(function(e){return e.domain}),this.cspDomainlist.length?this.applyCspRulesRemove(this.cspDomainlist):this.removeCspRulesRemove(),chrome.storage.onChanged.addListener(this.cspListListener.bind(this));case 6:case"end":return e.stop()}},e,this)})),function(){return A.apply(this,arguments)})},{key:"cspListListener",value:(I=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,r){var n;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:n="".concat(browser.runtime.id,"_csplist"),"sync"===r&&t[n]&&(this.cspDomainlist=t[n].newValue.filter(function(e){return e.enable}).map(function(e){return e.domain}),this.cspDomainlist.length?this.applyCspRulesRemove(this.cspDomainlist):this.removeCspRulesRemove());case 2:case"end":return e.stop()}},e,this)})),function(e,t){return I.apply(this,arguments)})},{key:"updateCspFromConfig",value:(R=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var r,t;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,getStoredCspList();case 2:(r=e.sent)&&(t=r,this.config.CSP_LIST.forEach(function(t){r.some(function(e){return e.domain===t})||r.push({domain:t,enable:!0})}),t!=r.length&&storeCspList(r));case 4:case"end":return e.stop()}},e,this)})),function(){return R.apply(this,arguments)})},{key:"firstInitListeners",value:function(){var t=this;browser.runtime.onInstalled.addListener(function(e){"install"!=e.reason&&"update"!=e.reason||(browser.storage.local.remove([chrome.runtime.id]),"install"==e.reason?(t.config.hasOwnProperty("ENABLE_INSTALL_URL")&&t.config.ENABLE_UNINSTALL_URL&&browser.tabs.create({url:t.config.INSTALL_URL}),t.ga.sendGaEvent("install","")):"update"==e.reason&&(t.updateCspFromConfig(),t.config.hasOwnProperty("ENABLE_UPDATE_URL")&&t.config.ENABLE_UNINSTALL_URL&&browser.tabs.create({url:t.config.UPDATE_URL}),p(browser.tabs.query,{}).then(function(e){localStorageRemove(e.map(function(e){return String(e.id)})).catch(function(e){})}).catch(function(e){}),t.tabsData={},t.getCurrActiveTab().then(function(e){e&&(t.updateExtensionIcon(e.id),t.installScripts([e]))})))})}},{key:"deepKeyValueSearch",value:function(e,t){var r,n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;for(r in e)if(e.hasOwnProperty(r)){if(r===t&&(null==n||e[r]===n))return e[r];if("object"===_typeof(e[r])&&null!==e[r]){var a=this.deepKeyValueSearch(e[r],t);if(void 0!==a)return a}}}},{key:"initListeners",value:function(){var k=this;this.config.hasOwnProperty("ENABLE_UNINSTALL_URL")&&this.config.ENABLE_UNINSTALL_URL&&this.config.hasOwnProperty("UINSTALL_URL")&&browser.runtime.setUninstallURL(this.config.UINSTALL_URL),browser.tabs.onUpdated.addListener(function(e,t,r){var n;t.hasOwnProperty("url")&&(null!==(n=k.tabsData)&&void 0!==n&&null!==(n=n[e])&&void 0!==n&&null!==(n=n.videoLinks)&&void 0!==n&&n.length&&Date.now()-(null===(n=k.tabsData)||void 0===n?void 0:n[e].time)<6e4?k.tabsData[e].url=t.url:k.resetClearOldLinks(e,t.url),k.updateExtensionIcon(e))}),browser.tabs.onRemoved.addListener(function(e,t){var r;k.getTabData(e),null!==(r=k.tabsData[e])&&void 0!==r&&r.injectedKey&&localStorageRemove(k.tabsData[e].injectedKey),k.removeTabDataToStorage(e),delete k.streamReload[e],k.clearExtDomainOpfsFilesOnTabRemoved(e)}),browser.webRequest.onBeforeRequest.addListener(function(e){k.requestHeaders[e.requestId]={requestBody:e.requestBody||{}}},{urls:["<all_urls>"],types:["media","xmlhttprequest","object","other","main_frame","sub_frame"]},["extraHeaders","requestBody"]),browser.webRequest.onSendHeaders.addListener(function(e){try{k.requestHeaders[e.requestId].requestHeaders=e.requestHeaders||[]}catch(e){}},{urls:["<all_urls>"],types:["media","xmlhttprequest","object","other","main_frame","sub_frame"]},["requestHeaders","extraHeaders"]),browser.webRequest.onHeadersReceived.addListener(function(){var t=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var r,n,a,i,o,s,u,c,l,d,f,h,m,g,v,b,y,_,w,E,T,S,x;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(n=(null===(u=k.requestHeaders[t.requestId])||void 0===u?void 0:u.requestHeaders)||[],a=(null===(c=k.requestHeaders[t.requestId])||void 0===c?void 0:c.requestBody)||{},delete k.requestHeaders[t.requestId],t.responseHeaders.length<1||t.statusCode<200||300<t.statusCode||"OPTIONS"===t.method)return e.abrupt("return");e.next=5;break;case 5:if(null!=t&&null!==(r=t.initiator)&&void 0!==r&&r.includes("videos.sproutvideo.com"))return e.abrupt("return");e.next=7;break;case 7:if(!0===(t.initiator||t.url).startsWith("chrome-extension://".concat(browser.runtime.id)))return e.abrupt("return");e.next=10;break;case 10:if(m=getPolyvNetManifestToken(t.url))return k.polyvNet.manifestIds[m.manifestId]=m.token,k.polyvNet.pandingStreams.has(m.manifestId)&&(l=k.polyvNet.pandingStreams.get(m.manifestId),k.polyvNet.pandingStreams.delete(m.manifestId),k.handleHlsDashStream(l)),e.abrupt("return");e.next=15;break;case 15:if(i=k.detailsHeadersToKeyValue(t.responseHeaders),-1!=t.tabId){e.next=39;break}if(!(!i.get("content-type")||i.get("content-type")&&mimeTypes.includes(i.get("content-type").toLowerCase().split(";")[0]))){e.next=38;break}if("xmlhttprequest"==t.type&&t.initiator&&200==(null==t?void 0:t.statusCode)&&-1==t.frameId&&-1==t.parentFrameId)return e.next=22,p(browser.tabs.query,{currentWindow:!0,url:t.initiator+"/*"}).catch(function(e){return null});e.next=34;break;case 22:if(!(0<(h=e.sent).length)){e.next=30;break}d=h.find(function(e){return e.active})||h[0],t.tabId=d.id,t.frameId=0,e.next=32;break;case 30:return e.abrupt("return");case 32:e.next=36;break;case 34:return e.abrupt("return");case 36:e.next=39;break;case 38:return e.abrupt("return");case 39:if(!(o=k.getMediaInfoFromReceivedHeader(i,t.url))){e.next=84;break}if(!o.ext||allowedExtensions.indexOf(o.ext)<0)return e.abrupt("return");e.next=43;break;case 43:if(o.download_url=t.url.replace(/&bytes=\d+-(\d+)?/,""),o.title||(o.title=t.title||"Unknown"),"stream"!=o.type&&needFilter(o.size,null===(f=k.filter)||void 0===f?void 0:f[o.type.toUpperCase()]))return e.abrupt("return");e.next=47;break;case 47:return e.next=49,k.getTabbyId(t.tabId);case 49:if(s=e.sent){e.next=52;break}return e.abrupt("return");case 52:if((null==s?void 0:s.url)===(null==t?void 0:t.url))return e.abrupt("return");e.next=54;break;case 54:o.webpage_url=s.url,(u=k.detailsHeadersToKeyValue(n).get()).range&&delete u.range,c=t.initiator||new URL(t.url).origin,m=new URL(t.url).origin,l=c!==m,h=null==n?void 0:n.some(function(e){e=e.name.toLowerCase();return"authorization"===e||e.startsWith("x-auth-")}),d=!(null==u||!u.cookie),f=null==i?void 0:i.get("access-control-allow-origin"),h=l?(m="false"===(null==i?void 0:i.get("access-control-allow-credentials")),"*"==f||null==f&&f!==c||!d&&!h||m?"omit":"include"):d||h?"include":"omit",g={headers:u,method:"POST"===t.method?"POST":"GET",credentials:h},!l||"*"==f&&f!==c||(o.checkIsCors=!0);try{"POST"===g.method&&null!=a&&null!==(v=a.raw)&&void 0!==v&&null!==(v=v[0])&&void 0!==v&&v.bytes&&(b=new TextDecoder("utf-8"),g.body=b.decode(new Uint8Array(a.raw[0].bytes)))}catch(e){}if("stream"!=o.type){e.next=73;break}y=null,"m3u8"==o.ext||"m3u"==o.ext?y="hls":"mpd"==o.ext&&(y="dash"),"hls"!=y&&"dash"!=y||k.delayCheckHandleHlsDashStream({manifestUrl:o.download_url,tabId:s.id,tabUrl:s.url,streamType:y,frameId:t.frameId,title:o.title,init:g}),e.next=82;break;case 73:if("image"!=o.type){e.next=78;break}if(k.config.DISABLE_IMAGE_HEADRECEIVE_DOMAINS_REGEXP.test(new URL(s.url).host))return e.abrupt("return");e.next=76;break;case 76:e.next=80;break;case 78:if(k.config.DISABLE_HEADRECEIVE_DOMAINS_REGEXP.test(new URL(s.url).host))return e.abrupt("return");e.next=80;break;case 80:o.init=g,k.addVideoLinks({videoLinks:[o],tabId:t.tabId,tabUrl:s.url,frameId:t.frameId,initiator:t.initiator,isFromWebReq:!0});case 82:e.next=100;break;case 84:if(!(y=t.url.match(/\.([^\.\/\?]+)($|\?)/))){e.next=100;break}if(-1!==(y=y[1]).indexOf(".m3u")||-1!==y.indexOf(".mpd"))return _=-1!==y.indexOf(".m3u")?"hls":"dash",(w=k.detailsHeadersToKeyValue(n).get()).range&&delete w.range,e.next=94,k.getTabbyId(t.tabId);e.next=100;break;case 94:if(E=e.sent){e.next=97;break}return e.abrupt("return");case 97:T={headers:w,method:"POST"===t.method?"POST":"GET"};try{"POST"===T.method&&null!=a&&null!==(S=a.raw)&&void 0!==S&&null!==(S=S[0])&&void 0!==S&&S.bytes&&(x=new TextDecoder("utf-8"),T.body=x.decode(new Uint8Array(a.raw[0].bytes)))}catch(e){}k.delayCheckHandleHlsDashStream({manifestUrl:t.url,tabId:E.id,tabUrl:E.url,streamType:_,frameId:t.frameId,init:T});case 100:case"end":return e.stop()}},e)}));return function(e){return t.apply(this,arguments)}}(),{urls:["<all_urls>"],types:["media","xmlhttprequest","object","other","main_frame","sub_frame"]},["extraHeaders","responseHeaders"]),browser.runtime.onMessage.addListener(function(r,t,n){var e;switch(r.message){case"sentry-report-msg":k.reportMsg(r.data.message,(null===(e=r.data)||void 0===e?void 0:e.level)||"error"),n();break;case"get-extension-id":n(browser.runtime.id);break;case"add-boomstream-info":k.boomstream.info.set(r.info.src,r.info),k.boomstream.pandingStreams.has(r.info.src)&&(i=k.boomstream.pandingStreams.get(r.info.src),k.boomstream.pandingStreams.delete(r.info.src),null!=a&&a.duration&&(i.duration=a.duration),null!=a&&a.title&&(i.title=a.title),k.handleHlsDashStream(i)),n();break;case"get-tab-info":var a={url:t.tab.url,title:t.tab.title,incognito:t.tab.incognito,pinned:t.tab.pinned,id:t.tab.id,frameId:t.frameId};n(a);break;case"get-tab-info-by-host":var i=r.host||new URL(t.tab.url).host;return k.getTabsInfoByHost(i).then(function(e){return n(e)}),!0;case"add-video-links":k.addVideoLinks({videoLinks:r.videoLinks,tabId:t.tab.id,tabUrl:t.tab.url,frameId:t.frameId,initiator:t.origin,isFromWebReq:!1}),n();break;case"install-script":return k.getTabbyId(r.tabId).then(function(e){k.installScripts([e],r.frameId||0).then(function(){return n(!0)})}).catch(function(e){n(!1)}),!0;case"reset-video-links":var o=null,s=null;null!=r&&r.tabId?(o=r.tabId,s=null==r?void 0:r.tabUrl):t.tab&&0==t.frameId&&(o=t.tab.id,s=t.tab.url,k.checkReloadCspDomain(o,s)),null!=o&&(k.resetClearOldLinks(o,s),k.updateExtensionIcon(o)),n();break;case"ajax-get":var s=r.hasOwnProperty("method")?r.method:"GET",u=r.hasOwnProperty("type")?r.type:"text",o=r.hasOwnProperty("init")?r.init:null;return null==o&&(o={method:s}),XMLRequest(r.url,o).then(function(){var t=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if("HEAD"==r.method)return e.abrupt("return",k.getMediaInfoFromReceivedHeader(t.headers,t.url));e.next=2;break;case 2:if("arrayBuffer"==u)return e.t0=n,e.t1=buf2hex,e.next=7,t.arrayBuffer();e.next=12;break;case 7:return e.t2=e.sent,e.t3=(0,e.t1)(e.t2),e.abrupt("return",(0,e.t0)(e.t3));case 12:if("json"==u)return e.t4=n,e.next=16,t.json();e.next=20;break;case 16:return e.t5=e.sent,e.abrupt("return",(0,e.t4)(e.t5));case 20:return e.t6=n,e.next=23,t.text();case 23:return e.t7=e.sent,e.abrupt("return",(0,e.t6)(e.t7));case 25:case"end":return e.stop()}},e)}));return function(e){return t.apply(this,arguments)}}()).catch(function(e){n()}),!0;case"ajax-request":k.ajaxRequestHandler(r,t),n({received:!0});break;case"get-video-links":return k.getTabData(r.tabId).then(function(e){e?k.sendDataTo(null==t?void 0:t.tabId,r.uniqueEventName,r.message+"-chunk",e,n):n({tabData:{}})}),!0;case"store-injected-linkskey":return k.storeInjectedVideoLinksKey(r.tabId,r.linkskey),n(),!0;case"show-youtube-warning":n();break;case"query-video-links":browser.tabs.create({url:k.config.QUERY_PATH+encodeURIComponent(r.url)}),n();break;case"handle-stream-url":var c={manifest:r.manifest,tabId:t.tab.id,tabUrl:t.tab.url,streamType:r.streamType,frameId:t.frameId||0,initiator:(null==r?void 0:r.initiator)||t.origin,manifestUrl:null==r?void 0:r.url,title:null==r?void 0:r.title,init:(null==r?void 0:r.init)||{},sig:(null==r?void 0:r.sig)||null,addVideoLinks:!0,isFromWebReq:!1};k.handleHlsDashStream(c).then(function(e){}),n();break;case"parse-stream-manifest":c={manifest:r.manifest,tabId:t.tab.id,tabUrl:t.tab.url,streamType:r.streamType,frameId:t.frameId||0,initiator:(null==r?void 0:r.initiator)||t.origin,manifestUrl:(null==r?void 0:r.url)||"",title:(null==r?void 0:r.title)||"",init:(null==r?void 0:r.init)||{},sig:(null==r?void 0:r.sig)||null,addVideoLinks:!1,isFromWebReq:!1,isParseRequest:!0};return k.handleHlsDashStream(c).then(function(e){k.sendDataTo(null==t?void 0:t.tabId,r.uniqueEventName,r.message+"-chunk",e,n)}),!0;case"download-video-link":return k.downloader.downloadVideoLink(r,t,n),!0;case"activate":return k.activate(r.license,r.instance_name).then(function(e){return n(e)}),!0;case"deactivate":return k.deactivate().then(function(e){return n(e)}),!0;case"checkLicense":return k.checkLicense().then(function(e){return n(e)}),!0;case"send-notification":browser.notifications.create(_objectSpread(_objectSpread({},r.content),{},{iconUrl:"images/logo.png"})),n()}}),browser.runtime.onMessageExternal.addListener(function(t,e,r){switch(t.message){case"download-video-link":return k.downloader.downloadVideoLink(t,e,r),!0;case"ajax-get":var n=t.hasOwnProperty("method")?t.method:"GET",a=t.hasOwnProperty("type")?t.type:"text",i=t.hasOwnProperty("init")?t.init:null;return null==i&&(i={method:n}),XMLRequest(t.url,i).then(function(e){return"HEAD"==t.method?k.getMediaInfoFromReceivedHeader(e.headers,e.url):r("hexstr"==a?e.arrayBuffer():e.text())}).catch(function(e){r()}),!0}})}},{key:"clearExtDomainOpfsFilesOnTabRemoved",value:(k=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var r,n;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,navigator.storage.estimate();case 2:if(r=e.sent,null!=(r=r.usageDetails)&&r.fileSystem)return e.next=7,navigator.storage.getDirectory();e.next=12;break;case 7:return n=e.sent,e.next=10,DownloadBuffers.clearTabTempFiles(t,n);case 10:return e.next=12,DownloadBuffers.clearOldTempFiles(n);case 12:case"end":return e.stop()}},e)})),function(e){return k.apply(this,arguments)})},{key:"ajaxRequestHandler",value:(x=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,r){var n,a,i,o,s,u,c,l,d,p,f,h,m,g,v,b,y,_,w,E,T,S,x;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return n=t.url,a=t.type,i=t.init,o=t.uniqueEventName,s=t.initiator,u=t.maxChunkSize,p=t.timeout,c=void 0===p?3e4:p,l=new AbortController,d=l.signal,p=setTimeout(function(){l.abort()},c),e.prev=5,e.next=8,fetch(n,_objectSpread(_objectSpread({},i),{},{signal:d})).catch(function(e){if("AbortError"==e.name)throw e;return null});case 8:if(f=e.sent,clearTimeout(p),f&&f.ok){e.next=34;break}if(h=[],[500,520,429,503].includes(null===(v=f)||void 0===v?void 0:v.status))return e.next=15,delay(1e3+2e3*Math.random());e.next=17;break;case 15:e.next=25;break;case 17:return e.next=19,delay(1e3);case 19:if((v=(null==i||null===(m=i.headers)||void 0===m?void 0:m.referer)||s)||null!=r&&null!==(g=r.tab)&&void 0!==g&&g.url.startsWith("chrome-extension://".concat(browser.runtime.id))||(v=r.tab.url),v)return e.next=24,updateSessionRules([n],v);e.next=25;break;case 24:h=e.sent;case 25:return p=setTimeout(function(){l.abort()},c),e.next=28,fetch(n,_objectSpread(_objectSpread({},i),{},{cache:"no-store",signal:d}));case 28:if(f=e.sent,clearTimeout(p),f.ok){e.next=34;break}return chrome.tabs.sendMessage(r.tab.id,{message:"ajax-response",uniqueEventName:o,error:"Not ok, ".concat(f.status)}),h.length&&browser.declarativeNetRequest.updateSessionRules({removeRuleIds:h}),e.abrupt("return");case 34:if("arrayBuffer"!==a){e.next=57;break}chrome.tabs.sendMessage(r.tab.id,{message:"ajax-response",uniqueEventName:o,data:null,progress:0,done:!1}),b=f.body.getReader(),y=[],w=_=0;case 40:return e.next=43,b.read();case 43:if(T=e.sent,E=T.done,T=T.value,E)return e.abrupt("break",54);e.next=48;break;case 48:y.push(Array.from(T)),_+=T.length,w+=T.length,u<=_&&(S=w/(getSizeFromReceivedHeader(f.headers)||20*w)*100,chrome.tabs.sendMessage(r.tab.id,{message:"ajax-response",uniqueEventName:o,data:_toConsumableArray(y),progress:S,done:E}),y=[],_=0),e.next=40;break;case 54:0<y.length&&(S=w/(f.headers.get("content-length")||w)*100,chrome.tabs.sendMessage(r.tab.id,{message:"ajax-response",uniqueEventName:o,data:y,progress:S,done:!0})),e.next=73;break;case 57:if("json"===a)return e.next=60,f.json();e.next=64;break;case 60:x=e.sent,chrome.tabs.sendMessage(r.tab.id,{message:"ajax-response",uniqueEventName:o,data:x}),e.next=73;break;case 64:if("headers"!=a&&"HEAD"!=(null==i?void 0:i.method)){e.next=69;break}"HEAD"!=(null==i?void 0:i.method)&&l.abort(),chrome.tabs.sendMessage(r.tab.id,{message:"ajax-response",uniqueEventName:o,data:responseHeadersToJson(f.headers)}),e.next=73;break;case 69:return e.next=71,f.text();case 71:x=e.sent,chrome.tabs.sendMessage(r.tab.id,{message:"ajax-response",uniqueEventName:o,data:x});case 73:e.next=79;break;case 75:e.prev=75,e.t0=e.catch(5),chrome.tabs.sendMessage(r.tab.id,{message:"ajax-response",uniqueEventName:o,error:e.t0.message});case 79:case"end":return e.stop()}},e,null,[[5,75]])})),function(e,t){return x.apply(this,arguments)})},{key:"getArrayLast",value:function(e){return e[e.length-1]}},{key:"removeCspRulesRemove",value:(S=_asyncToGenerator(_regeneratorRuntime().mark(function e(){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:browser.declarativeNetRequest.updateSessionRules({removeRuleIds:[1]});case 1:case"end":return e.stop()}},e)})),function(){return S.apply(this,arguments)})},{key:"applyCspRulesRemove",value:(T=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var r,n,a,i,o;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:for(r=["content-security-policy","content-security-policy-report-only","x-webkit-csp","x-content-security-policy"],n=[],a=[],i=0;i<t.length;i+=4)o=t.slice(i,i+4),n.push({id:i/4+1,priority:1,condition:{regexFilter:o.join("|"),resourceTypes:["main_frame","sub_frame"]},action:{type:"modifyHeaders",responseHeaders:r.map(function(e){return{operation:"remove",header:e}})}}),a.push(i/4+1);return e.next=6,browser.declarativeNetRequest.updateSessionRules({addRules:n,removeRuleIds:a});case 6:case"end":return e.stop()}},e)})),function(e){return T.apply(this,arguments)})},{key:"checkReloadCspDomain",value:function(e,t){var r=new URL(t).hostname;this.cspDomainlist.length&&this.cspDomainlist.includes(r)&&(null!==(t=this.streamReload)&&void 0!==t&&t[e]&&(null===(t=this.streamReload)||void 0===t?void 0:t[e])===r||(this.streamReload[e]=r,browser.tabs.reload(e,{bypassCache:!0})))}},{key:"installScripts",value:(E=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t,r,n,a=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t=0<a.length&&void 0!==a[0]?a[0]:null,r=1<a.length&&void 0!==a[1]?a[1]:null,n=browser.runtime.getManifest().content_scripts,null!==r&&(n[0].allFrames,n[0].frameIds=[r]),e.next=6,_installScripts2(n,t);case 6:case"end":return e.stop()}},e)})),function(){return E.apply(this,arguments)})},{key:"initContextMenu",value:(w=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var r=this;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:this.config.ENABLE_CONTEXTMENU&&(p(browser.contextMenus.create,{id:browser.runtime.id,title:this.config.CONTEXTMENU_TITLE+" '%s'",contexts:["selection"]}).catch(function(e){}),browser.contextMenus.onClicked.addListener(function(e,t){r.ga.sendGaEvent("contextMenu",{selectionText:e.selectionText}),browser.tabs.create({url:r.config.CONTEXTMENU_URL+encodeURIComponent(e.selectionText)})}));case 1:case"end":return e.stop()}},e,this)})),function(){return w.apply(this,arguments)})},{key:"getTabbyId",value:(_=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",p(browser.tabs.get,t).catch(function(e){return null}));case 1:case"end":return e.stop()}},e)})),function(e){return _.apply(this,arguments)})},{key:"detailsHeadersToKeyValue",value:function(e){var r={};try{e.forEach(function(e){r[e.name.toLowerCase()]=e.value})}catch(e){}return{get:function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:null,t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:null;if(r){if(e){e=e.toLowerCase();return r[e]||t}return r}return r}}}},{key:"getMediaInfoFromReceivedHeader",value:function(e,t){var r,n=null,a={},i=e.get("accept-ranges",null),o=(null===(d=e.get("content-type",null))||void 0===d?void 0:d.toLowerCase())||null,s=e.get("content-length",null),u=e.get("content-range",null),c=e.get("icy-br",null),l=e.get("icy-name",null),d=e.get("content-disposition",null);if(u?(e=getRangeInfo(u))&&(a.size=e.total):s&&(s=parseInt(s),a.size=s),l?a.title=l:!d||(l=getFileNameFromcontentDisposition(d))&&(r=getTitleExtFromFileName(l),a.title=r.title,n=r.ext),null==n&&(r=getTitleExtFromUrl(t),a.title||(a.title=r.title),n=r.ext),c&&(a.bitrate=c.trim().split(",")[0]),o){c=getTypeFormatFromExtMime(n,o);if(c.type){if(a.type=c.type,a.ext=c.ext,a.ext)return a;n&&allowedExtensions.indexOf(n)}else if(d||o.includes("audio")||o.includes("video")){if(videoExts.includes(n))return a.type="video",a.ext=n,a;if(audioExts.includes(n))return a.type="audio",a.ext=n,a;(o.includes("audio")||o.includes("video"))&&["video/mp2t"].includes(o.split(";")[0])}}if((d||("bytes"===i||u)&&a.size)&&n&&-1!==allowedExtensions.indexOf(n)){if(videoExts.includes(n))return a.type="video",a.ext=n,a;if(audioExts.includes(n))return a.type="audio",a.ext=n,a}return n&&streamExts.includes(n)&&(!a.size||a.size&&Number(a.size)<1048576)?(a.type="stream",a.ext=n||(t.match(/[\.\/](m3u|hls)/)?"m3u":"mpd"),a):(o&&(o.includes("audio")||o.includes("video"))&&["video/mp2t"].includes(o),null)}},{key:"getNewTabObject",value:function(){return{time:Date.now(),videoLinks:[],url:""}}},{key:"getResponseInfo",value:(y=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,r){var n,a,i,o,s;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return s=r.init,n=void 0===s?null:s,i=r.rangeRequest,a=void 0!==i&&i,s=r.cache,i=void 0===s?null:s,o=new AbortController,s=o.signal,n=null!=n?n:_objectSpread(_objectSpread({headers:_objectSpread({accept:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9"},a?{Range:"bytes=0-"}:{})},i?{cache:i}:{}),{},{credentials:"include",method:"GET"}),e.abrupt("return",XMLRequest(t,_objectSpread(_objectSpread({},n),{},{signal:s})).then(function(e){return o.abort(),e}));case 5:case"end":return e.stop()}},e)})),function(e,t){return y.apply(this,arguments)})},{key:"findVideoLinkAttrWithValue",value:function(e,t,r){return e.findIndex(function(e){return e.hasOwnProperty(t)&&r(e[t])})}},{key:"isVideoLinkAlreadyAdded",value:function(e,r,n){return e.some(function(t){return r.every(function(e){return!n.hasOwnProperty(e)||t.hasOwnProperty(e)&&t[e]===n[e]})})}},{key:"findIndexForVideoLink",value:function(e,r,n){return e.findIndex(function(t){return r.every(function(e){return!n.hasOwnProperty(e)||t.hasOwnProperty(e)&&t[e]===n[e]})})}},{key:"setBadge",value:function(e,t){e=e?e+"":"";try{p(browser.action.setBadgeBackgroundColor,{color:[244,63,94,255],tabId:t}).catch(function(){}),p(browser.action.setBadgeText,{text:e,tabId:t}).catch(function(){}),p(browser.action.setBadgeTextColor,{color:"#fff",tabId:t}).catch(function(){})}catch(e){}}},{key:"updateExtensionIcon",value:function(e){this.tabsData[e]&&0<this.tabsData[e].videoLinks.length?this.setBadge(this.tabsData[e].videoLinks.length,e):this.setBadge(0,e)}},{key:"addVideoLinkToTab",value:(b=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,r){var n,a,i,o,s,u,c,l,d,p,f,h,m,g,v,b,y,_=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(n=2<_.length&&void 0!==_[2]&&_[2],a=-1,t.id="id-".concat(Date.now(),"-").concat(Math.random()),t.time=Date.now(),!t.streamType){e.next=15;break}if(i=-1,"hls"==t.streamType?i=this.findIndexForVideoLink(this.tabsData[r].videoLinks,["streamType","manifestUrl","name","type","resolution","groupName","url"],t):"dash"==t.streamType&&(i=this.findIndexForVideoLink(this.tabsData[r].videoLinks,["streamType","manifestUrl","name","duration","url"],t)),-1!=i||null==t||!t.manifestUrl){e.next=12;break}if(-1!=(i=this.findVideoLinkAttrWithValue(this.tabsData[r].videoLinks,"url",function(e){return e===t.manifestUrl||(null==t?void 0:t.url)&&e===t.url})))return e.abrupt("return");e.next=11;break;case 11:null!=t&&t.url&&(i=this.findVideoLinkAttrWithValue(this.tabsData[r].videoLinks,"manifestUrl",function(e){return e===t.url}));case 12:-1==i?(t.title&&""!==t.title||(o=getTitleExtFromUrl((null==t?void 0:t.manifestUrl)||(null==t?void 0:t.url)),t.title=o.title),t.title=sanitizeFilename(t.title),this.tabsData[r].videoLinks.push(t),a=this.tabsData[r].videoLinks.length-1):(t.id=this.tabsData[r].videoLinks[i].id,areObjectsEqual(t,this.tabsData[r].videoLinks[i])||(this.tabsData[r].videoLinks[i]=Object.assign(this.tabsData[r].videoLinks[i],t),a=i)),e.next=100;break;case 15:if(!t.download_url){e.next=99;break}if(-1!=(s=this.findIndexForVideoLink(this.tabsData[r].videoLinks,["download_url"],t))){e.next=95;break}if(t.title&&""!==t.title||(l=getTitleExtFromUrl(t.webpage_url),t.title=l.title),t.title=sanitizeFilename(t.title),t.audioLink&&(t.audioLink.title=sanitizeFilename(t.audioLink.title)),t.size||t.skipSizeCheck){e.next=91;break}return u=null,e.prev=23,e.next=26,this.getResponseInfo(t.download_url,{rangeRequest:!0});case 26:if((u=e.sent).ok){e.next=30;break}throw new Error("Response status code: ".concat(u.status));case 30:e.next=53;break;case 32:return e.prev=32,e.t0=e.catch(23),l=(null==t||null===(c=t.init)||void 0===c||null===(c=c.headers)||void 0===c?void 0:c.referer)||(null==t?void 0:t.initiator)||(null===(d=this.tabsData[r])||void 0===d?void 0:d.url)||t.webpage_url,d=[],e.prev=36,e.next=39,updateSessionRules([t.download_url],l);case 39:return d=e.sent,e.next=42,this.getResponseInfo(t.download_url,{rangeRequest:!0,cache:"no-store"});case 42:if((u=e.sent).ok){e.next=46;break}throw new Error("Response status code: ".concat(u.status));case 46:e.next=53;break;case 48:throw e.prev=48,e.t1=e.catch(36),d.length&&browser.declarativeNetRequest.updateSessionRules({removeRuleIds:d}),new Error("getResponseInfo for size error: ".concat(null===e.t0||void 0===e.t0?void 0:e.t0.message," ").concat(null===e.t1||void 0===e.t1?void 0:e.t1.message));case 53:if(!t.skipCheck){e.next=76;break}if(!(p=this.getMediaInfoFromReceivedHeader(u.headers,u.url))){e.next=61;break}t.type||(t.type=p.type),t.ext||(t.ext=p.ext),t.size=p.size,e.next=73;break;case 61:if((f=getSizeFromReceivedHeader(u.headers))&&(t.size=f),e.prev=63,(f=u.headers.get("content-type",null))&&imageMimeTypes.includes(f.toLowerCase().split(";")[0]))return e.abrupt("return",null);e.next=67;break;case 67:e.next=72;break;case 69:e.prev=69,e.t2=e.catch(63);case 72:t.ext||(t.ext="video"==t.type?"mp4":"mp3");case 73:delete t.skipCheck,e.next=85;break;case 76:if(h=this.getMediaInfoFromReceivedHeader(u.headers,u.url)){e.next=79;break}return e.abrupt("return",null);case 79:if(t.type||(t.type=h.type),t.ext||(t.ext=h.ext),t.size=h.size,!n){e.next=85;break}if(needFilter(t.size,this.filter[h.type.toUpperCase()]))return e.abrupt("return");e.next=85;break;case 85:null!==(f=u.headers)&&void 0!==f&&f.get("content-range",null)&&(t.shouldBeChunked=!0),"true"===(null===(f=u.headers)||void 0===f?void 0:f.get("access-control-allow-credentials"))&&null!==(null===(f=u.headers)||void 0===f?void 0:f.get("access-control-allow-origin"))&&"*"!==(null===(m=u.headers)||void 0===m?void 0:m.get("access-control-allow-origin"))&&(t.init={credentials:"include"}),null==(null===(m=u.headers)||void 0===m?void 0:m.get("access-control-allow-origin"))&&"cross-origin"!=(null===(g=u.headers)||void 0===g?void 0:g.get("cross-origin-resource-policy"))&&t.initiator&&(m=new URL(t.initiator).hostname,g=new URL(t.download_url).hostname,m!==g&&(t.checkIsCors=!0)),this.tabsData[r]&&!this.isVideoLinkAlreadyAdded(this.tabsData[r].videoLinks,["download_url"],t)&&(this.tabsData[r].videoLinks.push(t),a=this.tabsData[r].videoLinks.length-1,this.updateExtensionIcon(r)),e.next=93;break;case 91:this.tabsData[r].videoLinks.push(t),a=this.tabsData[r].videoLinks.length-1;case 93:e.next=97;break;case 95:for(t.title&&t.title!==getTitleExtFromUrl(t.download_url).title&&(this.tabsData[r].videoLinks[s].title=sanitizeFilename(t.title)),v=0,b=["quality","height","width","duration","resolution"];v<b.length;v++)y=b[v],null!=t&&t[y]&&(this.tabsData[r].videoLinks[s][y]=t[y]);case 97:e.next=100;break;case 99:this.isVideoLinkAlreadyAdded(this.tabsData[r].videoLinks,["title"],t)||(this.tabsData[r].videoLinks.push(t),a=this.tabsData[r].videoLinks.length-1);case 100:return this.updateExtensionIcon(r),e.abrupt("return",-1!=a?this.tabsData[r].videoLinks[a]:null);case 102:case"end":return e.stop()}},e,this,[[23,32],[36,48],[63,69]])})),function(e,t){return b.apply(this,arguments)})},{key:"getTabDataFromStorage",value:(v=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var r;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,localStorageGet(String(t));case 2:if(null!=(r=e.sent)&&r.hasOwnProperty(String(t)))return e.abrupt("return",r[String(t)]);e.next=5;break;case 5:return e.abrupt("return",null);case 6:case"end":return e.stop()}},e)})),function(e){return v.apply(this,arguments)})},{key:"setTabDataToStorage",value:(g=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var r,n;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:n={},0<(null===(r=this.tabsData[t])||void 0===r||null===(r=r.videoLinks)||void 0===r?void 0:r.length)&&(n[String(t)]=this.tabsData[t],localStorageSet(n).catch(function(e){}));case 2:case"end":return e.stop()}},e,this)})),function(e){return g.apply(this,arguments)})},{key:"removeTabDataToStorage",value:function(e){delete this.tabsData[e],localStorageRemove(String(e))}},{key:"getTabData",value:(m=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(this.tabsData.hasOwnProperty(String(t))){e.next=5;break}return e.next=3,this.getTabDataFromStorage(t);case 3:this.tabsData[t]=e.sent,this.tabsData[t]&&this.updateExtensionIcon(t);case 5:return e.abrupt("return",this.tabsData[t]);case 6:case"end":return e.stop()}},e,this)})),function(e){return m.apply(this,arguments)})},{key:"resetClearOldLinks",value:(h=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,r){var n;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if((n=null!==(n=this.tabsData[t])&&void 0!==n&&n.url?new URL(this.tabsData[t].url).hostname:null)&&n===new URL(r).hostname)return e.next=4,this.rmOldVideoLinks(t);e.next=6;break;case 4:e.next=8;break;case 6:return e.next=8,this.resetVideoLinks(t);case 8:case"end":return e.stop()}},e,this)})),function(e,t){return h.apply(this,arguments)})},{key:"resetVideoLinks",value:(f=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var r;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.tabsData[t]){e.next=11;break}if(null===(r=this.tabsData[t].videoLinks)||void 0===r||!r.length){e.next=9;break}if(Date.now()-(null===(r=this.tabsData)||void 0===r?void 0:r[t].time)<2e3)return e.abrupt("return");e.next=5;break;case 5:return this.tabsData[t].time=Date.now(),this.tabsData[t].videoLinks=[],e.next=9,localStorageRemove(String(t)).catch(function(e){});case 9:e.next=12;break;case 11:this.tabsData[t]=this.getNewTabObject();case 12:case"end":return e.stop()}},e,this)})),function(e){return f.apply(this,arguments)})},{key:"rmOldVideoLinks",value:(d=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var r,n;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(null!==(r=this.tabsData[t])&&void 0!==r&&null!==(r=r.videoLinks)&&void 0!==r&&r.length){e.next=2;break}return e.abrupt("return");case 2:if(n=Date.now(),this.tabsData[t].videoLinks=this.tabsData[t].videoLinks.filter(function(e){return n-e.time<=5e3}),0===this.tabsData[t].videoLinks.length)return e.next=7,localStorageRemove(String(t)).catch(function(e){});e.next=9;break;case 7:e.next=11;break;case 9:return e.next=11,this.setTabDataToStorage(t);case 11:this.updateExtensionIcon(t);case 12:case"end":return e.stop()}},e,this)})),function(e){return d.apply(this,arguments)})},{key:"addVideoLinks",value:(l=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var r,n,a,i,o,s,u,c,l,d,p,f,h=this;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(r=t.videoLinks,n=t.tabId,a=t.tabUrl,i=t.frameId,o=t.initiator,c=t.isFromWebReq,s=void 0===c||c,c=t.sig,u=void 0===c?null:c,this.config.DISALLOW_DOMAINS_REGEXP&&this.config.DISALLOW_DOMAINS_REGEXP.test(a))return e.abrupt("return");e.next=3;break;case 3:return e.next=6,this.getTabData(n);case 6:if(this.tabsData[n]||(this.tabsData[n]=this.getNewTabObject()),a===this.tabsData[n].url){e.next=14;break}if(null!==(c=this.tabsData[n])&&void 0!==c&&c.url&&this.tabsData[n].url.length&&6e4<=Date.now()-(null===(d=this.tabsData)||void 0===d?void 0:d[n].time))return e.next=12,this.resetVideoLinks(n);e.next=12;break;case 12:this.tabsData[n].url=a;case 14:return(l=null)!=u&&u.sid&&(null!==(d=this.tabsData[n])&&void 0!==d&&d.sig||(this.tabsData[n].sig={}),u.sid in this.tabsData[n].sig||(l=u,this.tabsData[n].sig[u.sid]=u)),p=[],f=new Set,e.next=20,Promise.all(r.map(function(){var t=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var r;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t.frameId=i&&-1!=i?i:0,t.initiator=o,null!=u&&u.sid&&(t.sid=u.sid),e.prev=4,e.next=7,h.addVideoLinkToTab(t,n,s);case 7:(r=e.sent)&&p.push(r),e.next=14;break;case 11:e.prev=11,e.t0=e.catch(4),f.add(e.t0.message);case 14:case"end":return e.stop()}},e,null,[[4,11]])}));return function(e){return t.apply(this,arguments)}}()));case 20:return f.length&&this.reportMsg("addVideoLink error: ".concat(a,", ").concat(Array.from(f))),e.next=23,this.setTabDataToStorage(n);case 23:p.length&&this.updateVideoLinksToPop(n,p,l);case 24:case"end":return e.stop()}},e,this)})),function(e){return l.apply(this,arguments)})},{key:"getTabsInfoByHost",value:(c=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var r;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,browser.tabs.query({url:"*://".concat(t,"/*")});case 3:return r=e.sent,r=r.map(function(e){return{id:e.id,url:e.url,title:e.title,active:e.active,windowId:e.windowId,index:e.index,status:e.status}}),e.abrupt("return",r);case 8:return e.prev=8,e.t0=e.catch(0),e.abrupt("return",[]);case 12:case"end":return e.stop()}},e,null,[[0,8]])})),function(e){return c.apply(this,arguments)})},{key:"getCurrActiveTab",value:(u=_asyncToGenerator(_regeneratorRuntime().mark(function e(){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,p(browser.tabs.query,{active:!0,currentWindow:!0}).then(function(e){return e[0]}).catch(function(e){});case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e)})),function(){return u.apply(this,arguments)})},{key:"updateVideoLinksToPop",value:function(e,t,r){r={message:"update-video-links",tabId:e,videoLinks:t,sig:r};p(browser.runtime.sendMessage,r).catch(function(e){})}},{key:"sendDataTo",value:(s=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,r,n,a,i){var o,s,u,c,l,d;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!(5242880<(o=JSON.stringify(a)).length)){e.next=9;break}for(s=[],u=0;u<o.length;u+=5242880)s.push(o.slice(u,u+5242880));for(c=0;c<s.length;c++)l=(c+1)/s.length*100,d=c===s.length-1,d={message:n,uniqueEventName:r,data:s[c],progress:l,done:d},t?chrome.tabs.sendMessage(t,d):chrome.runtime.sendMessage(d);return e.abrupt("return",i({uniqueEventName:r}));case 9:return e.abrupt("return",i({data:a}));case 10:case"end":return e.stop()}},e)})),function(e,t,r,n,a){return s.apply(this,arguments)})},{key:"storeInjectedVideoLinksKey",value:(o=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,r){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:this.tabsData[t]={injectedKey:r},this.setTabDataToStorage(t);case 2:case"end":return e.stop()}},e,this)})),function(e,t){return o.apply(this,arguments)})},{key:"showYoutubeWarning",value:function(){browser.tabs.create({url:"http://smarturl.it/youtube-alert",selected:!0},function(e){})}},{key:"removeVideoDuplicates",value:function(e){var t={};return e.filter(function(e){e="".concat(e.NAME,"_").concat(e.RESOLUTION);if(!t.hasOwnProperty(e))return t[e]=!0})}},{key:"updateInitReferer",value:function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},t=1<arguments.length?arguments[1]:void 0;return e.headers=e.headers||{},e.headers.referer||(e.headers.referer=t),e}},{key:"checkManifestUrlExist",value:function(t){var e=this.findVideoLinkAttrWithValue(this.tabsData[t.tabId].videoLinks,"url",function(e){return e===t.manifestUrl||(null==t?void 0:t.url)&&e===t.url});return-1!==e||-1!==this.findVideoLinkAttrWithValue(this.tabsData[t.tabId].videoLinks,"manifestUrl",function(e){return e===t.manifestUrl})}},{key:"checkManifestUrlMatch",value:function(t){if(null!==(e=this.tabsData)&&void 0!==e&&null!==(e=e[t.tabId])&&void 0!==e&&null!==(e=e.videoLinks)&&void 0!==e&&e.length){var e=this.findVideoLinkAttrWithValue(this.tabsData[t.tabId].videoLinks,"url",function(e){return e.includes(t.manifestUrl)||e.includes(encodeURIComponent(t.manifestUrl))});if(-1!==e)return!0;if(-1!==(e=this.findVideoLinkAttrWithValue(this.tabsData[t.tabId].videoLinks,"manifestUrl",function(e){return e.includes(t.manifestUrl)||e.includes(encodeURIComponent(t.manifestUrl))})))return!0}return!1}},{key:"delayCheckHandleHlsDashStream",value:(i=_asyncToGenerator(_regeneratorRuntime().mark(function e(r){var n=this;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:setTimeout(function(){var e,t;((null===(e=n.tabsData)||void 0===e||null===(e=e[r.tabId])||void 0===e?void 0:e.videoLinks)||[]).length&&n.checkManifestUrlExist(r)||(isPolyvNetUrl(r.manifestUrl)?(t=getPolyvNetManifestId(r.manifestUrl),null!==(e=n.polyvNet.manifestIds)&&void 0!==e&&e[t]?n.handleHlsDashStream(r):n.polyvNet.pandingStreams.set(t,r)):n.handleHlsDashStream(r))},1500);case 1:case"end":return e.stop()}},e)})),function(e){return i.apply(this,arguments)})},{key:"handleHlsDashStream",value:(a=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var r,n,a,i,o,s;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(null!=t&&t.manifest||null==t||!t.manifestUrl){e.next=43;break}return e.next=3,this.requestHlsDashStreamManifest(t);case 3:if((r=e.sent).valid){e.next=6;break}return e.abrupt("return",[]);case 6:if(!r.childManifests){e.next=38;break}if(t.isParseRequest)return this.reportMsg("parseManifest return childmanifests: ".concat(t.tabUrl," ").concat(t.manifestUrl)),e.abrupt("return",[]);e.next=10;break;case 10:delay(1e3),n=[],a=0;case 14:if(!(a<r.childManifests.length)){e.next=37;break}if(s=r.childManifests[a],i=_objectSpread(_objectSpread({},t),{},{manifestUrl:s,manifest:null}),this.checkManifestUrlMatch(i))return e.abrupt("continue",34);e.next=19;break;case 19:return e.next=21,this.requestHlsDashStreamManifest(i);case 21:if(!(o=e.sent).valid||o.childManifests)return e.abrupt("continue",34);e.next=24;break;case 24:if(i.manifest=o.manifest,!o.manifestUrl){e.next=29;break}if(i.manifestUrl=o.manifestUrl,this.checkManifestUrlMatch(i))return e.abrupt("continue",34);e.next=29;break;case 29:return e.next=32,this.processManifest(i);case 32:s=e.sent,n.push.apply(n,_toConsumableArray(s));case 34:a++,e.next=14;break;case 37:return e.abrupt("return",n);case 38:return t.manifest=r.manifest,r.manifestUrl&&(t.manifestUrl=r.manifestUrl),e.next=42,this.processManifest(t);case 42:return e.abrupt("return",e.sent);case 43:return e.next=45,this.processManifest(t);case 45:return e.abrupt("return",e.sent);case 46:case"end":return e.stop()}},e,this)})),function(e){return a.apply(this,arguments)})},{key:"processManifest",value:(n=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var r,n,a,i,o;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!isBoomstreamUrl(null==t?void 0:t.manifestUrl)){e.next=35;break}if(!this.stream.isHlsMasterManifest(t.manifest)){e.next=21;break}if(this.boomstream.info.has(t.manifestUrl))return null!=(r=this.boomstream.info.get(t.manifestUrl))&&r.duration&&(t.duration=r.duration),null!=r&&r.title&&(t.title=r.title),e.next=8,this.handleHlsDashStreamText(_objectSpread(_objectSpread({},t),{},{skipValidation:!0}));e.next=13;break;case 8:return(n=e.sent)&&(r=n.map(function(e){return e.url}),this.boomstream.mediaLinks.set(t.manifestUrl,r)),e.abrupt("return",n);case 13:return e.next=15,this.handleHlsDashStreamText(_objectSpread(_objectSpread({},t),{},{skipValidation:!0}));case 15:return(n=e.sent)&&(n=n.map(function(e){return e.url}),this.boomstream.mediaLinks.set(t.manifestUrl,n)),this.boomstream.pandingStreams.set(t.manifestUrl,t),e.abrupt("return",[]);case 19:e.next=35;break;case 21:o=Array.from(this.boomstream.mediaLinks.keys()),a=0,i=o;case 23:if(!(a<i.length)){e.next=32;break}if(o=i[a],this.boomstream.mediaLinks.get(o).includes(t.manifestUrl))return this.boomstream.info.has(o)&&(o=this.boomstream.info.get(o),t.manifest=updateLoomstreamMediaPlaylist(t.manifest,o.state,o.apiBase)),e.abrupt("break",32);e.next=29;break;case 29:a++,e.next=23;break;case 32:return e.next=34,this.handleHlsDashStreamText(t);case 34:return e.abrupt("return",e.sent);case 35:if(null!=t&&t.manifest)return e.abrupt("return",this.handleHlsDashStreamText(t));e.next=37;break;case 37:return e.abrupt("return",[]);case 39:case"end":return e.stop()}},e,this)})),function(e){return n.apply(this,arguments)})},{key:"requestHlsDashStreamManifest",value:(r=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var r,n,a,i,o,s,u,c,l,d;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(r=t.tabUrl,n=t.initiator,a=t.manifestUrl,u=t.init,i=void 0===u?{}:u,e.prev=2,isPandavideoNoTokenUrl(a))return e.abrupt("return",{valid:!1});e.next=5;break;case 5:return i=this.updateInitReferer(i,n||r),a=updatePandavideoToken(a),e.next=9,this.stream.requestUrl(a,i);case 9:if(o=e.sent,null!==(s=o)&&void 0!==s&&s.ok){e.next=18;break}if(u=_objectSpread({},i),null!==(s=u.headers)&&void 0!==s&&s.referer)return delete u.headers.referer,e.next=16,this.stream.requestUrl(a,u);e.next=18;break;case 16:null!=(c=e.sent)&&c.ok&&(delete i.headers.referer,o=c);case 18:if(null!==(c=o)&&void 0!==c&&c.ok){e.next=22;break}return this.reportMsg("requestManifest failed: ".concat(r," status:").concat(null===(c=o)||void 0===c?void 0:c.status," ").concat(a)),e.abrupt("return",{valid:!1});case 22:return e.next=24,o.text();case 24:if(determineStreamType(l=e.sent))return d={valid:!0},o.url!==t.manifestUrl&&(d.manifestUrl=o.url),d.manifest=l,e.abrupt("return",d);e.next=30;break;case 30:if(e.prev=30,(d=this.stream.getManifestUrlsFromResponseText(a,l,o.status,o.headers)).length)return e.abrupt("return",{valid:!0,childManifests:d});e.next=34;break;case 34:e.next=40;break;case 36:e.prev=36,e.t0=e.catch(30),this.reportMsg("getManifestUrls error: ".concat(r," ").concat(null===e.t0||void 0===e.t0?void 0:e.t0.message));case 40:return e.abrupt("return",{valid:!1});case 43:return e.prev=43,e.t1=e.catch(2),this.reportMsg("requestHlsDashStreamManifest error: ".concat(r," ").concat(null===e.t1||void 0===e.t1?void 0:e.t1.message)),e.abrupt("return",{valid:!1,error:e.t1.message});case 48:case"end":return e.stop()}},e,this,[[2,43],[30,36]])})),function(e){return r.apply(this,arguments)})},{key:"handleHlsDashStreamText",value:(t=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var r,n,a,i,o,s,u,c,l,d,p,f,h,m,g,v;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return r=t.manifest,n=t.tabId,a=t.tabUrl,i=t.streamType,d=t.frameId,o=void 0===d?null:d,f=t.manifestUrl,s=void 0===f?"":f,p=t.sig,u=void 0===p?null:p,h=t.addVideoLinks,c=void 0===h||h,v=t.isFromWebReq,l=void 0===v||v,d=t.initiator,f=t.skipValidation,p=void 0!==f&&f,h=t.isParseRequest,v=void 0!==h&&h,f=t.title,h=t.init,h=void 0===h?{}:h,f||(m=getTitleExtFromUrl(null!=s&&s.length?s:a),f=m.title),m=[],e.prev=4,h=this.updateInitReferer(h,d||a),e.next=8,this.stream.getVideoLinksFromManifestText(r,i,f,s,h,p||v||u);case 8:m=e.sent,(v=getPolyvNetManifestId(null===(v=m)||void 0===v||null===(v=v[0])||void 0===v||null===(v=v.segments)||void 0===v||null===(v=v[0])||void 0===v||null===(v=v.key)||void 0===v?void 0:v.url))&&null!==(g=this.polyvNet.manifestIds)&&void 0!==g&&g[v]&&(m=updatePolyvNetKeyToken(m,this.polyvNet.manifestIds[v])),c&&this.addVideoLinks({videoLinks:m,tabId:n,tabUrl:a,frameId:o,initiator:d,isFromWebReq:l,sig:u}),e.next=18;break;case 14:e.prev=14,e.t0=e.catch(4),this.reportMsg("handleHlsDashStreamText error: tabUrl:".concat(a," streamUrl:").concat(s," ").concat(null===e.t0||void 0===e.t0?void 0:e.t0.message));case 18:return e.abrupt("return",m);case 19:case"end":return e.stop()}},e,this,[[4,14]])})),function(e){return t.apply(this,arguments)})}]),e}(),bg=new Background;
