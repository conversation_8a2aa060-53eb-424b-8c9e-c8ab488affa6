(()=>{const t=document.currentScript;(()=>{const e=t=>null!=t&&"object"==typeof t;this.getObjFunc=t=>t.split(".").reduce(((t,i)=>{if(e(t)){const e=i.match(/([^(]+)\((.*)\)/),s=t[e?e[1].trim():i];if(e){const i=e[2].split(",").map((t=>t.trim())).map((t=>t.match(/^['|"][\d\w=]+['|"]$/)?t.replace(/^['"]|['"]$/g,""):getObjFunc(t)));return"function"==typeof s?s.call(t,...i):void 0}return"function"==typeof s?s.bind(t):s}}),window),this.getObj=t=>t.split(".").reduce(((t,i)=>e(t)?t[i]:void 0),window);try{const{names:e,wait:i=0,func:s="getObj"}=JSON.parse(t.getAttribute("data-params")),r=e.map(this?.[s]||this.getObjFunc);if(i&&r.some((t=>void 0===t)))return void setTimeout((()=>{t.setAttribute("data-response",JSON.stringify(r)),t.setAttribute("data-status","timeout")}),i);t.setAttribute("data-response",JSON.stringify(r)),t.setAttribute("data-status","fulfilled")}catch(e){t.setAttribute("data-response",JSON.stringify(e)),t.setAttribute("data-status","rejected")}})()})();