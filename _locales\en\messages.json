{"1080pSupport": {"message": "Download 1080p, 2k and 4k videos"}, "Actions": {"message": "Actions"}, "Activate": {"message": "Activate"}, "ActivateNotices": {"message": "Activation failed. Click <a href='https://app.lemonsqueezy.com/my-orders/login' target='_blank'>here</a> to release the license key and manage your account. Need help? Contact us."}, "ActivateYourLicenseKey": {"message": "Activate Your License Key"}, "Audio": {"message": "Audio"}, "AudioFixer": {"message": "Audio Fixer"}, "AudioFixerDesc": {"message": "If an HLS/DASH video has audio, but the downloaded file is without audio, kindly add the domain to the follow list, refresh the page, and attempt the download again."}, "CheckLicenseFailed": {"message": "Check License Failed"}, "Choose": {"message": "Choose..."}, "Close": {"message": "Close"}, "Confirm": {"message": "Confirm"}, "Contact": {"message": "Contact"}, "Deactivate": {"message": "Deactivate"}, "DeactivateDes": {"message": "Deactivate you license key? You can reuse it on other devices afterwards?"}, "DeactivateLicenseKey": {"message": "Deactivate Your License Key"}, "DeactivateSuccess": {"message": "Your have successfully Deactivated"}, "Delete": {"message": "Delete"}, "Disable": {"message": "Disable"}, "Domain": {"message": "Domain"}, "EachRecordLimit": {"message": "Limitation of each recording mode"}, "Enable": {"message": "Enable"}, "EnableDisplayModalInfo": {"message": "Uncheck to enable the display of the follow modal when free users download HLS/DASH videos that require audio merging."}, "Encrypted": {"message": "Encrypted"}, "EncryptedDesc": {"message": "Recording the video has failed because it is encrypted. Please try using 'Tab capture mode'."}, "EnterLicenseKey": {"message": "Enter your license key"}, "FAQ": {"message": "FAQ"}, "Filter": {"message": "Filter"}, "General": {"message": "General"}, "GeneralSettings": {"message": "General Settings"}, "HlsDashNoAudioDesc": {"message": "After downloading, the current videos will be without audio. Upgrade to Pro for automatic merging of HLS/DASH video and audio streams."}, "Home": {"message": "Home"}, "HotKeys": {"message": "HotKeys:"}, "HowtoDownload": {"message": "Q: How to download a video?"}, "HowtoDownloadStep1": {"message": "Go to video site, then click out extension icon."}, "HowtoDownloadStep2": {"message": "The videos will be listed on the popup page."}, "KeepCurrDlTabOpen": {"message": "While downloading, please keep the current tab open to avoid issues."}, "License": {"message": "License"}, "LicenseKey": {"message": "License Key"}, "Lifetime": {"message": "Lifetime"}, "LifetimeDes": {"message": "Unlimited downloads forever"}, "LimitationActivate": {"message": "If you've already purchased the pro version, click the 'Activate' button below to unleash the full potential of your account. "}, "LimitationContent": {"message": "As a free user, you can download one HLS/DASH video stream and one 1080p+ videos every hour. Furthermore, each recording mode (video, audio, and tab) can be used once every hour. To unlock unlimited access and enjoy all premium features, consider upgrading to the pro version."}, "Limitations": {"message": "Limitations"}, "MergeAudioFailed": {"message": "Merge Audio Failed"}, "MergeAudioFailedDesc1": {"message": "Merge audio for {{1}} failed. Only the video will be downloaded."}, "MergeAudioFailedDesc2": {"message": "To fix this issue, navigate to the extension's {{1}}, and enter {{2}}. If the problem persists, please feel free to contact us for further assistance."}, "MergeAudioFailedRetry": {"message": "You can refresh the page and try again, or contact us if the issue persists."}, "Monthly": {"message": "Monthly"}, "MonthlyDes": {"message": "Suitable for trying out"}, "NewVersionReleased": {"message": "New version(v{{1}}) has been released, please download and install the latest version."}, "No": {"message": "No"}, "NoADsDL": {"message": "No ads, directly download"}, "NumEveryHours": {"message": "{{1}} every {{2}} hours"}, "NumStreamLimit": {"message": "Number of HLS/DASH stream downloads"}, "Options": {"message": "Options"}, "Pricing": {"message": "Pricing"}, "Pro": {"message": "Pro"}, "Progress": {"message": "Progress"}, "PurchaseLicenseKey": {"message": "Purchase License Key?"}, "Quality": {"message": "Quality"}, "Record": {"message": "Record"}, "Recording": {"message": "Recording"}, "RecordingMode": {"message": "Recording Mode"}, "Reset": {"message": "Reset"}, "Screen": {"message": "Screen"}, "SelectVideoOnPage": {"message": "Select Video on Page"}, "SetRecordCountDown": {"message": "Set Record Countdown:"}, "Settings": {"message": "Settings"}, "ShowNoAudioInfo": {"message": "Show no audio info when download HLS/DASH video"}, "ShowTabRecordTip": {"message": "Show tips when using Tab recording mode."}, "ShowVideoElRecordTip": {"message": "Show tips when using video recording mode."}, "Status": {"message": "Status"}, "StepsToInstallUpdate": {"message": "Steps to intall or update the extension"}, "Stream": {"message": "Video stream"}, "TabCapture": {"message": "Tab Capture"}, "Tips": {"message": "Tips"}, "UnlimitMediaDL": {"message": "Unlimited audio/video downloads"}, "Unlimited": {"message": "Unlimited"}, "Update": {"message": "Update"}, "Updateversion": {"message": "Update version."}, "Video": {"message": "Video"}, "VieoElRecordFailed": {"message": "Video Record Failed"}, "VieoElRecordFailedDesc": {"message": "Video recording failed. Please contact us and include the following URL, or try using 'Tab capture mode'."}, "VieoEncryptedUseTabCapture": {"message": "This video is encrypted, please use 'Tab Capture'"}, "Yearly": {"message": "Yearly"}, "YearlyDes": {"message": "{{1}} billed yearly, save {{2}}"}, "Yes": {"message": "Yes"}, "action": {"message": "Action"}, "addDomain": {"message": "Add domain"}, "addDomainFromCurrTab": {"message": "Add domain from current tab"}, "addDomainNote": {"message": "If the website behaves abnormally after adding the domain to this list, disable the matching rule and reopen it in a new tab."}, "alreadyLatestVersion": {"message": "You are already using the latest version."}, "applySettings": {"message": "These settings will take effect after refreshing the webpage."}, "brand": {"message": "VidHelper"}, "buyNow": {"message": "Buy Now"}, "canCopyLink": {"message": "Q: Can I copy the url without download it?"}, "canCopyLinkResult": {"message": "Yes, you can copy the link by click on the link icon or scanning the QR Code."}, "cancel": {"message": "Cancel"}, "cancelCmd": {"message": "Alt+Shift+C to cancel recording."}, "cancelRecord": {"message": "Cancel Record"}, "complete": {"message": "Download Complete"}, "contactFeedback": {"message": "Feel free to reach out to us via email at {{1}}, for any inquiries. Additionally, you can leave your feedback using our convenient feedback form below."}, "contactus": {"message": "Contact US"}, "convert": {"message": "Convert to MP3"}, "copyurl": {"message": "URL has been copied."}, "desc": {"message": "Download any video or audio from any website with just one click."}, "disableyoutube": {"message": "Downloading videos from YouTube is not allowed. You can use the Edge version to download videos. For further details, visit our website."}, "discountInfo": {"message": "25% off all items! Limited time offer."}, "doNotShowAgain": {"message": "Do not show again"}, "domainAlreadyAdded": {"message": "This domain already added."}, "download": {"message": "Download"}, "downloadAnother": {"message": "Download another video"}, "downloadStreamTip": {"message": "While the video is downloading, please refrain from operating or refreshing the current webpage to avoid interruptions."}, "downloadStreamTip2": {"message": "If the download is interrupted, or if you still want to use the webpage while downloading videos, you can enable the dedicated downloader page through the extension's settings."}, "downloadall": {"message": "Download All"}, "downloadbest": {"message": "Download Best"}, "downloading": {"message": "Downloading"}, "dropbox": {"message": "Dropbox"}, "failed": {"message": "Download Failed"}, "fileInfo": {"message": "File Info"}, "fileSizeFilter": {"message": "File Size Filter"}, "filename": {"message": "File Name"}, "filesize": {"message": "File Size"}, "hlsDashAudioFixer": {"message": "Merge video and audio for HLS/Dash"}, "hlsDashMerge": {"message": "Merging HLS/DASH video and audio streams"}, "howToInstall": {"message": "how to install {{1}} chrome extension"}, "howtoActivate": {"message": "Q: After completing the purchase, how do I input the license key?"}, "howtoActivateResult": {"message": "Kindly copy the provided license key and input it into our extension as below:"}, "howtoCancelSubscription": {"message": "Q: Can I cancel my subscription?"}, "howtoCancelSubscriptionResult": {"message": "After placing your order, check your email for a link to view your order. Inside the \"View Order\" page, you can then click \"Sign In\" to generate a temporary authorization link. Open the link to proceed and easily cancel your subscription."}, "howtoUpgrade": {"message": "Q: How to upgrade my plan?"}, "howtoUpgradeResult": {"message": "To upgrade your plan, please note that the process currently requires manual intervention from our side. To initiate the upgrade, kindly reach out to our support team and provide them with the details of your desired plan. They will assist you in upgrading to the plan that best fits your needs."}, "inputDomainLike": {"message": "Input domain like"}, "installSteps1": {"message": "Download and unzip the extension"}, "installSteps2": {"message": "Navigate to chrome://extensions in your browser."}, "installSteps3": {"message": "Enable Developer mode and click \"Load unpacked\" to load the extension"}, "installSteps4": {"message": "For more detailed information, please visit"}, "interrupted": {"message": "Download Interrupted"}, "invalidDomain": {"message": "Invalid domain format. Please enter a valid domain."}, "keepAutoClosedDownloadingTabOpen": {"message": "Please keep this tab open and refrain from refreshing while downloading; it will be automatically closed once all downloads are complete."}, "keepDownloadingTabOpen": {"message": "Please keep this tab open and refrain from refreshing while downloading. You may close it when all downloads are complete."}, "keepTabOpen": {"message": "Please keep this tab open during recording; it will be automatically closed once the recording is complete."}, "licenseActivated": {"message": "Your license is successfully activated"}, "licenseFailedDes": {"message": "Your license key verification failed for the following reason. To reactivate your license, please click the activate button, If you have any questions, please feel free to contact us."}, "licenseFailedWith": {"message": "License activation failed due to"}, "mergeDownload": {"message": "Merge Audio and Download"}, "name": {"message": "VidHelper - Video Download Helper"}, "needInstall": {"message": "Q: Do I need to install any companion application?"}, "needInstallResult": {"message": "No, for security reasons, we do not use any companion application, all codes are reviewed by Google."}, "notAllowed": {"message": "Not Allowed"}, "notfound": {"message": "No Videos Found"}, "novidehelp": {"message": "Can't find the videos? Try refreshing the page or playing the current video. Still can't find them? You can try using the recording mode for downloading, or contact us. We're working to support more websites."}, "paused": {"message": "Paused"}, "placeholder": {"message": "Search keywords or paste link here...."}, "play": {"message": "Play the video or audio"}, "popularSites": {"message": "Popular Sites:"}, "qrcode": {"message": "QRCode"}, "rateus": {"message": "If you like this extension, please rate us"}, "reEnableMergeInfo": {"message": "After hiding, you can re-enable it in the settings page."}, "recordTabArea": {"message": "Record tab area"}, "refresh": {"message": "Refresh"}, "scanqr": {"message": "Scan QR to view/download"}, "search": {"message": "Search"}, "selectVideo": {"message": "Select Video on Page"}, "setRecordDuration": {"message": "Set record duration"}, "showDownloadStreamTip": {"message": "Show tips when downloading HLS/DASH stream videos."}, "sitesSupported": {"message": "1000+ of video site support"}, "smallSizeFilter": {"message": "Slide to filter out small media files by size."}, "start": {"message": "Download Start"}, "startRecord": {"message": "Start Record"}, "status": {"message": "Status"}, "stopCmd": {"message": "Alt+Shift+Z to stop recording."}, "stopRecord": {"message": "Stop Record"}, "supportConvert": {"message": "Q: Can I convert videos to MP3 music locally?"}, "supportConvertResult": {"message": "Currently, we do not support this feature, but we will support it soon."}, "supportHLSDASH": {"message": "Q: Can this extension support HLS & DASH?"}, "supportHLSDASHResult": {"message": "Yes, we support downloading HLS (m3u8) and DASH (mpd) video streams."}, "tabRecordTip": {"message": "Before ."}, "title": {"message": "VidHelper - Video Download Helper"}, "useDedicatedDownloader": {"message": "Using dedicated downloader page."}, "videoElRecordTip": {"message": "To start recording from a specific point, simply drag the video player's progress bar to the desired location. Recording will start here and continue until playback stops."}, "videoElRecordTip2": {"message": "If the video offers quality options, opt for fixed settings over automatic to avoid resolution changes during poor network conditions."}, "videoProtected": {"message": "Video is protected, please use 'Tab Capture' to record it."}, "videoRecordInfo": {"message": "Choose the video you want to record from the page. If you can't find it, play the video first, then select it. if the video is encrypted please choose Tab capture mode."}, "waiting": {"message": "Waiting"}, "wantToLeave": {"message": "You have unfinished download tasks. Are you sure you want to leave?"}}