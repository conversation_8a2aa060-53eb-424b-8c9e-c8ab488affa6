(()=>{const t=document.currentScript;try{const{url:e}=JSON.parse(t.getAttribute("data-params")),[r]=Object.entries(window._yt_player).filter((([,t])=>t?.prototype?.get&&t.length>1&&/\.url/.test(t.toString()))),[s]=r,a=new window._yt_player[s](e,!0);if(a){const e=a.get("n");t.setAttribute("data-response",JSON.stringify({n:e})),t.setAttribute("data-status","fulfilled")}else t.setAttribute("data-response",new Error(`${s} not found`)),t.setAttribute("data-status","rejected")}catch(e){t.setAttribute("data-response",JSON.stringify(e)),t.setAttribute("data-status","rejected")}})();