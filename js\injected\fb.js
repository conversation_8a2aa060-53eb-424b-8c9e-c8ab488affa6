window.xhrCallback=(t,e,a,s)=>{if(/\/api\/graphql\//i.test(s.responseURL)&&"POST"===e){let t=s.responseText,e=new RegExp(/"(?:browser_native_(?:sd|hd)_url|dash_manifest(?:_url)?|playable_url(?:_quality_hd)?|(?:sd_src|hd_src)(?:_no_ratelimit)?)"\s*:\s*"[^"]+"/);try{if(t.match(e)){let a=[],s=t.split("\n");for(let t of s)t.match(e)&&a.push(t);if(a.length){const t=new CustomEvent("find-raw-data",{detail:{matched:a}});window.dispatchEvent(t)}}}catch(t){}}};