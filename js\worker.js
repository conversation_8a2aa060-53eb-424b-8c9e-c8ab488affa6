!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.FFmpegWorker=t():e.FFmpegWorker=t()}(self,(()=>(()=>{"use strict";var e={d:(t,r)=>{for(var a in r)e.o(r,a)&&!e.o(t,a)&&Object.defineProperty(t,a,{enumerable:!0,get:r[a]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{default:()=>p});var r;!function(e){e.LOAD="LOAD",e.EXEC="EXEC",e.WRITE_FILE="WRITE_FILE",e.READ_FILE="READ_FILE",e.DELETE_FILE="DELETE_FILE",e.RENAME="RENAME",e.CREATE_DIR="CREATE_DIR",e.LIST_DIR="LIST_DIR",e.DELETE_DIR="DELETE_DIR",e.ERROR="ERROR",e.DOWNLOAD="DOWNLOAD",e.PROGRESS="PROGRESS",e.LOG="LOG",e.MOUNT="MOUNT",e.UNMOUNT="UNMOUNT"}(r||(r={}));const a=new Error("unknown message type"),o=new Error("ffmpeg is not loaded, call `await ffmpeg.load()` first"),s=(new Error("called FFmpeg.terminate()"),new Error("failed to import ffmpeg-core.js"));let n;const i=async({data:{id:e,type:t,data:i}})=>{const E=[];let p;try{if(t!==r.LOAD&&!n)throw o;switch(t){case r.LOAD:p=await(async({coreURL:e,wasmURL:t,workerURL:a})=>{const o=!n;try{e||(e=""),importScripts(e)}catch{if(e||(e="".replace("/umd/","/esm/")),self.createFFmpegCore=(await import(e)).default,!self.createFFmpegCore)throw s}const i=e,E=t||e.replace(/.js$/g,".wasm"),p=a||e.replace(/.js$/g,".worker.js");return n=await self.createFFmpegCore({mainScriptUrlOrBlob:`${i}#${btoa(JSON.stringify({wasmURL:E,workerURL:p}))}`}),n.setLogger((e=>c({type:r.LOG,data:e}))),n.setProgress((e=>c({type:r.PROGRESS,data:e}))),o})(i);break;case r.EXEC:p=(({args:e,timeout:t=-1})=>{n.setTimeout(t),n.exec(...e);const r=n.ret;return n.reset(),r})(i);break;case r.WRITE_FILE:p=(({path:e,data:t})=>(n.FS.writeFile(e,t),!0))(i);break;case r.READ_FILE:p=(({path:e,encoding:t})=>n.FS.readFile(e,{encoding:t}))(i);break;case r.DELETE_FILE:p=(({path:e})=>(n.FS.unlink(e),!0))(i);break;case r.RENAME:p=(({oldPath:e,newPath:t})=>(n.FS.rename(e,t),!0))(i);break;case r.CREATE_DIR:p=(({path:e})=>(n.FS.mkdir(e),!0))(i);break;case r.LIST_DIR:p=(({path:e})=>{const t=n.FS.readdir(e),r=[];for(const a of t){const t=n.FS.stat(`${e}/${a}`),o=n.FS.isDir(t.mode);r.push({name:a,isDir:o})}return r})(i);break;case r.DELETE_DIR:p=(({path:e})=>(n.FS.rmdir(e),!0))(i);break;case r.MOUNT:p=(({fsType:e,options:t,mountPoint:r})=>{const a=e,o=n.FS.filesystems[a];return!!o&&(n.FS.mount(o,t,r),!0)})(i);break;case r.UNMOUNT:p=(({mountPoint:e})=>(n.FS.unmount(e),!0))(i);break;default:throw a}}catch(t){return void c({id:e,type:r.ERROR,data:t.toString()})}p instanceof Uint8Array&&E.push(p.buffer),c({id:e,type:t,data:p},E)},E={onmessage:e=>{},postMessage:(e,t)=>{}};let c;"undefined"!=typeof self&&void 0!==self.postMessage?(c=self.postMessage,self.onmessage=i):c=(e,t)=>{E.onmessage(e)},E.postMessage=i;const p=()=>E;return t})()));
//# sourceMappingURL=worker.js.map