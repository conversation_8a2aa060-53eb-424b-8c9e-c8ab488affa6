(()=>{const t=document.currentScript,e=()=>{const s=t=>{let e;const{select:s,objFuncName:r}=t,a=document.querySelector(s);return a?.[r]&&(e=(t=>"function"==typeof t)(a?.[r])?a?.[r]():(t=>"object"==typeof t)(a?.[r])?a?.[r]:null),e};try{const{items:r,wait:a=0}=JSON.parse(t.getAttribute("data-params")),i=r.map(s);if(a&&i.some((t=>void 0===t)))return void setTimeout(e,a);t.setAttribute("data-response",JSON.stringify(i)),t.setAttribute("data-status","fulfilled")}catch(e){t.setAttribute("data-response",JSON.stringify(e)),t.setAttribute("data-status","rejected")}};e()})();