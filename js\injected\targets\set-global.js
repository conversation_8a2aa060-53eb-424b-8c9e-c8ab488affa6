(()=>{const t=document.currentScript;try{const{name:e,value:r}=JSON.parse(t.getAttribute("data-params")),s=e.split("."),a=s.pop(),u=s.reduce(((t,e)=>{return null!=(r=t)&&"object"==typeof r?t[e]:void 0;var r}),window);u?(u[a]=r,t.setAttribute("data-response",JSON.stringify(!0)),t.setAttribute("data-status","fulfilled")):(t.setAttribute("data-response",new Error(`${e} not found`)),t.setAttribute("data-status","rejected"))}catch(e){t.setAttribute("data-response",JSON.stringify(e)),t.setAttribute("data-status","rejected")}})();