{"action": {"default_icon": "images/logo.png", "default_popup": "html/popup.html", "default_title": "__MSG_title__"}, "background": {"service_worker": "bg-release.js"}, "commands": {"cancel-recording": {"description": "Cancel recording", "suggested_key": {"default": "Alt+Shift+C"}}, "stop-recording": {"description": "Stop recording", "suggested_key": {"default": "Alt+Shift+Z"}}}, "content_scripts": [{"all_frames": true, "js": ["js/common.js", "js/common1.js", "js/content.js"], "match_about_blank": true, "matches": ["<all_urls>"], "run_at": "document_start"}], "content_security_policy": {"extension_pages": "script-src 'self' 'wasm-unsafe-eval'; object-src 'self'; worker-src 'self' 'wasm-unsafe-eval';"}, "default_locale": "en", "description": "__MSG_desc__", "differential_fingerprint": "1.c8b31d9c6c73e40ab2feacc7f8d9e78515a627e72d1546824e995ec0f156f64e", "homepage_url": "https://vidhelper.app", "host_permissions": ["<all_urls>"], "icons": {"128": "images/logo-128.png", "64": "images/logo.png"}, "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAvqaXgPaAc6zFrr/vR+lAc7kBRBPWQa8EToK0JFPxI98ZwV6VsfcUnHhNDxGg0gfdanx7f+p+2Dg5IGOB1guR8X0F7tJdCBvqhxRPQZdEQdCEnNOo3yN7JkP2Y95ji+Z7OhMN5VWUd3vZQTb7/32yKlqIidRs9dzvf+Lx/vxFiNKBb9KRX72eMrd3gwM43eunBEIsC3D7s9+RKTH+uj3GDNn7mfajc2qce3aX0ekIkX34fPNxpCc3EtBWHsKDNaw/oIgDCLriURR0Ip+undrungeQPkY9j4bpMchxIsKStLGNeTXqMjZY3CVbDGvDNBABAyrksWUD279hAJR5okJjwwIDAQAB", "manifest_version": 3, "name": "__MSG_name__", "permissions": ["tabs", "webRequest", "scripting", "storage", "unlimitedStorage", "downloads", "declarativeNetRequest", "contextMenus", "notifications", "tabCapture"], "sandbox": {"pages": ["html/sandbox.html"]}, "short_name": "__MSG_name__", "update_url": "https://clients2.google.com/service/update2/crx", "version": "2.3.10", "web_accessible_resources": [{"matches": ["<all_urls>"], "resources": ["html/*.html", "js/*.js", "js/injected/*.js", "js/injected/*/*.js", "css/*.css", "js/*.wasm", "images/*"]}]}