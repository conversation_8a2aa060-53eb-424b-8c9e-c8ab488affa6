window.xhrCallback=(e,a,i,l)=>{const p=[{domain:"api\\.bilibili\\.(?:com|tv)",paths:["x/player/wbi/playurl","x/player/playurl","x/space/wbi/playurl","pgc/player/web/v2/playurl","pgc/player/web/playurl","player/web/playurl","pugv/player/web/playurl","intl/gateway/web/playurl"]},{domain:"www\\.bilibili\\.com",paths:["audio/music-service-c/web/song/info","audio/music-service-c/web/song/of-menu","audio/music-service-c/web/url"]}];for(const e of p){const a=new RegExp(`^https?://${e.domain}/(${e.paths.join("|")})\\?`,"i"),i=l.responseURL.match(a);if(i){try{const e=i[1].split("/").pop();window.dispatchEvent(new CustomEvent("req-raw-data",{detail:{data:l.responseText,url:l.responseURL,type:e}}))}catch(e){}return}}};