(()=>{const e={};function t(e){return document.cookie.split("; ").reduce(((e,t)=>{const[n,a]=t.split("=");return e[n]=a,e}),{})[e]||null}const n=(()=>{const e=/[-_.]/g,t={"-":"+",_:"/",".":"="};return{base64ToU8:n=>{let a;return a=e.test(n)?n.replace(e,(function(e){return t[e]})):n,a=atob(a),new Uint8Array([...a].map((e=>e.charCodeAt(0))))},u8ToBase64:(e,t=!1)=>{const n=btoa(String.fromCharCode(...e));return t?n.replace(/\+/g,"-").replace(/\//g,"_"):n}}})();window.xhrCallback=(a,o,i,r)=>{if(/^https?:\/\/(?:(?:jnn-pa\.googleapis\.com\/\$rpc\/google\.internal\.waa\.v1\.Waa)|(?:www\.youtube\.com\/api\/jnn\/v1))\/(?:Create|GenerateIT)/i.test(r.responseURL)){const a=r.responseURL.match(/(v1\.Waa|jnn\/v1)\/(?<api>\w+)/)?.groups?.api;(async(a,o)=>{try{e[a]=o,"GenerateIT"==a&&((e,t=!1)=>{try{const n=new CustomEvent("send-potoken",{detail:{data:e,isHooked:t}});window.dispatchEvent(n)}catch(e){}})(await(async(e,a)=>{let o=null;e?o=(function(e){const t=n.base64ToU8(e);if(t.length){const e=(new TextDecoder).decode(t.map((e=>e+97))),[n,a,,o,i,r]=JSON.parse(e);return{script:a,interpreterHash:o,globalName:r,challenge:i,messageId:n}}})(e[1]):window.ytAtR&&(o=JSON.parse(window.ytAtR).bgChallenge);const i=(()=>{let e,t;return{promise:new Promise(((n,a)=>{e=n,t=a})),resolve:e,reject:t}})();attFunctions={},await window[o.globalName].a(o.program,((e,t,n,a)=>{i.resolve({asyncSnapshotFunction:e,shutdownFunction:t,passEventFunction:n,checkCameraFunction:a})}),!0,void 0,(()=>{}),[[],[]]),webPoSignalOutput=[],botguardResponse=await(async function(e){return await new Promise((async(t,n)=>{const a=await i.promise;await a.asyncSnapshotFunction((e=>t(e)),[e.contentBinding,e.signedTimestamp,e.webPoSignalOutput,e.skipPrivacyBuffer]),setTimeout((()=>{n(new Error("asyncSnapshotFunction timeout"))}),3e3)}))})({webPoSignalOutput:webPoSignalOutput}),integrityToken=a[0],getMinter=webPoSignalOutput[0],mintCallback=await getMinter(n.base64ToU8(integrityToken)),identifier=t("__Secure-3PAPISID")||t("SAPISID")?yt.config_.DATASYNC_ID:yt.config_.VISITOR_DATA;const r=await mintCallback((new TextEncoder).encode(identifier)),s=n.u8ToBase64(r,!0);if(s.length>80)return s})(e?.Create?.data,e[a].data))}catch(e){}})(a,{url:r.responseURL,data:JSON.parse(r.response),body:i})}}})();