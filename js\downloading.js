var _class,_class3,_class5,_excluded=["classWorkerURL","workerType"],_excluded2=["headers"];function _objectWithoutProperties(e,t){if(null==e)return{};var n,r=_objectWithoutPropertiesLoose(e,t);if(Object.getOwnPropertySymbols)for(var i=Object.getOwnPropertySymbols(e),o=0;o<i.length;o++)n=i[o],0<=t.indexOf(n)||Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n]);return r}function _objectWithoutPropertiesLoose(e,t){if(null==e)return{};for(var n,r={},i=Object.keys(e),o=0;o<i.length;o++)n=i[o],0<=t.indexOf(n)||(r[n]=e[n]);return r}function _classPrivateFieldInitSpec(e,t,n){_checkPrivateRedeclaration(e,t),t.set(e,n)}function _checkPrivateRedeclaration(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")}function _classPrivateFieldSet(e,t,n){return _classApplyDescriptorSet(e,_classExtractFieldDescriptor(e,t,"set"),n),n}function _classApplyDescriptorSet(e,t,n){if(t.set)t.set.call(e,n);else{if(!t.writable)throw new TypeError("attempted to set read only private field");t.value=n}}function _classPrivateFieldGet(e,t){return _classApplyDescriptorGet(e,_classExtractFieldDescriptor(e,t,"get"))}function _classExtractFieldDescriptor(e,t,n){if(!t.has(e))throw new TypeError("attempted to "+n+" private field on non-instance");return t.get(e)}function _classApplyDescriptorGet(e,t){return t.get?t.get.call(e):t.value}function _regeneratorRuntime(){"use strict";_regeneratorRuntime=function(){return a};var u,a={},e=Object.prototype,l=e.hasOwnProperty,c=Object.defineProperty||function(e,t,n){e[t]=n.value},t="function"==typeof Symbol?Symbol:{},r=t.iterator||"@@iterator",n=t.asyncIterator||"@@asyncIterator",i=t.toStringTag||"@@toStringTag";function o(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{o({},"")}catch(u){o=function(e,t,n){return e[t]=n}}function s(e,t,n,r){var i,o,a,s,t=t&&t.prototype instanceof y?t:y,t=Object.create(t.prototype),r=new E(r||[]);return c(t,"_invoke",{value:(i=e,o=n,a=r,s=f,function(e,t){if(s===p)throw new Error("Generator is already running");if(s===g){if("throw"===e)throw t;return{value:u,done:!0}}for(a.method=e,a.arg=t;;){var n=a.delegate;if(n){var r=function e(t,n){var r=n.method,i=t.iterator[r];if(i===u)return n.delegate=null,"throw"===r&&t.iterator.return&&(n.method="return",n.arg=u,e(t,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),m;i=d(i,t.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,m;var i=i.arg;return i?i.done?(n[t.resultName]=i.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=u),n.delegate=null,m):i:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,m)}(n,a);if(r){if(r===m)continue;return r}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if(s===f)throw s=g,a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);s=p;r=d(i,o,a);if("normal"===r.type){if(s=a.done?g:h,r.arg===m)continue;return{value:r.arg,done:a.done}}"throw"===r.type&&(s=g,a.method="throw",a.arg=r.arg)}})}),t}function d(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}a.wrap=s;var f="suspendedStart",h="suspendedYield",p="executing",g="completed",m={};function y(){}function v(){}function _(){}var b={};o(b,r,function(){return this});t=Object.getPrototypeOf,t=t&&t(t(C([])));t&&t!==e&&l.call(t,r)&&(b=t);var w=_.prototype=y.prototype=Object.create(b);function x(e){["next","throw","return"].forEach(function(t){o(e,t,function(e){return this._invoke(t,e)})})}function S(a,s){var t;c(this,"_invoke",{value:function(n,r){function e(){return new s(function(e,t){!function t(e,n,r,i){e=d(a[e],a,n);if("throw"!==e.type){var o=e.arg,n=o.value;return n&&"object"==_typeof(n)&&l.call(n,"__await")?s.resolve(n.__await).then(function(e){t("next",e,r,i)},function(e){t("throw",e,r,i)}):s.resolve(n).then(function(e){o.value=e,r(o)},function(e){return t("throw",e,r,i)})}i(e.arg)}(n,r,e,t)})}return t=t?t.then(e,e):e()}})}function T(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function k(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function E(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(T,this),this.reset(!0)}function C(t){if(t||""===t){var e=t[r];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,e=function e(){for(;++n<t.length;)if(l.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=u,e.done=!0,e};return e.next=e}}throw new TypeError(_typeof(t)+" is not iterable")}return c(w,"constructor",{value:v.prototype=_,configurable:!0}),c(_,"constructor",{value:v,configurable:!0}),v.displayName=o(_,i,"GeneratorFunction"),a.isGeneratorFunction=function(e){e="function"==typeof e&&e.constructor;return!!e&&(e===v||"GeneratorFunction"===(e.displayName||e.name))},a.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,_):(e.__proto__=_,o(e,i,"GeneratorFunction")),e.prototype=Object.create(w),e},a.awrap=function(e){return{__await:e}},x(S.prototype),o(S.prototype,n,function(){return this}),a.AsyncIterator=S,a.async=function(e,t,n,r,i){void 0===i&&(i=Promise);var o=new S(s(e,t,n,r),i);return a.isGeneratorFunction(t)?o:o.next().then(function(e){return e.done?e.value:o.next()})},x(w),o(w,i,"Generator"),o(w,r,function(){return this}),o(w,"toString",function(){return"[object Generator]"}),a.keys=function(e){var t,n=Object(e),r=[];for(t in n)r.push(t);return r.reverse(),function e(){for(;r.length;){var t=r.pop();if(t in n)return e.value=t,e.done=!1,e}return e.done=!0,e}},a.values=C,E.prototype={constructor:E,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=u,this.done=!1,this.delegate=null,this.method="next",this.arg=u,this.tryEntries.forEach(k),!e)for(var t in this)"t"===t.charAt(0)&&l.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=u)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(n){if(this.done)throw n;var r=this;function e(e,t){return o.type="throw",o.arg=n,r.next=e,t&&(r.method="next",r.arg=u),!!t}for(var t=this.tryEntries.length-1;0<=t;--t){var i=this.tryEntries[t],o=i.completion;if("root"===i.tryLoc)return e("end");if(i.tryLoc<=this.prev){var a=l.call(i,"catchLoc"),s=l.call(i,"finallyLoc");if(a&&s){if(this.prev<i.catchLoc)return e(i.catchLoc,!0);if(this.prev<i.finallyLoc)return e(i.finallyLoc)}else if(a){if(this.prev<i.catchLoc)return e(i.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return e(i.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;0<=n;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&l.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var i=r;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=e,o.arg=t,i?(this.method="next",this.next=i.finallyLoc,m):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),m},finish:function(e){for(var t=this.tryEntries.length-1;0<=t;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),k(n),m}},catch:function(e){for(var t=this.tryEntries.length-1;0<=t;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r,i=n.completion;return"throw"===i.type&&(r=i.arg,k(n)),r}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:C(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=u),m}},a}function _createForOfIteratorHelper(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=_unsupportedIterableToArray(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,t=function(){};return{s:t,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:t}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,o=!0,a=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){a=!0,i=e},f:function(){try{o||null==n.return||n.return()}finally{if(a)throw i}}}}function asyncGeneratorStep(e,t,n,r,i,o,a){try{var s=e[o](a),u=s.value}catch(e){return void n(e)}s.done?t(u):Promise.resolve(u).then(r,i)}function _asyncToGenerator(s){return function(){var e=this,a=arguments;return new Promise(function(t,n){var r=s.apply(e,a);function i(e){asyncGeneratorStep(r,t,n,i,o,"next",e)}function o(e){asyncGeneratorStep(r,t,n,i,o,"throw",e)}i(void 0)})}}function _wrapRegExp(){_wrapRegExp=function(e,t){return new r(e,void 0,t)};var i=RegExp.prototype,a=new WeakMap;function r(e,t,n){t=new RegExp(e,t);return a.set(t,n||a.get(e)),_setPrototypeOf(t,r.prototype)}function o(i,e){var o=a.get(e);return Object.keys(o).reduce(function(e,t){var n=o[t];if("number"==typeof n)e[t]=i[n];else{for(var r=0;void 0===i[n[r]]&&r+1<n.length;)r++;e[t]=i[n[r]]}return e},Object.create(null))}return _inherits(r,RegExp),r.prototype.exec=function(e){var t=i.exec.call(this,e);return t&&(t.groups=o(t,this),(e=t.indices)&&(e.groups=o(e,this))),t},r.prototype[Symbol.replace]=function(e,t){if("string"==typeof t){var n=a.get(this);return i[Symbol.replace].call(this,e,t.replace(/\$<([^>]+)>/g,function(e,t){t=n[t];return"$"+(Array.isArray(t)?t.join("$"):t)}))}if("function"!=typeof t)return i[Symbol.replace].call(this,e,t);var r=this;return i[Symbol.replace].call(this,e,function(){var e=arguments;return"object"!=_typeof(e[e.length-1])&&(e=[].slice.call(e)).push(o(e,r)),t.apply(this,e)})},_wrapRegExp.apply(this,arguments)}function ownKeys(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}function _objectSpread(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?ownKeys(Object(n),!0).forEach(function(e){_defineProperty(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function _defineProperty(e,t,n){return(t=_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function _toConsumableArray(e){return _arrayWithoutHoles(e)||_iterableToArray(e)||_unsupportedIterableToArray(e)||_nonIterableSpread()}function _nonIterableSpread(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _iterableToArray(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function _arrayWithoutHoles(e){if(Array.isArray(e))return _arrayLikeToArray(e)}function _slicedToArray(e,t){return _arrayWithHoles(e)||_iterableToArrayLimit(e,t)||_unsupportedIterableToArray(e,t)||_nonIterableRest()}function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return _arrayLikeToArray(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?_arrayLikeToArray(e,t):void 0}}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function _iterableToArrayLimit(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,o,a,s=[],u=!0,l=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;u=!1}else for(;!(u=(r=o.call(n)).done)&&(s.push(r.value),s.length!==t);u=!0);}catch(e){l=!0,i=e}finally{try{if(!u&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw i}}return s}}function _arrayWithHoles(e){if(Array.isArray(e))return e}function _inherits(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&_setPrototypeOf(e,t)}function _setPrototypeOf(e,t){return(_setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function _createSuper(n){var r=_isNativeReflectConstruct();return function(){var e,t=_getPrototypeOf(n);return _possibleConstructorReturn(this,r?(e=_getPrototypeOf(this).constructor,Reflect.construct(t,arguments,e)):t.apply(this,arguments))}}function _possibleConstructorReturn(e,t){if(t&&("object"===_typeof(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return _assertThisInitialized(e)}function _assertThisInitialized(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}function _getPrototypeOf(e){return(_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function _defineProperties(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,_toPropertyKey(r.key),r)}}function _createClass(e,t,n){return t&&_defineProperties(e.prototype,t),n&&_defineProperties(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function _toPropertyKey(e){e=_toPrimitive(e,"string");return"symbol"===_typeof(e)?e:String(e)}function _toPrimitive(e,t){if("object"!==_typeof(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0===n)return("string"===t?String:Number)(e);t=n.call(e,t||"default");if("object"!==_typeof(t))return t;throw new TypeError("@@toPrimitive must return a primitive value.")}function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _asyncIterator(e){var t,n,r,i=2;for("undefined"!=typeof Symbol&&(n=Symbol.asyncIterator,r=Symbol.iterator);i--;){if(n&&null!=(t=e[n]))return t.call(e);if(r&&null!=(t=e[r]))return new AsyncFromSyncIterator(t.call(e));n="@@asyncIterator",r="@@iterator"}throw new TypeError("Object is not async iterable")}function AsyncFromSyncIterator(e){function n(e){if(Object(e)!==e)return Promise.reject(new TypeError(e+" is not an object."));var t=e.done;return Promise.resolve(e.value).then(function(e){return{value:e,done:t}})}return(AsyncFromSyncIterator=function(e){this.s=e,this.n=e.next}).prototype={s:null,n:null,next:function(){return n(this.n.apply(this.s,arguments))},return:function(e){var t=this.s.return;return void 0===t?Promise.resolve({value:e,done:!0}):n(t.apply(this.s,arguments))},throw:function(e){var t=this.s.return;return void 0===t?Promise.reject(e):n(t.apply(this.s,arguments))}},new AsyncFromSyncIterator(e)}!function(e,t){"use strict";"object"==("undefined"==typeof module?"undefined":_typeof(module))&&"object"==_typeof(module.exports)?module.exports=e.document?t(e,!0):function(e){if(!e.document)throw new Error("jQuery requires a window with a document");return t(e)}:t(e)}("undefined"!=typeof window?window:this,function(S,e){"use strict";function g(e){return null!=e&&e===e.window}var t=[],n=Object.getPrototypeOf,s=t.slice,m=t.flat?function(e){return t.flat.call(e)}:function(e){return t.concat.apply([],e)},u=t.push,i=t.indexOf,r={},o=r.toString,y=r.hasOwnProperty,a=y.toString,l=a.call(Object),v={},_=function(e){return"function"==typeof e&&"number"!=typeof e.nodeType},T=S.document,c={type:!0,src:!0,nonce:!0,noModule:!0};function b(e,t,n){var r,i,o=(n=n||T).createElement("script");if(o.text=e,t)for(r in c)(i=t[r]||t.getAttribute&&t.getAttribute(r))&&o.setAttribute(r,i);n.head.appendChild(o).parentNode.removeChild(o)}function p(e){return null==e?e+"":"object"==_typeof(e)||"function"==typeof e?r[o.call(e)]||"object":_typeof(e)}var k=function e(t,n){return new e.fn.init(t,n)};function d(e){var t=!!e&&"length"in e&&e.length,n=p(e);return!_(e)&&!g(e)&&("array"===n||0===t||"number"==typeof t&&0<t&&t-1 in e)}k.fn=k.prototype={jquery:"3.5.1",constructor:k,length:0,toArray:function(){return s.call(this)},get:function(e){return null==e?s.call(this):e<0?this[e+this.length]:this[e]},pushStack:function(e){e=k.merge(this.constructor(),e);return e.prevObject=this,e},each:function(e){return k.each(this,e)},map:function(n){return this.pushStack(k.map(this,function(e,t){return n.call(e,t,e)}))},slice:function(){return this.pushStack(s.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack(k.grep(this,function(e,t){return(t+1)%2}))},odd:function(){return this.pushStack(k.grep(this,function(e,t){return t%2}))},eq:function(e){var t=this.length,e=+e+(e<0?t:0);return this.pushStack(0<=e&&e<t?[this[e]]:[])},end:function(){return this.prevObject||this.constructor()},push:u,sort:t.sort,splice:t.splice},k.extend=k.fn.extend=function(){var e,t,n,r,i,o=arguments[0]||{},a=1,s=arguments.length,u=!1;for("boolean"==typeof o&&(u=o,o=arguments[a]||{},a++),"object"==_typeof(o)||_(o)||(o={}),a===s&&(o=this,a--);a<s;a++)if(null!=(e=arguments[a]))for(t in e)n=e[t],"__proto__"!==t&&o!==n&&(u&&n&&(k.isPlainObject(n)||(r=Array.isArray(n)))?(i=o[t],i=r&&!Array.isArray(i)?[]:r||k.isPlainObject(i)?i:{},r=!1,o[t]=k.extend(u,i,n)):void 0!==n&&(o[t]=n));return o},k.extend({expando:"jQuery"+("3.5.1"+Math.random()).replace(/\D/g,""),isReady:!0,error:function(e){throw new Error(e)},noop:function(){},isPlainObject:function(e){return!(!e||"[object Object]"!==o.call(e)||(e=n(e))&&("function"!=typeof(e=y.call(e,"constructor")&&e.constructor)||a.call(e)!==l))},isEmptyObject:function(e){for(var t in e)return!1;return!0},globalEval:function(e,t,n){b(e,{nonce:t&&t.nonce},n)},each:function(e,t){var n,r=0;if(d(e))for(n=e.length;r<n&&!1!==t.call(e[r],r,e[r]);r++);else for(r in e)if(!1===t.call(e[r],r,e[r]))break;return e},makeArray:function(e,t){t=t||[];return null!=e&&(d(Object(e))?k.merge(t,"string"==typeof e?[e]:e):u.call(t,e)),t},inArray:function(e,t,n){return null==t?-1:i.call(t,e,n)},merge:function(e,t){for(var n=+t.length,r=0,i=e.length;r<n;r++)e[i++]=t[r];return e.length=i,e},grep:function(e,t,n){for(var r=[],i=0,o=e.length,a=!n;i<o;i++)!t(e[i],i)!=a&&r.push(e[i]);return r},map:function(e,t,n){var r,i,o=0,a=[];if(d(e))for(r=e.length;o<r;o++)null!=(i=t(e[o],o,n))&&a.push(i);else for(o in e)null!=(i=t(e[o],o,n))&&a.push(i);return m(a)},guid:1,support:v}),"function"==typeof Symbol&&(k.fn[Symbol.iterator]=t[Symbol.iterator]),k.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(e,t){r["[object "+t+"]"]=t.toLowerCase()});var f=function(n){function d(e,t){return e="0x"+e.slice(1)-65536,t||(e<0?String.fromCharCode(65536+e):String.fromCharCode(e>>10|55296,1023&e|56320))}function r(){x()}var e,h,b,o,i,p,f,g,w,u,l,x,S,a,T,m,s,c,y,k="sizzle"+ +new Date,v=n.document,E=0,_=0,C=ue(),A=ue(),D=ue(),I=ue(),R=function(e,t){return e===t&&(l=!0),0},O={}.hasOwnProperty,t=[],P=t.pop,L=t.push,F=t.push,U=t.slice,M=function(e,t){for(var n=0,r=e.length;n<r;n++)if(e[n]===t)return n;return-1},N="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",B="[\\x20\\t\\r\\n\\f]",j="(?:\\\\[\\da-fA-F]{1,6}"+B+"?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+",q="\\["+B+"*("+j+")(?:"+B+"*([*^$|!~]?=)"+B+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+j+"))|)"+B+"*\\]",G=":("+j+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+q+")*)|.*)\\)|)",H=new RegExp(B+"+","g"),z=new RegExp("^"+B+"+|((?:^|[^\\\\])(?:\\\\.)*)"+B+"+$","g"),W=new RegExp("^"+B+"*,"+B+"*"),V=new RegExp("^"+B+"*([>+~]|"+B+")"+B+"*"),$=new RegExp(B+"|>"),Y=new RegExp(G),X=new RegExp("^"+j+"$"),Q={ID:new RegExp("^#("+j+")"),CLASS:new RegExp("^\\.("+j+")"),TAG:new RegExp("^("+j+"|[*])"),ATTR:new RegExp("^"+q),PSEUDO:new RegExp("^"+G),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+B+"*(even|odd|(([+-]|)(\\d*)n|)"+B+"*(?:([+-]|)"+B+"*(\\d+)|))"+B+"*\\)|)","i"),bool:new RegExp("^(?:"+N+")$","i"),needsContext:new RegExp("^"+B+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+B+"*((?:-\\d)?\\d*)"+B+"*\\)|)(?=[^-]|$)","i")},K=/HTML$/i,J=/^(?:input|select|textarea|button)$/i,Z=/^h\d$/i,ee=/^[^{]+\{\s*\[native \w/,te=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,ne=/[+~]/,re=new RegExp("\\\\[\\da-fA-F]{1,6}"+B+"?|\\\\([^\\r\\n\\f])","g"),ie=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\0-\x1f\x7f-\uFFFF\w-]/g,oe=function(e,t){return t?"\0"===e?"�":e.slice(0,-1)+"\\"+e.charCodeAt(e.length-1).toString(16)+" ":"\\"+e},ae=ve(function(e){return!0===e.disabled&&"fieldset"===e.nodeName.toLowerCase()},{dir:"parentNode",next:"legend"});try{F.apply(t=U.call(v.childNodes),v.childNodes),t[v.childNodes.length].nodeType}catch(e){F={apply:t.length?function(e,t){L.apply(e,U.call(t))}:function(e,t){for(var n=e.length,r=0;e[n++]=t[r++];);e.length=n-1}}}function se(e,t,n,r){var i,o,a,s,u,l,c,d=t&&t.ownerDocument,f=t?t.nodeType:9;if(n=n||[],"string"!=typeof e||!e||1!==f&&9!==f&&11!==f)return n;if(!r&&(x(t),t=t||S,T)){if(11!==f&&(u=te.exec(e)))if(i=u[1]){if(9===f){if(!(a=t.getElementById(i)))return n;if(a.id===i)return n.push(a),n}else if(d&&(a=d.getElementById(i))&&y(t,a)&&a.id===i)return n.push(a),n}else{if(u[2])return F.apply(n,t.getElementsByTagName(e)),n;if((i=u[3])&&h.getElementsByClassName&&t.getElementsByClassName)return F.apply(n,t.getElementsByClassName(i)),n}if(h.qsa&&!I[e+" "]&&(!m||!m.test(e))&&(1!==f||"object"!==t.nodeName.toLowerCase())){if(c=e,d=t,1===f&&($.test(e)||V.test(e))){for((d=ne.test(e)&&ge(t.parentNode)||t)===t&&h.scope||((s=t.getAttribute("id"))?s=s.replace(ie,oe):t.setAttribute("id",s=k)),o=(l=p(e)).length;o--;)l[o]=(s?"#"+s:":scope")+" "+ye(l[o]);c=l.join(",")}try{return F.apply(n,d.querySelectorAll(c)),n}catch(t){I(e,!0)}finally{s===k&&t.removeAttribute("id")}}}return g(e.replace(z,"$1"),t,n,r)}function ue(){var r=[];return function e(t,n){return r.push(t+" ")>b.cacheLength&&delete e[r.shift()],e[t+" "]=n}}function le(e){return e[k]=!0,e}function ce(e){var t=S.createElement("fieldset");try{return!!e(t)}catch(e){return!1}finally{t.parentNode&&t.parentNode.removeChild(t),t=null}}function de(e,t){for(var n=e.split("|"),r=n.length;r--;)b.attrHandle[n[r]]=t}function fe(e,t){var n=t&&e,r=n&&1===e.nodeType&&1===t.nodeType&&e.sourceIndex-t.sourceIndex;if(r)return r;if(n)for(;n=n.nextSibling;)if(n===t)return-1;return e?1:-1}function he(t){return function(e){return"form"in e?e.parentNode&&!1===e.disabled?"label"in e?"label"in e.parentNode?e.parentNode.disabled===t:e.disabled===t:e.isDisabled===t||e.isDisabled!==!t&&ae(e)===t:e.disabled===t:"label"in e&&e.disabled===t}}function pe(a){return le(function(o){return o=+o,le(function(e,t){for(var n,r=a([],e.length,o),i=r.length;i--;)e[n=r[i]]&&(e[n]=!(t[n]=e[n]))})})}function ge(e){return e&&void 0!==e.getElementsByTagName&&e}for(e in h=se.support={},i=se.isXML=function(e){var t=e.namespaceURI,e=(e.ownerDocument||e).documentElement;return!K.test(t||e&&e.nodeName||"HTML")},x=se.setDocument=function(e){var t,e=e?e.ownerDocument||e:v;return e!=S&&9===e.nodeType&&e.documentElement&&(a=(S=e).documentElement,T=!i(S),v!=S&&(t=S.defaultView)&&t.top!==t&&(t.addEventListener?t.addEventListener("unload",r,!1):t.attachEvent&&t.attachEvent("onunload",r)),h.scope=ce(function(e){return a.appendChild(e).appendChild(S.createElement("div")),void 0!==e.querySelectorAll&&!e.querySelectorAll(":scope fieldset div").length}),h.attributes=ce(function(e){return e.className="i",!e.getAttribute("className")}),h.getElementsByTagName=ce(function(e){return e.appendChild(S.createComment("")),!e.getElementsByTagName("*").length}),h.getElementsByClassName=ee.test(S.getElementsByClassName),h.getById=ce(function(e){return a.appendChild(e).id=k,!S.getElementsByName||!S.getElementsByName(k).length}),h.getById?(b.filter.ID=function(e){var t=e.replace(re,d);return function(e){return e.getAttribute("id")===t}},b.find.ID=function(e,t){if(void 0!==t.getElementById&&T){e=t.getElementById(e);return e?[e]:[]}}):(b.filter.ID=function(e){var t=e.replace(re,d);return function(e){e=void 0!==e.getAttributeNode&&e.getAttributeNode("id");return e&&e.value===t}},b.find.ID=function(e,t){if(void 0!==t.getElementById&&T){var n,r,i,o=t.getElementById(e);if(o){if((n=o.getAttributeNode("id"))&&n.value===e)return[o];for(i=t.getElementsByName(e),r=0;o=i[r++];)if((n=o.getAttributeNode("id"))&&n.value===e)return[o]}return[]}}),b.find.TAG=h.getElementsByTagName?function(e,t){return void 0!==t.getElementsByTagName?t.getElementsByTagName(e):h.qsa?t.querySelectorAll(e):void 0}:function(e,t){var n,r=[],i=0,o=t.getElementsByTagName(e);if("*"!==e)return o;for(;n=o[i++];)1===n.nodeType&&r.push(n);return r},b.find.CLASS=h.getElementsByClassName&&function(e,t){if(void 0!==t.getElementsByClassName&&T)return t.getElementsByClassName(e)},s=[],m=[],(h.qsa=ee.test(S.querySelectorAll))&&(ce(function(e){var t;a.appendChild(e).innerHTML="<a id='"+k+"'></a><select id='"+k+"-\r\\' msallowcapture=''><option selected=''></option></select>",e.querySelectorAll("[msallowcapture^='']").length&&m.push("[*^$]="+B+"*(?:''|\"\")"),e.querySelectorAll("[selected]").length||m.push("\\["+B+"*(?:value|"+N+")"),e.querySelectorAll("[id~="+k+"-]").length||m.push("~="),(t=S.createElement("input")).setAttribute("name",""),e.appendChild(t),e.querySelectorAll("[name='']").length||m.push("\\["+B+"*name"+B+"*="+B+"*(?:''|\"\")"),e.querySelectorAll(":checked").length||m.push(":checked"),e.querySelectorAll("a#"+k+"+*").length||m.push(".#.+[+~]"),e.querySelectorAll("\\\f"),m.push("[\\r\\n\\f]")}),ce(function(e){e.innerHTML="<a href='' disabled='disabled'></a><select disabled='disabled'><option/></select>";var t=S.createElement("input");t.setAttribute("type","hidden"),e.appendChild(t).setAttribute("name","D"),e.querySelectorAll("[name=d]").length&&m.push("name"+B+"*[*^$|!~]?="),2!==e.querySelectorAll(":enabled").length&&m.push(":enabled",":disabled"),a.appendChild(e).disabled=!0,2!==e.querySelectorAll(":disabled").length&&m.push(":enabled",":disabled"),e.querySelectorAll("*,:x"),m.push(",.*:")})),(h.matchesSelector=ee.test(c=a.matches||a.webkitMatchesSelector||a.mozMatchesSelector||a.oMatchesSelector||a.msMatchesSelector))&&ce(function(e){h.disconnectedMatch=c.call(e,"*"),c.call(e,"[s!='']:x"),s.push("!=",G)}),m=m.length&&new RegExp(m.join("|")),s=s.length&&new RegExp(s.join("|")),t=ee.test(a.compareDocumentPosition),y=t||ee.test(a.contains)?function(e,t){var n=9===e.nodeType?e.documentElement:e,t=t&&t.parentNode;return e===t||!(!t||1!==t.nodeType||!(n.contains?n.contains(t):e.compareDocumentPosition&&16&e.compareDocumentPosition(t)))}:function(e,t){if(t)for(;t=t.parentNode;)if(t===e)return!0;return!1},R=t?function(e,t){if(e===t)return l=!0,0;var n=!e.compareDocumentPosition-!t.compareDocumentPosition;return n||(1&(n=(e.ownerDocument||e)==(t.ownerDocument||t)?e.compareDocumentPosition(t):1)||!h.sortDetached&&t.compareDocumentPosition(e)===n?e==S||e.ownerDocument==v&&y(v,e)?-1:t==S||t.ownerDocument==v&&y(v,t)?1:u?M(u,e)-M(u,t):0:4&n?-1:1)}:function(e,t){if(e===t)return l=!0,0;var n,r=0,i=e.parentNode,o=t.parentNode,a=[e],s=[t];if(!i||!o)return e==S?-1:t==S?1:i?-1:o?1:u?M(u,e)-M(u,t):0;if(i===o)return fe(e,t);for(n=e;n=n.parentNode;)a.unshift(n);for(n=t;n=n.parentNode;)s.unshift(n);for(;a[r]===s[r];)r++;return r?fe(a[r],s[r]):a[r]==v?-1:s[r]==v?1:0}),S},se.matches=function(e,t){return se(e,null,null,t)},se.matchesSelector=function(e,t){if(x(e),h.matchesSelector&&T&&!I[t+" "]&&(!s||!s.test(t))&&(!m||!m.test(t)))try{var n=c.call(e,t);if(n||h.disconnectedMatch||e.document&&11!==e.document.nodeType)return n}catch(e){I(t,!0)}return 0<se(t,S,null,[e]).length},se.contains=function(e,t){return(e.ownerDocument||e)!=S&&x(e),y(e,t)},se.attr=function(e,t){(e.ownerDocument||e)!=S&&x(e);var n=b.attrHandle[t.toLowerCase()],n=n&&O.call(b.attrHandle,t.toLowerCase())?n(e,t,!T):void 0;return void 0!==n?n:h.attributes||!T?e.getAttribute(t):(n=e.getAttributeNode(t))&&n.specified?n.value:null},se.escape=function(e){return(e+"").replace(ie,oe)},se.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},se.uniqueSort=function(e){var t,n=[],r=0,i=0;if(l=!h.detectDuplicates,u=!h.sortStable&&e.slice(0),e.sort(R),l){for(;t=e[i++];)t===e[i]&&(r=n.push(i));for(;r--;)e.splice(n[r],1)}return u=null,e},o=se.getText=function(e){var t,n="",r=0,i=e.nodeType;if(i){if(1===i||9===i||11===i){if("string"==typeof e.textContent)return e.textContent;for(e=e.firstChild;e;e=e.nextSibling)n+=o(e)}else if(3===i||4===i)return e.nodeValue}else for(;t=e[r++];)n+=o(t);return n},(b=se.selectors={cacheLength:50,createPseudo:le,match:Q,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(re,d),e[3]=(e[3]||e[4]||e[5]||"").replace(re,d),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||se.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&se.error(e[0]),e},PSEUDO:function(e){var t,n=!e[6]&&e[2];return Q.CHILD.test(e[0])?null:(e[3]?e[2]=e[4]||e[5]||"":n&&Y.test(n)&&(t=p(n,!0))&&(t=n.indexOf(")",n.length-t)-n.length)&&(e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(re,d).toLowerCase();return"*"===e?function(){return!0}:function(e){return e.nodeName&&e.nodeName.toLowerCase()===t}},CLASS:function(e){var t=C[e+" "];return t||(t=new RegExp("(^|"+B+")"+e+"("+B+"|$)"))&&C(e,function(e){return t.test("string"==typeof e.className&&e.className||void 0!==e.getAttribute&&e.getAttribute("class")||"")})},ATTR:function(t,n,r){return function(e){e=se.attr(e,t);return null==e?"!="===n:!n||(e+="","="===n?e===r:"!="===n?e!==r:"^="===n?r&&0===e.indexOf(r):"*="===n?r&&-1<e.indexOf(r):"$="===n?r&&e.slice(-r.length)===r:"~="===n?-1<(" "+e.replace(H," ")+" ").indexOf(r):"|="===n&&(e===r||e.slice(0,r.length+1)===r+"-"))}},CHILD:function(p,e,t,g,m){var y="nth"!==p.slice(0,3),v="last"!==p.slice(-4),_="of-type"===e;return 1===g&&0===m?function(e){return!!e.parentNode}:function(e,t,n){var r,i,o,a,s,u,l=y!=v?"nextSibling":"previousSibling",c=e.parentNode,d=_&&e.nodeName.toLowerCase(),f=!n&&!_,h=!1;if(c){if(y){for(;l;){for(a=e;a=a[l];)if(_?a.nodeName.toLowerCase()===d:1===a.nodeType)return!1;u=l="only"===p&&!u&&"nextSibling"}return!0}if(u=[v?c.firstChild:c.lastChild],v&&f){for(h=(s=(r=(i=(o=(a=c)[k]||(a[k]={}))[a.uniqueID]||(o[a.uniqueID]={}))[p]||[])[0]===E&&r[1])&&r[2],a=s&&c.childNodes[s];a=++s&&a&&a[l]||(h=s=0)||u.pop();)if(1===a.nodeType&&++h&&a===e){i[p]=[E,s,h];break}}else if(f&&(h=s=(r=(i=(o=(a=e)[k]||(a[k]={}))[a.uniqueID]||(o[a.uniqueID]={}))[p]||[])[0]===E&&r[1]),!1===h)for(;(a=++s&&a&&a[l]||(h=s=0)||u.pop())&&((_?a.nodeName.toLowerCase()!==d:1!==a.nodeType)||!++h||(f&&((i=(o=a[k]||(a[k]={}))[a.uniqueID]||(o[a.uniqueID]={}))[p]=[E,h]),a!==e)););return(h-=m)===g||h%g==0&&0<=h/g}}},PSEUDO:function(e,o){var t,a=b.pseudos[e]||b.setFilters[e.toLowerCase()]||se.error("unsupported pseudo: "+e);return a[k]?a(o):1<a.length?(t=[e,e,"",o],b.setFilters.hasOwnProperty(e.toLowerCase())?le(function(e,t){for(var n,r=a(e,o),i=r.length;i--;)e[n=M(e,r[i])]=!(t[n]=r[i])}):function(e){return a(e,0,t)}):a}},pseudos:{not:le(function(e){var r=[],i=[],s=f(e.replace(z,"$1"));return s[k]?le(function(e,t,n,r){for(var i,o=s(e,null,r,[]),a=e.length;a--;)(i=o[a])&&(e[a]=!(t[a]=i))}):function(e,t,n){return r[0]=e,s(r,null,n,i),r[0]=null,!i.pop()}}),has:le(function(t){return function(e){return 0<se(t,e).length}}),contains:le(function(t){return t=t.replace(re,d),function(e){return-1<(e.textContent||o(e)).indexOf(t)}}),lang:le(function(n){return X.test(n||"")||se.error("unsupported lang: "+n),n=n.replace(re,d).toLowerCase(),function(e){var t;do{if(t=T?e.lang:e.getAttribute("xml:lang")||e.getAttribute("lang"))return(t=t.toLowerCase())===n||0===t.indexOf(n+"-")}while((e=e.parentNode)&&1===e.nodeType);return!1}}),target:function(e){var t=n.location&&n.location.hash;return t&&t.slice(1)===e.id},root:function(e){return e===a},focus:function(e){return e===S.activeElement&&(!S.hasFocus||S.hasFocus())&&!!(e.type||e.href||~e.tabIndex)},enabled:he(!1),disabled:he(!0),checked:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&!!e.checked||"option"===t&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,!0===e.selected},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeType<6)return!1;return!0},parent:function(e){return!b.pseudos.empty(e)},header:function(e){return Z.test(e.nodeName)},input:function(e){return J.test(e.nodeName)},button:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&"button"===e.type||"button"===t},text:function(e){return"input"===e.nodeName.toLowerCase()&&"text"===e.type&&(null==(e=e.getAttribute("type"))||"text"===e.toLowerCase())},first:pe(function(){return[0]}),last:pe(function(e,t){return[t-1]}),eq:pe(function(e,t,n){return[n<0?n+t:n]}),even:pe(function(e,t){for(var n=0;n<t;n+=2)e.push(n);return e}),odd:pe(function(e,t){for(var n=1;n<t;n+=2)e.push(n);return e}),lt:pe(function(e,t,n){for(var r=n<0?n+t:t<n?t:n;0<=--r;)e.push(r);return e}),gt:pe(function(e,t,n){for(var r=n<0?n+t:n;++r<t;)e.push(r);return e})}}).pseudos.nth=b.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})b.pseudos[e]=function(t){return function(e){return"input"===e.nodeName.toLowerCase()&&e.type===t}}(e);for(e in{submit:!0,reset:!0})b.pseudos[e]=function(n){return function(e){var t=e.nodeName.toLowerCase();return("input"===t||"button"===t)&&e.type===n}}(e);function me(){}function ye(e){for(var t=0,n=e.length,r="";t<n;t++)r+=e[t].value;return r}function ve(a,e,t){var s=e.dir,u=e.next,l=u||s,c=t&&"parentNode"===l,d=_++;return e.first?function(e,t,n){for(;e=e[s];)if(1===e.nodeType||c)return a(e,t,n);return!1}:function(e,t,n){var r,i,o=[E,d];if(n){for(;e=e[s];)if((1===e.nodeType||c)&&a(e,t,n))return!0}else for(;e=e[s];)if(1===e.nodeType||c)if(r=(i=e[k]||(e[k]={}))[e.uniqueID]||(i[e.uniqueID]={}),u&&u===e.nodeName.toLowerCase())e=e[s]||e;else{if((i=r[l])&&i[0]===E&&i[1]===d)return o[2]=i[2];if((r[l]=o)[2]=a(e,t,n))return!0}return!1}}function _e(i){return 1<i.length?function(e,t,n){for(var r=i.length;r--;)if(!i[r](e,t,n))return!1;return!0}:i[0]}function be(e,t,n,r,i){for(var o,a=[],s=0,u=e.length,l=null!=t;s<u;s++)(o=e[s])&&(n&&!n(o,r,i)||(a.push(o),l&&t.push(s)));return a}function we(e){for(var r,t,n,i=e.length,o=b.relative[e[0].type],a=o||b.relative[" "],s=o?1:0,u=ve(function(e){return e===r},a,!0),l=ve(function(e){return-1<M(r,e)},a,!0),c=[function(e,t,n){n=!o&&(n||t!==w)||((r=t).nodeType?u:l)(e,t,n);return r=null,n}];s<i;s++)if(t=b.relative[e[s].type])c=[ve(_e(c),t)];else{if((t=b.filter[e[s].type].apply(null,e[s].matches))[k]){for(n=++s;n<i&&!b.relative[e[n].type];n++);return function e(h,p,g,m,y,t){return m&&!m[k]&&(m=e(m)),y&&!y[k]&&(y=e(y,t)),le(function(e,t,n,r){var i,o,a,s=[],u=[],l=t.length,c=e||function(e,t,n){for(var r=0,i=t.length;r<i;r++)se(e,t[r],n);return n}(p||"*",n.nodeType?[n]:n,[]),d=!h||!e&&p?c:be(c,s,h,n,r),f=g?y||(e?h:l||m)?[]:t:d;if(g&&g(d,f,n,r),m)for(i=be(f,u),m(i,[],n,r),o=i.length;o--;)(a=i[o])&&(f[u[o]]=!(d[u[o]]=a));if(e){if(y||h){if(y){for(i=[],o=f.length;o--;)(a=f[o])&&i.push(d[o]=a);y(null,f=[],i,r)}for(o=f.length;o--;)(a=f[o])&&-1<(i=y?M(e,a):s[o])&&(e[i]=!(t[i]=a))}}else f=be(f===t?f.splice(l,f.length):f),y?y(null,t,f,r):F.apply(t,f)})}(1<s&&_e(c),1<s&&ye(e.slice(0,s-1).concat({value:" "===e[s-2].type?"*":""})).replace(z,"$1"),t,s<n&&we(e.slice(s,n)),n<i&&we(e=e.slice(n)),n<i&&ye(e))}c.push(t)}return _e(c)}return me.prototype=b.filters=b.pseudos,b.setFilters=new me,p=se.tokenize=function(e,t){var n,r,i,o,a,s,u,l=A[e+" "];if(l)return t?0:l.slice(0);for(a=e,s=[],u=b.preFilter;a;){for(o in n&&!(r=W.exec(a))||(r&&(a=a.slice(r[0].length)||a),s.push(i=[])),n=!1,(r=V.exec(a))&&(n=r.shift(),i.push({value:n,type:r[0].replace(z," ")}),a=a.slice(n.length)),b.filter)!(r=Q[o].exec(a))||u[o]&&!(r=u[o](r))||(n=r.shift(),i.push({value:n,type:o,matches:r}),a=a.slice(n.length));if(!n)break}return t?a.length:a?se.error(e):A(e,s).slice(0)},f=se.compile=function(e,t){var n,m,y,v,_,r,i=[],o=[],a=D[e+" "];if(!a){for(n=(t=t||p(e)).length;n--;)((a=we(t[n]))[k]?i:o).push(a);(a=D(e,(m=o,v=0<(y=i).length,_=0<m.length,r=function(e,t,n,r,i){var o,a,s,u=0,l="0",c=e&&[],d=[],f=w,h=e||_&&b.find.TAG("*",i),p=E+=null==f?1:Math.random()||.1,g=h.length;for(i&&(w=t==S||t||i);l!==g&&null!=(o=h[l]);l++){if(_&&o){for(a=0,t||o.ownerDocument==S||(x(o),n=!T);s=m[a++];)if(s(o,t||S,n)){r.push(o);break}i&&(E=p)}v&&((o=!s&&o)&&u--,e&&c.push(o))}if(u+=l,v&&l!==u){for(a=0;s=y[a++];)s(c,d,t,n);if(e){if(0<u)for(;l--;)c[l]||d[l]||(d[l]=P.call(r));d=be(d)}F.apply(r,d),i&&!e&&0<d.length&&1<u+y.length&&se.uniqueSort(r)}return i&&(E=p,w=f),c},v?le(r):r))).selector=e}return a},g=se.select=function(e,t,n,r){var i,o,a,s,u,l="function"==typeof e&&e,c=!r&&p(e=l.selector||e);if(n=n||[],1===c.length){if(2<(o=c[0]=c[0].slice(0)).length&&"ID"===(a=o[0]).type&&9===t.nodeType&&T&&b.relative[o[1].type]){if(!(t=(b.find.ID(a.matches[0].replace(re,d),t)||[])[0]))return n;l&&(t=t.parentNode),e=e.slice(o.shift().value.length)}for(i=Q.needsContext.test(e)?0:o.length;i--&&(a=o[i],!b.relative[s=a.type]);)if((u=b.find[s])&&(r=u(a.matches[0].replace(re,d),ne.test(o[0].type)&&ge(t.parentNode)||t))){if(o.splice(i,1),!(e=r.length&&ye(o)))return F.apply(n,r),n;break}}return(l||f(e,c))(r,t,!T,n,!t||ne.test(e)&&ge(t.parentNode)||t),n},h.sortStable=k.split("").sort(R).join("")===k,h.detectDuplicates=!!l,x(),h.sortDetached=ce(function(e){return 1&e.compareDocumentPosition(S.createElement("fieldset"))}),ce(function(e){return e.innerHTML="<a href='#'></a>","#"===e.firstChild.getAttribute("href")})||de("type|href|height|width",function(e,t,n){if(!n)return e.getAttribute(t,"type"===t.toLowerCase()?1:2)}),h.attributes&&ce(function(e){return e.innerHTML="<input/>",e.firstChild.setAttribute("value",""),""===e.firstChild.getAttribute("value")})||de("value",function(e,t,n){if(!n&&"input"===e.nodeName.toLowerCase())return e.defaultValue}),ce(function(e){return null==e.getAttribute("disabled")})||de(N,function(e,t,n){if(!n)return!0===e[t]?t.toLowerCase():(t=e.getAttributeNode(t))&&t.specified?t.value:null}),se}(S);k.find=f,k.expr=f.selectors,k.expr[":"]=k.expr.pseudos,k.uniqueSort=k.unique=f.uniqueSort,k.text=f.getText,k.isXMLDoc=f.isXML,k.contains=f.contains,k.escapeSelector=f.escape;function h(e,t,n){for(var r=[],i=void 0!==n;(e=e[t])&&9!==e.nodeType;)if(1===e.nodeType){if(i&&k(e).is(n))break;r.push(e)}return r}function w(e,t){for(var n=[];e;e=e.nextSibling)1===e.nodeType&&e!==t&&n.push(e);return n}var x=k.expr.match.needsContext;function E(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()}var C=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;function A(e,n,r){return _(n)?k.grep(e,function(e,t){return!!n.call(e,t,e)!==r}):n.nodeType?k.grep(e,function(e){return e===n!==r}):"string"!=typeof n?k.grep(e,function(e){return-1<i.call(n,e)!==r}):k.filter(n,e,r)}k.filter=function(e,t,n){var r=t[0];return n&&(e=":not("+e+")"),1===t.length&&1===r.nodeType?k.find.matchesSelector(r,e)?[r]:[]:k.find.matches(e,k.grep(t,function(e){return 1===e.nodeType}))},k.fn.extend({find:function(e){var t,n,r=this.length,i=this;if("string"!=typeof e)return this.pushStack(k(e).filter(function(){for(t=0;t<r;t++)if(k.contains(i[t],this))return!0}));for(n=this.pushStack([]),t=0;t<r;t++)k.find(e,i[t],n);return 1<r?k.uniqueSort(n):n},filter:function(e){return this.pushStack(A(this,e||[],!1))},not:function(e){return this.pushStack(A(this,e||[],!0))},is:function(e){return!!A(this,"string"==typeof e&&x.test(e)?k(e):e||[],!1).length}});var D,I=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/;(k.fn.init=function(e,t,n){if(!e)return this;if(n=n||D,"string"!=typeof e)return e.nodeType?(this[0]=e,this.length=1,this):_(e)?void 0!==n.ready?n.ready(e):e(k):k.makeArray(e,this);if(!(r="<"===e[0]&&">"===e[e.length-1]&&3<=e.length?[null,e,null]:I.exec(e))||!r[1]&&t)return(!t||t.jquery?t||n:this.constructor(t)).find(e);if(r[1]){if(t=t instanceof k?t[0]:t,k.merge(this,k.parseHTML(r[1],t&&t.nodeType?t.ownerDocument||t:T,!0)),C.test(r[1])&&k.isPlainObject(t))for(var r in t)_(this[r])?this[r](t[r]):this.attr(r,t[r]);return this}return(e=T.getElementById(r[2]))&&(this[0]=e,this.length=1),this}).prototype=k.fn,D=k(T);var R=/^(?:parents|prev(?:Until|All))/,O={children:!0,contents:!0,next:!0,prev:!0};function P(e,t){for(;(e=e[t])&&1!==e.nodeType;);return e}k.fn.extend({has:function(e){var t=k(e,this),n=t.length;return this.filter(function(){for(var e=0;e<n;e++)if(k.contains(this,t[e]))return!0})},closest:function(e,t){var n,r=0,i=this.length,o=[],a="string"!=typeof e&&k(e);if(!x.test(e))for(;r<i;r++)for(n=this[r];n&&n!==t;n=n.parentNode)if(n.nodeType<11&&(a?-1<a.index(n):1===n.nodeType&&k.find.matchesSelector(n,e))){o.push(n);break}return this.pushStack(1<o.length?k.uniqueSort(o):o)},index:function(e){return e?"string"==typeof e?i.call(k(e),this[0]):i.call(this,e.jquery?e[0]:e):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){return this.pushStack(k.uniqueSort(k.merge(this.get(),k(e,t))))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}}),k.each({parent:function(e){e=e.parentNode;return e&&11!==e.nodeType?e:null},parents:function(e){return h(e,"parentNode")},parentsUntil:function(e,t,n){return h(e,"parentNode",n)},next:function(e){return P(e,"nextSibling")},prev:function(e){return P(e,"previousSibling")},nextAll:function(e){return h(e,"nextSibling")},prevAll:function(e){return h(e,"previousSibling")},nextUntil:function(e,t,n){return h(e,"nextSibling",n)},prevUntil:function(e,t,n){return h(e,"previousSibling",n)},siblings:function(e){return w((e.parentNode||{}).firstChild,e)},children:function(e){return w(e.firstChild)},contents:function(e){return null!=e.contentDocument&&n(e.contentDocument)?e.contentDocument:(E(e,"template")&&(e=e.content||e),k.merge([],e.childNodes))}},function(r,i){k.fn[r]=function(e,t){var n=k.map(this,i,e);return"Until"!==r.slice(-5)&&(t=e),t&&"string"==typeof t&&(n=k.filter(t,n)),1<this.length&&(O[r]||k.uniqueSort(n),R.test(r)&&n.reverse()),this.pushStack(n)}});var L=/[^\x20\t\r\n\f]+/g;function F(e){return e}function U(e){throw e}function M(e,t,n,r){var i;try{e&&_(i=e.promise)?i.call(e).done(t).fail(n):e&&_(i=e.then)?i.call(e,t,n):t.apply(void 0,[e].slice(r))}catch(e){n.apply(void 0,[e])}}k.Callbacks=function(r){var n;r="string"==typeof r?(n={},k.each(r.match(L)||[],function(e,t){n[t]=!0}),n):k.extend({},r);function i(){for(a=a||r.once,t=o=!0;u.length;l=-1)for(e=u.shift();++l<s.length;)!1===s[l].apply(e[0],e[1])&&r.stopOnFalse&&(l=s.length,e=!1);r.memory||(e=!1),o=!1,a&&(s=e?[]:"")}var o,e,t,a,s=[],u=[],l=-1,c={add:function(){return s&&(e&&!o&&(l=s.length-1,u.push(e)),function n(e){k.each(e,function(e,t){_(t)?r.unique&&c.has(t)||s.push(t):t&&t.length&&"string"!==p(t)&&n(t)})}(arguments),e&&!o&&i()),this},remove:function(){return k.each(arguments,function(e,t){for(var n;-1<(n=k.inArray(t,s,n));)s.splice(n,1),n<=l&&l--}),this},has:function(e){return e?-1<k.inArray(e,s):0<s.length},empty:function(){return s=s&&[],this},disable:function(){return a=u=[],s=e="",this},disabled:function(){return!s},lock:function(){return a=u=[],e||o||(s=e=""),this},locked:function(){return!!a},fireWith:function(e,t){return a||(t=[e,(t=t||[]).slice?t.slice():t],u.push(t),o||i()),this},fire:function(){return c.fireWith(this,arguments),this},fired:function(){return!!t}};return c},k.extend({Deferred:function(e){var o=[["notify","progress",k.Callbacks("memory"),k.Callbacks("memory"),2],["resolve","done",k.Callbacks("once memory"),k.Callbacks("once memory"),0,"resolved"],["reject","fail",k.Callbacks("once memory"),k.Callbacks("once memory"),1,"rejected"]],i="pending",a={state:function(){return i},always:function(){return s.done(arguments).fail(arguments),this},catch:function(e){return a.then(null,e)},pipe:function(){var i=arguments;return k.Deferred(function(r){k.each(o,function(e,t){var n=_(i[t[4]])&&i[t[4]];s[t[1]](function(){var e=n&&n.apply(this,arguments);e&&_(e.promise)?e.promise().progress(r.notify).done(r.resolve).fail(r.reject):r[t[0]+"With"](this,n?[e]:arguments)})}),i=null}).promise()},then:function(t,n,r){var u=0;function l(i,o,a,s){return function(){function e(){var e,t;if(!(i<u)){if((e=a.apply(n,r))===o.promise())throw new TypeError("Thenable self-resolution");t=e&&("object"==_typeof(e)||"function"==typeof e)&&e.then,_(t)?s?t.call(e,l(u,o,F,s),l(u,o,U,s)):(u++,t.call(e,l(u,o,F,s),l(u,o,U,s),l(u,o,F,o.notifyWith))):(a!==F&&(n=void 0,r=[e]),(s||o.resolveWith)(n,r))}}var n=this,r=arguments,t=s?e:function(){try{e()}catch(e){k.Deferred.exceptionHook&&k.Deferred.exceptionHook(e,t.stackTrace),u<=i+1&&(a!==U&&(n=void 0,r=[e]),o.rejectWith(n,r))}};i?t():(k.Deferred.getStackHook&&(t.stackTrace=k.Deferred.getStackHook()),S.setTimeout(t))}}return k.Deferred(function(e){o[0][3].add(l(0,e,_(r)?r:F,e.notifyWith)),o[1][3].add(l(0,e,_(t)?t:F)),o[2][3].add(l(0,e,_(n)?n:U))}).promise()},promise:function(e){return null!=e?k.extend(e,a):a}},s={};return k.each(o,function(e,t){var n=t[2],r=t[5];a[t[1]]=n.add,r&&n.add(function(){i=r},o[3-e][2].disable,o[3-e][3].disable,o[0][2].lock,o[0][3].lock),n.add(t[3].fire),s[t[0]]=function(){return s[t[0]+"With"](this===s?void 0:this,arguments),this},s[t[0]+"With"]=n.fireWith}),a.promise(s),e&&e.call(s,s),s},when:function(e){function t(t){return function(e){i[t]=this,o[t]=1<arguments.length?s.call(arguments):e,--n||a.resolveWith(i,o)}}var n=arguments.length,r=n,i=Array(r),o=s.call(arguments),a=k.Deferred();if(n<=1&&(M(e,a.done(t(r)).resolve,a.reject,!n),"pending"===a.state()||_(o[r]&&o[r].then)))return a.then();for(;r--;)M(o[r],t(r),a.reject);return a.promise()}});var N=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;k.Deferred.exceptionHook=function(e,t){S.console&&S.console.warn&&e&&N.test(e.name)&&S.console.warn("jQuery.Deferred exception: "+e.message,e.stack,t)},k.readyException=function(e){S.setTimeout(function(){throw e})};var B=k.Deferred();function j(){T.removeEventListener("DOMContentLoaded",j),S.removeEventListener("load",j),k.ready()}k.fn.ready=function(e){return B.then(e).catch(function(e){k.readyException(e)}),this},k.extend({isReady:!1,readyWait:1,ready:function(e){(!0===e?--k.readyWait:k.isReady)||(k.isReady=!0)!==e&&0<--k.readyWait||B.resolveWith(T,[k])}}),k.ready.then=B.then,"complete"===T.readyState||"loading"!==T.readyState&&!T.documentElement.doScroll?S.setTimeout(k.ready):(T.addEventListener("DOMContentLoaded",j),S.addEventListener("load",j));function q(e,t,n,r,i,o,a){var s=0,u=e.length,l=null==n;if("object"===p(n))for(s in i=!0,n)q(e,t,s,n[s],!0,o,a);else if(void 0!==r&&(i=!0,_(r)||(a=!0),l&&(t=a?(t.call(e,r),null):(l=t,function(e,t,n){return l.call(k(e),n)})),t))for(;s<u;s++)t(e[s],n,a?r:r.call(e[s],s,t(e[s],n)));return i?e:l?t.call(e):u?t(e[0],n):o}var G=/^-ms-/,H=/-([a-z])/g;function z(e,t){return t.toUpperCase()}function W(e){return e.replace(G,"ms-").replace(H,z)}function V(e){return 1===e.nodeType||9===e.nodeType||!+e.nodeType}function $(){this.expando=k.expando+$.uid++}$.uid=1,$.prototype={cache:function(e){var t=e[this.expando];return t||(t={},V(e)&&(e.nodeType?e[this.expando]=t:Object.defineProperty(e,this.expando,{value:t,configurable:!0}))),t},set:function(e,t,n){var r,i=this.cache(e);if("string"==typeof t)i[W(t)]=n;else for(r in t)i[W(r)]=t[r];return i},get:function(e,t){return void 0===t?this.cache(e):e[this.expando]&&e[this.expando][W(t)]},access:function(e,t,n){return void 0===t||t&&"string"==typeof t&&void 0===n?this.get(e,t):(this.set(e,t,n),void 0!==n?n:t)},remove:function(e,t){var n,r=e[this.expando];if(void 0!==r){if(void 0!==t){n=(t=Array.isArray(t)?t.map(W):(t=W(t))in r?[t]:t.match(L)||[]).length;for(;n--;)delete r[t[n]]}void 0!==t&&!k.isEmptyObject(r)||(e.nodeType?e[this.expando]=void 0:delete e[this.expando])}},hasData:function(e){e=e[this.expando];return void 0!==e&&!k.isEmptyObject(e)}};var Y=new $,X=new $,Q=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,K=/[A-Z]/g;function J(e,t,n){var r,i;if(void 0===n&&1===e.nodeType)if(r="data-"+t.replace(K,"-$&").toLowerCase(),"string"==typeof(n=e.getAttribute(r))){try{n="true"===(i=n)||"false"!==i&&("null"===i?null:i===+i+""?+i:Q.test(i)?JSON.parse(i):i)}catch(e){}X.set(e,t,n)}else n=void 0;return n}k.extend({hasData:function(e){return X.hasData(e)||Y.hasData(e)},data:function(e,t,n){return X.access(e,t,n)},removeData:function(e,t){X.remove(e,t)},_data:function(e,t,n){return Y.access(e,t,n)},_removeData:function(e,t){Y.remove(e,t)}}),k.fn.extend({data:function(n,e){var t,r,i,o=this[0],a=o&&o.attributes;if(void 0!==n)return"object"==_typeof(n)?this.each(function(){X.set(this,n)}):q(this,function(e){var t;return o&&void 0===e?void 0!==(t=X.get(o,n))||void 0!==(t=J(o,n))?t:void 0:void this.each(function(){X.set(this,n,e)})},null,e,1<arguments.length,null,!0);if(this.length&&(i=X.get(o),1===o.nodeType&&!Y.get(o,"hasDataAttrs"))){for(t=a.length;t--;)a[t]&&0===(r=a[t].name).indexOf("data-")&&(r=W(r.slice(5)),J(o,r,i[r]));Y.set(o,"hasDataAttrs",!0)}return i},removeData:function(e){return this.each(function(){X.remove(this,e)})}}),k.extend({queue:function(e,t,n){var r;if(e)return t=(t||"fx")+"queue",r=Y.get(e,t),n&&(!r||Array.isArray(n)?r=Y.access(e,t,k.makeArray(n)):r.push(n)),r||[]},dequeue:function(e,t){t=t||"fx";var n=k.queue(e,t),r=n.length,i=n.shift(),o=k._queueHooks(e,t);"inprogress"===i&&(i=n.shift(),r--),i&&("fx"===t&&n.unshift("inprogress"),delete o.stop,i.call(e,function(){k.dequeue(e,t)},o)),!r&&o&&o.empty.fire()},_queueHooks:function(e,t){var n=t+"queueHooks";return Y.get(e,n)||Y.access(e,n,{empty:k.Callbacks("once memory").add(function(){Y.remove(e,[t+"queue",n])})})}}),k.fn.extend({queue:function(t,n){var e=2;return"string"!=typeof t&&(n=t,t="fx",e--),arguments.length<e?k.queue(this[0],t):void 0===n?this:this.each(function(){var e=k.queue(this,t,n);k._queueHooks(this,t),"fx"===t&&"inprogress"!==e[0]&&k.dequeue(this,t)})},dequeue:function(e){return this.each(function(){k.dequeue(this,e)})},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){function n(){--i||o.resolveWith(a,[a])}var r,i=1,o=k.Deferred(),a=this,s=this.length;for("string"!=typeof e&&(t=e,e=void 0),e=e||"fx";s--;)(r=Y.get(a[s],e+"queueHooks"))&&r.empty&&(i++,r.empty.add(n));return n(),o.promise(t)}});var Z=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,ee=new RegExp("^(?:([+-])=|)("+Z+")([a-z%]*)$","i"),te=["Top","Right","Bottom","Left"],ne=T.documentElement,re=function(e){return k.contains(e.ownerDocument,e)},ie={composed:!0};ne.getRootNode&&(re=function(e){return k.contains(e.ownerDocument,e)||e.getRootNode(ie)===e.ownerDocument});function oe(e,t){return"none"===(e=t||e).style.display||""===e.style.display&&re(e)&&"none"===k.css(e,"display")}function ae(e,t,n,r){var i,o,a=20,s=r?function(){return r.cur()}:function(){return k.css(e,t,"")},u=s(),l=n&&n[3]||(k.cssNumber[t]?"":"px"),c=e.nodeType&&(k.cssNumber[t]||"px"!==l&&+u)&&ee.exec(k.css(e,t));if(c&&c[3]!==l){for(u/=2,l=l||c[3],c=+u||1;a--;)k.style(e,t,c+l),(1-o)*(1-(o=s()/u||.5))<=0&&(a=0),c/=o;c*=2,k.style(e,t,c+l),n=n||[]}return n&&(c=+c||+u||0,i=n[1]?c+(n[1]+1)*n[2]:+n[2],r&&(r.unit=l,r.start=c,r.end=i)),i}var se={};function ue(e,t){for(var n,r,i,o,a,s,u=[],l=0,c=e.length;l<c;l++)(r=e[l]).style&&(n=r.style.display,t?("none"===n&&(u[l]=Y.get(r,"display")||null,u[l]||(r.style.display="")),""===r.style.display&&oe(r)&&(u[l]=(s=o=i=void 0,o=r.ownerDocument,a=r.nodeName,(s=se[a])||(i=o.body.appendChild(o.createElement(a)),s=k.css(i,"display"),i.parentNode.removeChild(i),"none"===s&&(s="block"),se[a]=s)))):"none"!==n&&(u[l]="none",Y.set(r,"display",n)));for(l=0;l<c;l++)null!=u[l]&&(e[l].style.display=u[l]);return e}k.fn.extend({show:function(){return ue(this,!0)},hide:function(){return ue(this)},toggle:function(e){return"boolean"==typeof e?e?this.show():this.hide():this.each(function(){oe(this)?k(this).show():k(this).hide()})}});var le=/^(?:checkbox|radio)$/i,ce=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,de=/^$|^module$|\/(?:java|ecma)script/i,fe=T.createDocumentFragment().appendChild(T.createElement("div"));(f=T.createElement("input")).setAttribute("type","radio"),f.setAttribute("checked","checked"),f.setAttribute("name","t"),fe.appendChild(f),v.checkClone=fe.cloneNode(!0).cloneNode(!0).lastChild.checked,fe.innerHTML="<textarea>x</textarea>",v.noCloneChecked=!!fe.cloneNode(!0).lastChild.defaultValue,fe.innerHTML="<option></option>",v.option=!!fe.lastChild;var he={thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};function pe(e,t){var n=void 0!==e.getElementsByTagName?e.getElementsByTagName(t||"*"):void 0!==e.querySelectorAll?e.querySelectorAll(t||"*"):[];return void 0===t||t&&E(e,t)?k.merge([e],n):n}function ge(e,t){for(var n=0,r=e.length;n<r;n++)Y.set(e[n],"globalEval",!t||Y.get(t[n],"globalEval"))}he.tbody=he.tfoot=he.colgroup=he.caption=he.thead,he.th=he.td,v.option||(he.optgroup=he.option=[1,"<select multiple='multiple'>","</select>"]);var me=/<|&#?\w+;/;function ye(e,t,n,r,i){for(var o,a,s,u,l,c=t.createDocumentFragment(),d=[],f=0,h=e.length;f<h;f++)if((o=e[f])||0===o)if("object"===p(o))k.merge(d,o.nodeType?[o]:o);else if(me.test(o)){for(a=a||c.appendChild(t.createElement("div")),s=(ce.exec(o)||["",""])[1].toLowerCase(),s=he[s]||he._default,a.innerHTML=s[1]+k.htmlPrefilter(o)+s[2],l=s[0];l--;)a=a.lastChild;k.merge(d,a.childNodes),(a=c.firstChild).textContent=""}else d.push(t.createTextNode(o));for(c.textContent="",f=0;o=d[f++];)if(r&&-1<k.inArray(o,r))i&&i.push(o);else if(u=re(o),a=pe(c.appendChild(o),"script"),u&&ge(a),n)for(l=0;o=a[l++];)de.test(o.type||"")&&n.push(o);return c}var ve=/^key/,_e=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,be=/^([^.]*)(?:\.(.+)|)/;function we(){return!0}function xe(){return!1}function Se(e,t){return e===function(){try{return T.activeElement}catch(e){}}()==("focus"===t)}function Te(e,t,n,r,i,o){var a,s;if("object"==_typeof(t)){for(s in"string"!=typeof n&&(r=r||n,n=void 0),t)Te(e,s,n,r,t[s],o);return e}if(null==r&&null==i?(i=n,r=n=void 0):null==i&&("string"==typeof n?(i=r,r=void 0):(i=r,r=n,n=void 0)),!1===i)i=xe;else if(!i)return e;return 1===o&&(a=i,(i=function(e){return k().off(e),a.apply(this,arguments)}).guid=a.guid||(a.guid=k.guid++)),e.each(function(){k.event.add(this,t,i,r,n)})}function ke(e,i,o){o?(Y.set(e,i,!1),k.event.add(e,i,{namespace:!1,handler:function(e){var t,n,r=Y.get(this,i);if(1&e.isTrigger&&this[i]){if(r.length)(k.event.special[i]||{}).delegateType&&e.stopPropagation();else if(r=s.call(arguments),Y.set(this,i,r),t=o(this,i),this[i](),r!==(n=Y.get(this,i))||t?Y.set(this,i,!1):n={},r!==n)return e.stopImmediatePropagation(),e.preventDefault(),n.value}else r.length&&(Y.set(this,i,{value:k.event.trigger(k.extend(r[0],k.Event.prototype),r.slice(1),this)}),e.stopImmediatePropagation())}})):void 0===Y.get(e,i)&&k.event.add(e,i,we)}k.event={global:{},add:function(t,e,n,r,i){var o,a,s,u,l,c,d,f,h,p=Y.get(t);if(V(t))for(n.handler&&(n=(o=n).handler,i=o.selector),i&&k.find.matchesSelector(ne,i),n.guid||(n.guid=k.guid++),(s=p.events)||(s=p.events=Object.create(null)),(a=p.handle)||(a=p.handle=function(e){return void 0!==k&&k.event.triggered!==e.type?k.event.dispatch.apply(t,arguments):void 0}),u=(e=(e||"").match(L)||[""]).length;u--;)d=h=(l=be.exec(e[u])||[])[1],f=(l[2]||"").split(".").sort(),d&&(c=k.event.special[d]||{},d=(i?c.delegateType:c.bindType)||d,c=k.event.special[d]||{},l=k.extend({type:d,origType:h,data:r,handler:n,guid:n.guid,selector:i,needsContext:i&&k.expr.match.needsContext.test(i),namespace:f.join(".")},o),(h=s[d])||((h=s[d]=[]).delegateCount=0,c.setup&&!1!==c.setup.call(t,r,f,a)||t.addEventListener&&t.addEventListener(d,a)),c.add&&(c.add.call(t,l),l.handler.guid||(l.handler.guid=n.guid)),i?h.splice(h.delegateCount++,0,l):h.push(l),k.event.global[d]=!0)},remove:function(e,t,n,r,i){var o,a,s,u,l,c,d,f,h,p,g,m=Y.hasData(e)&&Y.get(e);if(m&&(u=m.events)){for(l=(t=(t||"").match(L)||[""]).length;l--;)if(h=g=(s=be.exec(t[l])||[])[1],p=(s[2]||"").split(".").sort(),h){for(d=k.event.special[h]||{},f=u[h=(r?d.delegateType:d.bindType)||h]||[],s=s[2]&&new RegExp("(^|\\.)"+p.join("\\.(?:.*\\.|)")+"(\\.|$)"),a=o=f.length;o--;)c=f[o],!i&&g!==c.origType||n&&n.guid!==c.guid||s&&!s.test(c.namespace)||r&&r!==c.selector&&("**"!==r||!c.selector)||(f.splice(o,1),c.selector&&f.delegateCount--,d.remove&&d.remove.call(e,c));a&&!f.length&&(d.teardown&&!1!==d.teardown.call(e,p,m.handle)||k.removeEvent(e,h,m.handle),delete u[h])}else for(h in u)k.event.remove(e,h+t[l],n,r,!0);k.isEmptyObject(u)&&Y.remove(e,"handle events")}},dispatch:function(e){var t,n,r,i,o,a=new Array(arguments.length),s=k.event.fix(e),u=(Y.get(this,"events")||Object.create(null))[s.type]||[],e=k.event.special[s.type]||{};for(a[0]=s,t=1;t<arguments.length;t++)a[t]=arguments[t];if(s.delegateTarget=this,!e.preDispatch||!1!==e.preDispatch.call(this,s)){for(o=k.event.handlers.call(this,s,u),t=0;(r=o[t++])&&!s.isPropagationStopped();)for(s.currentTarget=r.elem,n=0;(i=r.handlers[n++])&&!s.isImmediatePropagationStopped();)s.rnamespace&&!1!==i.namespace&&!s.rnamespace.test(i.namespace)||(s.handleObj=i,s.data=i.data,void 0!==(i=((k.event.special[i.origType]||{}).handle||i.handler).apply(r.elem,a))&&!1===(s.result=i)&&(s.preventDefault(),s.stopPropagation()));return e.postDispatch&&e.postDispatch.call(this,s),s.result}},handlers:function(e,t){var n,r,i,o,a,s=[],u=t.delegateCount,l=e.target;if(u&&l.nodeType&&!("click"===e.type&&1<=e.button))for(;l!==this;l=l.parentNode||this)if(1===l.nodeType&&("click"!==e.type||!0!==l.disabled)){for(o=[],a={},n=0;n<u;n++)void 0===a[i=(r=t[n]).selector+" "]&&(a[i]=r.needsContext?-1<k(i,this).index(l):k.find(i,this,null,[l]).length),a[i]&&o.push(r);o.length&&s.push({elem:l,handlers:o})}return l=this,u<t.length&&s.push({elem:l,handlers:t.slice(u)}),s},addProp:function(t,e){Object.defineProperty(k.Event.prototype,t,{enumerable:!0,configurable:!0,get:_(e)?function(){if(this.originalEvent)return e(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[t]},set:function(e){Object.defineProperty(this,t,{enumerable:!0,configurable:!0,writable:!0,value:e})}})},fix:function(e){return e[k.expando]?e:new k.Event(e)},special:{load:{noBubble:!0},click:{setup:function(e){e=this||e;return le.test(e.type)&&e.click&&E(e,"input")&&ke(e,"click",we),!1},trigger:function(e){e=this||e;return le.test(e.type)&&e.click&&E(e,"input")&&ke(e,"click"),!0},_default:function(e){e=e.target;return le.test(e.type)&&e.click&&E(e,"input")&&Y.get(e,"click")||E(e,"a")}},beforeunload:{postDispatch:function(e){void 0!==e.result&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}}},k.removeEvent=function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n)},k.Event=function(e,t){if(!(this instanceof k.Event))return new k.Event(e,t);e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||void 0===e.defaultPrevented&&!1===e.returnValue?we:xe,this.target=e.target&&3===e.target.nodeType?e.target.parentNode:e.target,this.currentTarget=e.currentTarget,this.relatedTarget=e.relatedTarget):this.type=e,t&&k.extend(this,t),this.timeStamp=e&&e.timeStamp||Date.now(),this[k.expando]=!0},k.Event.prototype={constructor:k.Event,isDefaultPrevented:xe,isPropagationStopped:xe,isImmediatePropagationStopped:xe,isSimulated:!1,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=we,e&&!this.isSimulated&&e.preventDefault()},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=we,e&&!this.isSimulated&&e.stopPropagation()},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=we,e&&!this.isSimulated&&e.stopImmediatePropagation(),this.stopPropagation()}},k.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,char:!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:function(e){var t=e.button;return null==e.which&&ve.test(e.type)?null!=e.charCode?e.charCode:e.keyCode:!e.which&&void 0!==t&&_e.test(e.type)?1&t?1:2&t?3:4&t?2:0:e.which}},k.event.addProp),k.each({focus:"focusin",blur:"focusout"},function(e,t){k.event.special[e]={setup:function(){return ke(this,e,Se),!1},trigger:function(){return ke(this,e),!0},delegateType:t}}),k.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(e,i){k.event.special[e]={delegateType:i,bindType:i,handle:function(e){var t,n=e.relatedTarget,r=e.handleObj;return n&&(n===this||k.contains(this,n))||(e.type=r.origType,t=r.handler.apply(this,arguments),e.type=i),t}}}),k.fn.extend({on:function(e,t,n,r){return Te(this,e,t,n,r)},one:function(e,t,n,r){return Te(this,e,t,n,r,1)},off:function(e,t,n){var r,i;if(e&&e.preventDefault&&e.handleObj)return r=e.handleObj,k(e.delegateTarget).off(r.namespace?r.origType+"."+r.namespace:r.origType,r.selector,r.handler),this;if("object"!=_typeof(e))return!1!==t&&"function"!=typeof t||(n=t,t=void 0),!1===n&&(n=xe),this.each(function(){k.event.remove(this,e,n,t)});for(i in e)this.off(i,t,e[i]);return this}});var Ee=/<script|<style|<link/i,Ce=/checked\s*(?:[^=]|=\s*.checked.)/i,Ae=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g;function De(e,t){return E(e,"table")&&E(11!==t.nodeType?t:t.firstChild,"tr")&&k(e).children("tbody")[0]||e}function Ie(e){return e.type=(null!==e.getAttribute("type"))+"/"+e.type,e}function Re(e){return"true/"===(e.type||"").slice(0,5)?e.type=e.type.slice(5):e.removeAttribute("type"),e}function Oe(e,t){var n,r,i,o;if(1===t.nodeType){if(Y.hasData(e)&&(o=Y.get(e).events))for(i in Y.remove(t,"handle events"),o)for(n=0,r=o[i].length;n<r;n++)k.event.add(t,i,o[i][n]);X.hasData(e)&&(e=X.access(e),e=k.extend({},e),X.set(t,e))}}function Pe(n,r,i,o){r=m(r);var e,t,a,s,u,l,c=0,d=n.length,f=d-1,h=r[0],p=_(h);if(p||1<d&&"string"==typeof h&&!v.checkClone&&Ce.test(h))return n.each(function(e){var t=n.eq(e);p&&(r[0]=h.call(this,e,t.html())),Pe(t,r,i,o)});if(d&&(t=(e=ye(r,n[0].ownerDocument,!1,n,o)).firstChild,1===e.childNodes.length&&(e=t),t||o)){for(s=(a=k.map(pe(e,"script"),Ie)).length;c<d;c++)u=e,c!==f&&(u=k.clone(u,!0,!0),s&&k.merge(a,pe(u,"script"))),i.call(n[c],u,c);if(s)for(l=a[a.length-1].ownerDocument,k.map(a,Re),c=0;c<s;c++)u=a[c],de.test(u.type||"")&&!Y.access(u,"globalEval")&&k.contains(l,u)&&(u.src&&"module"!==(u.type||"").toLowerCase()?k._evalUrl&&!u.noModule&&k._evalUrl(u.src,{nonce:u.nonce||u.getAttribute("nonce")},l):b(u.textContent.replace(Ae,""),u,l))}return n}function Le(e,t,n){for(var r,i=t?k.filter(t,e):e,o=0;null!=(r=i[o]);o++)n||1!==r.nodeType||k.cleanData(pe(r)),r.parentNode&&(n&&re(r)&&ge(pe(r,"script")),r.parentNode.removeChild(r));return e}k.extend({htmlPrefilter:function(e){return e},clone:function(e,t,n){var r,i,o,a,s,u,l,c=e.cloneNode(!0),d=re(e);if(!(v.noCloneChecked||1!==e.nodeType&&11!==e.nodeType||k.isXMLDoc(e)))for(a=pe(c),r=0,i=(o=pe(e)).length;r<i;r++)s=o[r],"input"===(l=(u=a[r]).nodeName.toLowerCase())&&le.test(s.type)?u.checked=s.checked:"input"!==l&&"textarea"!==l||(u.defaultValue=s.defaultValue);if(t)if(n)for(o=o||pe(e),a=a||pe(c),r=0,i=o.length;r<i;r++)Oe(o[r],a[r]);else Oe(e,c);return 0<(a=pe(c,"script")).length&&ge(a,!d&&pe(e,"script")),c},cleanData:function(e){for(var t,n,r,i=k.event.special,o=0;void 0!==(n=e[o]);o++)if(V(n)){if(t=n[Y.expando]){if(t.events)for(r in t.events)i[r]?k.event.remove(n,r):k.removeEvent(n,r,t.handle);n[Y.expando]=void 0}n[X.expando]&&(n[X.expando]=void 0)}}}),k.fn.extend({detach:function(e){return Le(this,e,!0)},remove:function(e){return Le(this,e)},text:function(e){return q(this,function(e){return void 0===e?k.text(this):this.empty().each(function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=e)})},null,e,arguments.length)},append:function(){return Pe(this,arguments,function(e){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||De(this,e).appendChild(e)})},prepend:function(){return Pe(this,arguments,function(e){var t;1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(t=De(this,e)).insertBefore(e,t.firstChild)})},before:function(){return Pe(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this)})},after:function(){return Pe(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)})},empty:function(){for(var e,t=0;null!=(e=this[t]);t++)1===e.nodeType&&(k.cleanData(pe(e,!1)),e.textContent="");return this},clone:function(e,t){return e=null!=e&&e,t=null==t?e:t,this.map(function(){return k.clone(this,e,t)})},html:function(e){return q(this,function(e){var t=this[0]||{},n=0,r=this.length;if(void 0===e&&1===t.nodeType)return t.innerHTML;if("string"==typeof e&&!Ee.test(e)&&!he[(ce.exec(e)||["",""])[1].toLowerCase()]){e=k.htmlPrefilter(e);try{for(;n<r;n++)1===(t=this[n]||{}).nodeType&&(k.cleanData(pe(t,!1)),t.innerHTML=e);t=0}catch(e){}}t&&this.empty().append(e)},null,e,arguments.length)},replaceWith:function(){var n=[];return Pe(this,arguments,function(e){var t=this.parentNode;k.inArray(this,n)<0&&(k.cleanData(pe(this)),t&&t.replaceChild(e,this))},n)}}),k.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(e,a){k.fn[e]=function(e){for(var t,n=[],r=k(e),i=r.length-1,o=0;o<=i;o++)t=o===i?this:this.clone(!0),k(r[o])[a](t),u.apply(n,t.get());return this.pushStack(n)}});function Fe(e,t,n){var r,i={};for(r in t)i[r]=e.style[r],e.style[r]=t[r];for(r in n=n.call(e),t)e.style[r]=i[r];return n}var Ue,Me,Ne,Be,je,qe,Ge,He,ze=new RegExp("^("+Z+")(?!px)[a-z%]+$","i"),We=function(e){var t=e.ownerDocument.defaultView;return t&&t.opener||(t=S),t.getComputedStyle(e)},Ve=new RegExp(te.join("|"),"i");function $e(e,t,n){var r,i,o=e.style;return(n=n||We(e))&&(""!==(i=n.getPropertyValue(t)||n[t])||re(e)||(i=k.style(e,t)),!v.pixelBoxStyles()&&ze.test(i)&&Ve.test(t)&&(r=o.width,e=o.minWidth,t=o.maxWidth,o.minWidth=o.maxWidth=o.width=i,i=n.width,o.width=r,o.minWidth=e,o.maxWidth=t)),void 0!==i?i+"":i}function Ye(e,t){return{get:function(){if(!e())return(this.get=t).apply(this,arguments);delete this.get}}}function Xe(){var e;He&&(Ge.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",He.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",ne.appendChild(Ge).appendChild(He),e=S.getComputedStyle(He),Ue="1%"!==e.top,qe=12===Qe(e.marginLeft),He.style.right="60%",Be=36===Qe(e.right),Me=36===Qe(e.width),He.style.position="absolute",Ne=12===Qe(He.offsetWidth/3),ne.removeChild(Ge),He=null)}function Qe(e){return Math.round(parseFloat(e))}Ge=T.createElement("div"),(He=T.createElement("div")).style&&(He.style.backgroundClip="content-box",He.cloneNode(!0).style.backgroundClip="",v.clearCloneStyle="content-box"===He.style.backgroundClip,k.extend(v,{boxSizingReliable:function(){return Xe(),Me},pixelBoxStyles:function(){return Xe(),Be},pixelPosition:function(){return Xe(),Ue},reliableMarginLeft:function(){return Xe(),qe},scrollboxSize:function(){return Xe(),Ne},reliableTrDimensions:function(){var e,t,n;return null==je&&(e=T.createElement("table"),n=T.createElement("tr"),t=T.createElement("div"),e.style.cssText="position:absolute;left:-11111px",n.style.height="1px",t.style.height="9px",ne.appendChild(e).appendChild(n).appendChild(t),n=S.getComputedStyle(n),je=3<parseInt(n.height),ne.removeChild(e)),je}}));var Ke=["Webkit","Moz","ms"],Je=T.createElement("div").style,Ze={};function et(e){return k.cssProps[e]||Ze[e]||(e in Je?e:Ze[e]=function(e){for(var t=e[0].toUpperCase()+e.slice(1),n=Ke.length;n--;)if((e=Ke[n]+t)in Je)return e}(e)||e)}var tt=/^(none|table(?!-c[ea]).+)/,nt=/^--/,rt={position:"absolute",visibility:"hidden",display:"block"},it={letterSpacing:"0",fontWeight:"400"};function ot(e,t,n){var r=ee.exec(t);return r?Math.max(0,r[2]-(n||0))+(r[3]||"px"):t}function at(e,t,n,r,i,o){var a="width"===t?1:0,s=0,u=0;if(n===(r?"border":"content"))return 0;for(;a<4;a+=2)"margin"===n&&(u+=k.css(e,n+te[a],!0,i)),r?("content"===n&&(u-=k.css(e,"padding"+te[a],!0,i)),"margin"!==n&&(u-=k.css(e,"border"+te[a]+"Width",!0,i))):(u+=k.css(e,"padding"+te[a],!0,i),"padding"!==n?u+=k.css(e,"border"+te[a]+"Width",!0,i):s+=k.css(e,"border"+te[a]+"Width",!0,i));return!r&&0<=o&&(u+=Math.max(0,Math.ceil(e["offset"+t[0].toUpperCase()+t.slice(1)]-o-u-s-.5))||0),u}function st(e,t,n){var r=We(e),i=(!v.boxSizingReliable()||n)&&"border-box"===k.css(e,"boxSizing",!1,r),o=i,a=$e(e,t,r),s="offset"+t[0].toUpperCase()+t.slice(1);if(ze.test(a)){if(!n)return a;a="auto"}return(!v.boxSizingReliable()&&i||!v.reliableTrDimensions()&&E(e,"tr")||"auto"===a||!parseFloat(a)&&"inline"===k.css(e,"display",!1,r))&&e.getClientRects().length&&(i="border-box"===k.css(e,"boxSizing",!1,r),(o=s in e)&&(a=e[s])),(a=parseFloat(a)||0)+at(e,t,n||(i?"border":"content"),o,r,a)+"px"}function ut(e,t,n,r,i){return new ut.prototype.init(e,t,n,r,i)}k.extend({cssHooks:{opacity:{get:function(e,t){if(t){e=$e(e,"opacity");return""===e?"1":e}}}},cssNumber:{animationIterationCount:!0,columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{},style:function(e,t,n,r){if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style){var i,o,a,s=W(t),u=nt.test(t),l=e.style;if(u||(t=et(s)),a=k.cssHooks[t]||k.cssHooks[s],void 0===n)return a&&"get"in a&&void 0!==(i=a.get(e,!1,r))?i:l[t];"string"===(o=_typeof(n))&&(i=ee.exec(n))&&i[1]&&(n=ae(e,t,i),o="number"),null!=n&&n==n&&("number"!==o||u||(n+=i&&i[3]||(k.cssNumber[s]?"":"px")),v.clearCloneStyle||""!==n||0!==t.indexOf("background")||(l[t]="inherit"),a&&"set"in a&&void 0===(n=a.set(e,n,r))||(u?l.setProperty(t,n):l[t]=n))}},css:function(e,t,n,r){var i,o=W(t);return nt.test(t)||(t=et(o)),(o=k.cssHooks[t]||k.cssHooks[o])&&"get"in o&&(i=o.get(e,!0,n)),void 0===i&&(i=$e(e,t,r)),"normal"===i&&t in it&&(i=it[t]),""===n||n?(t=parseFloat(i),!0===n||isFinite(t)?t||0:i):i}}),k.each(["height","width"],function(e,s){k.cssHooks[s]={get:function(e,t,n){if(t)return!tt.test(k.css(e,"display"))||e.getClientRects().length&&e.getBoundingClientRect().width?st(e,s,n):Fe(e,rt,function(){return st(e,s,n)})},set:function(e,t,n){var r,i=We(e),o=!v.scrollboxSize()&&"absolute"===i.position,a=(o||n)&&"border-box"===k.css(e,"boxSizing",!1,i),n=n?at(e,s,n,a,i):0;return a&&o&&(n-=Math.ceil(e["offset"+s[0].toUpperCase()+s.slice(1)]-parseFloat(i[s])-at(e,s,"border",!1,i)-.5)),n&&(r=ee.exec(t))&&"px"!==(r[3]||"px")&&(e.style[s]=t,t=k.css(e,s)),ot(0,t,n)}}}),k.cssHooks.marginLeft=Ye(v.reliableMarginLeft,function(e,t){if(t)return(parseFloat($e(e,"marginLeft"))||e.getBoundingClientRect().left-Fe(e,{marginLeft:0},function(){return e.getBoundingClientRect().left}))+"px"}),k.each({margin:"",padding:"",border:"Width"},function(i,o){k.cssHooks[i+o]={expand:function(e){for(var t=0,n={},r="string"==typeof e?e.split(" "):[e];t<4;t++)n[i+te[t]+o]=r[t]||r[t-2]||r[0];return n}},"margin"!==i&&(k.cssHooks[i+o].set=ot)}),k.fn.extend({css:function(e,t){return q(this,function(e,t,n){var r,i,o={},a=0;if(Array.isArray(t)){for(r=We(e),i=t.length;a<i;a++)o[t[a]]=k.css(e,t[a],!1,r);return o}return void 0!==n?k.style(e,t,n):k.css(e,t)},e,t,1<arguments.length)}}),((k.Tween=ut).prototype={constructor:ut,init:function(e,t,n,r,i,o){this.elem=e,this.prop=n,this.easing=i||k.easing._default,this.options=t,this.start=this.now=this.cur(),this.end=r,this.unit=o||(k.cssNumber[n]?"":"px")},cur:function(){var e=ut.propHooks[this.prop];return(e&&e.get?e:ut.propHooks._default).get(this)},run:function(e){var t,n=ut.propHooks[this.prop];return this.options.duration?this.pos=t=k.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):this.pos=t=e,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),(n&&n.set?n:ut.propHooks._default).set(this),this}}).init.prototype=ut.prototype,(ut.propHooks={_default:{get:function(e){return 1!==e.elem.nodeType||null!=e.elem[e.prop]&&null==e.elem.style[e.prop]?e.elem[e.prop]:(e=k.css(e.elem,e.prop,""))&&"auto"!==e?e:0},set:function(e){k.fx.step[e.prop]?k.fx.step[e.prop](e):1!==e.elem.nodeType||!k.cssHooks[e.prop]&&null==e.elem.style[et(e.prop)]?e.elem[e.prop]=e.now:k.style(e.elem,e.prop,e.now+e.unit)}}}).scrollTop=ut.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},k.easing={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2},_default:"swing"},k.fx=ut.prototype.init,k.fx.step={};var lt,ct,dt=/^(?:toggle|show|hide)$/,ft=/queueHooks$/;function ht(){ct&&(!1===T.hidden&&S.requestAnimationFrame?S.requestAnimationFrame(ht):S.setTimeout(ht,k.fx.interval),k.fx.tick())}function pt(){return S.setTimeout(function(){lt=void 0}),lt=Date.now()}function gt(e,t){var n,r=0,i={height:e};for(t=t?1:0;r<4;r+=2-t)i["margin"+(n=te[r])]=i["padding"+n]=e;return t&&(i.opacity=i.width=e),i}function mt(e,t,n){for(var r,i=(yt.tweeners[t]||[]).concat(yt.tweeners["*"]),o=0,a=i.length;o<a;o++)if(r=i[o].call(n,t,e))return r}function yt(i,e,t){var n,o,r=0,a=yt.prefilters.length,s=k.Deferred().always(function(){delete u.elem}),u=function(){if(o)return!1;for(var e=lt||pt(),e=Math.max(0,l.startTime+l.duration-e),t=1-(e/l.duration||0),n=0,r=l.tweens.length;n<r;n++)l.tweens[n].run(t);return s.notifyWith(i,[l,t,e]),t<1&&r?e:(r||s.notifyWith(i,[l,1,0]),s.resolveWith(i,[l]),!1)},l=s.promise({elem:i,props:k.extend({},e),opts:k.extend(!0,{specialEasing:{},easing:k.easing._default},t),originalProperties:e,originalOptions:t,startTime:lt||pt(),duration:t.duration,tweens:[],createTween:function(e,t){e=k.Tween(i,l.opts,e,t,l.opts.specialEasing[e]||l.opts.easing);return l.tweens.push(e),e},stop:function(e){var t=0,n=e?l.tweens.length:0;if(o)return this;for(o=!0;t<n;t++)l.tweens[t].run(1);return e?(s.notifyWith(i,[l,1,0]),s.resolveWith(i,[l,e])):s.rejectWith(i,[l,e]),this}}),c=l.props;for(function(e,t){var n,r,i,o,a;for(n in e)if(i=t[r=W(n)],o=e[n],Array.isArray(o)&&(i=o[1],o=e[n]=o[0]),n!==r&&(e[r]=o,delete e[n]),(a=k.cssHooks[r])&&"expand"in a)for(n in o=a.expand(o),delete e[r],o)n in e||(e[n]=o[n],t[n]=i);else t[r]=i}(c,l.opts.specialEasing);r<a;r++)if(n=yt.prefilters[r].call(l,i,c,l.opts))return _(n.stop)&&(k._queueHooks(l.elem,l.opts.queue).stop=n.stop.bind(n)),n;return k.map(c,mt,l),_(l.opts.start)&&l.opts.start.call(i,l),l.progress(l.opts.progress).done(l.opts.done,l.opts.complete).fail(l.opts.fail).always(l.opts.always),k.fx.timer(k.extend(u,{elem:i,anim:l,queue:l.opts.queue})),l}k.Animation=k.extend(yt,{tweeners:{"*":[function(e,t){var n=this.createTween(e,t);return ae(n.elem,e,ee.exec(t),n),n}]},tweener:function(e,t){for(var n,r=0,i=(e=_(e)?(t=e,["*"]):e.match(L)).length;r<i;r++)n=e[r],yt.tweeners[n]=yt.tweeners[n]||[],yt.tweeners[n].unshift(t)},prefilters:[function(e,t,n){var r,i,o,a,s,u,l,c="width"in t||"height"in t,d=this,f={},h=e.style,p=e.nodeType&&oe(e),g=Y.get(e,"fxshow");for(r in n.queue||(null==(a=k._queueHooks(e,"fx")).unqueued&&(a.unqueued=0,s=a.empty.fire,a.empty.fire=function(){a.unqueued||s()}),a.unqueued++,d.always(function(){d.always(function(){a.unqueued--,k.queue(e,"fx").length||a.empty.fire()})})),t)if(i=t[r],dt.test(i)){if(delete t[r],o=o||"toggle"===i,i===(p?"hide":"show")){if("show"!==i||!g||void 0===g[r])continue;p=!0}f[r]=g&&g[r]||k.style(e,r)}if((u=!k.isEmptyObject(t))||!k.isEmptyObject(f))for(r in c&&1===e.nodeType&&(n.overflow=[h.overflow,h.overflowX,h.overflowY],null==(l=g&&g.display)&&(l=Y.get(e,"display")),"none"===(c=k.css(e,"display"))&&(l?c=l:(ue([e],!0),l=e.style.display||l,c=k.css(e,"display"),ue([e]))),("inline"===c||"inline-block"===c&&null!=l)&&"none"===k.css(e,"float")&&(u||(d.done(function(){h.display=l}),null==l&&(c=h.display,l="none"===c?"":c)),h.display="inline-block")),n.overflow&&(h.overflow="hidden",d.always(function(){h.overflow=n.overflow[0],h.overflowX=n.overflow[1],h.overflowY=n.overflow[2]})),u=!1,f)u||(g?"hidden"in g&&(p=g.hidden):g=Y.access(e,"fxshow",{display:l}),o&&(g.hidden=!p),p&&ue([e],!0),d.done(function(){for(r in p||ue([e]),Y.remove(e,"fxshow"),f)k.style(e,r,f[r])})),u=mt(p?g[r]:0,r,d),r in g||(g[r]=u.start,p&&(u.end=u.start,u.start=0))}],prefilter:function(e,t){t?yt.prefilters.unshift(e):yt.prefilters.push(e)}}),k.speed=function(e,t,n){var r=e&&"object"==_typeof(e)?k.extend({},e):{complete:n||!n&&t||_(e)&&e,duration:e,easing:n&&t||t&&!_(t)&&t};return k.fx.off?r.duration=0:"number"!=typeof r.duration&&(r.duration in k.fx.speeds?r.duration=k.fx.speeds[r.duration]:r.duration=k.fx.speeds._default),null!=r.queue&&!0!==r.queue||(r.queue="fx"),r.old=r.complete,r.complete=function(){_(r.old)&&r.old.call(this),r.queue&&k.dequeue(this,r.queue)},r},k.fn.extend({fadeTo:function(e,t,n,r){return this.filter(oe).css("opacity",0).show().end().animate({opacity:t},e,n,r)},animate:function(t,e,n,r){var i=k.isEmptyObject(t),o=k.speed(e,n,r),r=function(){var e=yt(this,k.extend({},t),o);(i||Y.get(this,"finish"))&&e.stop(!0)};return r.finish=r,i||!1===o.queue?this.each(r):this.queue(o.queue,r)},stop:function(i,e,o){function a(e){var t=e.stop;delete e.stop,t(o)}return"string"!=typeof i&&(o=e,e=i,i=void 0),e&&this.queue(i||"fx",[]),this.each(function(){var e=!0,t=null!=i&&i+"queueHooks",n=k.timers,r=Y.get(this);if(t)r[t]&&r[t].stop&&a(r[t]);else for(t in r)r[t]&&r[t].stop&&ft.test(t)&&a(r[t]);for(t=n.length;t--;)n[t].elem!==this||null!=i&&n[t].queue!==i||(n[t].anim.stop(o),e=!1,n.splice(t,1));!e&&o||k.dequeue(this,i)})},finish:function(a){return!1!==a&&(a=a||"fx"),this.each(function(){var e,t=Y.get(this),n=t[a+"queue"],r=t[a+"queueHooks"],i=k.timers,o=n?n.length:0;for(t.finish=!0,k.queue(this,a,[]),r&&r.stop&&r.stop.call(this,!0),e=i.length;e--;)i[e].elem===this&&i[e].queue===a&&(i[e].anim.stop(!0),i.splice(e,1));for(e=0;e<o;e++)n[e]&&n[e].finish&&n[e].finish.call(this);delete t.finish})}}),k.each(["toggle","show","hide"],function(e,r){var i=k.fn[r];k.fn[r]=function(e,t,n){return null==e||"boolean"==typeof e?i.apply(this,arguments):this.animate(gt(r,!0),e,t,n)}}),k.each({slideDown:gt("show"),slideUp:gt("hide"),slideToggle:gt("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(e,r){k.fn[e]=function(e,t,n){return this.animate(r,e,t,n)}}),k.timers=[],k.fx.tick=function(){var e,t=0,n=k.timers;for(lt=Date.now();t<n.length;t++)(e=n[t])()||n[t]!==e||n.splice(t--,1);n.length||k.fx.stop(),lt=void 0},k.fx.timer=function(e){k.timers.push(e),k.fx.start()},k.fx.interval=13,k.fx.start=function(){ct||(ct=!0,ht())},k.fx.stop=function(){ct=null},k.fx.speeds={slow:600,fast:200,_default:400},k.fn.delay=function(r,e){return r=k.fx&&k.fx.speeds[r]||r,e=e||"fx",this.queue(e,function(e,t){var n=S.setTimeout(e,r);t.stop=function(){S.clearTimeout(n)}})},fe=T.createElement("input"),Z=T.createElement("select").appendChild(T.createElement("option")),fe.type="checkbox",v.checkOn=""!==fe.value,v.optSelected=Z.selected,(fe=T.createElement("input")).value="t",fe.type="radio",v.radioValue="t"===fe.value;var vt,_t=k.expr.attrHandle;k.fn.extend({attr:function(e,t){return q(this,k.attr,e,t,1<arguments.length)},removeAttr:function(e){return this.each(function(){k.removeAttr(this,e)})}}),k.extend({attr:function(e,t,n){var r,i,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return void 0===e.getAttribute?k.prop(e,t,n):(1===o&&k.isXMLDoc(e)||(i=k.attrHooks[t.toLowerCase()]||(k.expr.match.bool.test(t)?vt:void 0)),void 0!==n?null===n?void k.removeAttr(e,t):i&&"set"in i&&void 0!==(r=i.set(e,n,t))?r:(e.setAttribute(t,n+""),n):!(i&&"get"in i&&null!==(r=i.get(e,t)))&&null==(r=k.find.attr(e,t))?void 0:r)},attrHooks:{type:{set:function(e,t){if(!v.radioValue&&"radio"===t&&E(e,"input")){var n=e.value;return e.setAttribute("type",t),n&&(e.value=n),t}}}},removeAttr:function(e,t){var n,r=0,i=t&&t.match(L);if(i&&1===e.nodeType)for(;n=i[r++];)e.removeAttribute(n)}}),vt={set:function(e,t,n){return!1===t?k.removeAttr(e,n):e.setAttribute(n,n),n}},k.each(k.expr.match.bool.source.match(/\w+/g),function(e,t){var a=_t[t]||k.find.attr;_t[t]=function(e,t,n){var r,i,o=t.toLowerCase();return n||(i=_t[o],_t[o]=r,r=null!=a(e,t,n)?o:null,_t[o]=i),r}});var bt=/^(?:input|select|textarea|button)$/i,wt=/^(?:a|area)$/i;function xt(e){return(e.match(L)||[]).join(" ")}function St(e){return e.getAttribute&&e.getAttribute("class")||""}function Tt(e){return Array.isArray(e)?e:"string"==typeof e&&e.match(L)||[]}k.fn.extend({prop:function(e,t){return q(this,k.prop,e,t,1<arguments.length)},removeProp:function(e){return this.each(function(){delete this[k.propFix[e]||e]})}}),k.extend({prop:function(e,t,n){var r,i,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return 1===o&&k.isXMLDoc(e)||(t=k.propFix[t]||t,i=k.propHooks[t]),void 0!==n?i&&"set"in i&&void 0!==(r=i.set(e,n,t))?r:e[t]=n:i&&"get"in i&&null!==(r=i.get(e,t))?r:e[t]},propHooks:{tabIndex:{get:function(e){var t=k.find.attr(e,"tabindex");return t?parseInt(t,10):bt.test(e.nodeName)||wt.test(e.nodeName)&&e.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),v.optSelected||(k.propHooks.selected={get:function(e){e=e.parentNode;return e&&e.parentNode&&e.parentNode.selectedIndex,null},set:function(e){e=e.parentNode;e&&(e.selectedIndex,e.parentNode&&e.parentNode.selectedIndex)}}),k.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){k.propFix[this.toLowerCase()]=this}),k.fn.extend({addClass:function(t){var e,n,r,i,o,a,s=0;if(_(t))return this.each(function(e){k(this).addClass(t.call(this,e,St(this)))});if((e=Tt(t)).length)for(;n=this[s++];)if(a=St(n),r=1===n.nodeType&&" "+xt(a)+" "){for(o=0;i=e[o++];)r.indexOf(" "+i+" ")<0&&(r+=i+" ");a!==(a=xt(r))&&n.setAttribute("class",a)}return this},removeClass:function(t){var e,n,r,i,o,a,s=0;if(_(t))return this.each(function(e){k(this).removeClass(t.call(this,e,St(this)))});if(!arguments.length)return this.attr("class","");if((e=Tt(t)).length)for(;n=this[s++];)if(a=St(n),r=1===n.nodeType&&" "+xt(a)+" "){for(o=0;i=e[o++];)for(;-1<r.indexOf(" "+i+" ");)r=r.replace(" "+i+" "," ");a!==(a=xt(r))&&n.setAttribute("class",a)}return this},toggleClass:function(i,t){var o=_typeof(i),a="string"===o||Array.isArray(i);return"boolean"==typeof t&&a?t?this.addClass(i):this.removeClass(i):_(i)?this.each(function(e){k(this).toggleClass(i.call(this,e,St(this),t),t)}):this.each(function(){var e,t,n,r;if(a)for(t=0,n=k(this),r=Tt(i);e=r[t++];)n.hasClass(e)?n.removeClass(e):n.addClass(e);else void 0!==i&&"boolean"!==o||((e=St(this))&&Y.set(this,"__className__",e),this.setAttribute&&this.setAttribute("class",!e&&!1!==i&&Y.get(this,"__className__")||""))})},hasClass:function(e){for(var t,n=0,r=" "+e+" ";t=this[n++];)if(1===t.nodeType&&-1<(" "+xt(St(t))+" ").indexOf(r))return!0;return!1}});var kt=/\r/g;k.fn.extend({val:function(t){var n,e,r,i=this[0];return arguments.length?(r=_(t),this.each(function(e){1===this.nodeType&&(null==(e=r?t.call(this,e,k(this).val()):t)?e="":"number"==typeof e?e+="":Array.isArray(e)&&(e=k.map(e,function(e){return null==e?"":e+""})),(n=k.valHooks[this.type]||k.valHooks[this.nodeName.toLowerCase()])&&"set"in n&&void 0!==n.set(this,e,"value")||(this.value=e))})):i?(n=k.valHooks[i.type]||k.valHooks[i.nodeName.toLowerCase()])&&"get"in n&&void 0!==(e=n.get(i,"value"))?e:"string"==typeof(e=i.value)?e.replace(kt,""):null==e?"":e:void 0}}),k.extend({valHooks:{option:{get:function(e){var t=k.find.attr(e,"value");return null!=t?t:xt(k.text(e))}},select:{get:function(e){for(var t,n=e.options,r=e.selectedIndex,i="select-one"===e.type,o=i?null:[],a=i?r+1:n.length,s=r<0?a:i?r:0;s<a;s++)if(((t=n[s]).selected||s===r)&&!t.disabled&&(!t.parentNode.disabled||!E(t.parentNode,"optgroup"))){if(t=k(t).val(),i)return t;o.push(t)}return o},set:function(e,t){for(var n,r,i=e.options,o=k.makeArray(t),a=i.length;a--;)((r=i[a]).selected=-1<k.inArray(k.valHooks.option.get(r),o))&&(n=!0);return n||(e.selectedIndex=-1),o}}}}),k.each(["radio","checkbox"],function(){k.valHooks[this]={set:function(e,t){if(Array.isArray(t))return e.checked=-1<k.inArray(k(e).val(),t)}},v.checkOn||(k.valHooks[this].get=function(e){return null===e.getAttribute("value")?"on":e.value})}),v.focusin="onfocusin"in S;function Et(e){e.stopPropagation()}var Ct=/^(?:focusinfocus|focusoutblur)$/;k.extend(k.event,{trigger:function(e,t,n,r){var i,o,a,s,u,l,c,d=[n||T],f=y.call(e,"type")?e.type:e,h=y.call(e,"namespace")?e.namespace.split("."):[],p=c=o=n=n||T;if(3!==n.nodeType&&8!==n.nodeType&&!Ct.test(f+k.event.triggered)&&(-1<f.indexOf(".")&&(f=(h=f.split(".")).shift(),h.sort()),s=f.indexOf(":")<0&&"on"+f,(e=e[k.expando]?e:new k.Event(f,"object"==_typeof(e)&&e)).isTrigger=r?2:3,e.namespace=h.join("."),e.rnamespace=e.namespace?new RegExp("(^|\\.)"+h.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,e.result=void 0,e.target||(e.target=n),t=null==t?[e]:k.makeArray(t,[e]),l=k.event.special[f]||{},r||!l.trigger||!1!==l.trigger.apply(n,t))){if(!r&&!l.noBubble&&!g(n)){for(a=l.delegateType||f,Ct.test(a+f)||(p=p.parentNode);p;p=p.parentNode)d.push(p),o=p;o===(n.ownerDocument||T)&&d.push(o.defaultView||o.parentWindow||S)}for(i=0;(p=d[i++])&&!e.isPropagationStopped();)c=p,e.type=1<i?a:l.bindType||f,(u=(Y.get(p,"events")||Object.create(null))[e.type]&&Y.get(p,"handle"))&&u.apply(p,t),(u=s&&p[s])&&u.apply&&V(p)&&(e.result=u.apply(p,t),!1===e.result&&e.preventDefault());return e.type=f,r||e.isDefaultPrevented()||l._default&&!1!==l._default.apply(d.pop(),t)||!V(n)||s&&_(n[f])&&!g(n)&&((o=n[s])&&(n[s]=null),k.event.triggered=f,e.isPropagationStopped()&&c.addEventListener(f,Et),n[f](),e.isPropagationStopped()&&c.removeEventListener(f,Et),k.event.triggered=void 0,o&&(n[s]=o)),e.result}},simulate:function(e,t,n){e=k.extend(new k.Event,n,{type:e,isSimulated:!0});k.event.trigger(e,null,t)}}),k.fn.extend({trigger:function(e,t){return this.each(function(){k.event.trigger(e,t,this)})},triggerHandler:function(e,t){var n=this[0];if(n)return k.event.trigger(e,t,n,!0)}}),v.focusin||k.each({focus:"focusin",blur:"focusout"},function(n,r){function i(e){k.event.simulate(r,e.target,k.event.fix(e))}k.event.special[r]={setup:function(){var e=this.ownerDocument||this.document||this,t=Y.access(e,r);t||e.addEventListener(n,i,!0),Y.access(e,r,(t||0)+1)},teardown:function(){var e=this.ownerDocument||this.document||this,t=Y.access(e,r)-1;t?Y.access(e,r,t):(e.removeEventListener(n,i,!0),Y.remove(e,r))}}});var At=S.location,Dt={guid:Date.now()},It=/\?/;k.parseXML=function(e){var t;if(!e||"string"!=typeof e)return null;try{t=(new S.DOMParser).parseFromString(e,"text/xml")}catch(e){t=void 0}return t&&!t.getElementsByTagName("parsererror").length||k.error("Invalid XML: "+e),t};var Rt=/\[\]$/,Ot=/\r?\n/g,Pt=/^(?:submit|button|image|reset|file)$/i,Lt=/^(?:input|select|textarea|keygen)/i;k.param=function(e,t){function n(e,t){t=_(t)?t():t,i[i.length]=encodeURIComponent(e)+"="+encodeURIComponent(null==t?"":t)}var r,i=[];if(null==e)return"";if(Array.isArray(e)||e.jquery&&!k.isPlainObject(e))k.each(e,function(){n(this.name,this.value)});else for(r in e)!function n(r,e,i,o){if(Array.isArray(e))k.each(e,function(e,t){i||Rt.test(r)?o(r,t):n(r+"["+("object"==_typeof(t)&&null!=t?e:"")+"]",t,i,o)});else if(i||"object"!==p(e))o(r,e);else for(var t in e)n(r+"["+t+"]",e[t],i,o)}(r,e[r],t,n);return i.join("&")},k.fn.extend({serialize:function(){return k.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var e=k.prop(this,"elements");return e?k.makeArray(e):this}).filter(function(){var e=this.type;return this.name&&!k(this).is(":disabled")&&Lt.test(this.nodeName)&&!Pt.test(e)&&(this.checked||!le.test(e))}).map(function(e,t){var n=k(this).val();return null==n?null:Array.isArray(n)?k.map(n,function(e){return{name:t.name,value:e.replace(Ot,"\r\n")}}):{name:t.name,value:n.replace(Ot,"\r\n")}}).get()}});var Ft=/%20/g,Ut=/#.*$/,Mt=/([?&])_=[^&]*/,Nt=/^(.*?):[ \t]*([^\r\n]*)$/gm,Bt=/^(?:GET|HEAD)$/,jt=/^\/\//,qt={},Gt={},Ht="*/".concat("*"),zt=T.createElement("a");function Wt(o){return function(e,t){"string"!=typeof e&&(t=e,e="*");var n,r=0,i=e.toLowerCase().match(L)||[];if(_(t))for(;n=i[r++];)"+"===n[0]?(n=n.slice(1)||"*",(o[n]=o[n]||[]).unshift(t)):(o[n]=o[n]||[]).push(t)}}function Vt(t,r,i,o){var a={},s=t===Gt;function u(e){var n;return a[e]=!0,k.each(t[e]||[],function(e,t){t=t(r,i,o);return"string"!=typeof t||s||a[t]?s?!(n=t):void 0:(r.dataTypes.unshift(t),u(t),!1)}),n}return u(r.dataTypes[0])||!a["*"]&&u("*")}function $t(e,t){var n,r,i=k.ajaxSettings.flatOptions||{};for(n in t)void 0!==t[n]&&((i[n]?e:r=r||{})[n]=t[n]);return r&&k.extend(!0,e,r),e}zt.href=At.href,k.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:At.href,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(At.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Ht,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":k.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(e,t){return t?$t($t(e,k.ajaxSettings),t):$t(k.ajaxSettings,e)},ajaxPrefilter:Wt(qt),ajaxTransport:Wt(Gt),ajax:function(e,t){"object"==_typeof(e)&&(t=e,e=void 0),t=t||{};var u,l,c,n,d,r,f,h,i,o,p=k.ajaxSetup({},t),g=p.context||p,m=p.context&&(g.nodeType||g.jquery)?k(g):k.event,y=k.Deferred(),v=k.Callbacks("once memory"),_=p.statusCode||{},a={},s={},b="canceled",w={readyState:0,getResponseHeader:function(e){var t;if(f){if(!n)for(n={};t=Nt.exec(c);)n[t[1].toLowerCase()+" "]=(n[t[1].toLowerCase()+" "]||[]).concat(t[2]);t=n[e.toLowerCase()+" "]}return null==t?null:t.join(", ")},getAllResponseHeaders:function(){return f?c:null},setRequestHeader:function(e,t){return null==f&&(e=s[e.toLowerCase()]=s[e.toLowerCase()]||e,a[e]=t),this},overrideMimeType:function(e){return null==f&&(p.mimeType=e),this},statusCode:function(e){if(e)if(f)w.always(e[w.status]);else for(var t in e)_[t]=[_[t],e[t]];return this},abort:function(e){e=e||b;return u&&u.abort(e),x(0,e),this}};if(y.promise(w),p.url=((e||p.url||At.href)+"").replace(jt,At.protocol+"//"),p.type=t.method||t.type||p.method||p.type,p.dataTypes=(p.dataType||"*").toLowerCase().match(L)||[""],null==p.crossDomain){r=T.createElement("a");try{r.href=p.url,r.href=r.href,p.crossDomain=zt.protocol+"//"+zt.host!=r.protocol+"//"+r.host}catch(e){p.crossDomain=!0}}if(p.data&&p.processData&&"string"!=typeof p.data&&(p.data=k.param(p.data,p.traditional)),Vt(qt,p,t,w),f)return w;for(i in(h=k.event&&p.global)&&0==k.active++&&k.event.trigger("ajaxStart"),p.type=p.type.toUpperCase(),p.hasContent=!Bt.test(p.type),l=p.url.replace(Ut,""),p.hasContent?p.data&&p.processData&&0===(p.contentType||"").indexOf("application/x-www-form-urlencoded")&&(p.data=p.data.replace(Ft,"+")):(o=p.url.slice(l.length),p.data&&(p.processData||"string"==typeof p.data)&&(l+=(It.test(l)?"&":"?")+p.data,delete p.data),!1===p.cache&&(l=l.replace(Mt,"$1"),o=(It.test(l)?"&":"?")+"_="+Dt.guid+++o),p.url=l+o),p.ifModified&&(k.lastModified[l]&&w.setRequestHeader("If-Modified-Since",k.lastModified[l]),k.etag[l]&&w.setRequestHeader("If-None-Match",k.etag[l])),(p.data&&p.hasContent&&!1!==p.contentType||t.contentType)&&w.setRequestHeader("Content-Type",p.contentType),w.setRequestHeader("Accept",p.dataTypes[0]&&p.accepts[p.dataTypes[0]]?p.accepts[p.dataTypes[0]]+("*"!==p.dataTypes[0]?", "+Ht+"; q=0.01":""):p.accepts["*"]),p.headers)w.setRequestHeader(i,p.headers[i]);if(p.beforeSend&&(!1===p.beforeSend.call(g,w,p)||f))return w.abort();if(b="abort",v.add(p.complete),w.done(p.success),w.fail(p.error),u=Vt(Gt,p,t,w)){if(w.readyState=1,h&&m.trigger("ajaxSend",[w,p]),f)return w;p.async&&0<p.timeout&&(d=S.setTimeout(function(){w.abort("timeout")},p.timeout));try{f=!1,u.send(a,x)}catch(e){if(f)throw e;x(-1,e)}}else x(-1,"No Transport");function x(e,t,n,r){var i,o,a,s=t;f||(f=!0,d&&S.clearTimeout(d),u=void 0,c=r||"",w.readyState=0<e?4:0,r=200<=e&&e<300||304===e,n&&(a=function(e,t,n){for(var r,i,o,a,s=e.contents,u=e.dataTypes;"*"===u[0];)u.shift(),void 0===r&&(r=e.mimeType||t.getResponseHeader("Content-Type"));if(r)for(i in s)if(s[i]&&s[i].test(r)){u.unshift(i);break}if(u[0]in n)o=u[0];else{for(i in n){if(!u[0]||e.converters[i+" "+u[0]]){o=i;break}a=a||i}o=o||a}if(o)return o!==u[0]&&u.unshift(o),n[o]}(p,w,n)),!r&&-1<k.inArray("script",p.dataTypes)&&(p.converters["text script"]=function(){}),a=function(e,t,n,r){var i,o,a,s,u,l={},c=e.dataTypes.slice();if(c[1])for(a in e.converters)l[a.toLowerCase()]=e.converters[a];for(o=c.shift();o;)if(e.responseFields[o]&&(n[e.responseFields[o]]=t),!u&&r&&e.dataFilter&&(t=e.dataFilter(t,e.dataType)),u=o,o=c.shift())if("*"===o)o=u;else if("*"!==u&&u!==o){if(!(a=l[u+" "+o]||l["* "+o]))for(i in l)if((s=i.split(" "))[1]===o&&(a=l[u+" "+s[0]]||l["* "+s[0]])){!0===a?a=l[i]:!0!==l[i]&&(o=s[0],c.unshift(s[1]));break}if(!0!==a)if(a&&e.throws)t=a(t);else try{t=a(t)}catch(e){return{state:"parsererror",error:a?e:"No conversion from "+u+" to "+o}}}return{state:"success",data:t}}(p,a,w,r),r?(p.ifModified&&((n=w.getResponseHeader("Last-Modified"))&&(k.lastModified[l]=n),(n=w.getResponseHeader("etag"))&&(k.etag[l]=n)),204===e||"HEAD"===p.type?s="nocontent":304===e?s="notmodified":(s=a.state,i=a.data,r=!(o=a.error))):(o=s,!e&&s||(s="error",e<0&&(e=0))),w.status=e,w.statusText=(t||s)+"",r?y.resolveWith(g,[i,s,w]):y.rejectWith(g,[w,s,o]),w.statusCode(_),_=void 0,h&&m.trigger(r?"ajaxSuccess":"ajaxError",[w,p,r?i:o]),v.fireWith(g,[w,s]),h&&(m.trigger("ajaxComplete",[w,p]),--k.active||k.event.trigger("ajaxStop")))}return w},getJSON:function(e,t,n){return k.get(e,t,n,"json")},getScript:function(e,t){return k.get(e,void 0,t,"script")}}),k.each(["get","post"],function(e,i){k[i]=function(e,t,n,r){return _(t)&&(r=r||n,n=t,t=void 0),k.ajax(k.extend({url:e,type:i,dataType:r,data:t,success:n},k.isPlainObject(e)&&e))}}),k.ajaxPrefilter(function(e){for(var t in e.headers)"content-type"===t.toLowerCase()&&(e.contentType=e.headers[t]||"")}),k._evalUrl=function(e,t,n){return k.ajax({url:e,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,converters:{"text script":function(){}},dataFilter:function(e){k.globalEval(e,t,n)}})},k.fn.extend({wrapAll:function(e){return this[0]&&(_(e)&&(e=e.call(this[0])),e=k(e,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&e.insertBefore(this[0]),e.map(function(){for(var e=this;e.firstElementChild;)e=e.firstElementChild;return e}).append(this)),this},wrapInner:function(n){return _(n)?this.each(function(e){k(this).wrapInner(n.call(this,e))}):this.each(function(){var e=k(this),t=e.contents();t.length?t.wrapAll(n):e.append(n)})},wrap:function(t){var n=_(t);return this.each(function(e){k(this).wrapAll(n?t.call(this,e):t)})},unwrap:function(e){return this.parent(e).not("body").each(function(){k(this).replaceWith(this.childNodes)}),this}}),k.expr.pseudos.hidden=function(e){return!k.expr.pseudos.visible(e)},k.expr.pseudos.visible=function(e){return!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)},k.ajaxSettings.xhr=function(){try{return new S.XMLHttpRequest}catch(e){}};var Yt={0:200,1223:204},Xt=k.ajaxSettings.xhr();v.cors=!!Xt&&"withCredentials"in Xt,v.ajax=Xt=!!Xt,k.ajaxTransport(function(i){var o,a;if(v.cors||Xt&&!i.crossDomain)return{send:function(e,t){var n,r=i.xhr();if(r.open(i.type,i.url,i.async,i.username,i.password),i.xhrFields)for(n in i.xhrFields)r[n]=i.xhrFields[n];for(n in i.mimeType&&r.overrideMimeType&&r.overrideMimeType(i.mimeType),i.crossDomain||e["X-Requested-With"]||(e["X-Requested-With"]="XMLHttpRequest"),e)r.setRequestHeader(n,e[n]);o=function(e){return function(){o&&(o=a=r.onload=r.onerror=r.onabort=r.ontimeout=r.onreadystatechange=null,"abort"===e?r.abort():"error"===e?"number"!=typeof r.status?t(0,"error"):t(r.status,r.statusText):t(Yt[r.status]||r.status,r.statusText,"text"!==(r.responseType||"text")||"string"!=typeof r.responseText?{binary:r.response}:{text:r.responseText},r.getAllResponseHeaders()))}},r.onload=o(),a=r.onerror=r.ontimeout=o("error"),void 0!==r.onabort?r.onabort=a:r.onreadystatechange=function(){4===r.readyState&&S.setTimeout(function(){o&&a()})},o=o("abort");try{r.send(i.hasContent&&i.data||null)}catch(e){if(o)throw e}},abort:function(){o&&o()}}}),k.ajaxPrefilter(function(e){e.crossDomain&&(e.contents.script=!1)}),k.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(e){return k.globalEval(e),e}}}),k.ajaxPrefilter("script",function(e){void 0===e.cache&&(e.cache=!1),e.crossDomain&&(e.type="GET")}),k.ajaxTransport("script",function(n){var r,i;if(n.crossDomain||n.scriptAttrs)return{send:function(e,t){r=k("<script>").attr(n.scriptAttrs||{}).prop({charset:n.scriptCharset,src:n.url}).on("load error",i=function(e){r.remove(),i=null,e&&t("error"===e.type?404:200,e.type)}),T.head.appendChild(r[0])},abort:function(){i&&i()}}});var Qt=[],Kt=/(=)\?(?=&|$)|\?\?/;k.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=Qt.pop()||k.expando+"_"+Dt.guid++;return this[e]=!0,e}}),k.ajaxPrefilter("json jsonp",function(e,t,n){var r,i,o,a=!1!==e.jsonp&&(Kt.test(e.url)?"url":"string"==typeof e.data&&0===(e.contentType||"").indexOf("application/x-www-form-urlencoded")&&Kt.test(e.data)&&"data");if(a||"jsonp"===e.dataTypes[0])return r=e.jsonpCallback=_(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback,a?e[a]=e[a].replace(Kt,"$1"+r):!1!==e.jsonp&&(e.url+=(It.test(e.url)?"&":"?")+e.jsonp+"="+r),e.converters["script json"]=function(){return o||k.error(r+" was not called"),o[0]},e.dataTypes[0]="json",i=S[r],S[r]=function(){o=arguments},n.always(function(){void 0===i?k(S).removeProp(r):S[r]=i,e[r]&&(e.jsonpCallback=t.jsonpCallback,Qt.push(r)),o&&_(i)&&i(o[0]),o=i=void 0}),"script"}),v.createHTMLDocument=((fe=T.implementation.createHTMLDocument("").body).innerHTML="<form></form><form></form>",2===fe.childNodes.length),k.parseHTML=function(e,t,n){return"string"!=typeof e?[]:("boolean"==typeof t&&(n=t,t=!1),t||(v.createHTMLDocument?((r=(t=T.implementation.createHTMLDocument("")).createElement("base")).href=T.location.href,t.head.appendChild(r)):t=T),r=!n&&[],(n=C.exec(e))?[t.createElement(n[1])]:(n=ye([e],t,r),r&&r.length&&k(r).remove(),k.merge([],n.childNodes)));var r},k.fn.load=function(e,t,n){var r,i,o,a=this,s=e.indexOf(" ");return-1<s&&(r=xt(e.slice(s)),e=e.slice(0,s)),_(t)?(n=t,t=void 0):t&&"object"==_typeof(t)&&(i="POST"),0<a.length&&k.ajax({url:e,type:i||"GET",dataType:"html",data:t}).done(function(e){o=arguments,a.html(r?k("<div>").append(k.parseHTML(e)).find(r):e)}).always(n&&function(e,t){a.each(function(){n.apply(this,o||[e.responseText,t,e])})}),this},k.expr.pseudos.animated=function(t){return k.grep(k.timers,function(e){return t===e.elem}).length},k.offset={setOffset:function(e,t,n){var r,i,o,a,s=k.css(e,"position"),u=k(e),l={};"static"===s&&(e.style.position="relative"),o=u.offset(),r=k.css(e,"top"),a=k.css(e,"left"),a=("absolute"===s||"fixed"===s)&&-1<(r+a).indexOf("auto")?(i=(s=u.position()).top,s.left):(i=parseFloat(r)||0,parseFloat(a)||0),_(t)&&(t=t.call(e,n,k.extend({},o))),null!=t.top&&(l.top=t.top-o.top+i),null!=t.left&&(l.left=t.left-o.left+a),"using"in t?t.using.call(e,l):("number"==typeof l.top&&(l.top+="px"),"number"==typeof l.left&&(l.left+="px"),u.css(l))}},k.fn.extend({offset:function(t){if(arguments.length)return void 0===t?this:this.each(function(e){k.offset.setOffset(this,t,e)});var e,n=this[0];return n?n.getClientRects().length?(e=n.getBoundingClientRect(),n=n.ownerDocument.defaultView,{top:e.top+n.pageYOffset,left:e.left+n.pageXOffset}):{top:0,left:0}:void 0},position:function(){if(this[0]){var e,t,n,r=this[0],i={top:0,left:0};if("fixed"===k.css(r,"position"))t=r.getBoundingClientRect();else{for(t=this.offset(),n=r.ownerDocument,e=r.offsetParent||n.documentElement;e&&(e===n.body||e===n.documentElement)&&"static"===k.css(e,"position");)e=e.parentNode;e&&e!==r&&1===e.nodeType&&((i=k(e).offset()).top+=k.css(e,"borderTopWidth",!0),i.left+=k.css(e,"borderLeftWidth",!0))}return{top:t.top-i.top-k.css(r,"marginTop",!0),left:t.left-i.left-k.css(r,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var e=this.offsetParent;e&&"static"===k.css(e,"position");)e=e.offsetParent;return e||ne})}}),k.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(t,i){var o="pageYOffset"===i;k.fn[t]=function(e){return q(this,function(e,t,n){var r;return g(e)?r=e:9===e.nodeType&&(r=e.defaultView),void 0===n?r?r[i]:e[t]:void(r?r.scrollTo(o?r.pageXOffset:n,o?n:r.pageYOffset):e[t]=n)},t,e,arguments.length)}}),k.each(["top","left"],function(e,n){k.cssHooks[n]=Ye(v.pixelPosition,function(e,t){if(t)return t=$e(e,n),ze.test(t)?k(e).position()[n]+"px":t})}),k.each({Height:"height",Width:"width"},function(a,s){k.each({padding:"inner"+a,content:s,"":"outer"+a},function(r,o){k.fn[o]=function(e,t){var n=arguments.length&&(r||"boolean"!=typeof e),i=r||(!0===e||!0===t?"margin":"border");return q(this,function(e,t,n){var r;return g(e)?0===o.indexOf("outer")?e["inner"+a]:e.document.documentElement["client"+a]:9===e.nodeType?(r=e.documentElement,Math.max(e.body["scroll"+a],r["scroll"+a],e.body["offset"+a],r["offset"+a],r["client"+a])):void 0===n?k.css(e,t,i):k.style(e,t,n,i)},s,n?e:void 0,n)}})}),k.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(e,t){k.fn[t]=function(e){return this.on(t,e)}}),k.fn.extend({bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,n,r){return this.on(t,e,n,r)},undelegate:function(e,t,n){return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",n)},hover:function(e,t){return this.mouseenter(e).mouseleave(t||e)}}),k.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),function(e,n){k.fn[n]=function(e,t){return 0<arguments.length?this.on(n,null,e,t):this.trigger(n)}});var Jt=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g;k.proxy=function(e,t){var n,r;if("string"==typeof t&&(r=e[t],t=e,e=r),_(e))return n=s.call(arguments,2),(r=function(){return e.apply(t||this,n.concat(s.call(arguments)))}).guid=e.guid=e.guid||k.guid++,r},k.holdReady=function(e){e?k.readyWait++:k.ready(!0)},k.isArray=Array.isArray,k.parseJSON=JSON.parse,k.nodeName=E,k.isFunction=_,k.isWindow=g,k.camelCase=W,k.type=p,k.now=Date.now,k.isNumeric=function(e){var t=k.type(e);return("number"===t||"string"===t)&&!isNaN(e-parseFloat(e))},k.trim=function(e){return null==e?"":(e+"").replace(Jt,"")},"function"==typeof define&&define.amd&&define("jquery",[],function(){return k});var Zt=S.jQuery,en=S.$;return k.noConflict=function(e){return S.$===k&&(S.$=en),e&&S.jQuery===k&&(S.jQuery=Zt),k},void 0===e&&(S.jQuery=S.$=k),k}),function(e,t){"object"==("undefined"==typeof exports?"undefined":_typeof(exports))&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):e.Popper=t()}(this,function(){"use strict";function i(e){return e&&"[object Function]"==={}.toString.call(e)}function d(e,t){if(1!==e.nodeType)return[];e=e.ownerDocument.defaultView.getComputedStyle(e,null);return t?e[t]:e}function l(e){return"HTML"===e.nodeName?e:e.parentNode||e.host}function c(e){if(!e)return document.body;switch(e.nodeName){case"HTML":case"BODY":return e.ownerDocument.body;case"#document":return e.body}var t=d(e),n=t.overflow,r=t.overflowX,t=t.overflowY;return/(auto|scroll|overlay)/.test(n+t+r)?e:c(l(e))}function f(e){return e&&e.referenceNode?e.referenceNode:e}function h(e){return 11===e?H:10!==e&&H||z}function g(e){if(!e)return document.documentElement;for(var t=h(10)?document.body:null,n=e.offsetParent||null;n===t&&e.nextElementSibling;)n=(e=e.nextElementSibling).offsetParent;var r=n&&n.nodeName;return r&&"BODY"!==r&&"HTML"!==r?-1!==["TH","TD","TABLE"].indexOf(n.nodeName)&&"static"===d(n,"position")?g(n):n:(e?e.ownerDocument:document).documentElement}function o(e){return null===e.parentNode?e:o(e.parentNode)}function p(e,t){if(!(e&&e.nodeType&&t&&t.nodeType))return document.documentElement;var n=e.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_FOLLOWING,r=n?e:t,i=n?t:e,n=document.createRange();n.setStart(r,0),n.setEnd(i,0);n=n.commonAncestorContainer;if(e!==n&&t!==n||r.contains(i))return"BODY"===(i=(r=n).nodeName)||"HTML"!==i&&g(r.firstElementChild)!==r?g(n):n;n=o(e);return n.host?p(n.host,t):p(e,o(t).host)}function m(e,t){var n="top"===(1<arguments.length&&void 0!==t?t:"top")?"scrollTop":"scrollLeft",t=e.nodeName;if("BODY"!==t&&"HTML"!==t)return e[n];t=e.ownerDocument.documentElement;return(e.ownerDocument.scrollingElement||t)[n]}function u(e,t){var n="x"===t?"Left":"Top",t="Left"==n?"Right":"Bottom";return parseFloat(e["border"+n+"Width"])+parseFloat(e["border"+t+"Width"])}function r(e,t,n,r){return B(t["offset"+e],t["scroll"+e],n["client"+e],n["offset"+e],n["scroll"+e],h(10)?parseInt(n["offset"+e])+parseInt(r["margin"+("Height"===e?"Top":"Left")])+parseInt(r["margin"+("Height"===e?"Bottom":"Right")]):0)}function y(e){var t=e.body,n=e.documentElement,e=h(10)&&getComputedStyle(n);return{height:r("Height",t,n,e),width:r("Width",t,n,e)}}function v(e){return $({},e,{right:e.left+e.width,bottom:e.top+e.height})}function _(e){var t,n,r={};try{h(10)?(r=e.getBoundingClientRect(),t=m(e,"top"),n=m(e,"left"),r.top+=t,r.left+=n,r.bottom+=t,r.right+=n):r=e.getBoundingClientRect()}catch(e){}var i={left:r.left,top:r.top,width:r.right-r.left,height:r.bottom-r.top},o="HTML"===e.nodeName?y(e.ownerDocument):{},a=o.width||e.clientWidth||i.width,s=o.height||e.clientHeight||i.height,o=e.offsetWidth-a,a=e.offsetHeight-s;return(o||a)&&(o-=u(s=d(e),"x"),a-=u(s,"y"),i.width-=o,i.height-=a),v(i)}function b(e,t,n){var r=2<arguments.length&&void 0!==n&&n,i=h(10),o="HTML"===t.nodeName,a=_(e),s=_(t),u=c(e),l=d(t),n=parseFloat(l.borderTopWidth),e=parseFloat(l.borderLeftWidth);r&&o&&(s.top=B(s.top,0),s.left=B(s.left,0));a=v({top:a.top-s.top-n,left:a.left-s.left-e,width:a.width,height:a.height});return a.marginTop=0,a.marginLeft=0,!i&&o&&(o=parseFloat(l.marginTop),l=parseFloat(l.marginLeft),a.top-=n-o,a.bottom-=n-o,a.left-=e-l,a.right-=e-l,a.marginTop=o,a.marginLeft=l),(i&&!r?t.contains(u):t===u&&"BODY"!==u.nodeName)&&(a=function(e,t,n){var r=2<arguments.length&&void 0!==n&&n,n=m(t,"top"),t=m(t,"left"),r=r?-1:1;return e.top+=n*r,e.bottom+=n*r,e.left+=t*r,e.right+=t*r,e}(a,t)),a}function w(e){if(!e||!e.parentElement||h())return document.documentElement;for(var t=e.parentElement;t&&"none"===d(t,"transform");)t=t.parentElement;return t||document.documentElement}function x(e,t,n,r,i){var o,a=4<arguments.length&&void 0!==i&&i,s={top:0,left:0},i=a?w(e):p(e,f(t));"viewport"===r?s=function(e,t){var n=1<arguments.length&&void 0!==t&&t,r=e.ownerDocument.documentElement,i=b(e,r),o=B(r.clientWidth,window.innerWidth||0),t=B(r.clientHeight,window.innerHeight||0),e=n?0:m(r),r=n?0:m(r,"left");return v({top:e-i.top+i.marginTop,left:r-i.left+i.marginLeft,width:o,height:t})}(i,a):("scrollParent"===r?"BODY"===(o=c(l(t))).nodeName&&(o=e.ownerDocument.documentElement):o="window"===r?e.ownerDocument.documentElement:r,u=b(o,i,a),"HTML"!==o.nodeName||function e(t){var n=t.nodeName;if("BODY"===n||"HTML"===n)return!1;if("fixed"===d(t,"position"))return!0;t=l(t);return!!t&&e(t)}(i)?s=u:(e=(i=y(e.ownerDocument)).height,i=i.width,s.top+=u.top-u.marginTop,s.bottom=e+u.top,s.left+=u.left-u.marginLeft,s.right=i+u.left));var u="number"==typeof(n=n||0);return s.left+=u?n:n.left||0,s.top+=u?n:n.top||0,s.right-=u?n:n.right||0,s.bottom-=u?n:n.bottom||0,s}function a(e,t,n,r,i,o){o=5<arguments.length&&void 0!==o?o:0;if(-1===e.indexOf("auto"))return e;var i=x(n,r,o,i),a={top:{width:i.width,height:t.top-i.top},right:{width:i.right-t.right,height:i.height},bottom:{width:i.width,height:i.bottom-t.bottom},left:{width:t.left-i.left,height:i.height}},t=Object.keys(a).map(function(e){return $({key:e},a[e],{area:(e=a[e]).width*e.height})}).sort(function(e,t){return t.area-e.area}),i=t.filter(function(e){var t=e.width,e=e.height;return t>=n.clientWidth&&e>=n.clientHeight}),t=(0<i.length?i:t)[0].key,e=e.split("-")[1];return t+(e?"-"+e:"")}function s(e,t,n,r){r=3<arguments.length&&void 0!==r?r:null;return b(n,r?w(t):p(t,f(n)),r)}function S(e){var t=e.ownerDocument.defaultView.getComputedStyle(e),n=parseFloat(t.marginTop||0)+parseFloat(t.marginBottom||0),t=parseFloat(t.marginLeft||0)+parseFloat(t.marginRight||0);return{width:e.offsetWidth+t,height:e.offsetHeight+n}}function T(e){var t={left:"right",right:"left",bottom:"top",top:"bottom"};return e.replace(/left|right|bottom|top/g,function(e){return t[e]})}function k(e,t,n){n=n.split("-")[0];var r=S(e),i={width:r.width,height:r.height},o=-1!==["right","left"].indexOf(n),a=o?"top":"left",s=o?"left":"top",e=o?"height":"width",o=o?"width":"height";return i[a]=t[a]+t[e]/2-r[e]/2,i[s]=n===s?t[s]-r[o]:t[T(s)],i}function E(e,t){return Array.prototype.find?e.find(t):e.filter(t)[0]}function C(e,n,t){return(void 0===t?e:e.slice(0,function(e,t,n){if(Array.prototype.findIndex)return e.findIndex(function(e){return e[t]===n});var r=E(e,function(e){return e[t]===n});return e.indexOf(r)}(e,"name",t))).forEach(function(e){e.function;var t=e.function||e.fn;e.enabled&&i(t)&&(n.offsets.popper=v(n.offsets.popper),n.offsets.reference=v(n.offsets.reference),n=t(n,e))}),n}function e(e,n){return e.some(function(e){var t=e.name;return e.enabled&&t===n})}function A(e){for(var t=[!1,"ms","Webkit","Moz","O"],n=e.charAt(0).toUpperCase()+e.slice(1),r=0;r<t.length;r++){var i=t[r],i=i?""+i+n:e;if(void 0!==document.body.style[i])return i}return null}function D(e){e=e.ownerDocument;return e?e.defaultView:window}function t(e,t,n,r){n.updateBound=r,D(e).addEventListener("resize",n.updateBound,{passive:!0});e=c(e);return function e(t,n,r,i){var o="BODY"===t.nodeName,t=o?t.ownerDocument.defaultView:t;t.addEventListener(n,r,{passive:!0}),o||e(c(t.parentNode),n,r,i),i.push(t)}(e,"scroll",n.updateBound,n.scrollParents),n.scrollElement=e,n.eventsEnabled=!0,n}function n(){var e,t;this.state.eventsEnabled&&(cancelAnimationFrame(this.scheduleUpdate),this.state=(e=this.reference,t=this.state,D(e).removeEventListener("resize",t.updateBound),t.scrollParents.forEach(function(e){e.removeEventListener("scroll",t.updateBound)}),t.updateBound=null,t.scrollParents=[],t.scrollElement=null,t.eventsEnabled=!1,t))}function I(e){return""!==e&&!isNaN(parseFloat(e))&&isFinite(e)}function R(n,r){Object.keys(r).forEach(function(e){var t="";-1!==["width","height","top","right","bottom","left"].indexOf(e)&&I(r[e])&&(t="px"),n.style[e]=r[e]+t})}function O(e,t,n){var r=E(e,function(e){return e.name===t}),e=!!r&&e.some(function(e){return e.name===n&&e.enabled&&e.order<r.order});return e||0,e}function P(e,t){t=1<arguments.length&&void 0!==t&&t,e=Q.indexOf(e),e=Q.slice(e+1).concat(Q.slice(0,e));return t?e.reverse():e}function L(e,i,o,t){var a=[0,0],s=-1!==["right","left"].indexOf(t),n=e.split(/(\+|\-)/).map(function(e){return e.trim()}),t=n.indexOf(E(n,function(e){return-1!==e.search(/,|\s/)}));n[t]&&n[t].indexOf(",");e=/\s*,\s*|\s+/;return(-1===t?[n]:[n.slice(0,t).concat([n[t].split(e)[0]]),[n[t].split(e)[1]].concat(n.slice(t+1))]).map(function(e,t){var n=(1===t?!s:s)?"height":"width",r=!1;return e.reduce(function(e,t){return""===e[e.length-1]&&-1!==["+","-"].indexOf(t)?(e[e.length-1]=t,r=!0,e):r?(e[e.length-1]+=t,r=!1,e):e.concat(t)},[]).map(function(e){return function(e,t,n,r){var i,o=+(a=e.match(/((?:\-|\+)?\d*\.?\d*)(.*)/))[1],a=a[2];if(!o)return e;if(0!==a.indexOf("%"))return"vh"!==a&&"vw"!==a?o:("vh"===a?B(document.documentElement.clientHeight,window.innerHeight||0):B(document.documentElement.clientWidth,window.innerWidth||0))/100*o;switch(a){case"%p":i=n;break;case"%":case"%r":default:i=r}return v(i)[t]/100*o}(e,n,i,o)})}).forEach(function(n,r){n.forEach(function(e,t){I(e)&&(a[r]+=e*("-"===n[t-1]?-1:1))})}),a}function F(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var U=Math.min,M=Math.floor,N=Math.round,B=Math.max,j="undefined"!=typeof window&&"undefined"!=typeof document&&"undefined"!=typeof navigator,q=function(){for(var e=["Edge","Trident","Firefox"],t=0;t<e.length;t+=1)if(j&&0<=navigator.userAgent.indexOf(e[t]))return 1;return 0}(),G=j&&window.Promise?function(e){var t=!1;return function(){t||(t=!0,window.Promise.resolve().then(function(){t=!1,e()}))}}:function(e){var t=!1;return function(){t||(t=!0,setTimeout(function(){t=!1,e()},q))}},H=j&&!(!window.MSInputMethodContext||!document.documentMode),z=j&&/MSIE 10/.test(navigator.userAgent),W=function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},V=function(e,t,n){return t&&te(e.prototype,t),n&&te(e,n),e},$=Object.assign||function(e){for(var t,n=1;n<arguments.length;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},Y=j&&/Firefox/i.test(navigator.userAgent),X=["auto-start","auto","auto-end","top-start","top","top-end","right-start","right","right-end","bottom-end","bottom","bottom-start","left-end","left","left-start"],Q=X.slice(3),K="flip",J="clockwise",Z="counterclockwise",V=(V(ee,[{key:"update",value:function(){return function(){var e;this.state.isDestroyed||((e={instance:this,styles:{},arrowStyles:{},attributes:{},flipped:!1,offsets:{}}).offsets.reference=s(this.state,this.popper,this.reference,this.options.positionFixed),e.placement=a(this.options.placement,e.offsets.reference,this.popper,this.reference,this.options.modifiers.flip.boundariesElement,this.options.modifiers.flip.padding),e.originalPlacement=e.placement,e.positionFixed=this.options.positionFixed,e.offsets.popper=k(this.popper,e.offsets.reference,e.placement),e.offsets.popper.position=this.options.positionFixed?"fixed":"absolute",e=C(this.modifiers,e),this.state.isCreated?this.options.onUpdate(e):(this.state.isCreated=!0,this.options.onCreate(e)))}.call(this)}},{key:"destroy",value:function(){return function(){return this.state.isDestroyed=!0,e(this.modifiers,"applyStyle")&&(this.popper.removeAttribute("x-placement"),this.popper.style.position="",this.popper.style.top="",this.popper.style.left="",this.popper.style.right="",this.popper.style.bottom="",this.popper.style.willChange="",this.popper.style[A("transform")]=""),this.disableEventListeners(),this.options.removeOnDestroy&&this.popper.parentNode.removeChild(this.popper),this}.call(this)}},{key:"enableEventListeners",value:function(){return function(){this.state.eventsEnabled||(this.state=t(this.reference,this.options,this.state,this.scheduleUpdate))}.call(this)}},{key:"disableEventListeners",value:function(){return n.call(this)}}]),ee);function ee(e,t){var n=this,r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{};W(this,ee),this.scheduleUpdate=function(){return requestAnimationFrame(n.update)},this.update=G(this.update.bind(this)),this.options=$({},ee.Defaults,r),this.state={isDestroyed:!1,isCreated:!1,scrollParents:[]},this.reference=e&&e.jquery?e[0]:e,this.popper=t&&t.jquery?t[0]:t,this.options.modifiers={},Object.keys($({},ee.Defaults.modifiers,r.modifiers)).forEach(function(e){n.options.modifiers[e]=$({},ee.Defaults.modifiers[e]||{},r.modifiers?r.modifiers[e]:{})}),this.modifiers=Object.keys(this.options.modifiers).map(function(e){return $({name:e},n.options.modifiers[e])}).sort(function(e,t){return e.order-t.order}),this.modifiers.forEach(function(e){e.enabled&&i(e.onLoad)&&e.onLoad(n.reference,n.popper,n.options,e,n.state)}),this.update();t=this.options.eventsEnabled;t&&this.enableEventListeners(),this.state.eventsEnabled=t}function te(e,t){for(var n,r=0;r<t.length;r++)(n=t[r]).enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}return V.Utils=("undefined"==typeof window?global:window).PopperUtils,V.placements=X,V.Defaults={placement:"bottom",positionFixed:!1,eventsEnabled:!0,removeOnDestroy:!1,onCreate:function(){},onUpdate:function(){},modifiers:{shift:{order:100,enabled:!0,fn:function(e){var t,n,r=e.placement,i=r.split("-")[0],o=r.split("-")[1];return o&&(t=(n=e.offsets).reference,r=n.popper,i=(n=-1!==["bottom","top"].indexOf(i))?"width":"height",i={start:F({},n=n?"left":"top",t[n]),end:F({},n,t[n]+t[i]-r[i])},e.offsets.popper=$({},r,i[o])),e}},offset:{order:200,enabled:!0,fn:function(e,t){var n=t.offset,r=e.placement,t=(i=e.offsets).popper,i=i.reference,r=r.split("-")[0],i=I(+n)?[+n,0]:L(n,t,i,r);return"left"===r?(t.top+=i[0],t.left-=i[1]):"right"===r?(t.top+=i[0],t.left+=i[1]):"top"===r?(t.left+=i[0],t.top-=i[1]):"bottom"===r&&(t.left+=i[0],t.top+=i[1]),e.popper=t,e},offset:0},preventOverflow:{order:300,enabled:!0,fn:function(e,r){var t=r.boundariesElement||g(e.instance.popper);e.instance.reference===t&&(t=g(t));var n=A("transform"),i=e.instance.popper.style,o=i.top,a=i.left,s=i[n];i.top="",i.left="",i[n]="";var u=x(e.instance.popper,e.instance.reference,r.padding,t,e.positionFixed);i.top=o,i.left=a,i[n]=s,r.boundaries=u;var s=r.priority,l=e.offsets.popper,c={primary:function(e){var t=l[e];return l[e]<u[e]&&!r.escapeWithReference&&(t=B(l[e],u[e])),F({},e,t)},secondary:function(e){var t="right"===e?"left":"top",n=l[t];return l[e]>u[e]&&!r.escapeWithReference&&(n=U(l[t],u[e]-("right"===e?l.width:l.height))),F({},t,n)}};return s.forEach(function(e){var t=-1===["left","top"].indexOf(e)?"secondary":"primary";l=$({},l,c[t](e))}),e.offsets.popper=l,e},priority:["left","right","top","bottom"],padding:5,boundariesElement:"scrollParent"},keepTogether:{order:400,enabled:!0,fn:function(e){var t=e.offsets,n=t.popper,r=t.reference,i=e.placement.split("-")[0],o=M,a=-1!==["top","bottom"].indexOf(i),t=a?"right":"bottom",i=a?"left":"top",a=a?"width":"height";return n[t]<o(r[i])&&(e.offsets.popper[i]=o(r[i])-n[a]),n[i]>o(r[t])&&(e.offsets.popper[i]=o(r[t])),e}},arrow:{order:500,enabled:!0,fn:function(e,t){if(!O(e.instance.modifiers,"arrow","keepTogether"))return e;var n=t.element;if("string"==typeof n){if(!(n=e.instance.popper.querySelector(n)))return e}else if(!e.instance.popper.contains(n))return e;var r=e.placement.split("-")[0],i=e.offsets,o=i.popper,a=i.reference,s=-1!==["left","right"].indexOf(r),u=s?"height":"width",l=s?"Top":"Left",c=l.toLowerCase(),t=s?"left":"top",i=s?"bottom":"right",r=S(n)[u];a[i]-r<o[c]&&(e.offsets.popper[c]-=o[c]-(a[i]-r)),a[c]+r>o[i]&&(e.offsets.popper[c]+=a[c]+r-o[i]),e.offsets.popper=v(e.offsets.popper);s=a[c]+a[u]/2-r/2,i=d(e.instance.popper),a=parseFloat(i["margin"+l]),l=parseFloat(i["border"+l+"Width"]),l=s-e.offsets.popper[c]-a-l,l=B(U(o[u]-r,l),0);return e.arrowElement=n,e.offsets.arrow=(F(n={},c,N(l)),F(n,t,""),n),e},element:"[x-arrow]"},flip:{order:600,enabled:!0,fn:function(l,c){if(e(l.instance.modifiers,"inner"))return l;if(l.flipped&&l.placement===l.originalPlacement)return l;var d=x(l.instance.popper,l.instance.reference,c.padding,c.boundariesElement,l.positionFixed),f=l.placement.split("-")[0],h=T(f),p=l.placement.split("-")[1]||"",g=[];switch(c.behavior){case K:g=[f,h];break;case J:g=P(f);break;case Z:g=P(f,!0);break;default:g=c.behavior}return g.forEach(function(e,t){if(f!==e||g.length===t+1)return l;f=l.placement.split("-")[0],h=T(f);var n=l.offsets.popper,r=l.offsets.reference,i=M,o="left"===f&&i(n.right)>i(r.left)||"right"===f&&i(n.left)<i(r.right)||"top"===f&&i(n.bottom)>i(r.top)||"bottom"===f&&i(n.top)<i(r.bottom),a=i(n.left)<i(d.left),s=i(n.right)>i(d.right),u=i(n.top)<i(d.top),e=i(n.bottom)>i(d.bottom),r="left"===f&&a||"right"===f&&s||"top"===f&&u||"bottom"===f&&e,n=-1!==["top","bottom"].indexOf(f),i=!!c.flipVariations&&(n&&"start"===p&&a||n&&"end"===p&&s||!n&&"start"===p&&u||!n&&"end"===p&&e),u=!!c.flipVariationsByContent&&(n&&"start"===p&&s||n&&"end"===p&&a||!n&&"start"===p&&e||!n&&"end"===p&&u),u=i||u;(o||r||u)&&(l.flipped=!0,(o||r)&&(f=g[t+1]),u&&(p="end"===(u=p)?"start":"start"===u?"end":u),l.placement=f+(p?"-"+p:""),l.offsets.popper=$({},l.offsets.popper,k(l.instance.popper,l.offsets.reference,l.placement)),l=C(l.instance.modifiers,l,"flip"))}),l},behavior:"flip",padding:5,boundariesElement:"viewport",flipVariations:!1,flipVariationsByContent:!1},inner:{order:700,enabled:!1,fn:function(e){var t=e.placement,n=t.split("-")[0],r=e.offsets,i=r.popper,o=r.reference,a=-1!==["left","right"].indexOf(n),r=-1===["top","left"].indexOf(n);return i[a?"left":"top"]=o[n]-(r?i[a?"width":"height"]:0),e.placement=T(t),e.offsets.popper=v(i),e}},hide:{order:800,enabled:!0,fn:function(e){if(!O(e.instance.modifiers,"hide","preventOverflow"))return e;var t=e.offsets.reference,n=E(e.instance.modifiers,function(e){return"preventOverflow"===e.name}).boundaries;if(t.bottom<n.top||t.left>n.right||t.top>n.bottom||t.right<n.left){if(!0===e.hide)return e;e.hide=!0,e.attributes["x-out-of-boundaries"]=""}else{if(!1===e.hide)return e;e.hide=!1,e.attributes["x-out-of-boundaries"]=!1}return e}},computeStyle:{order:850,enabled:!0,fn:function(e,t){var n,r,i,o,a=t.x,s=t.y,u=e.offsets.popper,l=E(e.instance.modifiers,function(e){return"applyStyle"===e.name}).gpuAcceleration,c=void 0===l?t.gpuAcceleration:l,d=g(e.instance.popper),f=_(d),h={position:u.position},t=(n=e,r=window.devicePixelRatio<2||!Y,p=(o=n.offsets).popper,i=o.reference,t=function(e){return e},u=(l=N)(i.width),o=l(p.width),i=-1!==["left","right"].indexOf(n.placement),n=-1!==n.placement.indexOf("-"),i=r?i||n||u%2==o%2?l:M:t,t=r?l:t,{left:i(1==u%2&&1==o%2&&!n&&r?p.left-1:p.left),top:t(p.top),bottom:t(p.bottom),right:i(p.right)}),i="bottom"===a?"top":"bottom",p="right"===s?"left":"right",a=A("transform"),s="bottom"==i?"HTML"===d.nodeName?-d.clientHeight+t.bottom:-f.height+t.bottom:t.top,t="right"==p?"HTML"===d.nodeName?-d.clientWidth+t.right:-f.width+t.right:t.left;c&&a?(h[a]="translate3d("+t+"px, "+s+"px, 0)",h[i]=0,h[p]=0,h.willChange="transform"):(c="bottom"==i?-1:1,a="right"==p?-1:1,h[i]=s*c,h[p]=t*a,h.willChange=i+", "+p);var p={"x-placement":e.placement};return e.attributes=$({},p,e.attributes),e.styles=$({},h,e.styles),e.arrowStyles=$({},e.offsets.arrow,e.arrowStyles),e},gpuAcceleration:!0,x:"bottom",y:"right"},applyStyle:{order:900,enabled:!0,fn:function(e){return R(e.instance.popper,e.styles),t=e.instance.popper,n=e.attributes,Object.keys(n).forEach(function(e){!1===n[e]?t.removeAttribute(e):t.setAttribute(e,n[e])}),e.arrowElement&&Object.keys(e.arrowStyles).length&&R(e.arrowElement,e.arrowStyles),e;var t,n},onLoad:function(e,t,n,r,i){i=s(i,t,e,n.positionFixed),e=a(n.placement,i,t,e,n.modifiers.flip.boundariesElement,n.modifiers.flip.padding);return t.setAttribute("x-placement",e),R(t,{position:n.positionFixed?"fixed":"absolute"}),n},gpuAcceleration:void 0}}},V}),function(e,t){"object"==("undefined"==typeof exports?"undefined":_typeof(exports))&&"undefined"!=typeof module?t(exports,require("jquery"),require("popper.js")):"function"==typeof define&&define.amd?define(["exports","jquery","popper.js"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).bootstrap={},e.jQuery,e.Popper)}(this,function(e,t,n){"use strict";function r(e){return e&&"object"==_typeof(e)&&"default"in e?e:{default:e}}var c=r(t),i=r(n);function o(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function a(e,t,n){return t&&o(e.prototype,t),n&&o(e,n),e}function s(){return(s=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n,r=arguments[t];for(n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var d={TRANSITION_END:"bsTransitionEnd",getUID:function(e){for(;e+=~~(1e6*Math.random()),document.getElementById(e););return e},getSelectorFromElement:function(e){var t,n=e.getAttribute("data-target");n&&"#"!==n||(n=(t=e.getAttribute("href"))&&"#"!==t?t.trim():"");try{return document.querySelector(n)?n:null}catch(e){return null}},getTransitionDurationFromElement:function(e){if(!e)return 0;var t=c.default(e).css("transition-duration"),n=c.default(e).css("transition-delay"),r=parseFloat(t),e=parseFloat(n);return r||e?(t=t.split(",")[0],n=n.split(",")[0],1e3*(parseFloat(t)+parseFloat(n))):0},reflow:function(e){return e.offsetHeight},triggerTransitionEnd:function(e){c.default(e).trigger("transitionend")},supportsTransitionEnd:function(){return Boolean("transitionend")},isElement:function(e){return(e[0]||e).nodeType},typeCheckConfig:function(e,t,n){for(var r in n)if(Object.prototype.hasOwnProperty.call(n,r)){var i=n[r],o=t[r],o=o&&d.isElement(o)?"element":null===o||void 0===o?""+o:{}.toString.call(o).match(/\s([a-z]+)/i)[1].toLowerCase();if(!new RegExp(i).test(o))throw new Error(e.toUpperCase()+': Option "'+r+'" provided type "'+o+'" but expected type "'+i+'".')}},findShadowRoot:function(e){if(!document.documentElement.attachShadow)return null;if("function"!=typeof e.getRootNode)return e instanceof ShadowRoot?e:e.parentNode?d.findShadowRoot(e.parentNode):null;e=e.getRootNode();return e instanceof ShadowRoot?e:null},jQueryDetection:function(){if(void 0===c.default)throw new TypeError("Bootstrap's JavaScript requires jQuery. jQuery must be included before Bootstrap's JavaScript.");var e=c.default.fn.jquery.split(" ")[0].split(".");if(e[0]<2&&e[1]<9||1===e[0]&&9===e[1]&&e[2]<1||4<=e[0])throw new Error("Bootstrap's JavaScript requires at least jQuery v1.9.1 but less than v4.0.0")}};d.jQueryDetection(),c.default.fn.emulateTransitionEnd=function(e){var t=this,n=!1;return c.default(this).one(d.TRANSITION_END,function(){n=!0}),setTimeout(function(){n||d.triggerTransitionEnd(t)},e),this},c.default.event.special[d.TRANSITION_END]={bindType:"transitionend",delegateType:"transitionend",handle:function(e){if(c.default(e.target).is(this))return e.handleObj.handler.apply(this,arguments)}};var u="alert",l=c.default.fn[u],f=((n=h.prototype).close=function(e){var t=this._element;e&&(t=this._getRootElement(e)),this._triggerCloseEvent(t).isDefaultPrevented()||this._removeElement(t)},n.dispose=function(){c.default.removeData(this._element,"bs.alert"),this._element=null},n._getRootElement=function(e){var t=d.getSelectorFromElement(e),n=!1;return t&&(n=document.querySelector(t)),n=n||c.default(e).closest(".alert")[0]},n._triggerCloseEvent=function(e){var t=c.default.Event("close.bs.alert");return c.default(e).trigger(t),t},n._removeElement=function(t){var e,n=this;c.default(t).removeClass("show"),c.default(t).hasClass("fade")?(e=d.getTransitionDurationFromElement(t),c.default(t).one(d.TRANSITION_END,function(e){return n._destroyElement(t,e)}).emulateTransitionEnd(e)):this._destroyElement(t)},n._destroyElement=function(e){c.default(e).detach().trigger("closed.bs.alert").remove()},h._jQueryInterface=function(n){return this.each(function(){var e=c.default(this),t=e.data("bs.alert");t||(t=new h(this),e.data("bs.alert",t)),"close"===n&&t[n](this)})},h._handleDismiss=function(t){return function(e){e&&e.preventDefault(),t.close(this)}},a(h,null,[{key:"VERSION",get:function(){return"4.5.3"}}]),h);function h(e){this._element=e}c.default(document).on("click.bs.alert.data-api",'[data-dismiss="alert"]',f._handleDismiss(new f)),c.default.fn[u]=f._jQueryInterface,c.default.fn[u].Constructor=f,c.default.fn[u].noConflict=function(){return c.default.fn[u]=l,f._jQueryInterface};var p=c.default.fn.button,g=((n=m.prototype).toggle=function(){var e,t=!0,n=!0,r=c.default(this._element).closest('[data-toggle="buttons"]')[0];!r||(e=this._element.querySelector('input:not([type="hidden"])'))&&("radio"===e.type&&(e.checked&&this._element.classList.contains("active")?t=!1:(r=r.querySelector(".active"))&&c.default(r).removeClass("active")),t&&("checkbox"!==e.type&&"radio"!==e.type||(e.checked=!this._element.classList.contains("active")),this.shouldAvoidTriggerChange||c.default(e).trigger("change")),e.focus(),n=!1),this._element.hasAttribute("disabled")||this._element.classList.contains("disabled")||(n&&this._element.setAttribute("aria-pressed",!this._element.classList.contains("active")),t&&c.default(this._element).toggleClass("active"))},n.dispose=function(){c.default.removeData(this._element,"bs.button"),this._element=null},m._jQueryInterface=function(n,r){return this.each(function(){var e=c.default(this),t=e.data("bs.button");t||(t=new m(this),e.data("bs.button",t)),t.shouldAvoidTriggerChange=r,"toggle"===n&&t[n]()})},a(m,null,[{key:"VERSION",get:function(){return"4.5.3"}}]),m);function m(e){this._element=e,this.shouldAvoidTriggerChange=!1}c.default(document).on("click.bs.button.data-api",'[data-toggle^="button"]',function(e){var t,n=e.target,r=n;c.default(n).hasClass("btn")||(n=c.default(n).closest(".btn")[0]),!n||n.hasAttribute("disabled")||n.classList.contains("disabled")||(t=n.querySelector('input:not([type="hidden"])'))&&(t.hasAttribute("disabled")||t.classList.contains("disabled"))?e.preventDefault():"INPUT"!==r.tagName&&"LABEL"===n.tagName||g._jQueryInterface.call(c.default(n),"toggle","INPUT"===r.tagName)}).on("focus.bs.button.data-api blur.bs.button.data-api",'[data-toggle^="button"]',function(e){var t=c.default(e.target).closest(".btn")[0];c.default(t).toggleClass("focus",/^focus(in)?$/.test(e.type))}),c.default(window).on("load.bs.button.data-api",function(){for(var e=[].slice.call(document.querySelectorAll('[data-toggle="buttons"] .btn')),t=0,n=e.length;t<n;t++){var r=e[t],i=r.querySelector('input:not([type="hidden"])');i.checked||i.hasAttribute("checked")?r.classList.add("active"):r.classList.remove("active")}for(var o=0,a=(e=[].slice.call(document.querySelectorAll('[data-toggle="button"]'))).length;o<a;o++){var s=e[o];"true"===s.getAttribute("aria-pressed")?s.classList.add("active"):s.classList.remove("active")}}),c.default.fn.button=g._jQueryInterface,c.default.fn.button.Constructor=g,c.default.fn.button.noConflict=function(){return c.default.fn.button=p,g._jQueryInterface};var y="carousel",v=c.default.fn[y],_={interval:5e3,keyboard:!0,slide:!1,pause:"hover",wrap:!0,touch:!0},b={interval:"(number|boolean)",keyboard:"boolean",slide:"(boolean|string)",pause:"(string|boolean)",wrap:"boolean",touch:"boolean"},w={TOUCH:"touch",PEN:"pen"},x=((n=S.prototype).next=function(){this._isSliding||this._slide("next")},n.nextWhenVisible=function(){var e=c.default(this._element);!document.hidden&&e.is(":visible")&&"hidden"!==e.css("visibility")&&this.next()},n.prev=function(){this._isSliding||this._slide("prev")},n.pause=function(e){e||(this._isPaused=!0),this._element.querySelector(".carousel-item-next, .carousel-item-prev")&&(d.triggerTransitionEnd(this._element),this.cycle(!0)),clearInterval(this._interval),this._interval=null},n.cycle=function(e){e||(this._isPaused=!1),this._interval&&(clearInterval(this._interval),this._interval=null),this._config.interval&&!this._isPaused&&(this._interval=setInterval((document.visibilityState?this.nextWhenVisible:this.next).bind(this),this._config.interval))},n.to=function(e){var t=this;this._activeElement=this._element.querySelector(".active.carousel-item");var n=this._getItemIndex(this._activeElement);if(!(e>this._items.length-1||e<0))if(this._isSliding)c.default(this._element).one("slid.bs.carousel",function(){return t.to(e)});else{if(n===e)return this.pause(),void this.cycle();n=n<e?"next":"prev";this._slide(n,this._items[e])}},n.dispose=function(){c.default(this._element).off(".bs.carousel"),c.default.removeData(this._element,"bs.carousel"),this._items=null,this._config=null,this._element=null,this._interval=null,this._isPaused=null,this._isSliding=null,this._activeElement=null,this._indicatorsElement=null},n._getConfig=function(e){return e=s({},_,e),d.typeCheckConfig(y,e,b),e},n._handleSwipe=function(){var e=Math.abs(this.touchDeltaX);e<=40||(e=e/this.touchDeltaX,(this.touchDeltaX=0)<e&&this.prev(),e<0&&this.next())},n._addEventListeners=function(){var t=this;this._config.keyboard&&c.default(this._element).on("keydown.bs.carousel",function(e){return t._keydown(e)}),"hover"===this._config.pause&&c.default(this._element).on("mouseenter.bs.carousel",function(e){return t.pause(e)}).on("mouseleave.bs.carousel",function(e){return t.cycle(e)}),this._config.touch&&this._addTouchEventListeners()},n._addTouchEventListeners=function(){var e,t,n=this;this._touchSupported&&(e=function(e){n._pointerEvent&&w[e.originalEvent.pointerType.toUpperCase()]?n.touchStartX=e.originalEvent.clientX:n._pointerEvent||(n.touchStartX=e.originalEvent.touches[0].clientX)},t=function(e){n._pointerEvent&&w[e.originalEvent.pointerType.toUpperCase()]&&(n.touchDeltaX=e.originalEvent.clientX-n.touchStartX),n._handleSwipe(),"hover"===n._config.pause&&(n.pause(),n.touchTimeout&&clearTimeout(n.touchTimeout),n.touchTimeout=setTimeout(function(e){return n.cycle(e)},500+n._config.interval))},c.default(this._element.querySelectorAll(".carousel-item img")).on("dragstart.bs.carousel",function(e){return e.preventDefault()}),this._pointerEvent?(c.default(this._element).on("pointerdown.bs.carousel",e),c.default(this._element).on("pointerup.bs.carousel",t),this._element.classList.add("pointer-event")):(c.default(this._element).on("touchstart.bs.carousel",e),c.default(this._element).on("touchmove.bs.carousel",function(e){(e=e).originalEvent.touches&&1<e.originalEvent.touches.length?n.touchDeltaX=0:n.touchDeltaX=e.originalEvent.touches[0].clientX-n.touchStartX}),c.default(this._element).on("touchend.bs.carousel",t)))},n._keydown=function(e){if(!/input|textarea/i.test(e.target.tagName))switch(e.which){case 37:e.preventDefault(),this.prev();break;case 39:e.preventDefault(),this.next()}},n._getItemIndex=function(e){return this._items=e&&e.parentNode?[].slice.call(e.parentNode.querySelectorAll(".carousel-item")):[],this._items.indexOf(e)},n._getItemByDirection=function(e,t){var n="next"===e,r="prev"===e,i=this._getItemIndex(t),o=this._items.length-1;if((r&&0===i||n&&i===o)&&!this._config.wrap)return t;e=(i+("prev"===e?-1:1))%this._items.length;return-1==e?this._items[this._items.length-1]:this._items[e]},n._triggerSlideEvent=function(e,t){var n=this._getItemIndex(e),r=this._getItemIndex(this._element.querySelector(".active.carousel-item")),n=c.default.Event("slide.bs.carousel",{relatedTarget:e,direction:t,from:r,to:n});return c.default(this._element).trigger(n),n},n._setActiveIndicatorElement=function(e){var t;this._indicatorsElement&&(t=[].slice.call(this._indicatorsElement.querySelectorAll(".active")),c.default(t).removeClass("active"),(e=this._indicatorsElement.children[this._getItemIndex(e)])&&c.default(e).addClass("active"))},n._slide=function(e,t){var n,r,i,o=this,a=this._element.querySelector(".active.carousel-item"),s=this._getItemIndex(a),u=t||a&&this._getItemByDirection(e,a),l=this._getItemIndex(u),t=Boolean(this._interval),e="next"===e?(n="carousel-item-left",r="carousel-item-next","left"):(n="carousel-item-right",r="carousel-item-prev","right");u&&c.default(u).hasClass("active")?this._isSliding=!1:!this._triggerSlideEvent(u,e).isDefaultPrevented()&&a&&u&&(this._isSliding=!0,t&&this.pause(),this._setActiveIndicatorElement(u),i=c.default.Event("slid.bs.carousel",{relatedTarget:u,direction:e,from:s,to:l}),c.default(this._element).hasClass("slide")?(c.default(u).addClass(r),d.reflow(u),c.default(a).addClass(n),c.default(u).addClass(n),(l=parseInt(u.getAttribute("data-interval"),10))?(this._config.defaultInterval=this._config.defaultInterval||this._config.interval,this._config.interval=l):this._config.interval=this._config.defaultInterval||this._config.interval,l=d.getTransitionDurationFromElement(a),c.default(a).one(d.TRANSITION_END,function(){c.default(u).removeClass(n+" "+r).addClass("active"),c.default(a).removeClass("active "+r+" "+n),o._isSliding=!1,setTimeout(function(){return c.default(o._element).trigger(i)},0)}).emulateTransitionEnd(l)):(c.default(a).removeClass("active"),c.default(u).addClass("active"),this._isSliding=!1,c.default(this._element).trigger(i)),t&&this.cycle())},S._jQueryInterface=function(r){return this.each(function(){var e=c.default(this).data("bs.carousel"),t=s({},_,c.default(this).data());"object"==_typeof(r)&&(t=s({},t,r));var n="string"==typeof r?r:t.slide;if(e||(e=new S(this,t),c.default(this).data("bs.carousel",e)),"number"==typeof r)e.to(r);else if("string"==typeof n){if(void 0===e[n])throw new TypeError('No method named "'+n+'"');e[n]()}else t.interval&&t.ride&&(e.pause(),e.cycle())})},S._dataApiClickHandler=function(e){var t,n,r=d.getSelectorFromElement(this);!r||(t=c.default(r)[0])&&c.default(t).hasClass("carousel")&&(n=s({},c.default(t).data(),c.default(this).data()),(r=this.getAttribute("data-slide-to"))&&(n.interval=!1),S._jQueryInterface.call(c.default(t),n),r&&c.default(t).data("bs.carousel").to(r),e.preventDefault())},a(S,null,[{key:"VERSION",get:function(){return"4.5.3"}},{key:"Default",get:function(){return _}}]),S);function S(e,t){this._items=null,this._interval=null,this._activeElement=null,this._isPaused=!1,this._isSliding=!1,this.touchTimeout=null,this.touchStartX=0,this.touchDeltaX=0,this._config=this._getConfig(t),this._element=e,this._indicatorsElement=this._element.querySelector(".carousel-indicators"),this._touchSupported="ontouchstart"in document.documentElement||0<navigator.maxTouchPoints,this._pointerEvent=Boolean(window.PointerEvent||window.MSPointerEvent),this._addEventListeners()}c.default(document).on("click.bs.carousel.data-api","[data-slide], [data-slide-to]",x._dataApiClickHandler),c.default(window).on("load.bs.carousel.data-api",function(){for(var e=[].slice.call(document.querySelectorAll('[data-ride="carousel"]')),t=0,n=e.length;t<n;t++){var r=c.default(e[t]);x._jQueryInterface.call(r,r.data())}}),c.default.fn[y]=x._jQueryInterface,c.default.fn[y].Constructor=x,c.default.fn[y].noConflict=function(){return c.default.fn[y]=v,x._jQueryInterface};var T="collapse",k=c.default.fn[T],E={toggle:!0,parent:""},C={toggle:"boolean",parent:"(string|element)"},A=((n=D.prototype).toggle=function(){c.default(this._element).hasClass("show")?this.hide():this.show()},n.show=function(){var e,t,n,r,i=this;this._isTransitioning||c.default(this._element).hasClass("show")||(this._parent&&0===(r=[].slice.call(this._parent.querySelectorAll(".show, .collapsing")).filter(function(e){return"string"==typeof i._config.parent?e.getAttribute("data-parent")===i._config.parent:e.classList.contains("collapse")})).length&&(r=null),r&&(n=c.default(r).not(this._selector).data("bs.collapse"))&&n._isTransitioning)||(e=c.default.Event("show.bs.collapse"),c.default(this._element).trigger(e),e.isDefaultPrevented()||(r&&(D._jQueryInterface.call(c.default(r).not(this._selector),"hide"),n||c.default(r).data("bs.collapse",null)),t=this._getDimension(),c.default(this._element).removeClass("collapse").addClass("collapsing"),this._element.style[t]=0,this._triggerArray.length&&c.default(this._triggerArray).removeClass("collapsed").attr("aria-expanded",!0),this.setTransitioning(!0),n="scroll"+(t[0].toUpperCase()+t.slice(1)),r=d.getTransitionDurationFromElement(this._element),c.default(this._element).one(d.TRANSITION_END,function(){c.default(i._element).removeClass("collapsing").addClass("collapse show"),i._element.style[t]="",i.setTransitioning(!1),c.default(i._element).trigger("shown.bs.collapse")}).emulateTransitionEnd(r),this._element.style[t]=this._element[n]+"px"))},n.hide=function(){var e=this;if(!this._isTransitioning&&c.default(this._element).hasClass("show")){var t=c.default.Event("hide.bs.collapse");if(c.default(this._element).trigger(t),!t.isDefaultPrevented()){t=this._getDimension();this._element.style[t]=this._element.getBoundingClientRect()[t]+"px",d.reflow(this._element),c.default(this._element).addClass("collapsing").removeClass("collapse show");var n=this._triggerArray.length;if(0<n)for(var r=0;r<n;r++){var i=this._triggerArray[r],o=d.getSelectorFromElement(i);null!==o&&(c.default([].slice.call(document.querySelectorAll(o))).hasClass("show")||c.default(i).addClass("collapsed").attr("aria-expanded",!1))}this.setTransitioning(!0),this._element.style[t]="";t=d.getTransitionDurationFromElement(this._element);c.default(this._element).one(d.TRANSITION_END,function(){e.setTransitioning(!1),c.default(e._element).removeClass("collapsing").addClass("collapse").trigger("hidden.bs.collapse")}).emulateTransitionEnd(t)}}},n.setTransitioning=function(e){this._isTransitioning=e},n.dispose=function(){c.default.removeData(this._element,"bs.collapse"),this._config=null,this._parent=null,this._element=null,this._triggerArray=null,this._isTransitioning=null},n._getConfig=function(e){return(e=s({},E,e)).toggle=Boolean(e.toggle),d.typeCheckConfig(T,e,C),e},n._getDimension=function(){return c.default(this._element).hasClass("width")?"width":"height"},n._getParent=function(){var e,n=this;d.isElement(this._config.parent)?(e=this._config.parent,void 0!==this._config.parent.jquery&&(e=this._config.parent[0])):e=document.querySelector(this._config.parent);var t='[data-toggle="collapse"][data-parent="'+this._config.parent+'"]',t=[].slice.call(e.querySelectorAll(t));return c.default(t).each(function(e,t){n._addAriaAndCollapsedClass(D._getTargetFromElement(t),[t])}),e},n._addAriaAndCollapsedClass=function(e,t){e=c.default(e).hasClass("show");t.length&&c.default(t).toggleClass("collapsed",!e).attr("aria-expanded",e)},D._getTargetFromElement=function(e){e=d.getSelectorFromElement(e);return e?document.querySelector(e):null},D._jQueryInterface=function(r){return this.each(function(){var e=c.default(this),t=e.data("bs.collapse"),n=s({},E,e.data(),"object"==_typeof(r)&&r?r:{});if(!t&&n.toggle&&"string"==typeof r&&/show|hide/.test(r)&&(n.toggle=!1),t||(t=new D(this,n),e.data("bs.collapse",t)),"string"==typeof r){if(void 0===t[r])throw new TypeError('No method named "'+r+'"');t[r]()}})},a(D,null,[{key:"VERSION",get:function(){return"4.5.3"}},{key:"Default",get:function(){return E}}]),D);function D(t,e){this._isTransitioning=!1,this._element=t,this._config=this._getConfig(e),this._triggerArray=[].slice.call(document.querySelectorAll('[data-toggle="collapse"][href="#'+t.id+'"],[data-toggle="collapse"][data-target="#'+t.id+'"]'));for(var n=[].slice.call(document.querySelectorAll('[data-toggle="collapse"]')),r=0,i=n.length;r<i;r++){var o=n[r],a=d.getSelectorFromElement(o),s=[].slice.call(document.querySelectorAll(a)).filter(function(e){return e===t});null!==a&&0<s.length&&(this._selector=a,this._triggerArray.push(o))}this._parent=this._config.parent?this._getParent():null,this._config.parent||this._addAriaAndCollapsedClass(this._element,this._triggerArray),this._config.toggle&&this.toggle()}c.default(document).on("click.bs.collapse.data-api",'[data-toggle="collapse"]',function(e){"A"===e.currentTarget.tagName&&e.preventDefault();var n=c.default(this),e=d.getSelectorFromElement(this),e=[].slice.call(document.querySelectorAll(e));c.default(e).each(function(){var e=c.default(this),t=e.data("bs.collapse")?"toggle":n.data();A._jQueryInterface.call(e,t)})}),c.default.fn[T]=A._jQueryInterface,c.default.fn[T].Constructor=A,c.default.fn[T].noConflict=function(){return c.default.fn[T]=k,A._jQueryInterface};var I="dropdown",R=c.default.fn[I],O=new RegExp("38|40|27"),P={offset:0,flip:!0,boundary:"scrollParent",reference:"toggle",display:"dynamic",popperConfig:null},L={offset:"(number|string|function)",flip:"boolean",boundary:"(string|element)",reference:"(string|element)",display:"string",popperConfig:"(null|object)"},F=((n=U.prototype).toggle=function(){var e;this._element.disabled||c.default(this._element).hasClass("disabled")||(e=c.default(this._menu).hasClass("show"),U._clearMenus(),e||this.show(!0))},n.show=function(e){if(void 0===e&&(e=!1),!(this._element.disabled||c.default(this._element).hasClass("disabled")||c.default(this._menu).hasClass("show"))){var t={relatedTarget:this._element},n=c.default.Event("show.bs.dropdown",t),r=U._getParentFromElement(this._element);if(c.default(r).trigger(n),!n.isDefaultPrevented()){if(!this._inNavbar&&e){if(void 0===i.default)throw new TypeError("Bootstrap's dropdowns require Popper.js (https://popper.js.org/)");e=this._element;"parent"===this._config.reference?e=r:d.isElement(this._config.reference)&&(e=this._config.reference,void 0!==this._config.reference.jquery&&(e=this._config.reference[0])),"scrollParent"!==this._config.boundary&&c.default(r).addClass("position-static"),this._popper=new i.default(e,this._menu,this._getPopperConfig())}"ontouchstart"in document.documentElement&&0===c.default(r).closest(".navbar-nav").length&&c.default(document.body).children().on("mouseover",null,c.default.noop),this._element.focus(),this._element.setAttribute("aria-expanded",!0),c.default(this._menu).toggleClass("show"),c.default(r).toggleClass("show").trigger(c.default.Event("shown.bs.dropdown",t))}}},n.hide=function(){var e,t,n;this._element.disabled||c.default(this._element).hasClass("disabled")||!c.default(this._menu).hasClass("show")||(e={relatedTarget:this._element},t=c.default.Event("hide.bs.dropdown",e),n=U._getParentFromElement(this._element),c.default(n).trigger(t),t.isDefaultPrevented()||(this._popper&&this._popper.destroy(),c.default(this._menu).toggleClass("show"),c.default(n).toggleClass("show").trigger(c.default.Event("hidden.bs.dropdown",e))))},n.dispose=function(){c.default.removeData(this._element,"bs.dropdown"),c.default(this._element).off(".bs.dropdown"),this._element=null,(this._menu=null)!==this._popper&&(this._popper.destroy(),this._popper=null)},n.update=function(){this._inNavbar=this._detectNavbar(),null!==this._popper&&this._popper.scheduleUpdate()},n._addEventListeners=function(){var t=this;c.default(this._element).on("click.bs.dropdown",function(e){e.preventDefault(),e.stopPropagation(),t.toggle()})},n._getConfig=function(e){return e=s({},this.constructor.Default,c.default(this._element).data(),e),d.typeCheckConfig(I,e,this.constructor.DefaultType),e},n._getMenuElement=function(){var e;return this._menu||(e=U._getParentFromElement(this._element))&&(this._menu=e.querySelector(".dropdown-menu")),this._menu},n._getPlacement=function(){var e=c.default(this._element.parentNode),t="bottom-start";return e.hasClass("dropup")?t=c.default(this._menu).hasClass("dropdown-menu-right")?"top-end":"top-start":e.hasClass("dropright")?t="right-start":e.hasClass("dropleft")?t="left-start":c.default(this._menu).hasClass("dropdown-menu-right")&&(t="bottom-end"),t},n._detectNavbar=function(){return 0<c.default(this._element).closest(".navbar").length},n._getOffset=function(){var t=this,e={};return"function"==typeof this._config.offset?e.fn=function(e){return e.offsets=s({},e.offsets,t._config.offset(e.offsets,t._element)||{}),e}:e.offset=this._config.offset,e},n._getPopperConfig=function(){var e={placement:this._getPlacement(),modifiers:{offset:this._getOffset(),flip:{enabled:this._config.flip},preventOverflow:{boundariesElement:this._config.boundary}}};return"static"===this._config.display&&(e.modifiers.applyStyle={enabled:!1}),s({},e,this._config.popperConfig)},U._jQueryInterface=function(t){return this.each(function(){var e=c.default(this).data("bs.dropdown");if(e||(e=new U(this,"object"==_typeof(t)?t:null),c.default(this).data("bs.dropdown",e)),"string"==typeof t){if(void 0===e[t])throw new TypeError('No method named "'+t+'"');e[t]()}})},U._clearMenus=function(e){if(!e||3!==e.which&&("keyup"!==e.type||9===e.which))for(var t=[].slice.call(document.querySelectorAll('[data-toggle="dropdown"]')),n=0,r=t.length;n<r;n++){var i,o,a=U._getParentFromElement(t[n]),s=c.default(t[n]).data("bs.dropdown"),u={relatedTarget:t[n]};e&&"click"===e.type&&(u.clickEvent=e),s&&(i=s._menu,!c.default(a).hasClass("show")||e&&("click"===e.type&&/input|textarea/i.test(e.target.tagName)||"keyup"===e.type&&9===e.which)&&c.default.contains(a,e.target)||(o=c.default.Event("hide.bs.dropdown",u),c.default(a).trigger(o),o.isDefaultPrevented()||("ontouchstart"in document.documentElement&&c.default(document.body).children().off("mouseover",null,c.default.noop),t[n].setAttribute("aria-expanded","false"),s._popper&&s._popper.destroy(),c.default(i).removeClass("show"),c.default(a).removeClass("show").trigger(c.default.Event("hidden.bs.dropdown",u)))))}},U._getParentFromElement=function(e){var t,n=d.getSelectorFromElement(e);return n&&(t=document.querySelector(n)),t||e.parentNode},U._dataApiKeydownHandler=function(e){if(!(/input|textarea/i.test(e.target.tagName)?32===e.which||27!==e.which&&(40!==e.which&&38!==e.which||c.default(e.target).closest(".dropdown-menu").length):!O.test(e.which))&&!this.disabled&&!c.default(this).hasClass("disabled")){var t=U._getParentFromElement(this),n=c.default(t).hasClass("show");if(n||27!==e.which){if(e.preventDefault(),e.stopPropagation(),!n||27===e.which||32===e.which)return 27===e.which&&c.default(t.querySelector('[data-toggle="dropdown"]')).trigger("focus"),void c.default(this).trigger("click");n=[].slice.call(t.querySelectorAll(".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)")).filter(function(e){return c.default(e).is(":visible")});0!==n.length&&(t=n.indexOf(e.target),38===e.which&&0<t&&t--,40===e.which&&t<n.length-1&&t++,t<0&&(t=0),n[t].focus())}}},a(U,null,[{key:"VERSION",get:function(){return"4.5.3"}},{key:"Default",get:function(){return P}},{key:"DefaultType",get:function(){return L}}]),U);function U(e,t){this._element=e,this._popper=null,this._config=this._getConfig(t),this._menu=this._getMenuElement(),this._inNavbar=this._detectNavbar(),this._addEventListeners()}c.default(document).on("keydown.bs.dropdown.data-api",'[data-toggle="dropdown"]',F._dataApiKeydownHandler).on("keydown.bs.dropdown.data-api",".dropdown-menu",F._dataApiKeydownHandler).on("click.bs.dropdown.data-api keyup.bs.dropdown.data-api",F._clearMenus).on("click.bs.dropdown.data-api",'[data-toggle="dropdown"]',function(e){e.preventDefault(),e.stopPropagation(),F._jQueryInterface.call(c.default(this),"toggle")}).on("click.bs.dropdown.data-api",".dropdown form",function(e){e.stopPropagation()}),c.default.fn[I]=F._jQueryInterface,c.default.fn[I].Constructor=F,c.default.fn[I].noConflict=function(){return c.default.fn[I]=R,F._jQueryInterface};var M=c.default.fn.modal,N={backdrop:!0,keyboard:!0,focus:!0,show:!0},B={backdrop:"(boolean|string)",keyboard:"boolean",focus:"boolean",show:"boolean"},j=((n=q.prototype).toggle=function(e){return this._isShown?this.hide():this.show(e)},n.show=function(e){var t,n=this;this._isShown||this._isTransitioning||(c.default(this._element).hasClass("fade")&&(this._isTransitioning=!0),t=c.default.Event("show.bs.modal",{relatedTarget:e}),c.default(this._element).trigger(t),this._isShown||t.isDefaultPrevented()||(this._isShown=!0,this._checkScrollbar(),this._setScrollbar(),this._adjustDialog(),this._setEscapeEvent(),this._setResizeEvent(),c.default(this._element).on("click.dismiss.bs.modal",'[data-dismiss="modal"]',function(e){return n.hide(e)}),c.default(this._dialog).on("mousedown.dismiss.bs.modal",function(){c.default(n._element).one("mouseup.dismiss.bs.modal",function(e){c.default(e.target).is(n._element)&&(n._ignoreBackdropClick=!0)})}),this._showBackdrop(function(){return n._showElement(e)})))},n.hide=function(e){var t=this;e&&e.preventDefault(),this._isShown&&!this._isTransitioning&&(e=c.default.Event("hide.bs.modal"),c.default(this._element).trigger(e),this._isShown&&!e.isDefaultPrevented()&&(this._isShown=!1,(e=c.default(this._element).hasClass("fade"))&&(this._isTransitioning=!0),this._setEscapeEvent(),this._setResizeEvent(),c.default(document).off("focusin.bs.modal"),c.default(this._element).removeClass("show"),c.default(this._element).off("click.dismiss.bs.modal"),c.default(this._dialog).off("mousedown.dismiss.bs.modal"),e?(e=d.getTransitionDurationFromElement(this._element),c.default(this._element).one(d.TRANSITION_END,function(e){return t._hideModal(e)}).emulateTransitionEnd(e)):this._hideModal()))},n.dispose=function(){[window,this._element,this._dialog].forEach(function(e){return c.default(e).off(".bs.modal")}),c.default(document).off("focusin.bs.modal"),c.default.removeData(this._element,"bs.modal"),this._config=null,this._element=null,this._dialog=null,this._backdrop=null,this._isShown=null,this._isBodyOverflowing=null,this._ignoreBackdropClick=null,this._isTransitioning=null,this._scrollbarWidth=null},n.handleUpdate=function(){this._adjustDialog()},n._getConfig=function(e){return e=s({},N,e),d.typeCheckConfig("modal",e,B),e},n._triggerBackdropTransition=function(){var e,t,n,r=this;"static"===this._config.backdrop?(e=c.default.Event("hidePrevented.bs.modal"),c.default(this._element).trigger(e),e.isDefaultPrevented()||((t=this._element.scrollHeight>document.documentElement.clientHeight)||(this._element.style.overflowY="hidden"),this._element.classList.add("modal-static"),n=d.getTransitionDurationFromElement(this._dialog),c.default(this._element).off(d.TRANSITION_END),c.default(this._element).one(d.TRANSITION_END,function(){r._element.classList.remove("modal-static"),t||c.default(r._element).one(d.TRANSITION_END,function(){r._element.style.overflowY=""}).emulateTransitionEnd(r._element,n)}).emulateTransitionEnd(n),this._element.focus())):this.hide()},n._showElement=function(e){var t=this,n=c.default(this._element).hasClass("fade"),r=this._dialog?this._dialog.querySelector(".modal-body"):null;this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE||document.body.appendChild(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),c.default(this._dialog).hasClass("modal-dialog-scrollable")&&r?r.scrollTop=0:this._element.scrollTop=0,n&&d.reflow(this._element),c.default(this._element).addClass("show"),this._config.focus&&this._enforceFocus();var i=c.default.Event("shown.bs.modal",{relatedTarget:e}),e=function(){t._config.focus&&t._element.focus(),t._isTransitioning=!1,c.default(t._element).trigger(i)};n?(n=d.getTransitionDurationFromElement(this._dialog),c.default(this._dialog).one(d.TRANSITION_END,e).emulateTransitionEnd(n)):e()},n._enforceFocus=function(){var t=this;c.default(document).off("focusin.bs.modal").on("focusin.bs.modal",function(e){document!==e.target&&t._element!==e.target&&0===c.default(t._element).has(e.target).length&&t._element.focus()})},n._setEscapeEvent=function(){var t=this;this._isShown?c.default(this._element).on("keydown.dismiss.bs.modal",function(e){t._config.keyboard&&27===e.which?(e.preventDefault(),t.hide()):t._config.keyboard||27!==e.which||t._triggerBackdropTransition()}):this._isShown||c.default(this._element).off("keydown.dismiss.bs.modal")},n._setResizeEvent=function(){var t=this;this._isShown?c.default(window).on("resize.bs.modal",function(e){return t.handleUpdate(e)}):c.default(window).off("resize.bs.modal")},n._hideModal=function(){var e=this;this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._showBackdrop(function(){c.default(document.body).removeClass("modal-open"),e._resetAdjustments(),e._resetScrollbar(),c.default(e._element).trigger("hidden.bs.modal")})},n._removeBackdrop=function(){this._backdrop&&(c.default(this._backdrop).remove(),this._backdrop=null)},n._showBackdrop=function(e){var t,n=this,r=c.default(this._element).hasClass("fade")?"fade":"";this._isShown&&this._config.backdrop?(this._backdrop=document.createElement("div"),this._backdrop.className="modal-backdrop",r&&this._backdrop.classList.add(r),c.default(this._backdrop).appendTo(document.body),c.default(this._element).on("click.dismiss.bs.modal",function(e){n._ignoreBackdropClick?n._ignoreBackdropClick=!1:e.target===e.currentTarget&&n._triggerBackdropTransition()}),r&&d.reflow(this._backdrop),c.default(this._backdrop).addClass("show"),e&&(r?(t=d.getTransitionDurationFromElement(this._backdrop),c.default(this._backdrop).one(d.TRANSITION_END,e).emulateTransitionEnd(t)):e())):!this._isShown&&this._backdrop?(c.default(this._backdrop).removeClass("show"),r=function(){n._removeBackdrop(),e&&e()},c.default(this._element).hasClass("fade")?(t=d.getTransitionDurationFromElement(this._backdrop),c.default(this._backdrop).one(d.TRANSITION_END,r).emulateTransitionEnd(t)):r()):e&&e()},n._adjustDialog=function(){var e=this._element.scrollHeight>document.documentElement.clientHeight;!this._isBodyOverflowing&&e&&(this._element.style.paddingLeft=this._scrollbarWidth+"px"),this._isBodyOverflowing&&!e&&(this._element.style.paddingRight=this._scrollbarWidth+"px")},n._resetAdjustments=function(){this._element.style.paddingLeft="",this._element.style.paddingRight=""},n._checkScrollbar=function(){var e=document.body.getBoundingClientRect();this._isBodyOverflowing=Math.round(e.left+e.right)<window.innerWidth,this._scrollbarWidth=this._getScrollbarWidth()},n._setScrollbar=function(){var e,t,i=this;this._isBodyOverflowing&&(e=[].slice.call(document.querySelectorAll(".fixed-top, .fixed-bottom, .is-fixed, .sticky-top")),t=[].slice.call(document.querySelectorAll(".sticky-top")),c.default(e).each(function(e,t){var n=t.style.paddingRight,r=c.default(t).css("padding-right");c.default(t).data("padding-right",n).css("padding-right",parseFloat(r)+i._scrollbarWidth+"px")}),c.default(t).each(function(e,t){var n=t.style.marginRight,r=c.default(t).css("margin-right");c.default(t).data("margin-right",n).css("margin-right",parseFloat(r)-i._scrollbarWidth+"px")}),e=document.body.style.paddingRight,t=c.default(document.body).css("padding-right"),c.default(document.body).data("padding-right",e).css("padding-right",parseFloat(t)+this._scrollbarWidth+"px")),c.default(document.body).addClass("modal-open")},n._resetScrollbar=function(){var e=[].slice.call(document.querySelectorAll(".fixed-top, .fixed-bottom, .is-fixed, .sticky-top"));c.default(e).each(function(e,t){var n=c.default(t).data("padding-right");c.default(t).removeData("padding-right"),t.style.paddingRight=n||""});e=[].slice.call(document.querySelectorAll(".sticky-top"));c.default(e).each(function(e,t){var n=c.default(t).data("margin-right");void 0!==n&&c.default(t).css("margin-right",n).removeData("margin-right")});e=c.default(document.body).data("padding-right");c.default(document.body).removeData("padding-right"),document.body.style.paddingRight=e||""},n._getScrollbarWidth=function(){var e=document.createElement("div");e.className="modal-scrollbar-measure",document.body.appendChild(e);var t=e.getBoundingClientRect().width-e.clientWidth;return document.body.removeChild(e),t},q._jQueryInterface=function(n,r){return this.each(function(){var e=c.default(this).data("bs.modal"),t=s({},N,c.default(this).data(),"object"==_typeof(n)&&n?n:{});if(e||(e=new q(this,t),c.default(this).data("bs.modal",e)),"string"==typeof n){if(void 0===e[n])throw new TypeError('No method named "'+n+'"');e[n](r)}else t.show&&e.show(r)})},a(q,null,[{key:"VERSION",get:function(){return"4.5.3"}},{key:"Default",get:function(){return N}}]),q);function q(e,t){this._config=this._getConfig(t),this._element=e,this._dialog=e.querySelector(".modal-dialog"),this._backdrop=null,this._isShown=!1,this._isBodyOverflowing=!1,this._ignoreBackdropClick=!1,this._isTransitioning=!1,this._scrollbarWidth=0}c.default(document).on("click.bs.modal.data-api",'[data-toggle="modal"]',function(e){var t,n=this,r=d.getSelectorFromElement(this);r&&(t=document.querySelector(r));r=c.default(t).data("bs.modal")?"toggle":s({},c.default(t).data(),c.default(this).data());"A"!==this.tagName&&"AREA"!==this.tagName||e.preventDefault();var i=c.default(t).one("show.bs.modal",function(e){e.isDefaultPrevented()||i.one("hidden.bs.modal",function(){c.default(n).is(":visible")&&n.focus()})});j._jQueryInterface.call(c.default(t),r,this)}),c.default.fn.modal=j._jQueryInterface,c.default.fn.modal.Constructor=j,c.default.fn.modal.noConflict=function(){return c.default.fn.modal=M,j._jQueryInterface};var G=["background","cite","href","itemtype","longdesc","poster","src","xlink:href"],H=/^(?:(?:https?|mailto|ftp|tel|file):|[^#&/:?]*(?:[#/?]|$))/gi,z=/^data:(?:image\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\/(?:mpeg|mp4|ogg|webm)|audio\/(?:mp3|oga|ogg|opus));base64,[\d+/a-z]+=*$/i;function W(e,i,t){if(0===e.length)return e;if(t&&"function"==typeof t)return t(e);for(var e=(new window.DOMParser).parseFromString(e,"text/html"),o=Object.keys(i),a=[].slice.call(e.body.querySelectorAll("*")),n=0,r=a.length;n<r;n++)!function(e){var t=a[e],n=t.nodeName.toLowerCase();if(-1===o.indexOf(t.nodeName.toLowerCase()))return t.parentNode.removeChild(t);var e=[].slice.call(t.attributes),r=[].concat(i["*"]||[],i[n]||[]);e.forEach(function(e){!function(e,t){var n=e.nodeName.toLowerCase();if(-1!==t.indexOf(n))return-1===G.indexOf(n)||Boolean(e.nodeValue.match(H)||e.nodeValue.match(z));for(var r=t.filter(function(e){return e instanceof RegExp}),i=0,o=r.length;i<o;i++)if(n.match(r[i]))return 1}(e,r)&&t.removeAttribute(e.nodeName)})}(n);return e.body.innerHTML}var V="tooltip",$=c.default.fn[V],Y=new RegExp("(^|\\s)bs-tooltip\\S+","g"),X=["sanitize","whiteList","sanitizeFn"],Q={animation:"boolean",template:"string",title:"(string|element|function)",trigger:"string",delay:"(number|object)",html:"boolean",selector:"(string|boolean)",placement:"(string|function)",offset:"(number|string|function)",container:"(string|element|boolean)",fallbackPlacement:"(string|array)",boundary:"(string|element)",sanitize:"boolean",sanitizeFn:"(null|function)",whiteList:"object",popperConfig:"(null|object)"},K={AUTO:"auto",TOP:"top",RIGHT:"right",BOTTOM:"bottom",LEFT:"left"},J={animation:!0,template:'<div class="tooltip" role="tooltip"><div class="arrow"></div><div class="tooltip-inner"></div></div>',trigger:"hover focus",title:"",delay:0,html:!1,selector:!1,placement:"top",offset:0,container:!1,fallbackPlacement:"flip",boundary:"scrollParent",sanitize:!0,sanitizeFn:null,whiteList:{"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],div:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},popperConfig:null},Z={HIDE:"hide.bs.tooltip",HIDDEN:"hidden.bs.tooltip",SHOW:"show.bs.tooltip",SHOWN:"shown.bs.tooltip",INSERTED:"inserted.bs.tooltip",CLICK:"click.bs.tooltip",FOCUSIN:"focusin.bs.tooltip",FOCUSOUT:"focusout.bs.tooltip",MOUSEENTER:"mouseenter.bs.tooltip",MOUSELEAVE:"mouseleave.bs.tooltip"},ee=((n=te.prototype).enable=function(){this._isEnabled=!0},n.disable=function(){this._isEnabled=!1},n.toggleEnabled=function(){this._isEnabled=!this._isEnabled},n.toggle=function(e){var t,n;this._isEnabled&&(e?(t=this.constructor.DATA_KEY,(n=c.default(e.currentTarget).data(t))||(n=new this.constructor(e.currentTarget,this._getDelegateConfig()),c.default(e.currentTarget).data(t,n)),n._activeTrigger.click=!n._activeTrigger.click,n._isWithActiveTrigger()?n._enter(null,n):n._leave(null,n)):c.default(this.getTipElement()).hasClass("show")?this._leave(null,this):this._enter(null,this))},n.dispose=function(){clearTimeout(this._timeout),c.default.removeData(this.element,this.constructor.DATA_KEY),c.default(this.element).off(this.constructor.EVENT_KEY),c.default(this.element).closest(".modal").off("hide.bs.modal",this._hideModalHandler),this.tip&&c.default(this.tip).remove(),this._isEnabled=null,this._timeout=null,this._hoverState=null,this._activeTrigger=null,this._popper&&this._popper.destroy(),this._popper=null,this.element=null,this.config=null,this.tip=null},n.show=function(){var t=this;if("none"===c.default(this.element).css("display"))throw new Error("Please use show on visible elements");var e,n,r=c.default.Event(this.constructor.Event.SHOW);this.isWithContent()&&this._isEnabled&&(c.default(this.element).trigger(r),n=d.findShadowRoot(this.element),e=c.default.contains(null!==n?n:this.element.ownerDocument.documentElement,this.element),!r.isDefaultPrevented()&&e&&(n=this.getTipElement(),r=d.getUID(this.constructor.NAME),n.setAttribute("id",r),this.element.setAttribute("aria-describedby",r),this.setContent(),this.config.animation&&c.default(n).addClass("fade"),e="function"==typeof this.config.placement?this.config.placement.call(this,n,this.element):this.config.placement,r=this._getAttachment(e),this.addAttachmentClass(r),e=this._getContainer(),c.default(n).data(this.constructor.DATA_KEY,this),c.default.contains(this.element.ownerDocument.documentElement,this.tip)||c.default(n).appendTo(e),c.default(this.element).trigger(this.constructor.Event.INSERTED),this._popper=new i.default(this.element,n,this._getPopperConfig(r)),c.default(n).addClass("show"),"ontouchstart"in document.documentElement&&c.default(document.body).children().on("mouseover",null,c.default.noop),r=function(){t.config.animation&&t._fixTransition();var e=t._hoverState;t._hoverState=null,c.default(t.element).trigger(t.constructor.Event.SHOWN),"out"===e&&t._leave(null,t)},c.default(this.tip).hasClass("fade")?(n=d.getTransitionDurationFromElement(this.tip),c.default(this.tip).one(d.TRANSITION_END,r).emulateTransitionEnd(n)):r()))},n.hide=function(e){function t(){"show"!==n._hoverState&&r.parentNode&&r.parentNode.removeChild(r),n._cleanTipClass(),n.element.removeAttribute("aria-describedby"),c.default(n.element).trigger(n.constructor.Event.HIDDEN),null!==n._popper&&n._popper.destroy(),e&&e()}var n=this,r=this.getTipElement(),i=c.default.Event(this.constructor.Event.HIDE);c.default(this.element).trigger(i),i.isDefaultPrevented()||(c.default(r).removeClass("show"),"ontouchstart"in document.documentElement&&c.default(document.body).children().off("mouseover",null,c.default.noop),this._activeTrigger.click=!1,this._activeTrigger.focus=!1,this._activeTrigger.hover=!1,c.default(this.tip).hasClass("fade")?(i=d.getTransitionDurationFromElement(r),c.default(r).one(d.TRANSITION_END,t).emulateTransitionEnd(i)):t(),this._hoverState="")},n.update=function(){null!==this._popper&&this._popper.scheduleUpdate()},n.isWithContent=function(){return Boolean(this.getTitle())},n.addAttachmentClass=function(e){c.default(this.getTipElement()).addClass("bs-tooltip-"+e)},n.getTipElement=function(){return this.tip=this.tip||c.default(this.config.template)[0],this.tip},n.setContent=function(){var e=this.getTipElement();this.setElementContent(c.default(e.querySelectorAll(".tooltip-inner")),this.getTitle()),c.default(e).removeClass("fade show")},n.setElementContent=function(e,t){"object"!=_typeof(t)||!t.nodeType&&!t.jquery?this.config.html?(this.config.sanitize&&(t=W(t,this.config.whiteList,this.config.sanitizeFn)),e.html(t)):e.text(t):this.config.html?c.default(t).parent().is(e)||e.empty().append(t):e.text(c.default(t).text())},n.getTitle=function(){return this.element.getAttribute("data-original-title")||("function"==typeof this.config.title?this.config.title.call(this.element):this.config.title)},n._getPopperConfig=function(e){var t=this;return s({},{placement:e,modifiers:{offset:this._getOffset(),flip:{behavior:this.config.fallbackPlacement},arrow:{element:".arrow"},preventOverflow:{boundariesElement:this.config.boundary}},onCreate:function(e){e.originalPlacement!==e.placement&&t._handlePopperPlacementChange(e)},onUpdate:function(e){return t._handlePopperPlacementChange(e)}},this.config.popperConfig)},n._getOffset=function(){var t=this,e={};return"function"==typeof this.config.offset?e.fn=function(e){return e.offsets=s({},e.offsets,t.config.offset(e.offsets,t.element)||{}),e}:e.offset=this.config.offset,e},n._getContainer=function(){return!1===this.config.container?document.body:d.isElement(this.config.container)?c.default(this.config.container):c.default(document).find(this.config.container)},n._getAttachment=function(e){return K[e.toUpperCase()]},n._setListeners=function(){var n=this;this.config.trigger.split(" ").forEach(function(e){var t;"click"===e?c.default(n.element).on(n.constructor.Event.CLICK,n.config.selector,function(e){return n.toggle(e)}):"manual"!==e&&(t="hover"===e?n.constructor.Event.MOUSEENTER:n.constructor.Event.FOCUSIN,e="hover"===e?n.constructor.Event.MOUSELEAVE:n.constructor.Event.FOCUSOUT,c.default(n.element).on(t,n.config.selector,function(e){return n._enter(e)}).on(e,n.config.selector,function(e){return n._leave(e)}))}),this._hideModalHandler=function(){n.element&&n.hide()},c.default(this.element).closest(".modal").on("hide.bs.modal",this._hideModalHandler),this.config.selector?this.config=s({},this.config,{trigger:"manual",selector:""}):this._fixTitle()},n._fixTitle=function(){var e=_typeof(this.element.getAttribute("data-original-title"));!this.element.getAttribute("title")&&"string"===e||(this.element.setAttribute("data-original-title",this.element.getAttribute("title")||""),this.element.setAttribute("title",""))},n._enter=function(e,t){var n=this.constructor.DATA_KEY;(t=t||c.default(e.currentTarget).data(n))||(t=new this.constructor(e.currentTarget,this._getDelegateConfig()),c.default(e.currentTarget).data(n,t)),e&&(t._activeTrigger["focusin"===e.type?"focus":"hover"]=!0),c.default(t.getTipElement()).hasClass("show")||"show"===t._hoverState?t._hoverState="show":(clearTimeout(t._timeout),t._hoverState="show",t.config.delay&&t.config.delay.show?t._timeout=setTimeout(function(){"show"===t._hoverState&&t.show()},t.config.delay.show):t.show())},n._leave=function(e,t){var n=this.constructor.DATA_KEY;(t=t||c.default(e.currentTarget).data(n))||(t=new this.constructor(e.currentTarget,this._getDelegateConfig()),c.default(e.currentTarget).data(n,t)),e&&(t._activeTrigger["focusout"===e.type?"focus":"hover"]=!1),t._isWithActiveTrigger()||(clearTimeout(t._timeout),t._hoverState="out",t.config.delay&&t.config.delay.hide?t._timeout=setTimeout(function(){"out"===t._hoverState&&t.hide()},t.config.delay.hide):t.hide())},n._isWithActiveTrigger=function(){for(var e in this._activeTrigger)if(this._activeTrigger[e])return!0;return!1},n._getConfig=function(e){var t=c.default(this.element).data();return Object.keys(t).forEach(function(e){-1!==X.indexOf(e)&&delete t[e]}),"number"==typeof(e=s({},this.constructor.Default,t,"object"==_typeof(e)&&e?e:{})).delay&&(e.delay={show:e.delay,hide:e.delay}),"number"==typeof e.title&&(e.title=e.title.toString()),"number"==typeof e.content&&(e.content=e.content.toString()),d.typeCheckConfig(V,e,this.constructor.DefaultType),e.sanitize&&(e.template=W(e.template,e.whiteList,e.sanitizeFn)),e},n._getDelegateConfig=function(){var e={};if(this.config)for(var t in this.config)this.constructor.Default[t]!==this.config[t]&&(e[t]=this.config[t]);return e},n._cleanTipClass=function(){var e=c.default(this.getTipElement()),t=e.attr("class").match(Y);null!==t&&t.length&&e.removeClass(t.join(""))},n._handlePopperPlacementChange=function(e){this.tip=e.instance.popper,this._cleanTipClass(),this.addAttachmentClass(this._getAttachment(e.placement))},n._fixTransition=function(){var e=this.getTipElement(),t=this.config.animation;null===e.getAttribute("x-placement")&&(c.default(e).removeClass("fade"),this.config.animation=!1,this.hide(),this.show(),this.config.animation=t)},te._jQueryInterface=function(r){return this.each(function(){var e=c.default(this),t=e.data("bs.tooltip"),n="object"==_typeof(r)&&r;if((t||!/dispose|hide/.test(r))&&(t||(t=new te(this,n),e.data("bs.tooltip",t)),"string"==typeof r)){if(void 0===t[r])throw new TypeError('No method named "'+r+'"');t[r]()}})},a(te,null,[{key:"VERSION",get:function(){return"4.5.3"}},{key:"Default",get:function(){return J}},{key:"NAME",get:function(){return V}},{key:"DATA_KEY",get:function(){return"bs.tooltip"}},{key:"Event",get:function(){return Z}},{key:"EVENT_KEY",get:function(){return".bs.tooltip"}},{key:"DefaultType",get:function(){return Q}}]),te);function te(e,t){if(void 0===i.default)throw new TypeError("Bootstrap's tooltips require Popper.js (https://popper.js.org/)");this._isEnabled=!0,this._timeout=0,this._hoverState="",this._activeTrigger={},this._popper=null,this.element=e,this.config=this._getConfig(t),this.tip=null,this._setListeners()}c.default.fn[V]=ee._jQueryInterface,c.default.fn[V].Constructor=ee,c.default.fn[V].noConflict=function(){return c.default.fn[V]=$,ee._jQueryInterface};var ne="popover",re=c.default.fn[ne],ie=new RegExp("(^|\\s)bs-popover\\S+","g"),oe=s({},ee.Default,{placement:"right",trigger:"click",content:"",template:'<div class="popover" role="tooltip"><div class="arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>'}),ae=s({},ee.DefaultType,{content:"(string|element|function)"}),se={HIDE:"hide.bs.popover",HIDDEN:"hidden.bs.popover",SHOW:"show.bs.popover",SHOWN:"shown.bs.popover",INSERTED:"inserted.bs.popover",CLICK:"click.bs.popover",FOCUSIN:"focusin.bs.popover",FOCUSOUT:"focusout.bs.popover",MOUSEENTER:"mouseenter.bs.popover",MOUSELEAVE:"mouseleave.bs.popover"},ue=function(e){var t;function r(){return e.apply(this,arguments)||this}n=e,(t=r).prototype=Object.create(n.prototype),(t.prototype.constructor=t).__proto__=n;var n=r.prototype;return n.isWithContent=function(){return this.getTitle()||this._getContent()},n.addAttachmentClass=function(e){c.default(this.getTipElement()).addClass("bs-popover-"+e)},n.getTipElement=function(){return this.tip=this.tip||c.default(this.config.template)[0],this.tip},n.setContent=function(){var e=c.default(this.getTipElement());this.setElementContent(e.find(".popover-header"),this.getTitle());var t=this._getContent();"function"==typeof t&&(t=t.call(this.element)),this.setElementContent(e.find(".popover-body"),t),e.removeClass("fade show")},n._getContent=function(){return this.element.getAttribute("data-content")||this.config.content},n._cleanTipClass=function(){var e=c.default(this.getTipElement()),t=e.attr("class").match(ie);null!==t&&0<t.length&&e.removeClass(t.join(""))},r._jQueryInterface=function(n){return this.each(function(){var e=c.default(this).data("bs.popover"),t="object"==_typeof(n)?n:null;if((e||!/dispose|hide/.test(n))&&(e||(e=new r(this,t),c.default(this).data("bs.popover",e)),"string"==typeof n)){if(void 0===e[n])throw new TypeError('No method named "'+n+'"');e[n]()}})},a(r,null,[{key:"VERSION",get:function(){return"4.5.3"}},{key:"Default",get:function(){return oe}},{key:"NAME",get:function(){return ne}},{key:"DATA_KEY",get:function(){return"bs.popover"}},{key:"Event",get:function(){return se}},{key:"EVENT_KEY",get:function(){return".bs.popover"}},{key:"DefaultType",get:function(){return ae}}]),r}(ee);c.default.fn[ne]=ue._jQueryInterface,c.default.fn[ne].Constructor=ue,c.default.fn[ne].noConflict=function(){return c.default.fn[ne]=re,ue._jQueryInterface};var le="scrollspy",ce=c.default.fn[le],de={offset:10,method:"auto",target:""},fe={offset:"number",method:"string",target:"(string|element)"},he=((n=pe.prototype).refresh=function(){var t=this,e=this._scrollElement===this._scrollElement.window?"offset":"position",r="auto"===this._config.method?e:this._config.method,i="position"===r?this._getScrollTop():0;this._offsets=[],this._targets=[],this._scrollHeight=this._getScrollHeight(),[].slice.call(document.querySelectorAll(this._selector)).map(function(e){var t,n=d.getSelectorFromElement(e);if(n&&(t=document.querySelector(n)),t){e=t.getBoundingClientRect();if(e.width||e.height)return[c.default(t)[r]().top+i,n]}return null}).filter(function(e){return e}).sort(function(e,t){return e[0]-t[0]}).forEach(function(e){t._offsets.push(e[0]),t._targets.push(e[1])})},n.dispose=function(){c.default.removeData(this._element,"bs.scrollspy"),c.default(this._scrollElement).off(".bs.scrollspy"),this._element=null,this._scrollElement=null,this._config=null,this._selector=null,this._offsets=null,this._targets=null,this._activeTarget=null,this._scrollHeight=null},n._getConfig=function(e){var t;return"string"!=typeof(e=s({},de,"object"==_typeof(e)&&e?e:{})).target&&d.isElement(e.target)&&((t=c.default(e.target).attr("id"))||(t=d.getUID(le),c.default(e.target).attr("id",t)),e.target="#"+t),d.typeCheckConfig(le,e,fe),e},n._getScrollTop=function(){return this._scrollElement===window?this._scrollElement.pageYOffset:this._scrollElement.scrollTop},n._getScrollHeight=function(){return this._scrollElement.scrollHeight||Math.max(document.body.scrollHeight,document.documentElement.scrollHeight)},n._getOffsetHeight=function(){return this._scrollElement===window?window.innerHeight:this._scrollElement.getBoundingClientRect().height},n._process=function(){var e=this._getScrollTop()+this._config.offset,t=this._getScrollHeight(),n=this._config.offset+t-this._getOffsetHeight();if(this._scrollHeight!==t&&this.refresh(),n<=e){n=this._targets[this._targets.length-1];this._activeTarget!==n&&this._activate(n)}else{if(this._activeTarget&&e<this._offsets[0]&&0<this._offsets[0])return this._activeTarget=null,void this._clear();for(var r=this._offsets.length;r--;)this._activeTarget!==this._targets[r]&&e>=this._offsets[r]&&(void 0===this._offsets[r+1]||e<this._offsets[r+1])&&this._activate(this._targets[r])}},n._activate=function(t){this._activeTarget=t,this._clear();var e=this._selector.split(",").map(function(e){return e+'[data-target="'+t+'"],'+e+'[href="'+t+'"]'}),e=c.default([].slice.call(document.querySelectorAll(e.join(","))));e.hasClass("dropdown-item")?(e.closest(".dropdown").find(".dropdown-toggle").addClass("active"),e.addClass("active")):(e.addClass("active"),e.parents(".nav, .list-group").prev(".nav-link, .list-group-item").addClass("active"),e.parents(".nav, .list-group").prev(".nav-item").children(".nav-link").addClass("active")),c.default(this._scrollElement).trigger("activate.bs.scrollspy",{relatedTarget:t})},n._clear=function(){[].slice.call(document.querySelectorAll(this._selector)).filter(function(e){return e.classList.contains("active")}).forEach(function(e){return e.classList.remove("active")})},pe._jQueryInterface=function(t){return this.each(function(){var e=c.default(this).data("bs.scrollspy");if(e||(e=new pe(this,"object"==_typeof(t)&&t),c.default(this).data("bs.scrollspy",e)),"string"==typeof t){if(void 0===e[t])throw new TypeError('No method named "'+t+'"');e[t]()}})},a(pe,null,[{key:"VERSION",get:function(){return"4.5.3"}},{key:"Default",get:function(){return de}}]),pe);function pe(e,t){var n=this;this._element=e,this._scrollElement="BODY"===e.tagName?window:e,this._config=this._getConfig(t),this._selector=this._config.target+" .nav-link,"+this._config.target+" .list-group-item,"+this._config.target+" .dropdown-item",this._offsets=[],this._targets=[],this._activeTarget=null,this._scrollHeight=0,c.default(this._scrollElement).on("scroll.bs.scrollspy",function(e){return n._process(e)}),this.refresh(),this._process()}c.default(window).on("load.bs.scrollspy.data-api",function(){for(var e=[].slice.call(document.querySelectorAll('[data-spy="scroll"]')),t=e.length;t--;){var n=c.default(e[t]);he._jQueryInterface.call(n,n.data())}}),c.default.fn[le]=he._jQueryInterface,c.default.fn[le].Constructor=he,c.default.fn[le].noConflict=function(){return c.default.fn[le]=ce,he._jQueryInterface};var ge=c.default.fn.tab,me=((n=ye.prototype).show=function(){var e,t,n,r,i,o,a=this;this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE&&c.default(this._element).hasClass("active")||c.default(this._element).hasClass("disabled")||(o=c.default(this._element).closest(".nav, .list-group")[0],t=d.getSelectorFromElement(this._element),o&&(i="UL"===o.nodeName||"OL"===o.nodeName?"> li > .active":".active",n=(n=c.default.makeArray(c.default(o).find(i)))[n.length-1]),r=c.default.Event("hide.bs.tab",{relatedTarget:this._element}),i=c.default.Event("show.bs.tab",{relatedTarget:n}),n&&c.default(n).trigger(r),c.default(this._element).trigger(i),i.isDefaultPrevented()||r.isDefaultPrevented()||(t&&(e=document.querySelector(t)),this._activate(this._element,o),o=function(){var e=c.default.Event("hidden.bs.tab",{relatedTarget:a._element}),t=c.default.Event("shown.bs.tab",{relatedTarget:n});c.default(n).trigger(e),c.default(a._element).trigger(t)},e?this._activate(e,e.parentNode,o):o()))},n.dispose=function(){c.default.removeData(this._element,"bs.tab"),this._element=null},n._activate=function(e,t,n){var r=this,i=(!t||"UL"!==t.nodeName&&"OL"!==t.nodeName?c.default(t).children(".active"):c.default(t).find("> li > .active"))[0],o=n&&i&&c.default(i).hasClass("fade"),t=function(){return r._transitionComplete(e,i,n)};i&&o?(o=d.getTransitionDurationFromElement(i),c.default(i).removeClass("show").one(d.TRANSITION_END,t).emulateTransitionEnd(o)):t()},n._transitionComplete=function(e,t,n){var r;t&&(c.default(t).removeClass("active"),(r=c.default(t.parentNode).find("> .dropdown-menu .active")[0])&&c.default(r).removeClass("active"),"tab"===t.getAttribute("role")&&t.setAttribute("aria-selected",!1)),c.default(e).addClass("active"),"tab"===e.getAttribute("role")&&e.setAttribute("aria-selected",!0),d.reflow(e),e.classList.contains("fade")&&e.classList.add("show"),e.parentNode&&c.default(e.parentNode).hasClass("dropdown-menu")&&((t=c.default(e).closest(".dropdown")[0])&&(t=[].slice.call(t.querySelectorAll(".dropdown-toggle")),c.default(t).addClass("active")),e.setAttribute("aria-expanded",!0)),n&&n()},ye._jQueryInterface=function(n){return this.each(function(){var e=c.default(this),t=e.data("bs.tab");if(t||(t=new ye(this),e.data("bs.tab",t)),"string"==typeof n){if(void 0===t[n])throw new TypeError('No method named "'+n+'"');t[n]()}})},a(ye,null,[{key:"VERSION",get:function(){return"4.5.3"}}]),ye);function ye(e){this._element=e}c.default(document).on("click.bs.tab.data-api",'[data-toggle="tab"], [data-toggle="pill"], [data-toggle="list"]',function(e){e.preventDefault(),me._jQueryInterface.call(c.default(this),"show")}),c.default.fn.tab=me._jQueryInterface,c.default.fn.tab.Constructor=me,c.default.fn.tab.noConflict=function(){return c.default.fn.tab=ge,me._jQueryInterface};var ve=c.default.fn.toast,_e={animation:"boolean",autohide:"boolean",delay:"number"},be={animation:!0,autohide:!0,delay:500},we=((n=xe.prototype).show=function(){var e,t=this,n=c.default.Event("show.bs.toast");c.default(this._element).trigger(n),n.isDefaultPrevented()||(this._clearTimeout(),this._config.animation&&this._element.classList.add("fade"),e=function(){t._element.classList.remove("showing"),t._element.classList.add("show"),c.default(t._element).trigger("shown.bs.toast"),t._config.autohide&&(t._timeout=setTimeout(function(){t.hide()},t._config.delay))},this._element.classList.remove("hide"),d.reflow(this._element),this._element.classList.add("showing"),this._config.animation?(n=d.getTransitionDurationFromElement(this._element),c.default(this._element).one(d.TRANSITION_END,e).emulateTransitionEnd(n)):e())},n.hide=function(){var e;this._element.classList.contains("show")&&(e=c.default.Event("hide.bs.toast"),c.default(this._element).trigger(e),e.isDefaultPrevented()||this._close())},n.dispose=function(){this._clearTimeout(),this._element.classList.contains("show")&&this._element.classList.remove("show"),c.default(this._element).off("click.dismiss.bs.toast"),c.default.removeData(this._element,"bs.toast"),this._element=null,this._config=null},n._getConfig=function(e){return e=s({},be,c.default(this._element).data(),"object"==_typeof(e)&&e?e:{}),d.typeCheckConfig("toast",e,this.constructor.DefaultType),e},n._setListeners=function(){var e=this;c.default(this._element).on("click.dismiss.bs.toast",'[data-dismiss="toast"]',function(){return e.hide()})},n._close=function(){function e(){n._element.classList.add("hide"),c.default(n._element).trigger("hidden.bs.toast")}var t,n=this;this._element.classList.remove("show"),this._config.animation?(t=d.getTransitionDurationFromElement(this._element),c.default(this._element).one(d.TRANSITION_END,e).emulateTransitionEnd(t)):e()},n._clearTimeout=function(){clearTimeout(this._timeout),this._timeout=null},xe._jQueryInterface=function(n){return this.each(function(){var e=c.default(this),t=e.data("bs.toast");if(t||(t=new xe(this,"object"==_typeof(n)&&n),e.data("bs.toast",t)),"string"==typeof n){if(void 0===t[n])throw new TypeError('No method named "'+n+'"');t[n](this)}})},a(xe,null,[{key:"VERSION",get:function(){return"4.5.3"}},{key:"DefaultType",get:function(){return _e}},{key:"Default",get:function(){return be}}]),xe);function xe(e,t){this._element=e,this._config=this._getConfig(t),this._timeout=null,this._setListeners()}c.default.fn.toast=we._jQueryInterface,c.default.fn.toast.Constructor=we,c.default.fn.toast.noConflict=function(){return c.default.fn.toast=ve,we._jQueryInterface},e.Alert=f,e.Button=g,e.Carousel=x,e.Collapse=A,e.Dropdown=F,e.Modal=j,e.Popover=ue,e.Scrollspy=he,e.Tab=me,e.Toast=we,e.Tooltip=ee,e.Util=d,Object.defineProperty(e,"__esModule",{value:!0})}),function(e,t){"object"==("undefined"==typeof exports?"undefined":_typeof(exports))&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).aesDecrypter={})}(this,function(e){"use strict";var d=null,g=function(){function c(e){var t,n,r;_classCallCheck(this,c),d=d||function(){for(var e,t,n,r,i,o,a,s=[[[],[],[],[],[]],[[],[],[],[],[]]],u=s[0],l=s[1],c=u[4],d=l[4],f=[],h=[],p=0;p<256;p++)h[(f[p]=p<<1^283*(p>>7))^p]=p;for(e=t=0;!c[e];e^=n||1,t=h[t]||1)for(i=(i=t^t<<1^t<<2^t<<3^t<<4)>>8^255&i^99,a=16843009*f[r=f[n=f[d[c[e]=i]=e]]]^65537*r^257*n^16843008*e,o=257*f[i]^16843008*i,p=0;p<4;p++)u[p][e]=o=o<<24^o>>>8,l[p][i]=a=a<<24^a>>>8;for(p=0;p<5;p++)u[p]=u[p].slice(0),l[p]=l[p].slice(0);return s}(),this._tables=[[d[0][0].slice(),d[0][1].slice(),d[0][2].slice(),d[0][3].slice(),d[0][4].slice()],[d[1][0].slice(),d[1][1].slice(),d[1][2].slice(),d[1][3].slice(),d[1][4].slice()]];var i=this._tables[0][4],o=this._tables[1],a=e.length,s=1;if(4!==a&&6!==a&&8!==a)throw new Error("Invalid aes key size");var u=e.slice(0),l=[];for(this._key=[u,l],t=a;t<4*a+28;t++)r=u[t-1],(t%a==0||8===a&&t%a==4)&&(r=i[r>>>24]<<24^i[r>>16&255]<<16^i[r>>8&255]<<8^i[255&r],t%a==0&&(r=r<<8^r>>>24^s<<24,s=s<<1^283*(s>>7))),u[t]=u[t-a]^r;for(n=0;t;n++,t--)r=u[3&n?t:t-4],l[n]=t<=4||n<4?r:o[0][i[r>>>24]]^o[1][i[r>>16&255]]^o[2][i[r>>8&255]]^o[3][i[255&r]]}return _createClass(c,[{key:"decrypt",value:function(e,t,n,r,i,o){for(var a,s,u,l=this._key[1],c=e^l[0],d=r^l[1],f=n^l[2],h=t^l[3],p=l.length/4-2,g=4,t=this._tables[1],m=t[0],y=t[1],v=t[2],_=t[3],b=t[4],w=0;w<p;w++)a=m[c>>>24]^y[d>>16&255]^v[f>>8&255]^_[255&h]^l[g],s=m[d>>>24]^y[f>>16&255]^v[h>>8&255]^_[255&c]^l[g+1],u=m[f>>>24]^y[h>>16&255]^v[c>>8&255]^_[255&d]^l[g+2],h=m[h>>>24]^y[c>>16&255]^v[d>>8&255]^_[255&f]^l[g+3],g+=4,c=a,d=s,f=u;for(w=0;w<4;w++)i[(3&-w)+o]=b[c>>>24]<<24^b[d>>16&255]<<16^b[f>>8&255]<<8^b[255&h]^l[g++],a=c,c=d,d=f,f=h,h=a}}]),c}(),r=((n=t.prototype).on=function(e,t){this.listeners[e]||(this.listeners[e]=[]),this.listeners[e].push(t)},n.off=function(e,t){if(!this.listeners[e])return!1;t=this.listeners[e].indexOf(t);return this.listeners[e]=this.listeners[e].slice(0),this.listeners[e].splice(t,1),-1<t},n.trigger=function(e){var t=this.listeners[e];if(t)if(2===arguments.length)for(var n=t.length,r=0;r<n;++r)t[r].call(this,arguments[1]);else for(var i=Array.prototype.slice.call(arguments,1),o=t.length,a=0;a<o;++a)t[a].apply(this,i)},n.dispose=function(){this.listeners={}},n.pipe=function(t){this.on("data",function(e){t.push(e)})},t);function t(){this.listeners={}}function o(e,t,n){for(var r,i,o,a,s=new Int32Array(e.buffer,e.byteOffset,e.byteLength>>2),u=new g(Array.prototype.slice.call(t)),e=new Uint8Array(e.byteLength),l=new Int32Array(e.buffer),c=n[0],d=n[1],f=n[2],h=n[3],p=0;p<s.length;p+=4)r=m(s[p]),i=m(s[p+1]),o=m(s[p+2]),a=m(s[p+3]),u.decrypt(r,i,o,a,l,p),l[p]=m(l[p]^c),l[p+1]=m(l[p+1]^d),l[p+2]=m(l[p+2]^f),l[p+3]=m(l[p+3]^h),c=r,d=i,f=o,h=a;return e}var l=function(){_inherits(n,r);var t=_createSuper(n);function n(){var e;return _classCallCheck(this,n),(e=t.call(this,r)).jobs=[],e.delay=1,e.timeout_=null,e}return _createClass(n,[{key:"processJob_",value:function(){this.jobs.shift()(),this.jobs.length?this.timeout_=setTimeout(this.processJob_.bind(this),this.delay):this.timeout_=null}},{key:"push",value:function(e){this.jobs.push(e),this.timeout_||(this.timeout_=setTimeout(this.processJob_.bind(this),this.delay))}}]),n}(),m=function(e){return e<<24|(65280&e)<<8|(16711680&e)>>8|e>>>24},n=function(){function u(e,t,n,r){_classCallCheck(this,u);var i=u.STEP,o=new Int32Array(e.buffer),a=new Uint8Array(e.byteLength),s=0;for(this.asyncStream_=new l,this.asyncStream_.push(this.decryptChunk_(o.subarray(s,s+i),t,n,a)),s=i;s<o.length;s+=i)n=new Uint32Array([m(o[s-4]),m(o[s-3]),m(o[s-2]),m(o[s-1])]),this.asyncStream_.push(this.decryptChunk_(o.subarray(s,s+i),t,n,a));this.asyncStream_.push(function(){r(null,a.subarray(0,a.byteLength-a[a.byteLength-1]))})}return _createClass(u,[{key:"decryptChunk_",value:function(t,n,r,i){return function(){var e=o(t,n,r);i.set(e,t.byteOffset)}}}],[{key:"STEP",get:function(){return 32e3}}]),u}();e.AsyncStream=l,e.Decrypter=n,e.decrypt=o,Object.defineProperty(e,"__esModule",{value:!0})});var ISOBoxer={parseBuffer:function(e){return new ISOFile(e).parse()},addBoxProcessor:function(e,t){"string"==typeof e&&"function"==typeof t&&(ISOBox.prototype._boxProcessors[e]=t)},createFile:function(){return new ISOFile},createBox:function(e,t,n){e=ISOBox.create(e);return t&&t.append(e,n),e},createFullBox:function(e,t,n){n=ISOBoxer.createBox(e,t,n);return n.version=0,n.flags=0,n},Utils:{}};ISOBoxer.Utils.dataViewToString=function(e,t){t=t||"utf-8";if("undefined"!=typeof TextDecoder)return new TextDecoder(t).decode(e);var n=[],r=0;if("utf-8"===t)for(;r<e.byteLength;){var i=e.getUint8(r++);i<128||(i<224?i=(31&i)<<6:(i<240?i=(15&i)<<12:(i=(7&i)<<18,i|=(63&e.getUint8(r++))<<12),i|=(63&e.getUint8(r++))<<6),i|=63&e.getUint8(r++)),n.push(String.fromCharCode(i))}else for(;r<e.byteLength;)n.push(String.fromCharCode(e.getUint8(r++)));return n.join("")},ISOBoxer.Utils.utf8ToByteArray=function(e){var t,n;if("undefined"!=typeof TextEncoder)t=(new TextEncoder).encode(e);else for(t=[],n=0;n<e.length;++n){var r=e.charCodeAt(n);r<128?t.push(r):(r<2048?t.push(192|r>>6):(r<65536?t.push(224|r>>12):(t.push(240|r>>18),t.push(128|63&r>>12)),t.push(128|63&r>>6)),t.push(128|63&r))}return t},ISOBoxer.Utils.appendBox=function(e,t,n){if(t._offset=e._cursor.offset,t._root=e._root||e,t._raw=e._raw,t._parent=e,-1!==n)if(null!=n){var r,i=-1;if("number"==typeof n)i=n;else{if("string"==typeof n)r=n;else{if("object"!=_typeof(n)||!n.type)return void e.boxes.push(t);r=n.type}for(var o=0;o<e.boxes.length;o++)if(r===e.boxes[o].type){i=o+1;break}}e.boxes.splice(i,0,t)}else e.boxes.push(t)},"undefined"!=typeof exports&&(exports.parseBuffer=ISOBoxer.parseBuffer,exports.addBoxProcessor=ISOBoxer.addBoxProcessor,exports.createFile=ISOBoxer.createFile,exports.createBox=ISOBoxer.createBox,exports.createFullBox=ISOBoxer.createFullBox,exports.Utils=ISOBoxer.Utils),ISOBoxer.Cursor=function(e){this.offset=void 0===e?0:e};var ISOFile=function(e){this._cursor=new ISOBoxer.Cursor,this.boxes=[],e&&(this._raw=new DataView(e))};ISOFile.prototype.fetch=function(e){e=this.fetchAll(e,!0);return e.length?e[0]:null},ISOFile.prototype.fetchAll=function(e,t){var n=[];return ISOFile._sweep.call(this,e,n,t),n},ISOFile.prototype.parse=function(){for(this._cursor.offset=0,this.boxes=[];this._cursor.offset<this._raw.byteLength;){var e=ISOBox.parse(this);if(void 0===e.type)break;this.boxes.push(e)}return this},ISOFile._sweep=function(e,t,n){for(var r in this.type&&this.type==e&&t.push(this),this.boxes){if(t.length&&n)return;ISOFile._sweep.call(this.boxes[r],e,t,n)}},ISOFile.prototype.write=function(){for(var e=0,t=0;t<this.boxes.length;t++)e+=this.boxes[t].getLength(!1);var n=new Uint8Array(e);for(this._rawo=new DataView(n.buffer),this.bytes=n,t=this._cursor.offset=0;t<this.boxes.length;t++)this.boxes[t].write();return n.buffer},ISOFile.prototype.append=function(e,t){ISOBoxer.Utils.appendBox(this,e,t)};var ISOBox=function(){this._cursor=new ISOBoxer.Cursor};ISOBox.parse=function(e){var t=new ISOBox;return t._offset=e._cursor.offset,t._root=e._root||e,t._raw=e._raw,t._parent=e,t._parseBox(),e._cursor.offset=t._raw.byteOffset+t._raw.byteLength,t},ISOBox.create=function(e){var t=new ISOBox;return t.type=e,t.boxes=[],t},ISOBox.prototype._boxContainers=["dinf","edts","mdia","meco","mfra","minf","moof","moov","mvex","stbl","strk","traf","trak","tref","udta","vttc","sinf","schi","encv","enca","meta","grpl","prsl"],ISOBox.prototype._boxProcessors={},ISOBox.prototype._procField=function(e,t,n){this._parsing?this[e]=this._readField(t,n):this._writeField(t,n,this[e])},ISOBox.prototype._procFieldArray=function(e,t,n,r){var i;if(this._parsing)for(this[e]=[],i=0;i<t;i++)this[e][i]=this._readField(n,r);else for(i=0;i<this[e].length;i++)this._writeField(n,r,this[e][i])},ISOBox.prototype._procFullBox=function(){this._procField("version","uint",8),this._procField("flags","uint",24)},ISOBox.prototype._procEntries=function(e,t,n){var r;if(this._parsing)for(this[e]=[],r=0;r<t;r++)this[e].push({}),n.call(this,this[e][r]);else for(r=0;r<t;r++)n.call(this,this[e][r])},ISOBox.prototype._procSubEntries=function(e,t,n,r){var i;if(this._parsing)for(e[t]=[],i=0;i<n;i++)e[t].push({}),r.call(this,e[t][i]);else for(i=0;i<n;i++)r.call(this,e[t][i])},ISOBox.prototype._procEntryField=function(e,t,n,r){this._parsing?e[t]=this._readField(n,r):this._writeField(n,r,e[t])},ISOBox.prototype._procSubBoxes=function(e,t){var n;if(this._parsing)for(this[e]=[],n=0;n<t;n++)this[e].push(ISOBox.parse(this));else for(n=0;n<t;n++)this._rawo?this[e][n].write():this.size+=this[e][n].getLength()},ISOBox.prototype._readField=function(e,t){switch(e){case"uint":return this._readUint(t);case"int":return this._readInt(t);case"template":return this._readTemplate(t);case"string":return-1===t?this._readTerminatedString():this._readString(t);case"data":return this._readData(t);case"utf8":return this._readUTF8String();case"utf8string":return this._readUTF8TerminatedString();default:return-1}},ISOBox.prototype._readInt=function(e){var t=null,n=this._cursor.offset-this._raw.byteOffset;switch(e){case 8:t=this._raw.getInt8(n);break;case 16:t=this._raw.getInt16(n);break;case 32:t=this._raw.getInt32(n);break;case 64:var r=this._raw.getInt32(n),i=this._raw.getInt32(4+n),t=r*Math.pow(2,32)+i}return this._cursor.offset+=e>>3,t},ISOBox.prototype._readUint=function(e){var t,n,r=null,i=this._cursor.offset-this._raw.byteOffset;switch(e){case 8:r=this._raw.getUint8(i);break;case 16:r=this._raw.getUint16(i);break;case 24:r=((t=this._raw.getUint16(i))<<8)+(n=this._raw.getUint8(2+i));break;case 32:r=this._raw.getUint32(i);break;case 64:t=this._raw.getUint32(i),n=this._raw.getUint32(4+i),r=t*Math.pow(2,32)+n}return this._cursor.offset+=e>>3,r},ISOBox.prototype._readString=function(e){for(var t="",n=0;n<e;n++){var r=this._readUint(8);t+=String.fromCharCode(r)}return t},ISOBox.prototype._readTemplate=function(e){return this._readUint(e/2)+this._readUint(e/2)/Math.pow(2,e/2)},ISOBox.prototype._readTerminatedString=function(){for(var e="";this._cursor.offset-this._offset<this._raw.byteLength;){var t=this._readUint(8);if(0===t)break;e+=String.fromCharCode(t)}return e},ISOBox.prototype._readData=function(e){var t=0<e?e:this._raw.byteLength-(this._cursor.offset-this._offset);if(0<t){e=new Uint8Array(this._raw.buffer,this._cursor.offset,t);return this._cursor.offset+=t,e}return null},ISOBox.prototype._readUTF8String=function(){var e=this._raw.byteLength-(this._cursor.offset-this._offset),t=null;return 0<e&&(t=new DataView(this._raw.buffer,this._cursor.offset,e),this._cursor.offset+=e),t&&ISOBoxer.Utils.dataViewToString(t)},ISOBox.prototype._readUTF8TerminatedString=function(){var e=this._raw.byteLength-(this._cursor.offset-this._offset),t=null;if(0<e){for(var t=new DataView(this._raw.buffer,this._cursor.offset,e),n=0;n<e&&0!==t.getUint8(n);n++);t=new DataView(this._raw.buffer,this._cursor.offset,n),this._cursor.offset+=Math.min(n+1,e)}return t&&ISOBoxer.Utils.dataViewToString(t)},ISOBox.prototype._parseBox=function(){if(this._parsing=!0,this._cursor.offset=this._offset,this._offset+8>this._raw.buffer.byteLength)this._root._incomplete=!0;else{switch(this._procField("size","uint",32),this._procField("type","string",4),1===this.size&&this._procField("largesize","uint",64),"uuid"===this.type&&this._procFieldArray("usertype",16,"uint",8),this.size){case 0:this._raw=new DataView(this._raw.buffer,this._offset);break;case 1:this._offset+this.size>this._raw.buffer.byteLength?(this._incomplete=!0,this._root._incomplete=!0):this._raw=new DataView(this._raw.buffer,this._offset,this.largesize);break;default:this._offset+this.size>this._raw.buffer.byteLength?(this._incomplete=!0,this._root._incomplete=!0):this._raw=new DataView(this._raw.buffer,this._offset,this.size)}this._incomplete||(this._boxProcessors[this.type]&&this._boxProcessors[this.type].call(this),-1!==this._boxContainers.indexOf(this.type)?this._parseContainerBox():this._data=this._readData())}},ISOBox.prototype._parseFullBox=function(){this.version=this._readUint(8),this.flags=this._readUint(24)},ISOBox.prototype._parseContainerBox=function(){for(this.boxes=[];this._cursor.offset-this._raw.byteOffset<this._raw.byteLength;)this.boxes.push(ISOBox.parse(this))},ISOBox.prototype.append=function(e,t){ISOBoxer.Utils.appendBox(this,e,t)},ISOBox.prototype.getLength=function(){if(this._parsing=!1,this._rawo=null,this.size=0,this._procField("size","uint",32),this._procField("type","string",4),1===this.size&&this._procField("largesize","uint",64),"uuid"===this.type&&this._procFieldArray("usertype",16,"uint",8),this._boxProcessors[this.type]&&this._boxProcessors[this.type].call(this),-1!==this._boxContainers.indexOf(this.type))for(var e=0;e<this.boxes.length;e++)this.size+=this.boxes[e].getLength();return this._data&&this._writeData(this._data),this.size},ISOBox.prototype.write=function(){switch(this._parsing=!1,this._cursor.offset=this._parent._cursor.offset,this.size){case 0:this._rawo=new DataView(this._parent._rawo.buffer,this._cursor.offset,this.parent._rawo.byteLength-this._cursor.offset);break;case 1:this._rawo=new DataView(this._parent._rawo.buffer,this._cursor.offset,this.largesize);break;default:this._rawo=new DataView(this._parent._rawo.buffer,this._cursor.offset,this.size)}if(this._procField("size","uint",32),this._procField("type","string",4),1===this.size&&this._procField("largesize","uint",64),"uuid"===this.type&&this._procFieldArray("usertype",16,"uint",8),this._boxProcessors[this.type]&&this._boxProcessors[this.type].call(this),-1!==this._boxContainers.indexOf(this.type))for(var e=0;e<this.boxes.length;e++)this.boxes[e].write();return this._data&&this._writeData(this._data),this._parent._cursor.offset+=this.size,this.size},ISOBox.prototype._writeInt=function(e,t){if(this._rawo){var n=this._cursor.offset-this._rawo.byteOffset;switch(e){case 8:this._rawo.setInt8(n,t);break;case 16:this._rawo.setInt16(n,t);break;case 32:this._rawo.setInt32(n,t);break;case 64:var r=Math.floor(t/Math.pow(2,32)),i=t-r*Math.pow(2,32);this._rawo.setUint32(n,r),this._rawo.setUint32(4+n,i)}this._cursor.offset+=e>>3}else this.size+=e>>3},ISOBox.prototype._writeUint=function(e,t){if(this._rawo){var n,r,i=this._cursor.offset-this._rawo.byteOffset;switch(e){case 8:this._rawo.setUint8(i,t);break;case 16:this._rawo.setUint16(i,t);break;case 24:n=(16776960&t)>>8,r=255&t,this._rawo.setUint16(i,n),this._rawo.setUint8(2+i,r);break;case 32:this._rawo.setUint32(i,t);break;case 64:r=t-(n=Math.floor(t/Math.pow(2,32)))*Math.pow(2,32),this._rawo.setUint32(i,n),this._rawo.setUint32(4+i,r)}this._cursor.offset+=e>>3}else this.size+=e>>3},ISOBox.prototype._writeString=function(e,t){for(var n=0;n<e;n++)this._writeUint(8,t.charCodeAt(n))},ISOBox.prototype._writeTerminatedString=function(e){if(0!==e.length){for(var t=0;t<e.length;t++)this._writeUint(8,e.charCodeAt(t));this._writeUint(8,0)}},ISOBox.prototype._writeTemplate=function(e,t){var n=Math.floor(t),t=(t-n)*Math.pow(2,e/2);this._writeUint(e/2,n),this._writeUint(e/2,t)},ISOBox.prototype._writeData=function(e){if(e)if(this._rawo){if(e instanceof Array){for(var t=this._cursor.offset-this._rawo.byteOffset,n=0;n<e.length;n++)this._rawo.setInt8(t+n,e[n]);this._cursor.offset+=e.length}e instanceof Uint8Array&&(this._root.bytes.set(e,this._cursor.offset),this._cursor.offset+=e.length)}else this.size+=e.length},ISOBox.prototype._writeUTF8String=function(e){var t=ISOBoxer.Utils.utf8ToByteArray(e);if(this._rawo)for(var n=new DataView(this._rawo.buffer,this._cursor.offset,t.length),r=0;r<t.length;r++)n.setUint8(r,t[r]);else this.size+=t.length},ISOBox.prototype._writeField=function(e,t,n){switch(e){case"uint":this._writeUint(t,n);break;case"int":this._writeInt(t,n);break;case"template":this._writeTemplate(t,n);break;case"string":-1==t?this._writeTerminatedString(n):this._writeString(t,n);break;case"data":this._writeData(n);break;case"utf8":this._writeUTF8String(n)}},ISOBox.prototype._boxProcessors.ardi=function(){this._procFullBox(),this._procField("audio_rendering_indication","uint",8)},ISOBox.prototype._boxProcessors.avc1=ISOBox.prototype._boxProcessors.avc2=ISOBox.prototype._boxProcessors.avc3=ISOBox.prototype._boxProcessors.avc4=ISOBox.prototype._boxProcessors.hvc1=ISOBox.prototype._boxProcessors.hev1=ISOBox.prototype._boxProcessors.encv=function(){this._procFieldArray("reserved1",6,"uint",8),this._procField("data_reference_index","uint",16),this._procField("pre_defined1","uint",16),this._procField("reserved2","uint",16),this._procFieldArray("pre_defined2",3,"uint",32),this._procField("width","uint",16),this._procField("height","uint",16),this._procField("horizresolution","template",32),this._procField("vertresolution","template",32),this._procField("reserved3","uint",32),this._procField("frame_count","uint",16),this._procFieldArray("compressorname",32,"uint",8),this._procField("depth","uint",16),this._procField("pre_defined3","int",16),this._procField("config","data",-1)},ISOBox.prototype._boxProcessors.ctts=function(){this._procFullBox(),this._procField("entry_count","uint",32),this._procEntries("entries",this.entry_count,function(e){this._procEntryField(e,"sample_count","uint",32),this._procEntryField(e,"sample_offset",1===this.version?"int":"uint",32)})},ISOBox.prototype._boxProcessors.dref=function(){this._procFullBox(),this._procField("entry_count","uint",32),this._procSubBoxes("entries",this.entry_count)},ISOBox.prototype._boxProcessors.elng=function(){this._procFullBox(),this._procField("extended_language","utf8string")},ISOBox.prototype._boxProcessors.elst=function(){this._procFullBox(),this._procField("entry_count","uint",32),this._procEntries("entries",this.entry_count,function(e){this._procEntryField(e,"segment_duration","uint",1===this.version?64:32),this._procEntryField(e,"media_time","int",1===this.version?64:32),this._procEntryField(e,"media_rate_integer","int",16),this._procEntryField(e,"media_rate_fraction","int",16)})},ISOBox.prototype._boxProcessors.emsg=function(){this._procFullBox(),1==this.version?(this._procField("timescale","uint",32),this._procField("presentation_time","uint",64),this._procField("event_duration","uint",32),this._procField("id","uint",32),this._procField("scheme_id_uri","string",-1),this._procField("value","string",-1)):(this._procField("scheme_id_uri","string",-1),this._procField("value","string",-1),this._procField("timescale","uint",32),this._procField("presentation_time_delta","uint",32),this._procField("event_duration","uint",32),this._procField("id","uint",32)),this._procField("message_data","data",-1)},ISOBox.prototype._boxProcessors.free=ISOBox.prototype._boxProcessors.skip=function(){this._procField("data","data",-1)},ISOBox.prototype._boxProcessors.frma=function(){this._procField("data_format","uint",32)},ISOBox.prototype._boxProcessors.ftyp=ISOBox.prototype._boxProcessors.styp=function(){this._procField("major_brand","string",4),this._procField("minor_version","uint",32);var e=-1;this._parsing&&(e=(this._raw.byteLength-(this._cursor.offset-this._raw.byteOffset))/4),this._procFieldArray("compatible_brands",e,"string",4)},ISOBox.prototype._boxProcessors.hdlr=function(){this._procFullBox(),this._procField("pre_defined","uint",32),this._procField("handler_type","string",4),this._procFieldArray("reserved",3,"uint",32),this._procField("name","string",-1)},ISOBox.prototype._boxProcessors.imda=function(){this._procField("imda_identifier","uint",32),this._procField("data","data",-1)},ISOBox.prototype._boxProcessors.kind=function(){this._procFullBox(),this._procField("schemeURI","utf8string"),this._procField("value","utf8string")},ISOBox.prototype._boxProcessors.labl=function(){this._procFullBox(),this.is_group_label=0!=(1&this.flags),this._procField("label_id","uint",16),this._procField("language","utf8string"),this._procField("label","utf8string")},ISOBox.prototype._boxProcessors.mdat=function(){this._procField("data","data",-1)},ISOBox.prototype._boxProcessors.mdhd=function(){this._procFullBox(),this._procField("creation_time","uint",1==this.version?64:32),this._procField("modification_time","uint",1==this.version?64:32),this._procField("timescale","uint",32),this._procField("duration","uint",1==this.version?64:32),this._parsing||"string"!=typeof this.language||(this.language=this.language.charCodeAt(0)-96<<10|this.language.charCodeAt(1)-96<<5|this.language.charCodeAt(2)-96),this._procField("language","uint",16),this._parsing&&(this.language=String.fromCharCode(96+(this.language>>10&31),96+(this.language>>5&31),96+(31&this.language))),this._procField("pre_defined","uint",16)},ISOBox.prototype._boxProcessors.mehd=function(){this._procFullBox(),this._procField("fragment_duration","uint",1==this.version?64:32)},ISOBox.prototype._boxProcessors.meta=function(){this._procFullBox()},ISOBox.prototype._boxProcessors.mfhd=function(){this._procFullBox(),this._procField("sequence_number","uint",32)},ISOBox.prototype._boxProcessors.mfro=function(){this._procFullBox(),this._procField("mfra_size","uint",32)},ISOBox.prototype._boxProcessors.mp4a=ISOBox.prototype._boxProcessors.enca=function(){this._procFieldArray("reserved1",6,"uint",8),this._procField("data_reference_index","uint",16),this._procFieldArray("reserved2",2,"uint",32),this._procField("channelcount","uint",16),this._procField("samplesize","uint",16),this._procField("pre_defined","uint",16),this._procField("reserved3","uint",16),this._procField("samplerate","template",32),this._procField("esds","data",-1)},ISOBox.prototype._boxProcessors.mvhd=function(){this._procFullBox(),this._procField("creation_time","uint",1==this.version?64:32),this._procField("modification_time","uint",1==this.version?64:32),this._procField("timescale","uint",32),this._procField("duration","uint",1==this.version?64:32),this._procField("rate","template",32),this._procField("volume","template",16),this._procField("reserved1","uint",16),this._procFieldArray("reserved2",2,"uint",32),this._procFieldArray("matrix",9,"template",32),this._procFieldArray("pre_defined",6,"uint",32),this._procField("next_track_ID","uint",32)},ISOBox.prototype._boxProcessors.payl=function(){this._procField("cue_text","utf8")},ISOBox.prototype._boxProcessors.prft=function(){this._procFullBox(),this._procField("reference_track_ID","uint",32),this._procField("ntp_timestamp_sec","uint",32),this._procField("ntp_timestamp_frac","uint",32),this._procField("media_time","uint",1==this.version?64:32)},ISOBox.prototype._boxProcessors.prsl=function(){this._procFullBox(),this._procField("group_id","uint",32),this._procField("num_entities_in_group","uint",32),this._procEntries("entities",this.num_entities_in_group,function(e){this._procEntryField(e,"entity_id","uint",32)}),4096&this.flags&&this._procField("preselection_tag","utf8string"),8192&this.flags&&this._procField("selection_priority","uint",8),16384&this.flags&&this._procField("interleaving_tag","utf8string")},ISOBox.prototype._boxProcessors.pssh=function(){this._procFullBox(),this._procFieldArray("SystemID",16,"uint",8),this._procField("DataSize","uint",32),this._procFieldArray("Data",this.DataSize,"uint",8)},ISOBox.prototype._boxProcessors.schm=function(){this._procFullBox(),this._procField("scheme_type","uint",32),this._procField("scheme_version","uint",32),1&this.flags&&this._procField("scheme_uri","string",-1)},ISOBox.prototype._boxProcessors.sdtp=function(){this._procFullBox();var e=-1;this._parsing&&(e=this._raw.byteLength-(this._cursor.offset-this._raw.byteOffset)),this._procFieldArray("sample_dependency_table",e,"uint",8)},ISOBox.prototype._boxProcessors.sidx=function(){this._procFullBox(),this._procField("reference_ID","uint",32),this._procField("timescale","uint",32),this._procField("earliest_presentation_time","uint",1==this.version?64:32),this._procField("first_offset","uint",1==this.version?64:32),this._procField("reserved","uint",16),this._procField("reference_count","uint",16),this._procEntries("references",this.reference_count,function(e){this._parsing||(e.reference=(1&e.reference_type)<<31,e.reference|=2147483647&e.referenced_size,e.sap=(1&e.starts_with_SAP)<<31,e.sap|=(3&e.SAP_type)<<28,e.sap|=268435455&e.SAP_delta_time),this._procEntryField(e,"reference","uint",32),this._procEntryField(e,"subsegment_duration","uint",32),this._procEntryField(e,"sap","uint",32),this._parsing&&(e.reference_type=e.reference>>31&1,e.referenced_size=2147483647&e.reference,e.starts_with_SAP=e.sap>>31&1,e.SAP_type=e.sap>>28&7,e.SAP_delta_time=268435455&e.sap)})},ISOBox.prototype._boxProcessors.smhd=function(){this._procFullBox(),this._procField("balance","uint",16),this._procField("reserved","uint",16)},ISOBox.prototype._boxProcessors.ssix=function(){this._procFullBox(),this._procField("subsegment_count","uint",32),this._procEntries("subsegments",this.subsegment_count,function(e){this._procEntryField(e,"ranges_count","uint",32),this._procSubEntries(e,"ranges",e.ranges_count,function(e){this._procEntryField(e,"level","uint",8),this._procEntryField(e,"range_size","uint",24)})})},ISOBox.prototype._boxProcessors.stsd=function(){this._procFullBox(),this._procField("entry_count","uint",32),this._procSubBoxes("entries",this.entry_count)},ISOBox.prototype._boxProcessors.sttg=function(){this._procField("settings","utf8")},ISOBox.prototype._boxProcessors.stts=function(){this._procFullBox(),this._procField("entry_count","uint",32),this._procEntries("entries",this.entry_count,function(e){this._procEntryField(e,"sample_count","uint",32),this._procEntryField(e,"sample_delta","uint",32)})},ISOBox.prototype._boxProcessors.subs=function(){this._procFullBox(),this._procField("entry_count","uint",32),this._procEntries("entries",this.entry_count,function(e){this._procEntryField(e,"sample_delta","uint",32),this._procEntryField(e,"subsample_count","uint",16),this._procSubEntries(e,"subsamples",e.subsample_count,function(e){this._procEntryField(e,"subsample_size","uint",1===this.version?32:16),this._procEntryField(e,"subsample_priority","uint",8),this._procEntryField(e,"discardable","uint",8),this._procEntryField(e,"codec_specific_parameters","uint",32)})})},ISOBox.prototype._boxProcessors.tenc=function(){this._procFullBox(),this._procField("default_IsEncrypted","uint",24),this._procField("default_IV_size","uint",8),this._procFieldArray("default_KID",16,"uint",8)},ISOBox.prototype._boxProcessors.tfdt=function(){this._procFullBox(),this._procField("baseMediaDecodeTime","uint",1==this.version?64:32)},ISOBox.prototype._boxProcessors.tfhd=function(){this._procFullBox(),this._procField("track_ID","uint",32),1&this.flags&&this._procField("base_data_offset","uint",64),2&this.flags&&this._procField("sample_description_offset","uint",32),8&this.flags&&this._procField("default_sample_duration","uint",32),16&this.flags&&this._procField("default_sample_size","uint",32),32&this.flags&&this._procField("default_sample_flags","uint",32)},ISOBox.prototype._boxProcessors.tfra=function(){this._procFullBox(),this._procField("track_ID","uint",32),this._parsing||(this.reserved=0,this.reserved|=(48&this.length_size_of_traf_num)<<4,this.reserved|=(12&this.length_size_of_trun_num)<<2,this.reserved|=3&this.length_size_of_sample_num),this._procField("reserved","uint",32),this._parsing&&(this.length_size_of_traf_num=(48&this.reserved)>>4,this.length_size_of_trun_num=(12&this.reserved)>>2,this.length_size_of_sample_num=3&this.reserved),this._procField("number_of_entry","uint",32),this._procEntries("entries",this.number_of_entry,function(e){this._procEntryField(e,"time","uint",1===this.version?64:32),this._procEntryField(e,"moof_offset","uint",1===this.version?64:32),this._procEntryField(e,"traf_number","uint",8*(this.length_size_of_traf_num+1)),this._procEntryField(e,"trun_number","uint",8*(this.length_size_of_trun_num+1)),this._procEntryField(e,"sample_number","uint",8*(this.length_size_of_sample_num+1))})},ISOBox.prototype._boxProcessors.tkhd=function(){this._procFullBox(),this._procField("creation_time","uint",1==this.version?64:32),this._procField("modification_time","uint",1==this.version?64:32),this._procField("track_ID","uint",32),this._procField("reserved1","uint",32),this._procField("duration","uint",1==this.version?64:32),this._procFieldArray("reserved2",2,"uint",32),this._procField("layer","uint",16),this._procField("alternate_group","uint",16),this._procField("volume","template",16),this._procField("reserved3","uint",16),this._procFieldArray("matrix",9,"template",32),this._procField("width","template",32),this._procField("height","template",32)},ISOBox.prototype._boxProcessors.trex=function(){this._procFullBox(),this._procField("track_ID","uint",32),this._procField("default_sample_description_index","uint",32),this._procField("default_sample_duration","uint",32),this._procField("default_sample_size","uint",32),this._procField("default_sample_flags","uint",32)},ISOBox.prototype._boxProcessors.trun=function(){this._procFullBox(),this._procField("sample_count","uint",32),1&this.flags&&this._procField("data_offset","int",32),4&this.flags&&this._procField("first_sample_flags","uint",32),this._procEntries("samples",this.sample_count,function(e){256&this.flags&&this._procEntryField(e,"sample_duration","uint",32),512&this.flags&&this._procEntryField(e,"sample_size","uint",32),1024&this.flags&&this._procEntryField(e,"sample_flags","uint",32),2048&this.flags&&this._procEntryField(e,"sample_composition_time_offset",1===this.version?"int":"uint",32)})},ISOBox.prototype._boxProcessors["url "]=ISOBox.prototype._boxProcessors["urn "]=function(){this._procFullBox(),"urn "===this.type&&this._procField("name","string",-1),this._procField("location","string",-1)},ISOBox.prototype._boxProcessors.vlab=function(){this._procField("source_label","utf8")},ISOBox.prototype._boxProcessors.vmhd=function(){this._procFullBox(),this._procField("graphicsmode","uint",16),this._procFieldArray("opcolor",3,"uint",16)},ISOBox.prototype._boxProcessors.vttC=function(){this._procField("config","utf8")},ISOBox.prototype._boxProcessors.vtte=function(){};var SEARCHBOX_UA_REGEX=/alexa|bot|crawl(er|ing)|facebookexternalhit|feedburner|google web preview|nagios|postrank|pingdom|slurp|spider|yahoo!|yandex/,SEARCHBOT_OS_REGEX=/(nuhk|curl|Googlebot|Yammybot|Openbot|Slurp|MSNBot|Ask\ Jeeves\/Teoma|ia_archiver)/,REQUIRED_VERSION_PARTS=3,userAgentRules=[["aol",/AOLShield\/([0-9\._]+)/],["edge",/Edge\/([0-9\._]+)/],["edge-ios",/EdgiOS\/([0-9\._]+)/],["yandexbrowser",/YaBrowser\/([0-9\._]+)/],["kakaotalk",/KAKAOTALK\s([0-9\.]+)/],["samsung",/SamsungBrowser\/([0-9\.]+)/],["silk",/\bSilk\/([0-9._-]+)\b/],["miui",/MiuiBrowser\/([0-9\.]+)$/],["beaker",/BeakerBrowser\/([0-9\.]+)/],["edge-chromium",/EdgA?\/([0-9\.]+)/],["chromium-webview",/(?!Chrom.*OPR)wv\).*Chrom(?:e|ium)\/([0-9\.]+)(:?\s|$)/],["chrome",/(?!Chrom.*OPR)Chrom(?:e|ium)\/([0-9\.]+)(:?\s|$)/],["phantomjs",/PhantomJS\/([0-9\.]+)(:?\s|$)/],["crios",/CriOS\/([0-9\.]+)(:?\s|$)/],["firefox",/Firefox\/([0-9\.]+)(?:\s|$)/],["fxios",/FxiOS\/([0-9\.]+)/],["opera-mini",/Opera Mini.*Version\/([0-9\.]+)/],["opera",/Opera\/([0-9\.]+)(?:\s|$)/],["opera",/OPR\/([0-9\.]+)(:?\s|$)/],["pie",/^Microsoft Pocket Internet Explorer\/(\d+\.\d+)$/],["pie",/^Mozilla\/\d\.\d+\s\(compatible;\s(?:MSP?IE|MSInternet Explorer) (\d+\.\d+);.*Windows CE.*\)$/],["netfront",/^Mozilla\/\d\.\d+.*NetFront\/(\d.\d)/],["ie",/Trident\/7\.0.*rv\:([0-9\.]+).*\).*Gecko$/],["ie",/MSIE\s([0-9\.]+);.*Trident\/[4-7].0/],["ie",/MSIE\s(7\.0)/],["bb10",/BB10;\sTouch.*Version\/([0-9\.]+)/],["android",/Android\s([0-9\.]+)/],["ios",/Version\/([0-9\._]+).*Mobile.*Safari.*/],["safari",/Version\/([0-9\._]+).*Safari/],["facebook",/FB[AS]V\/([0-9\.]+)/],["instagram",/Instagram\s([0-9\.]+)/],["ios-webview",/AppleWebKit\/([0-9\.]+).*Mobile/],["ios-webview",/AppleWebKit\/([0-9\.]+).*Gecko\)$/],["curl",/^curl\/([0-9\.]+)$/],["searchbot",SEARCHBOX_UA_REGEX]],operatingSystemRules=[["iOS",/iP(hone|od|ad)/],["Android OS",/Android/],["BlackBerry OS",/BlackBerry|BB10/],["Windows Mobile",/IEMobile/],["Amazon OS",/Kindle/],["Windows 3.11",/Win16/],["Windows 95",/(Windows 95)|(Win95)|(Windows_95)/],["Windows 98",/(Windows 98)|(Win98)/],["Windows 2000",/(Windows NT 5.0)|(Windows 2000)/],["Windows XP",/(Windows NT 5.1)|(Windows XP)/],["Windows Server 2003",/(Windows NT 5.2)/],["Windows Vista",/(Windows NT 6.0)/],["Windows 7",/(Windows NT 6.1)/],["Windows 8",/(Windows NT 6.2)/],["Windows 8.1",/(Windows NT 6.3)/],["Windows 10",/(Windows NT 10.0)/],["Windows ME",/Windows ME/],["Windows CE",/Windows CE|WinCE|Microsoft Pocket Internet Explorer/],["Open BSD",/OpenBSD/],["Sun OS",/SunOS/],["Chrome OS",/CrOS/],["Linux",/(Linux)|(X11)/],["Mac OS",/(Mac_PowerPC)|(Macintosh)/],["QNX",/QNX/],["BeOS",/BeOS/],["OS/2",/OS\/2/]];function matchUserAgent(r){return""!==r&&userAgentRules.reduce(function(e,t){var n=t[0],t=t[1];if(e)return e;t=t.exec(r);return!!t&&[n,t]},!1)}function detectOS(e){for(var t=0,n=operatingSystemRules.length;t<n;t++){var r=_slicedToArray(operatingSystemRules[t],2),i=r[0];if(r[1].exec(e))return i}return null}function parseUserAgent(e){var t=matchUserAgent(e);if(!t)return null;var n=t[0],r=t[1],t=r[1]&&r[1].split(".").join("_").split("_").slice(0,3);t?t.length<REQUIRED_VERSION_PARTS&&(t=[].concat(_toConsumableArray(t),_toConsumableArray(function(e){for(var t=[],n=0;n<e;n++)t.push("0");return t}(REQUIRED_VERSION_PARTS-t.length)))):t=[];r=t.join("."),t=detectOS(e),e=SEARCHBOT_OS_REGEX.exec(e);return e&&e[1]?{browser:n,version:r,os:t,bot:e[1]}:{browser:n,version:r,os:t}}var browser,isChrome=!navigator.userAgent.includes("Edg")&&!navigator.userAgent.includes("OPR")&&(navigator.userAgent.includes("Chrome")||navigator.userAgent.includes("Crios")),chromeVersion=isChrome&&Number((navigator.userAgent.match(/(?:Chrome|Crios)\/(\d+)/)||[])[1]||0)||0;navigator.userAgent.includes("Chrome")&&("function"==typeof Window?window.browser=chrome:browser=chrome);var extension_source="chrome",enable_yt=!1,enable_check_update=!1,freeLimit={hqDownload:{maxCount:1,hours:1},streamDownload:{maxCount:1,hours:1},videoRecord:{maxCount:1,hours:1},audioRecord:{maxCount:1,hours:1},tabRecord:{maxCount:1,hours:1},screenRecord:{maxCount:1,hours:1}},configuration={BRAND:"VidHelper",DOMAIN:"https://vidhelper.app",FAQ:"https://vidhelper.app#faqs",MEASUREMENT_ID:"G-LNBWCVYXVX",API_SECRET:"vzBAqqNRSb6ugOR-mqf6CA",LICENSE_BASEURL:"https://vidhelper.app/api",CHECK_FOR_UPDATE:enable_check_update,UPDATE_INTERVAL:36e5,CHECK_UPDATE_URL:"https://app.vidhelper.app/latest",HOWTO_INSTAL_URL:"https://vidhelper.app/how-to-manually-install-vidhelper-extension",STORE_ID:19749,PRODUCT_IDS:[170497,170523,170502,170524,170494,170522],PRODUCT:{name:browser.i18n.getMessage("Pro"),features:[{name:browser.i18n.getMessage("UnlimitMediaDL"),free:browser.i18n.getMessage("Yes"),paid:browser.i18n.getMessage("Yes")},{name:browser.i18n.getMessage("sitesSupported"),free:browser.i18n.getMessage("Yes"),paid:browser.i18n.getMessage("Yes")},{name:browser.i18n.getMessage("1080pSupport"),free:browser.i18n.getMessage("NumEveryHours").replace("{{1}}",freeLimit.hqDownload.maxCount).replace("{{2}}",freeLimit.hqDownload.hours),paid:browser.i18n.getMessage("Unlimited")},{name:browser.i18n.getMessage("NumStreamLimit"),free:browser.i18n.getMessage("NumEveryHours").replace("{{1}}",freeLimit.streamDownload.maxCount).replace("{{2}}",freeLimit.streamDownload.hours),paid:browser.i18n.getMessage("Unlimited")},{name:browser.i18n.getMessage("EachRecordLimit"),free:browser.i18n.getMessage("NumEveryHours").replace("{{1}}",freeLimit.videoRecord.maxCount).replace("{{2}}",freeLimit.videoRecord.hours),paid:browser.i18n.getMessage("Unlimited")},{name:browser.i18n.getMessage("hlsDashMerge"),free:browser.i18n.getMessage("No"),paid:browser.i18n.getMessage("Yes")}],discount:browser.i18n.getMessage("discountInfo"),plans:[{name:browser.i18n.getMessage("Monthly"),checkoutUrl:"https://vidhelper.app/checkout?plan=monthly",price:"$5.9",priceMonthly:"$5.9",origPriceMonthly:"$7.9",description:browser.i18n.getMessage("MonthlyDes")},{name:browser.i18n.getMessage("Yearly"),checkoutUrl:"https://vidhelper.app/checkout?plan=yearly",price:"$29",priceMonthly:"$2.4",origPriceMonthly:"$3.2",description:browser.i18n.getMessage("YearlyDes").replace("{{1}}","$29").replace("{{2}}","$30"),mostPopular:!0},{name:browser.i18n.getMessage("Lifetime"),checkoutUrl:"https://vidhelper.app/checkout?plan=lifetime",price:"$55",origPrice:"$75",description:browser.i18n.getMessage("LifetimeDes")}]},sentryDSN:"https://<EMAIL>/4508324004036608",FREE_LIMIT:freeLimit,GENERAL:{showMergeInfo:!1,showVideoElRecordTip:!0,showTabRecordTip:!1,showDownloadStreamTip:!0,recordCountDown:3,useDedicatedDownloader:!1,dedicatedDownloader:"downloadingPage",defaultDownloader:"downloadingFrame",enableRegionSelector:!0,enableScreenRecord:!0,tabRecordType:"recordingPage"},MEDIA_FILTER:{VIDEO:{enable:!0,size:1048576,range:[0,104857600],step:1048576,candisable:!1},AUDIO:{enable:!0,size:307200,range:[0,20971520],step:1024,candisable:!1}},CSP_LIST:["www.facebook.com","www.instagram.com","twitter.com","x.com","youtube.com","vimeo.com","www.tokopedia.com"],SHAKA_PLAYER_LIST:["vk.com","ok.ru"],SKIP_BG_DOWNLOAD:["www.tiktok.com","streamplay.pw"],ENABLE_POPUP_QUERY:!1,INFOBOX_UP:!1,CONTACT_US:"mailto:<EMAIL>",RATE_US:"https://chrome.google.com/webstore/detail/".concat(browser.runtime.id,"/reviews"),DROPBOX_KEY:"6awh8g09fftibys",ENABLE_CONTEXTMENU:!1,CONTEXTMENU_TITLE:"Search Videos for",ENABLE_INSTALL_URL:!1,INSTAL_SUFFIX:"/install",ENABLE_UNINSTALL_URL:!1,UNINSTAL_SUFFIX:"/uninstall",ENABLE_UPDATE_URL:!1,UPDATE_URL:"/uninstall",DISALLOW_YOUTUBE:!enable_yt,DISALLOW_DOMAINS_REGEXP:enable_yt?null:/^(?:(?:https?:)?\/\/)?(?:[^/]*.)?youtube.com/,DISABLE_HEADRECEIVE_DOMAINS_REGEXP:/^(?:(?:https?:)?\/\/)?(?:[^/]*.)?(?:www.tiktok.com|www.instagram.com|www.facebook.com|vimeo.com|youtube.com|www.bilibili.com|www.bilibili.tv)/,DISABLE_IMAGE_HEADRECEIVE_DOMAINS_REGEXP:/^(?:(?:https?:)?\/\/)?(?:[^/]*.)?(?:vidhelper.app)/,INSTALL_DAYS:30,USE_COUNT:10,MAX_USE_STATISTICS:60,ENABLE_FINDERS:_objectSpread(_objectSpread({vimeo:"VMFinder",dailymotion:"DMFinder",facebook:"FBFinder",tiktok:"TTFinder",instagram:"IGFinder",soundcloud:"SCFinder"},enable_yt?{youtube:"YTFinder"}:{}),{},{bilibili:"BILIFinder",xx:"XXFinder"}),FINDER_MATCHES:["<all_urls>"],FINDER_EXCLUDE_MATCHES:["*://https://vidhelper.app/*"]},contentMap={m3u8:"video/mp4",m3u:"video/mp4",mp4:"video/mp4","3gp":"video/3gpp",flv:"video/x-flv",mov:"video/quicktime",avi:"video/x-msvideo",wmv:"video/x-ms-wmv",webm:"video/webm",ogg:"video/ogg",ogv:"video/ogg",f4v:"video/x-f4v",acc:"application/vnd.americandynamics.acc",mkv:"video/x-matroska",rmvb:"application/vnd.rn-realmedia-vbr",m4s:"video/iso.segment"},streamExts=["m3u8","m3u","mpd"],audioExts=["aac","mid","mp3","oga","wav","wma","flac","acc"],videoExts=["webm","3gp","3g2","rmvb","mp4","3gp2","flv","mov","avi","wmv","ogg","ogv","f4v","mkv"],imageExts=["bmp","gif","avif","ico","png","apng","svg","tif","tiff","webp","jpeg","jpg"],imageFormats={"image/bmp":["bmp"],"image/gif":["gif"],"image/avif":["avif"],"image/x-icon":["ico"],"image/vnd.microsoft.icon":["ico"],"image/png":["png"],"image/apng":["apng"],"image/svg+xml":["svg"],"image/tiff":["tif","tiff"],"image/webp":["webp"],"image/jpeg":["jpeg","jpg"],"image/jpg":["jpeg","jpg"]},imageMimeTypes=Object.keys(imageFormats),audioFormats={"audio/mp3":["mp3"],"audio/mp4":["m4a"],"audio/aac":["aac"],"audio/midi":["mid"],"audio/x-midi":["mid"],"audio/mpeg":["mp3"],"audio/ogg":["oga"],"audio/wav":["wav"],"audio/x-wav":["wav"],"audio/vnd.wave":["wav"],"audio/wave":["wav"],"audio/x-pn-wav":["wav"],"audio/webm":["webm"],"audio/3gpp":["3gp"],"audio/3gpp2":["3g2"],"audio/x-ms-wma":["wma"],"audio/flac":["flac"]},audioMimeTypes=Object.keys(audioFormats),videoFormats={"application/vnd.americandynamics.acc":["acc"],"application/vnd.rn-realmedia-vbr":["rmvb"],"video/mp4":["mp4","m4s","m4v"],"video/3gpp":["3gp"],"video/3gpp2":["3gp2"],"video/x-flv":["flv"],"video/x-m4v":["m4v"],"video/quicktime":["mov"],"video/x-msvideo":["avi"],"video/x-ms-wmv":["wmv"],"video/webm":["webm"],"video/ogg":["ogg","ogv"],"application/ogg":["ogg"],"video/x-f4v":["f4v"],"video/x-matroska":["mkv"],"video/iso.segment":["m4s"],"application/mp4":["mp4"]},videoMimeTypes=Object.keys(videoFormats),streamFormats={"audio/mpegurl":["m3u8","m3u"],"audio/x-mpegurl":["m3u8","m3u"],"application/vnd.apple.mpegurl":["m3u8","m3u"],"application/x-mpegurl":["m3u8","m3u"],"application/dash+xml":["mpd"],"application/octet-stream":["m3u8","m3u","mpd","mp4","webm","avi","ogg","flv","mkv","3gp","ts","m4v","mp3","wav"],"binary/octet-stream":["m3u8","m3u","mpd","mp4","webm","avi","ogg","flv","mkv","3gp","ts","m4v","mp3","wav"]},streamMimeTypes=Object.keys(streamFormats),mimeTypeFormats={audio:audioFormats,video:videoFormats,stream:streamFormats},allowedExtensions=[audioExts,videoExts,streamExts].flat(),typeExts=Object.entries(mimeTypeFormats).flatMap(function(e){var t=_slicedToArray(e,2),e=t[0],t=t[1];if("stream"!==e)return _defineProperty({},e,Object.values(t).flatMap(function(e){return e}))}).reduce(function(e,t){return Object.assign(e,t)},{}),mimeTypes=Object.entries(mimeTypeFormats).flatMap(function(e){e=_slicedToArray(e,2),e[0],e=e[1];return Object.keys(e)});function getTypeFromExt(e){if("string"!=typeof e)return null;for(var t=0,n=Object.keys(mimeTypeFormats);t<n.length;t++){var r=n[t],i=Object.values(mimeTypeFormats[r]).flat();if("stream"!=r&&-1!=i.indexOf(e))return r}return null}function getFileNameFromURL(e){var t=e.match(/\/?([^/?#]+)(?:$|\?|#)/);return(t=t||e.match(/\/([^/]+)\/?$/))?t[1]:"unknown"}function getFileNameFromURLPathName(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:64,n=2<arguments.length&&void 0!==arguments[2]&&arguments[2];try{var r,i=new URL(t).pathname;n&&(r=i.match(_wrapRegExp(/(.+?)(?:\.(\w{2,4}))?$/,{name:1,ext:2})),r&&(i=r.groups.name),r.groups.ext);var i=i.split("").reverse().join(""),o=_toConsumableArray(i.matchAll(/\//g)).map(function(e){return e.index}),a=e;if(0<o.length)for(var s=o.length-1;0<=s;s--)if(e>=o[s]){a=o[s];break}return i.slice(0,a).split("").map(function(e){return"/"===e?"-":e}).join("").split("").reverse().join("")}catch(e){return getFileNameFromURL(t)}}function getTitleExtFromFileName(e){var t=_slicedToArray(e.split(/\.(\w{2,4})$/),2),e=t[0],t=t[1],t=void 0===t?null:t;return{title:e,ext:(null==t?void 0:t.toLowerCase())||null}}function getTitleExtFromUrl(e){var t=1<arguments.length&&void 0!==arguments[1]&&arguments[1],n={title:"unknow",ext:null};return"string"!=typeof e||(n=getTitleExtFromFileName(getFileNameFromURL(e))).ext&&t&&allowedExtensions.indexOf(n.ext)<0&&(n.ext=null),n}function getTypeFromExt(e){return videoExts.includes(e)?"video":audioExts.includes(e)?"audio":streamExts.includes(e)?"stream":null}function getTypeFormatFromExtMime(e,t){var n={type:null,ext:null};if("string"!=typeof(t=t&&t.toLowerCase().split(";")[0]))return n;for(var r=0,i=Object.keys(mimeTypeFormats);r<i.length;r++){var o=i[r],a=mimeTypeFormats[o][t];if("stream"!=o&&a){var s=a.indexOf(e);return n.type=o,n.ext=s<0?a[0]:a[s],n}}if(mimeTypeFormats.stream[t]){var u=mimeTypeFormats.stream[t].indexOf(e);if(n.type="stream",0<=t.indexOf("octet-stream")){n.ext=u<0?null:mimeTypeFormats.stream[t][u];for(var l=0,c=Object.entries(typeExts);l<c.length;l++){var d=_slicedToArray(c[l],2),f=d[0];if(d[1].includes(n.ext)){n.type=f;break}}}else n.ext=u<0?mimeTypeFormats.stream[t][0]:mimeTypeFormats.stream[t][u];return n}return n}function areObjectsEqual(e,t){var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(var i=0,o=n;i<o.length;i++){var a=o[i];if(null==t||!t.key||e[a]!==t[a])return!1}return!0}function p(e){return _p.apply(this,arguments)}function _p(){return(_p=_asyncToGenerator(_regeneratorRuntime().mark(function e(r){var t,i,n,o=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:for(t=o.length,i=new Array(1<t?t-1:0),n=1;n<t;n++)i[n-1]=o[n];return e.abrupt("return",new Promise(function(t,n){r.apply(void 0,i.concat([function(e){browser.runtime.lastError?n(browser.runtime.lastError):t(e)}]))}));case 2:case"end":return e.stop()}},e)}))).apply(this,arguments)}function isDevelop(){return browser.runtime.getManifest().name.startsWith("Develop")}function parse_match_pattern(e){if("string"!=typeof e)return null;function t(e){return e.replace(/[[^$.|?*+(){}\\]/g,"\\$&")}var n="(?:^",r=/^(\*|https?|file|ftp|chrome-extension):\/\//.exec(e);if(!r)return null;if(e=e.substr(r[0].length),n+="*"===r[1]?"https?://":r[1]+"://","file"!==r[1]){if(!(r=/^(?:\*|(\*\.)?([^\/*]+))(?=\/)/.exec(e)))return null;e=e.substr(r[0].length),"*"===r[0]?n+="[^/]+":(r[1]&&(n+="(?:[^/]+\\.)?"),n+=t(r[2]))}return n+=e.split("*").map(t).join(".*"),n+="$)"}function formatBytes(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:2,n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:1024;if("string"==typeof e&&(e=parseInt(e)),0===e)return"0 Bytes";var r=t<0?0:t,t=Math.floor(Math.log(e)/Math.log(n));return parseFloat((e/Math.pow(n,t)).toFixed(r))+" "+["Bytes","KB","MB","GB","TB","PB","EB","ZB","YB"][t]}function is1080pOrHigher(e){if(null!=e&&e.width&&null!=e&&e.height){if(1920<=e.width&&1080<=e.height||1080<=e.width&&1920<=e.height)return!0}else if(e.resolution)try{var t=e.resolution.split("x"),n=parseInt(t[0],10),t=parseInt(t[1],10);if(1920<=n&&1080<=t||1080<=n&&1920<=t)return!0}catch(e){}else if(e.quality)try{if(1080<=parseInt(e.quality.split("p")[0]))return!0}catch(e){}return!1}function getFileNameFromcontentDisposition(e){e=e.match(/filename=["']?([^'"\s;]+)["']?/i);return e?e[1]:null}function getRangeInfo(e){var t=e.split(" ");if(2===t.length){e=t[1].split("/");if(2===e.length){t=parseInt(e[1]);if(t)return{chunk:e[0],total:t}}}return null}function getSizeFromReceivedHeader(e){var t=e.get("content-length",null),e=e.get("content-range",null);if(t)return parseInt(t);if(e){e=getRangeInfo(e);if(e)return e.total}return null}function jsonHeadersToResponseHeaders(e){var n=_objectSpread({},e);return{get:function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:null,t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:null;if(n){if(e){e=e.toLowerCase();return n[e]||t}return n}return n}}}function responseHeadersToJson(e){var t={},n=_createForOfIteratorHelper(e.entries());try{for(n.s();!(i=n.n()).done;){var r=_slicedToArray(i.value,2),i=r[0],r=r[1];t[i]=r}}catch(e){n.e(e)}finally{n.f()}return t}function needPicker(e){if(1610612736<(null==e?void 0:e.size))return!0;if(null!=e&&e.duration){var t,n,r=0;if(null!=e&&e.bandwidth?r=e.bandwidth:e.width&&e.height?(n=(null==e?void 0:e.frameRate)||30,r=e.width*e.height*n*.1):e.resolution&&(t=e.resolution.split("x"),n=(null==e?void 0:e.frameRate)||30,r=t[0]*t[1]*n*.1),1610612736<e.duration*r/8)return!0}return!1}function hms(e){var t,n=e<3600?(t=14,5):(t=11,8);return new Date(1e3*e).toISOString().substr(t,n)}function getQualityFromVideoLink(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"";return e.width&&e.height&&(e.resolution="".concat(e.width,"x").concat(e.height)),e.bandwidth&&e.resolution?t="".concat(Math.floor(e.bandwidth/1e3)," kbps (").concat(e.resolution,")"):e.resolution?t=e.resolution:e.bandwidth&&e.quality?t="".concat(Math.floor(e.bandwidth/1e3)," kbps (").concat(e.quality,")"):e.quality?t=e.quality:e.bandwidth?t="".concat(Math.floor(e.bandwidth/1e3)," kbps"):e.size?t=formatBytes(e.size):e.duration?t=e.duration:e.name&&(t=e.name),t}function formatResolution(e,t){for(var n=0,r=Object.entries({"144p":[256,144],"240p":[426,240],"360p":[640,360],"480p":[854,480],"720p":[1280,720],"1080p":[1920,1080],"2K":[2048,1080],QHD:[2560,1440],"4K":[3840,2160],"DCI 4K":[4096,2160],"8K":[7680,4320]});n<r.length;n++){var i=_slicedToArray(r[n],2),o=i[0],a=_slicedToArray(i[1],2),i=a[0],a=a[1];if(e===i&&t===a)return o}return"".concat(e,"x").concat(t)}function getQualityFromVideoLink_new(e){var t=[];e.width&&e.height&&t.push("".concat(formatResolution(e.width,e.height))),e.frameRate&&t.push("".concat(e.frameRate," fps")),e.bandwidth?t.push("".concat(Math.floor(e.bandwidth/1e3)," kbps")):e.bitrate&&t.push("".concat(e.bitrate," kbps"));try{e.duration&&t.push(hms(e.duration))}catch(e){}return e.size&&t.push(formatBytes(e.size)),"audio"==e.type&&e.name&&t.push(e.name),t.join(" | ")}function checkAttr(e,t,n){for(var r=0,i=e.length;r<i;r++)if(e[r][t]==n)return!0;return!1}function checkScript(e,t){return checkAttr(document.scripts,e,t)}function inIframe(){try{return window.self!==window.top}catch(e){return!0}}function delay(t){return new Promise(function(e){return setTimeout(e,t)})}function waitHeaderLoaded(){return _waitHeaderLoaded.apply(this,arguments)}function _waitHeaderLoaded(){return(_waitHeaderLoaded=_asyncToGenerator(_regeneratorRuntime().mark(function e(){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",new Promise(function(e){function t(){return document.head||document.documentElement}var n;t()?e():(n=new MutationObserver(function(){t()&&(n.disconnect(),e())})).observe(document,{childList:!0,subtree:!0})}));case 1:case"end":return e.stop()}},e)}))).apply(this,arguments)}function waitBodyLoaded(){return _waitBodyLoaded.apply(this,arguments)}function _waitBodyLoaded(){return(_waitBodyLoaded=_asyncToGenerator(_regeneratorRuntime().mark(function e(){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",new Promise(function(e){function t(){return document.body}var n;t()?e():(n=new MutationObserver(function(){t()&&(n.disconnect(),e())})).observe(document,{childList:!0,subtree:!0})}));case 1:case"end":return e.stop()}},e)}))).apply(this,arguments)}function waitLoaded(){return _waitLoaded.apply(this,arguments)}function _waitLoaded(){return(_waitLoaded=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var n,r,t=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(n=0<t.length&&void 0!==t[0]?t[0]:null,"loading"!==document.readyState)return e.abrupt("return");e.next=5;break;case 5:return r=null,e.abrupt("return",new Promise(function(e,t){"number"==typeof n&&(r=setTimeout(function(){t(new Error("Timeout exceeded"))},n)),document.addEventListener("DOMContentLoaded",function(){null!=r&&(clearTimeout(r),r=null),e()})}));case 7:case"end":return e.stop()}},e)}))).apply(this,arguments)}function loadScript(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:null,n=!(2<arguments.length&&void 0!==arguments[2])||arguments[2]?document.head||document.documentElement:document.body,r=document.createElement("script"),i=setTimeout(function(){t(r)},1e3);if(e.src?r.src=e.src:e.textContent?r.textContent=e.textContent:e.innerHTML&&(r.innerHTML=e.innerHTML),e.attrs)for(var o=0,a=Object.entries(e.attrs);o<a.length;o++){var s=_slicedToArray(a[o],2),u=s[0],s=s[1],s=void 0===s||s;r.setAttribute(u,s)}t&&(r.onload=function(){null!=i&&(clearTimeout(i),i=null),t(r)}),n.appendChild(r)}function loadScriptPromise(n){var r=1<arguments.length&&void 0!==arguments[1]&&arguments[1];return new Promise(function(t,e){loadScript(n,function(e){t(e)},r)})}function checkLoadScript(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"src",n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;"innerHTML"==t?checkScript(t,e[t])||loadScript(e,n):e.attrs&&e.attrs[t]&&!checkScript(t,e.attrs[t])&&loadScript(e,n)}function loadLink(e){var t=document.head,n=document.createElement("link");if(n.rel="stylesheet",n.href=e.href,e.attrs)for(var r=0,i=Object.entries(e.attrs);r<i.length;r++){var o=_slicedToArray(i[r],2),a=o[0],o=o[1],o=void 0===o||o;n.setAttribute(a,o)}t.appendChild(n)}function checkLink(e,t){return checkAttr(document.styleSheets,e,t)}function checkLoadLink(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"href";e.attrs&&e.attrs[t]&&!checkLink(t,e.attrs[t])&&loadLink(e)}function sendMessage(e){return _sendMessage.apply(this,arguments)}function _sendMessage(){return(_sendMessage=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",p(browser.runtime.sendMessage,t).catch(function(e){return null}));case 1:case"end":return e.stop()}},e)}))).apply(this,arguments)}function XMLRequest(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:null;return null==t&&(t={method:"GET"}),t.cache=t.cache||"default",fetch(e,t).then(function(e){return e})}function syncStorageGet(e){return _syncStorageGet.apply(this,arguments)}function _syncStorageGet(){return(_syncStorageGet=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,p(browser.storage.sync.get.bind(browser.storage.sync),t).catch(function(e){});case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e)}))).apply(this,arguments)}function syncStorageSet(e){return _syncStorageSet.apply(this,arguments)}function _syncStorageSet(){return(_syncStorageSet=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,p(browser.storage.sync.set.bind(browser.storage.sync),t).catch(function(e){});case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e)}))).apply(this,arguments)}function syncStorageRemove(e){return _syncStorageRemove.apply(this,arguments)}function _syncStorageRemove(){return(_syncStorageRemove=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,p(browser.storage.sync.remove.bind(browser.storage.sync),t).catch(function(e){});case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e)}))).apply(this,arguments)}function localStorageGet(e){return _localStorageGet.apply(this,arguments)}function _localStorageGet(){return(_localStorageGet=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,p(browser.storage.local.get.bind(browser.storage.local),t).catch(function(e){});case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e)}))).apply(this,arguments)}function localStorageSet(e){return _localStorageSet.apply(this,arguments)}function _localStorageSet(){return(_localStorageSet=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,p(browser.storage.local.set.bind(browser.storage.local),t).catch(function(e){});case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e)}))).apply(this,arguments)}function localStorageRemove(e){return _localStorageRemove.apply(this,arguments)}function _localStorageRemove(){return(_localStorageRemove=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,p(browser.storage.local.remove.bind(browser.storage.local),t).catch(function(e){});case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e)}))).apply(this,arguments)}function isDocumentReady(){return!(!document.head&&!document.documentElement)}function toBlobURL(e,t){return _toBlobURL.apply(this,arguments)}function _toBlobURL(){return(_toBlobURL=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,n){var r,i=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return r=2<i.length&&void 0!==i[2]?i[2]:void 0,e.next=3,fetch(t,r);case 3:return e.next=5,e.sent.arrayBuffer();case 5:return r=e.sent,r=new Blob([r],{type:n}),e.abrupt("return",URL.createObjectURL(r));case 8:case"end":return e.stop()}},e)}))).apply(this,arguments)}function mediaIsMP4(e){return 0===e[0]&&0===e[1]&&0===e[2]&&(24===e[3]||32===e[3])&&102===e[4]&&116===e[5]&&121===e[6]&&112===e[7]}function mediaIsTs(e){if(71!==e[0])return!1;for(var t=188;t<Math.min(e.length,940);t+=188)if(71!==e[t])return!1;return!0}function determineStreamType(e){if(!e||"string"!=typeof e)return null;var t=Math.min(e.length,1e3),t=e.slice(0,t);return t.includes("#EXTM3U")?"hls":/\s*<MPD\s/.test(t)?"dash":null}function getResponseInfoSafe(e){return _getResponseInfoSafe.apply(this,arguments)}function _getResponseInfoSafe(){return(_getResponseInfoSafe=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var n,r=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return n=1<r.length&&void 0!==r[1]?r[1]:{},e.abrupt("return",getResponseInfo(t,n).catch(function(e){return null}));case 2:case"end":return e.stop()}},e)}))).apply(this,arguments)}function getResponseInfo(e){return _getResponseInfo.apply(this,arguments)}function _getResponseInfo(){return(_getResponseInfo=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var n,r,i,o,a,s=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return r=(a=1<s.length&&void 0!==s[1]?s[1]:{}).init,n=void 0===r?null:r,r=a.redirect,r=void 0===r?"follow":r,a=a.abortData,i=void 0===a||a,o=new AbortController,a=o.signal,n=null!=n?n:{headers:{accept:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9"},credentials:"include",redirect:r,method:"GET"},e.abrupt("return",XMLRequest(t,_objectSpread(_objectSpread({},n),{},{signal:a})).then(function(e){return i&&o.abort(),e}));case 6:case"end":return e.stop()}},e)}))).apply(this,arguments)}function setHeaders(e,t){return _setHeaders.apply(this,arguments)}function _setHeaders(){return(_setHeaders=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,n){var r,i,o,a,s;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:for(r=[],i=[],o=Date.now()%Math.pow(2,31),a=0;a<t.length;a++)s=a+o,t[a]&&t[a].startsWith("http")&&(r.push({id:s,priority:1,action:{type:"modifyHeaders",requestHeaders:n.map(function(e){return{operation:e.operation,header:e.header,value:e.value}})},condition:{urlFilter:"||".concat(new URL(t[a]).hostname)}}),i.push(s));return e.next=6,browser.declarativeNetRequest.updateSessionRules({addRules:r,removeRuleIds:i});case 6:return e.abrupt("return",i);case 7:case"end":return e.stop()}},e)}))).apply(this,arguments)}function removeRulesForTabs(e){return _removeRulesForTabs.apply(this,arguments)}function _removeRulesForTabs(){return(_removeRulesForTabs=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var n;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,chrome.declarativeNetRequest.getSessionRules();case 2:if(n=e.sent,0<(n=n.filter(function(e){return null===(e=e.condition)||void 0===e||null===(e=e.tabIds)||void 0===e?void 0:e.some(function(e){return t.includes(e)})}).map(function(e){return e.id})).length)return e.next=7,chrome.declarativeNetRequest.updateSessionRules({removeRuleIds:n});e.next=7;break;case 7:case"end":return e.stop()}},e)}))).apply(this,arguments)}function updateSessionRules(e,t){return _updateSessionRules.apply(this,arguments)}function _updateSessionRules(){return(_updateSessionRules=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,n){var r,i,o,a,s,u,l,c,d,f=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:for(r=(o=2<f.length&&void 0!==f[2]?f[2]:{}).tabIds,i=void 0===r?null:r,o=o.attrs,a=void 0===o?[]:o,n&&a.push({header:"Referer",value:n}),s=[],u=[],l=Date.now()%Math.pow(2,31),c=0;c<t.length;c++)d=c+l,t[c]&&t[c].startsWith("http")&&(s.push({id:d,priority:1,action:{type:"modifyHeaders",requestHeaders:a.map(function(e){return{operation:(null==e?void 0:e.operation)||"set",header:e.header,value:e.value}})},condition:{urlFilter:"||".concat(new URL(t[c]).hostname),tabIds:i||[browser.tabs.TAB_ID_NONE]}}),u.push(d));return e.prev=7,e.next=10,browser.declarativeNetRequest.updateSessionRules({addRules:s,removeRuleIds:u});case 10:e.next=19;break;case 12:if(e.prev=12,e.t0=e.catch(7),i)return e.next=17,removeRulesForTabs(i);e.next=17;break;case 17:return e.next=19,browser.declarativeNetRequest.updateSessionRules({addRules:s,removeRuleIds:u});case 19:return e.abrupt("return",u);case 20:case"end":return e.stop()}},e,null,[[7,12]])}))).apply(this,arguments)}function filterHeaders(){var e,t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},n=["x-client-data","referer","user-agent","origin","cache-control","pragma","accept-encoding","accept-language","sec-ch-ua","sec-ch-ua-mobile","sec-ch-ua-platform","sec-fetch-dest","sec-fetch-mode","sec-fetch-site"],r={};for(e in t)n.includes(e)||(r[e]=t[e]);return r}function playVideoLink(h,e){var o=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null,t=3<arguments.length&&void 0!==arguments[3]?arguments[3]:["Html5","Shaka"],n=(null==h?void 0:h.download_url)||(null==h?void 0:h.url),r="video/mp4",r=n&&"video"!==h.type||null==h||!h.manifestUrl?(0<n.indexOf(".webm")?r="video/webm":0<n.indexOf(".flv")?r="video/x-flv":0<n.indexOf(".mpd")?r="application/dash+xml":0<n.indexOf(".m3u8")&&(r="application/x-mpegURL"),{type:r,src:updatePandavideoToken(n)}):("hls"===h.streamType?r="application/x-mpegURL":"dash"===h.streamType&&(r="application/dash+xml"),{type:r,src:updatePandavideoToken(h.manifestUrl)});videojs.log.level="debug";var p=videojs(e,{controlBar:{fullscreenToggle:!1},techOrder:t,playbackRates:[.5,1,1.25,1.5,2,4]});return p.ready(function(){p.controlBar.subsCapsButton.hide()}),p.src(r),p.on("loadedmetadata",function(){if("audio"==h.type&&p.audioOnlyMode(!0),"Shaka"==p.techName_){var e,t=p.tech(!0).shaka_;t.configure({abr:{enabled:!1}});var n,r="audio"==h.type?h.language:(null==h||null===(e=h.audioLink)||void 0===e?void 0:e.language)||null;!r||(n=t.getAudioLanguagesAndRoles().filter(function(e){return e.language==r})).length&&t.selectAudioLanguage(n[0].language,n[0].role),"video"!=h.type||(n=t.getVariantTracks().filter(function(e){return!("variant"!==e.type||(null==h?void 0:h.width)!=(null==e?void 0:e.width)||(null==h?void 0:h.height)!=(null==e?void 0:e.height)||null!=h&&h.bandwidth&&(null==h?void 0:h.bandwidth)!=((null==e?void 0:e.videoBandwidth)||(null==e?void 0:e.bandwidth)))})).length&&t.selectVariantTrack(n[0],!0)}else{var i,o="audio"==h.type?h.name:(null==h||null===(i=h.audioLink)||void 0===i?void 0:i.name)||null;if(!o||(i=Array.from(p.audioTracks()).find(function(e){return e.label===o}))&&(i.enabled=!0),"video"==h.type){var a=p.qualityLevels().levels_;if(1<a.length&&(h.width&&h.height||h.bandwidth)&&1<a.length&&h){for(var s=h.width&&h.height?h.width*h.height:null,u=h.bandwidth||null,l=!1,c=0;c<a.length;c++){var d=a[c],f=s&&d.width*d.height===s||u&&d.bitrate===u;d.enabled=f,l=l||f}l||(a[0].enabled=!0)}}}}),p.on("xhr-hooks-ready",function(){isPrivacyCombrUrl(p.currentSrc())&&p.tech(!0).vhs.xhr.onRequest(function(e){return e.beforeSend=function(t){var e,n=updatePrivacyCombrHeaders(t.url,filterHeaders((null==h||null===(e=h.init)||void 0===e?void 0:e.headers)||{}));Object.keys(n).forEach(function(e){t.setRequestHeader(e,n[e])})},e});var i=!1;p.tech(!0).vhs.xhr.onResponse(function(e,t,n){var r;403==n.statusCode&&0==i&&isPandavideoTokenUrl(p.currentSrc())&&(i=!0,r=(null==h||null===(r=h.init)||void 0===r||null===(r=r.headers)||void 0===r?void 0:r.referer)||o||playUrl,updateSessionRules([e.url],r))})}),p.on(["loadstart","play","playing","firstplay","pause","ended","adplay","adplaying","adfirstplay","adpause","adended","contentplay","contentplaying","contentfirstplay","contentpause","contentended","contentupdate","loadeddata","loadedmetadata"],function(e){}),p}function getDataFromBg(e){return _getDataFromBg.apply(this,arguments)}function _getDataFromBg(){return(_getDataFromBg=_asyncToGenerator(_regeneratorRuntime().mark(function e(u){var l,c,t=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return l=1<t.length&&void 0!==t[1]?t[1]:{},c=2<t.length&&void 0!==t[2]?t[2]:location.href,e.abrupt("return",new Promise(function(n,r){function i(){e&&(clearTimeout(e),e=null)}function o(e){if(e.message==="".concat(u,"-chunk")&&e.uniqueEventName===a&&(i(),s+=e.data,e.done))try{chrome.runtime.onMessage.removeListener(o);var t=JSON.parse(s);n(t)}catch(e){r(new Error("Error parsing tab data: ".concat(e.message,", ").concat(c,", ").concat(u,", ").concat(JSON.stringify(l))))}}var a="".concat(u,"-").concat(Date.now(),"-").concat(Math.random()),s="",e=null;chrome.runtime.onMessage.addListener(o),sendMessage(_objectSpread({message:u,uniqueEventName:a},l)).then(function(e){i(),e.data&&(chrome.runtime.onMessage.removeListener(o),n(e.data))}).catch(function(e){r(e)}),e=setTimeout(function(){chrome.runtime.onMessage.removeListener(o),r(new Error("".concat(u," timed out")))},1e4)}));case 3:case"end":return e.stop()}},e)}))).apply(this,arguments)}function _linkClickDownload(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"",n=document.createElement("a");n.setAttribute("download",configuration.BRAND+"/"+t),n.setAttribute("target","_blank"),n.href=e,document.body.appendChild(n),n.click(),n.remove()}function _backgroundDownload2(e,t){return _backgroundDownload.apply(this,arguments)}function _backgroundDownload(){return(_backgroundDownload=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,n){var r,i,o,a=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(i={message:"download-video-link",url:t,fileName:n},"object"==_typeof(r=2<a.length&&void 0!==a[2]?a[2]:{}))for(o in r)i[o]=r[o];return e.next=5,sendMessage(i);case 5:if(e.t0=e.sent,e.t0){e.next=8;break}e.t0={};case 8:return e.abrupt("return",e.t0);case 9:case"end":return e.stop()}},e)}))).apply(this,arguments)}var AbstractDownloader=(_defineProperty(_class=function(){"use strict";function n(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:null,t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:null;_classCallCheck(this,n),this.stateListener=e,this.progressListener=t}var r,i,t,o;return _createClass(n,[{key:"handleProgress",value:function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{};this.progressListener&&e&&this.progressListener(e,t,n)}},{key:"handleStateChange",value:function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:"";this.stateListener&&e&&this.stateListener(e,t,n)}},{key:"xmlDownload",value:(o=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,n){var r,i,o,a,s,u,l,c,d,f,h,p,g=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return o=2<g.length&&void 0!==g[2]?g[2]:{},r=new AbortController,i=null,o=o.timeoutMillis,i=setTimeout(function(){r.abort()},void 0===o?3e4:o),this.handleStateChange(n,LinkDownloader.STATE.START),e.prev=6,e.next=9,XMLRequest(t.download_url,{signal:r.signal});case 9:if(a=e.sent,clearTimeout(i),a.ok){e.next=13;break}throw new Error("Network response was not ok");case 13:s=null,u=t.title+t.ext,null!==(f=a.headers.get("content-length"))&&(s=parseInt(f,10)),l=a.body.getReader(),c=0,d=[];case 20:return e.next=23,l.read();case 23:if(h=e.sent,f=h.done,h=h.value,f)return p=URL.createObjectURL(new Blob(d)),this.linkClickDownload(p,u),URL.revokeObjectURL(p),this.handleProgress(n,1),this.handleStateChange(n,LinkDownloader.STATE.COMPLETE),e.abrupt("break",39);e.next=33;break;case 33:c+=h.length,d.push(h),null!==s&&(p=Math.floor(c/s*100),this.handleProgress(n,p)),e.next=20;break;case 39:e.next=47;break;case 41:e.prev=41,e.t0=e.catch(6),clearTimeout(i),r.abort(),this.handleStateChange(n,LinkDownloader.STATE.FAILED);case 47:case"end":return e.stop()}},e,this,[[6,41]])})),function(e,t){return o.apply(this,arguments)})},{key:"linkClickDownload",value:function(e){return _linkClickDownload(e,1<arguments.length&&void 0!==arguments[1]?arguments[1]:"")}},{key:"windowOpen",value:(t=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:window.open(t);case 1:case"end":return e.stop()}},e)})),function(e){return t.apply(this,arguments)})},{key:"backgroundDownload",value:(i=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,n){var r,i=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return r=2<i.length&&void 0!==i[2]?i[2]:{},e.abrupt("return",_backgroundDownload2(t,n,r));case 2:case"end":return e.stop()}},e)})),function(e,t){return i.apply(this,arguments)})},{key:"download",value:(r=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,n){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:throw new Error("You have to implement the method doSomething!");case 2:case"end":return e.stop()}},e)})),function(e,t){return r.apply(this,arguments)})}]),n}(),"STATE",{START:"start",DOWNLOADING:"downloading",PAUSED:"paused",INTERRUPTED:"interrupted",COMPLETE:"complete",FAILED:"failed"}),_class),LinkDownloader=function(){"use strict";_inherits(o,AbstractDownloader);var n,r=_createSuper(o);function o(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:null,t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:null;return _classCallCheck(this,o),(t=r.call(this,e,t)).listener=t.initListener(),t}return _createClass(o,[{key:"initListener",value:function(){function e(e,t,n){switch(e.message){case"download-changed":r.handleStateChange(e.key,e.state,(null==e?void 0:e.errmsg)||""),n();break;case"progress-changed":r.handleProgress(e.key,e.progress),n();break;case"complete":n()}}var r=this;return browser.runtime.onMessage.addListener(e),e}},{key:"dispose",value:function(){this.listener&&(browser.runtime.onMessage.removeListener(this.listener),this.listener=null),this.stateListener=null,this.progressListener=null}},{key:"download",value:(n=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,n){var r,i;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return i=t.title,new RegExp("."+t.ext+"$").test(t.title)||(i=t.title+"."+t.ext),this.handleStateChange(n,o.STATE.START),r=t.download_url,e.prev=5,e.next=8,this.backgroundDownload(r,i);case 8:"result"in(i=e.sent)&&0<i.result?this.handleStateChange(n,o.STATE.DOWNLOADING):this.handleStateChange(n,o.STATE.FAILED),e.next=15;break;case 12:e.prev=12,e.t0=e.catch(5),this.xmlDownload(t);case 15:case"end":return e.stop()}},e,this,[[5,12]])})),function(e,t){return n.apply(this,arguments)})}]),o}();function isPandavideoUrl(e){return/https?:\/\/(?:[^.]+\.)*(?:[^/]+)\.pandavideo\.com(?:\.[^/]+)?\/.*\.m3u8/.test(e)}function isPandavideoNoTokenUrl(e){return/https?:\/\/(?:[^.]+\.)*(?:[^/]+)\.pandavideo\.com(?:\.[^/]+)?\/.*\.m3u8(?:(?!.token=[a-zA-Z0-9]{64})[?&][^&]*)?$/.test(e)}function isPandavideoTokenUrl(e){return _wrapRegExp(/https?:\/\/(?:[^.]+\.)*(?:[^/]+)\.pandavideo\.com(?:\.[^/]+)?\/.*\.m3u8\?(?:[^&]+&)?token=([a-zA-Z0-9]{64})/,{token:1}).test(e)}function updatePandavideoToken(e){var t=_wrapRegExp(/https?:\/\/(?:[^.]+\.)*(?:[^/]+)\.pandavideo\.com(?:\.[^/]+)?\/.*\.m3u8\?(?:[^&]+&)?token=([a-zA-Z0-9]{64})/,{token:1}),t=e.match(t);return t&&(e=e.replace(t.groups.token,function(){for(var e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",t="",n=0;n<64;n++){var r=Math.floor(Math.random()*e.length);t+=e.charAt(r)}return t}())),e}function isPolyvNetUrl(e){return!(null==e||!e.match(/^https?:\/\/hls\.videocc\.net/))}function getPolyvNetManifestId(e){e=null==e?void 0:e.match(/^https?:\/\/hls\.videocc\.net\/.*?\/([^\/_]+)(_\d+\.key|(_\d+)?.m3u8)/);return e?e[1]:null}function getPolyvNetManifestToken(e){e=null==e?void 0:e.match(/^https?:\/\/hls\.videocc\.net\/.*?\/([^\/]+)_\d+\.key\?token=([^&]+)/);return e?{manifestId:e[1],token:e[2]}:null}function updatePolyvNetKeyToken(e,t){var n;try{if(null!=e&&null!==(n=e[0])&&void 0!==n&&null!==(n=n.segments)&&void 0!==n&&null!==(n=n[0])&&void 0!==n&&null!==(n=n.key)&&void 0!==n&&null!==(n=n.url)&&void 0!==n&&n.match(/^https?:\/\/hls\.videocc\.net\/.*?\/([^\/]+)_\d+\.key/)){var r=_createForOfIteratorHelper(e);try{for(r.s();!(i=r.n()).done;){var i=i.value,o=_createForOfIteratorHelper(null==i?void 0:i.segments);try{for(o.s();!(s=o.n()).done;){var a=s.value,s=new URL(a.key.url);s.searchParams.set("token",t),a.key.url=s.toString()}}catch(e){o.e(e)}finally{o.f()}}}catch(e){r.e(e)}finally{r.f()}}}catch(e){}return e}function isBoomstreamUrl(e){return!(null==e||!e.match(/^https?:\/\/(?:[^\.]+\.)*?boomstream\.com/))}function updateLoomstreamMediaPlaylist(e,t,n){var r=/(?:#EXT-X-MEDIA-READY: *(\w+))\r?\n?/.exec(e);if(!r||!t)return e;var i="bv17b7v24iedrvzoaihwvugef89ewy7834f35",o="001a5068005b176d560504";function a(e,t){for(var n=[];t.length<e.length;)t+=t;for(var r=0;r<e.length;r+=2){var i=e.substring(r,r+2),i=parseInt(i,16)^t.charCodeAt(r/2);n.push(String.fromCharCode(i))}return n.join("")}var s=a(r=r[1],a(o,i)),r=(r=s,new Uint8Array([].map.call(r,function(e){return e.charCodeAt(0)})).buffer),r="0x"+(r=r.slice(20,36),Array.prototype.map.call(new Uint8Array(r),function(e){return("00"+e.toString(16)).slice(-2)}).join("")),i=n+"/process/"+function(e,t){for(var n=[];t.length<e.length;)t+=t;for(var r=0;r<e.length;r++){var i=(e.charCodeAt(r)^t.charCodeAt(r)).toString(16);i.length<2&&(i="0"+i),n.push(i)}return n.join("")}(s.slice(0,20)+t,a(o,i));return(e=e.replace("[KEY]",i)).replace("[IV]",r)}function isPrivacyCombrUrl(e){return!(null==e||!e.match(/^https?:\/\/(?:[^\.]+\.)*?privacy.com\.br/))}function updatePrivacyCombrHeaders(e,t){if(t["x-content-uri"]&&isPrivacyCombrUrl(e))try{var n=new URL(e).pathname.split("/").filter(function(e){return e});0<n.length&&(t["x-content-uri"]=n[n.length-1])}catch(e){}return t}function middleTruncate_old(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:64;if(e.length<=t)return e;var n=Math.ceil((t-3)/2),t=Math.floor((t-3)/2);return e.slice(0,n)+"..."+e.slice(e.length-t)}function middleTruncate(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:64,n=new TextEncoder;if(n.encode(e).length<=t)return e;for(var r=Math.ceil((t-3)/2),i=Math.floor((t-3)/2),o="",a="",s=0;s<e.length;s++){var u=e[s];if(n.encode(o+u).length>r)break;o+=u}for(var l=e.length-1;0<=l;l--){var c=e[l];if(n.encode(c+a).length>i)break;a=c+a}return o+"..."+a}function truncate_utf8_bak(e,t){if("string"!=typeof e)throw new Error("Input must be string");for(var n,r,i,o,a=e.length,s=0,u=0;u<a;u+=1){if(n=e.charCodeAt(u),r=e[u],55296<=(o=n)&&o<=56319&&(56320<=(i=e.charCodeAt(u+1))&&i<=57343)&&(r+=e[u+=1]),(s+=unescape(encodeURIComponent(r)).length)===t)return e.slice(0,u+1);if(t<s)return e.slice(0,u-r.length+1)}return e}function truncate_utf8(e,t){var n=!(2<arguments.length&&void 0!==arguments[2])||arguments[2];if("string"!=typeof e)throw new Error("Input must be a string");var r=_toConsumableArray(e),i=0;if(n)for(var o=0;o<r.length;o++){var a=r[o];if(t<(i+=unescape(encodeURIComponent(a)).length))return r.slice(0,o).join("")}else for(var s=r.length-1;0<=s;s--){var u=r[s];if(t<(i+=unescape(encodeURIComponent(u)).length))return r.slice(s+1).join("")}return e}function sanitizeFilename(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:100;try{return truncate_utf8(e=e.replace(/^\./,"_").replace(/[\u0000-\u001f\u007f-\u009f\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200b-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,"").replace(/&quot;/g,"").replace(/&amp;/g,"&").replace(/[\\/:*?<>|~↵"\t]/g,"_"),t)}catch(e){}}function reportMsg(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"error";chrome.runtime.sendMessage({message:"sentry-report-msg",data:{message:e,level:t}},function(){})}function sendGaPreview(e,t){return sendMessage({message:"ga-msg",type:"pageview",title:e,location:t,params:2<arguments.length&&void 0!==arguments[2]?arguments[2]:{}},function(){})}function sendGaEvent(e){return sendMessage({message:"ga-msg",type:"event",name:e,params:1<arguments.length&&void 0!==arguments[1]?arguments[1]:{}},function(){})}function sendGaException(e){return sendMessage({message:"ga-msg",type:"exception",error:e,params:1<arguments.length&&void 0!==arguments[1]?arguments[1]:{}},function(){})}function onError(r){"object"==_typeof(window.onerror)&&(window.onerror=function(e,t,n){sendGaException("page: ".concat(r,": url:").concat(t," err: ").concat(e))})}function localizeHtmlPage(){for(var e=document.getElementsByTagName("html"),t=0;t<e.length;t++){var n=e[t],r=n.innerHTML.toString(),i=r.replace(/__MSG_(\w+)__/g,function(e,t){return t?browser.i18n.getMessage(t):""});i!=r&&(n.innerHTML=i)}}function url_host_split_bak(e){try{var t=e.match(/^(?:(?:https?:)?\/\/)?((?:[\w-]+\.)?(([\w-]+)\.[^?\/]+))/);return t.shift(),t.reverse(),t}catch(e){return null}}function url_host_split(e){try{for(var t=[],n=e.match(/^(?:(?:https?:)?\/\/)?((?:[\w-]+\.)?[^?\/]+)/)[1].split("."),r=n.length,i=0;i<r-1;i++)t.unshift(n.join(".")),2<n.length&&n.shift();return t.unshift(n[0]),t}catch(e){return null}}function storeGeneral(e){return _storeGeneral.apply(this,arguments)}function _storeGeneral(){return(_storeGeneral=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,syncStorageSet(_defineProperty({},"".concat(browser.runtime.id,"_general"),t));case 2:case"end":return e.stop()}},e)}))).apply(this,arguments)}function getStoredGeneral(){return _getStoredGeneral.apply(this,arguments)}function _getStoredGeneral(){return(_getStoredGeneral=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t,n;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t="".concat(browser.runtime.id,"_general"),e.next=3,syncStorageGet(t);case 3:return n=e.sent,e.abrupt("return",(null==n?void 0:n[t])||{});case 5:case"end":return e.stop()}},e)}))).apply(this,arguments)}function storeFilter(e){return _storeFilter.apply(this,arguments)}function _storeFilter(){return(_storeFilter=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,syncStorageSet(_defineProperty({},"".concat(browser.runtime.id,"_filter"),t));case 2:case"end":return e.stop()}},e)}))).apply(this,arguments)}function getStoredFilter(){return _getStoredFilter.apply(this,arguments)}function _getStoredFilter(){return(_getStoredFilter=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t,n;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t="".concat(browser.runtime.id,"_filter"),e.next=3,syncStorageGet(t);case 3:return n=e.sent,e.abrupt("return",(null==n?void 0:n[t])||{});case 5:case"end":return e.stop()}},e)}))).apply(this,arguments)}function updateFilter(e,t){return _updateFilter.apply(this,arguments)}function _updateFilter(){return(_updateFilter=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,n){var r,i,o,a,s,u,l;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:for(o in i=function(e,t,n){return void 0!==e&&e>=t[0]&&e<=t[1]?e:n},r={},n)u=t[o]||{},a=void 0===(l=u.enable)?n[o].enable:l,s=u.size,l=n[o],u=l.range,l=l.size,r[o]={enable:a,size:i(s,u,l)};return e.abrupt("return",r);case 4:case"end":return e.stop()}},e)}))).apply(this,arguments)}function needFilter(e,t){return!t||(e<t.size||0==t.enable)}function getStoredCspList(){return _getStoredCspList.apply(this,arguments)}function _getStoredCspList(){return(_getStoredCspList=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t,n;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t="".concat(browser.runtime.id,"_csplist"),e.next=3,syncStorageGet(t);case 3:return n=e.sent,e.abrupt("return",(null==n?void 0:n[t])||null);case 5:case"end":return e.stop()}},e)}))).apply(this,arguments)}function storeCspList(e){return _storeCspList.apply(this,arguments)}function _storeCspList(){return(_storeCspList=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,syncStorageSet(_defineProperty({},"".concat(browser.runtime.id,"_csplist"),t));case 2:case"end":return e.stop()}},e)}))).apply(this,arguments)}function addElement(e,t){switch(2<arguments.length&&void 0!==arguments[2]?arguments[2]:"prepend"){case"insertBefore":t.insertAdjacentElement("beforebegin",e);break;case"insertAfter":t.insertAdjacentElement("afterend",e);break;case"prepend":t.prepend(e);break;case"append":t.append(e);break;case"appendChild":t.appendChild(e)}}var Base64={_keyStr:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",encode:function(e){var t,n,r,i,o,a,s="",u=0;for(e=Base64._utf8_encode(e);u<e.length;)r=(a=e.charCodeAt(u++))>>2,i=(3&a)<<4|(t=e.charCodeAt(u++))>>4,o=(15&t)<<2|(n=e.charCodeAt(u++))>>6,a=63&n,isNaN(t)?o=a=64:isNaN(n)&&(a=64),s=s+this._keyStr.charAt(r)+this._keyStr.charAt(i)+this._keyStr.charAt(o)+this._keyStr.charAt(a);return s},decode:function(e){var t,n,r,i,o,a="",s=0;for(e=e.replace(/[^A-Za-z0-9+/=]/g,"");s<e.length;)t=this._keyStr.indexOf(e.charAt(s++))<<2|(r=this._keyStr.indexOf(e.charAt(s++)))>>4,n=(15&r)<<4|(i=this._keyStr.indexOf(e.charAt(s++)))>>2,r=(3&i)<<6|(o=this._keyStr.indexOf(e.charAt(s++))),a+=String.fromCharCode(t),64!=i&&(a+=String.fromCharCode(n)),64!=o&&(a+=String.fromCharCode(r));return a=Base64._utf8_decode(a)},_utf8_encode:function(e){var t="";e=e.replace(/\r\n/g,"\n");for(var n=0;n<e.length;n++){var r=e.charCodeAt(n);r<128?t+=String.fromCharCode(r):(127<r&&r<2048?t+=String.fromCharCode(r>>6|192):(t+=String.fromCharCode(r>>12|224),t+=String.fromCharCode(r>>6&63|128)),t+=String.fromCharCode(63&r|128))}return t},_utf8_decode:function(e){for(var t,n,r="",i=0,o=t=0;i<e.length;)(o=e.charCodeAt(i))<128?(r+=String.fromCharCode(o),i++):191<o&&o<224?(t=e.charCodeAt(i+1),r+=String.fromCharCode((31&o)<<6|63&t),i+=2):(t=e.charCodeAt(i+1),n=e.charCodeAt(i+2),r+=String.fromCharCode((15&o)<<12|(63&t)<<6|63&n),i+=3);return r}};function buf2hex(e){return _toConsumableArray(new Uint8Array(e)).map(function(e){return e.toString(16).padStart(2,"0")}).join("")}function hex2buf(e){return new Uint8Array(e.match(/[\da-f]{2}/gi).map(function(e){return parseInt(e,16)}))}function genUniqueId(){for(var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:8,t="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",n="",r=0;r<e;r++)n+=t[Math.floor(Math.random()*t.length)];return n}function sproutVideoSig(e,t){var n={jpg:"j",key:"k",m3u8:"m",ts:"t"},r=new URL(e);if(r.search.length)return e;var i,r=r.pathname.match(new RegExp("\\.(?<ext>".concat(Object.keys(n).join("|"),")")));if(r){r=n[r.groups.ext];return e+(i=r,i=t.signatures[i],"?Policy=".concat(encodeURIComponent(i["CloudFront-Policy"]),"&Signature=").concat(encodeURIComponent(i["CloudFront-Signature"]),"&Key-Pair-Id=").concat(encodeURIComponent(i["CloudFront-Key-Pair-Id"]),"&sessionID=").concat(encodeURIComponent(t.sessionID)))}return e}function applySig(t,e){try{return window[e.func](t,e.config)}catch(e){return t}}function showModal(e){var t,n=browser.runtime.id.substr(0,8),r="modal-".concat(n);function i(){$(".".concat(r)).toggleClass("".concat(r,"-show"))}document.querySelector(".".concat(r))||(function(){var e="modal-".concat(n);if(!document.getElementById(e)){var t=document.createElement("style");t.id=e,t.innerHTML="\n                .".concat(r," {\n                    visibility: hidden;\n                    opacity: 0;\n                    position: fixed;\n                    z-index: 2147483647;\n                    top: 0;\n                    right: 0;\n                    bottom: 0;\n                    left: 0;\n                    display: flex;\n                    align-items: center;\n                    justify-content: center;\n                    background: rgba(77, 77, 77, .7);\n                    transition: all .2s;\n                }\n                .").concat(r," a:hover {\n                    text-decoration: none !important;\n                }\n                .").concat(r,"-show {\n                    visibility: visible;\n                    opacity: 1;\n                }\n                .").concat(r,"-body {\n                    border-radius: 4px;\n                    position: relative;\n                    width: 700px;\n                    max-width: 80%;\n                    background: #fff;\n                    padding: 1em 2em;\n                    color: #232323;\n                }\n                .").concat(r,"-close {\n                    position: absolute;\n                    top: 10px;\n                    right: 10px;\n                    color: #585858;\n                    text-decoration: none;\n                }");try{document.head.appendChild(t)}catch(e){}}}(),(t=document.createElement("div")).className="".concat(r),t.innerHTML='\n                <div class="'.concat(r,'-body">\n                    <div style="\n                        display: inline-flex;\n                        align-items: center;\n                    ">\n                        <img style="width:24px;height: 24px;margin-right: 3px;" src="').concat(chrome.runtime.getURL("images/logo.png"),'">').concat(browser.i18n.getMessage("brand"),'\n                    </div>\n                    <a href="#" class="').concat(r,'-close">&times;</a>\n                    <div class="').concat(r,'-content"></div>\n                </div>'),document.body.appendChild(t),$("body").on("click",".".concat(r," a"),function(e){e.preventDefault(),e.stopPropagation(),i()}),$("body").on("click",".".concat(r),function(e){e.target==e.currentTarget&&i()})),$(".".concat(r," .").concat(r,"-content")).html(e),$(".".concat(r)).addClass("".concat(r,"-show"))}window.MP4Processor=function(){"use strict";function t(e){_classCallCheck(this,t),this.totalDuration=e,this.base_sequence_number=0,this.trackBaseMediaDecodeTimes={},this.trackTimescales={}}return _createClass(t,[{key:"getNextSegmentStartTime",value:function(){var n=this,e=Object.entries(this.trackBaseMediaDecodeTimes).map(function(e){var t=_slicedToArray(e,2),e=t[0];return t[1]/(n.trackTimescales[e]||1)});return Math.min.apply(Math,_toConsumableArray(e))}},{key:"processMP4Segment",value:function(e,t){var n=ISOBoxer.parseBuffer(e.buffer);if(n.boxes=n.boxes.filter(function(e){return(0==t||"ftyp"!==e.type&&"moov"!==e.type)&&"mfra"!==e.type}),0===t){var e=n.fetch("moov"),r=e.boxes.find(function(e){return"mvhd"===e.type});if(r){r.duration=Math.floor(this.totalDuration*r.timescale);var i=_createForOfIteratorHelper(e.boxes.filter(function(e){return"trak"===e.type}));try{for(i.s();!(a=i.n()).done;){var o=a.value,a=o.boxes.find(function(e){return"tkhd"===e.type}),o=o.boxes.find(function(e){return"mdia"===e.type}),o=o&&o.boxes.find(function(e){return"mdhd"===e.type});a&&o&&(a.duration=Math.floor(this.totalDuration*r.timescale),this.trackTimescales[a.track_ID]=o.timescale)}}catch(e){i.e(e)}finally{i.f()}}}var s=this.base_sequence_number,u=_createForOfIteratorHelper(n.fetchAll("moof"));try{for(u.s();!(c=u.n()).done;){var l=c.value,c=l.boxes.filter(function(e){return"mfhd"===e.type}),d=_createForOfIteratorHelper(c);try{for(d.s();!(f=d.n()).done;){var f=f.value;f.sequence_number=this.base_sequence_number+f.sequence_number}}catch(e){d.e(e)}finally{d.f()}0<c.length&&(s=c[c.length-1].sequence_number);var h=_createForOfIteratorHelper(l.boxes.filter(function(e){return"traf"===e.type}));try{for(h.s();!(m=h.n()).done;){var p=m.value,g=p.boxes.find(function(e){return"tfhd"===e.type}),m=p.boxes.find(function(e){return"tfdt"===e.type}),y=p.boxes.find(function(e){return"trun"===e.type});if(g&&m&&y){p=g.track_ID;this.trackBaseMediaDecodeTimes[p]||(this.trackBaseMediaDecodeTimes[p]=0);var v=0;if(y.samples&&0<y.samples.length)if(void 0!==y.samples[0].sample_duration){var _,b=_createForOfIteratorHelper(y.samples);try{for(b.s();!(_=b.n()).done;)v+=_.value.sample_duration||y.default_sample_duration||0}catch(e){b.e(e)}finally{b.f()}}else{if(!g.default_sample_duration)throw new Error("Can't get fragmentDuration from moof");v=y.sample_count*g.default_sample_duration}0!==t?(g=this.trackBaseMediaDecodeTimes[p],m.baseMediaDecodeTime=Math.floor(g),this.trackBaseMediaDecodeTimes[p]+=v):this.trackBaseMediaDecodeTimes[p]=m.baseMediaDecodeTime+v}}}catch(e){h.e(e)}finally{h.f()}}}catch(e){u.e(e)}finally{u.f()}return this.base_sequence_number=s,n.write()}}]),t}(),function(e,t){"object"==("undefined"==typeof exports?"undefined":_typeof(exports))&&"object"==("undefined"==typeof module?"undefined":_typeof(module))?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==("undefined"==typeof exports?"undefined":_typeof(exports))?exports.FFmpegWASM=t():e.FFmpegWASM=t()}(self,function(){"use strict";var i,e,r={d:function(e,t){for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},o:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r:function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};r.r(t),r.d(t,{FFmpeg:function(){return s}}),(e=i=i||{}).LOAD="LOAD",e.EXEC="EXEC",e.WRITE_FILE="WRITE_FILE",e.READ_FILE="READ_FILE",e.DELETE_FILE="DELETE_FILE",e.RENAME="RENAME",e.CREATE_DIR="CREATE_DIR",e.LIST_DIR="LIST_DIR",e.DELETE_DIR="DELETE_DIR",e.ERROR="ERROR",e.DOWNLOAD="DOWNLOAD",e.PROGRESS="PROGRESS",e.LOG="LOG",e.MOUNT="MOUNT",e.UNMOUNT="UNMOUNT";var n,u=(n=0,function(){return n++}),l=(new Error("unknown message type"),new Error("ffmpeg is not loaded, call `await ffmpeg.load()` first")),o=new Error("called FFmpeg.terminate()");new Error("failed to import ffmpeg-core.js");var c=new WeakMap,d=new WeakMap,f=new WeakMap,a=new WeakMap,h=new WeakMap,p=new WeakMap,g=new WeakMap,s=function(){function t(){var s=this,e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:null;_classCallCheck(this,t),_classPrivateFieldInitSpec(this,c,{writable:!0,value:null}),_classPrivateFieldInitSpec(this,d,{writable:!0,value:{}}),_classPrivateFieldInitSpec(this,f,{writable:!0,value:{}}),_classPrivateFieldInitSpec(this,a,{writable:!0,value:[]}),_classPrivateFieldInitSpec(this,h,{writable:!0,value:[]}),_defineProperty(this,"loaded",!1),_defineProperty(this,"createWorker",void 0),_classPrivateFieldInitSpec(this,p,{writable:!0,value:function(){_classPrivateFieldGet(s,c)&&(_classPrivateFieldGet(s,c).onmessage=function(e){var t=e.data,n=t.id,e=t.type,r=t.data;switch(e){case i.LOAD:s.loaded=!0,_classPrivateFieldGet(s,d)[n](r);break;case i.MOUNT:case i.UNMOUNT:case i.EXEC:case i.WRITE_FILE:case i.READ_FILE:case i.DELETE_FILE:case i.RENAME:case i.CREATE_DIR:case i.LIST_DIR:case i.DELETE_DIR:_classPrivateFieldGet(s,d)[n](r);break;case i.LOG:_classPrivateFieldGet(s,a).forEach(function(e){return e(r)});break;case i.PROGRESS:_classPrivateFieldGet(s,h).forEach(function(e){return e(r)});break;case i.ERROR:_classPrivateFieldGet(s,f)[n](r)}delete _classPrivateFieldGet(s,d)[n],delete _classPrivateFieldGet(s,f)[n]})}}),_classPrivateFieldInitSpec(this,g,{writable:!0,value:function(e){var r=e.type,i=e.data,o=1<arguments.length&&void 0!==arguments[1]?arguments[1]:[],a=2<arguments.length?arguments[2]:void 0;return _classPrivateFieldGet(s,c)?new Promise(function(e,t){var n=u();_classPrivateFieldGet(s,c)&&_classPrivateFieldGet(s,c).postMessage({id:n,type:r,data:i},o),_classPrivateFieldGet(s,d)[n]=e,_classPrivateFieldGet(s,f)[n]=t,null!=a&&a.addEventListener("abort",function(){t(new DOMException("Message # ".concat(n," was aborted"),"AbortError"))},{once:!0})}):Promise.reject(l)}}),_defineProperty(this,"load",function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},t=e.classWorkerURL,n=void 0===t?"./worker.js":t,r=e.workerType,t=void 0===r?"classic":r,r=_objectWithoutProperties(e,_excluded),e=(1<arguments.length&&void 0!==arguments[1]?arguments[1]:{}).signal;return _classPrivateFieldGet(s,c)||(_classPrivateFieldSet(s,c,s.createWorker(new URL(n,location.href),{type:t})),_classPrivateFieldGet(s,p).call(s)),_classPrivateFieldGet(s,g).call(s,{type:i.LOAD,data:r},void 0,e)}),_defineProperty(this,"exec",function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:-1,n=(2<arguments.length&&void 0!==arguments[2]?arguments[2]:{}).signal;return _classPrivateFieldGet(s,g).call(s,{type:i.EXEC,data:{args:e,timeout:t}},void 0,n)}),_defineProperty(this,"terminate",function(){for(var e=0,t=Object.keys(_classPrivateFieldGet(s,f));e<t.length;e++){var n=t[e];_classPrivateFieldGet(s,f)[n](o),delete _classPrivateFieldGet(s,f)[n],delete _classPrivateFieldGet(s,d)[n]}_classPrivateFieldGet(s,c)&&(_classPrivateFieldGet(s,c).terminate(),_classPrivateFieldSet(s,c,null),s.loaded=!1)}),_defineProperty(this,"writeFile",function(e,t){var n=(2<arguments.length&&void 0!==arguments[2]?arguments[2]:{}).signal,r=[];return t instanceof Uint8Array&&r.push(t.buffer),_classPrivateFieldGet(s,g).call(s,{type:i.WRITE_FILE,data:{path:e,data:t}},r,n)}),_defineProperty(this,"mount",function(e,t,n){return _classPrivateFieldGet(s,g).call(s,{type:i.MOUNT,data:{fsType:e,options:t,mountPoint:n}},[])}),_defineProperty(this,"unmount",function(e){return _classPrivateFieldGet(s,g).call(s,{type:i.UNMOUNT,data:{mountPoint:e}},[])}),_defineProperty(this,"readFile",function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"binary",n=(2<arguments.length&&void 0!==arguments[2]?arguments[2]:{}).signal;return _classPrivateFieldGet(s,g).call(s,{type:i.READ_FILE,data:{path:e,encoding:t}},void 0,n)}),_defineProperty(this,"deleteFile",function(e){var t=(1<arguments.length&&void 0!==arguments[1]?arguments[1]:{}).signal;return _classPrivateFieldGet(s,g).call(s,{type:i.DELETE_FILE,data:{path:e}},void 0,t)}),_defineProperty(this,"rename",function(e,t){var n=(2<arguments.length&&void 0!==arguments[2]?arguments[2]:{}).signal;return _classPrivateFieldGet(s,g).call(s,{type:i.RENAME,data:{oldPath:e,newPath:t}},void 0,n)}),_defineProperty(this,"createDir",function(e){var t=(1<arguments.length&&void 0!==arguments[1]?arguments[1]:{}).signal;return _classPrivateFieldGet(s,g).call(s,{type:i.CREATE_DIR,data:{path:e}},void 0,t)}),_defineProperty(this,"listDir",function(e){var t=(1<arguments.length&&void 0!==arguments[1]?arguments[1]:{}).signal;return _classPrivateFieldGet(s,g).call(s,{type:i.LIST_DIR,data:{path:e}},void 0,t)}),_defineProperty(this,"deleteDir",function(e){var t=(1<arguments.length&&void 0!==arguments[1]?arguments[1]:{}).signal;return _classPrivateFieldGet(s,g).call(s,{type:i.DELETE_DIR,data:{path:e}},void 0,t)}),this.createWorker=e||function(e,t){return new Worker(e,t)}}return _createClass(t,[{key:"on",value:function(e,t){"log"===e?_classPrivateFieldGet(this,a).push(t):"progress"===e&&_classPrivateFieldGet(this,h).push(t)}},{key:"off",value:function(e,t){"log"===e?_classPrivateFieldSet(this,a,_classPrivateFieldGet(this,a).filter(function(e){return e!==t})):"progress"===e&&_classPrivateFieldSet(this,h,_classPrivateFieldGet(this,h).filter(function(e){return e!==t}))}}]),t}();return t}),function(e,t){"object"==("undefined"==typeof exports?"undefined":_typeof(exports))&&"undefined"!=typeof module?module.exports=t(require("global/window")):"function"==typeof define&&define.amd?define(["global/window"],t):(e="undefined"!=typeof globalThis?globalThis:e||self).muxjs=t(e.window)}(this,function(e){"use strict";var t,f=(t=e)&&"object"==_typeof(t)&&"default"in t?t:{default:t},n=function(){this.init=function(){var o={};this.on=function(e,t){o[e]||(o[e]=[]),o[e]=o[e].concat(t)},this.off=function(e,t){return!!o[e]&&(t=o[e].indexOf(t),o[e]=o[e].slice(),o[e].splice(t,1),-1<t)},this.trigger=function(e){var t,n,r,i;if(t=o[e])if(2===arguments.length)for(r=t.length,n=0;n<r;++n)t[n].call(this,arguments[1]);else{for(i=[],n=arguments.length,n=1;n<arguments.length;++n)i.push(arguments[n]);for(r=t.length,n=0;n<r;++n)t[n].apply(this,i)}},this.dispose=function(){o={}}}};n.prototype.pipe=function(t){return this.on("data",function(e){t.push(e)}),this.on("done",function(e){t.flush(e)}),this.on("partialdone",function(e){t.partialFlush(e)}),this.on("endedtimeline",function(e){t.endTimeline(e)}),this.on("reset",function(e){t.reset(e)}),t},n.prototype.push=function(e){this.trigger("data",e)},n.prototype.flush=function(e){this.trigger("done",e)},n.prototype.partialFlush=function(e){this.trigger("partialdone",e)},n.prototype.endTimeline=function(e){this.trigger("endedtimeline",e)},n.prototype.reset=function(e){this.trigger("reset",e)};var r,i,o,a,s,u=n,l=function(e,t){return r(a(e,t))},c=function(e,t){return i(o(e),t)},d=function(e,t,n){return o(n?e:e-t)},h=r=function(e){return 9e4*e},p=(i=function(e,t){return e*t},o=function(e){return e/9e4}),g=(a=function(e,t){return e/t},l),m=c,y=d,v=[96e3,88200,64e3,48e3,44100,32e3,24e3,22050,16e3,12e3,11025,8e3,7350];(s=function(u){var l,c=0;s.prototype.init.call(this),this.skipWarn_=function(e,t){this.trigger("log",{level:"warn",message:"adts skiping bytes "+e+" to "+t+" in frame "+c+" outside syncword"})},this.push=function(e){var t,n,r,i,o,a,s=0;if(u||(c=0),"audio"===e.type){for(l&&l.length?(r=l,(l=new Uint8Array(r.byteLength+e.data.byteLength)).set(r),l.set(e.data,r.byteLength)):l=e.data;s+7<l.length;)if(255===l[s]&&240==(246&l[s+1])){if("number"==typeof a&&(this.skipWarn_(a,s),a=null),n=2*(1&~l[s+1]),t=(3&l[s+3])<<11|l[s+4]<<3|(224&l[s+5])>>5,o=9e4*(i=1024*(1+(3&l[s+6])))/v[(60&l[s+2])>>>2],l.byteLength-s<t)break;this.trigger("data",{pts:e.pts+c*o,dts:e.dts+c*o,sampleCount:i,audioobjecttype:1+(l[s+2]>>>6&3),channelcount:(1&l[s+2])<<2|(192&l[s+3])>>>6,samplerate:v[(60&l[s+2])>>>2],samplingfrequencyindex:(60&l[s+2])>>>2,samplesize:16,data:l.subarray(s+7+n,s+t)}),c++,s+=t}else"number"!=typeof a&&(a=s),s++;"number"==typeof a&&(this.skipWarn_(a,s),a=null),l=l.subarray(s)}},this.flush=function(){c=0,this.trigger("done")},this.reset=function(){l=void 0,this.trigger("reset")},this.endTimeline=function(){l=void 0,this.trigger("endedtimeline")}}).prototype=new u;function _(r){var i=r.byteLength,o=0,a=0;this.length=function(){return 8*i},this.bitsAvailable=function(){return 8*i+a},this.loadWord=function(){var e=r.byteLength-i,t=new Uint8Array(4),n=Math.min(4,i);if(0===n)throw new Error("no bytes available");t.set(r.subarray(e,e+n)),o=new DataView(t.buffer).getUint32(0),a=8*n,i-=n},this.skipBits=function(e){var t;e<a||(e-=a,e-=8*(t=Math.floor(e/8)),i-=t,this.loadWord()),o<<=e,a-=e},this.readBits=function(e){var t=Math.min(a,e),n=o>>>32-t;return 0<(a-=t)?o<<=t:0<i&&this.loadWord(),0<(t=e-t)?n<<t|this.readBits(t):n},this.skipLeadingZeros=function(){for(var e=0;e<a;++e)if(0!=(o&2147483648>>>e))return o<<=e,a-=e,e;return this.loadWord(),e+this.skipLeadingZeros()},this.skipUnsignedExpGolomb=function(){this.skipBits(1+this.skipLeadingZeros())},this.skipExpGolomb=function(){this.skipBits(1+this.skipLeadingZeros())},this.readUnsignedExpGolomb=function(){var e=this.skipLeadingZeros();return this.readBits(e+1)-1},this.readExpGolomb=function(){var e=this.readUnsignedExpGolomb();return 1&e?1+e>>>1:-1*(e>>>1)},this.readBoolean=function(){return 1===this.readBits(1)},this.readUnsignedByte=function(){return this.readBits(8)},this.loadWord()}var b,w,x,S=s;(w=function(){var r,i,o=0;w.prototype.init.call(this),this.push=function(e){for(var t,n=(i=i?((t=new Uint8Array(i.byteLength+e.data.byteLength)).set(i),t.set(e.data,i.byteLength),t):e.data).byteLength;o<n-3;o++)if(1===i[o+2]){r=o+5;break}for(;r<n;)switch(i[r]){case 0:if(0!==i[r-1]){r+=2;break}if(0!==i[r-2]){r++;break}for(o+3!==r-2&&this.trigger("data",i.subarray(o+3,r-2));r++,1!==i[r]&&r<n;);o=r-2,r+=3;break;case 1:if(0!==i[r-1]||0!==i[r-2]){r+=3;break}this.trigger("data",i.subarray(o+3,r-2)),o=r-2,r+=3;break;default:r+=3}i=i.subarray(o),r-=o,o=0},this.reset=function(){i=null,o=0,this.trigger("reset")},this.flush=function(){i&&3<i.byteLength&&this.trigger("data",i.subarray(o+3)),i=null,o=0,this.trigger("done")},this.endTimeline=function(){this.flush(),this.trigger("endedtimeline")}}).prototype=new u,x={100:!0,110:!0,122:!0,244:!0,44:!0,83:!0,86:!0,118:!0,128:!0,138:!0,139:!0,134:!0},(b=function(){var n,r,i,o,a,s,m,t=new w;b.prototype.init.call(this),(n=this).push=function(e){"video"===e.type&&(r=e.trackId,i=e.pts,o=e.dts,t.push(e))},t.on("data",function(e){var t={trackId:r,pts:i,dts:o,data:e,nalUnitTypeCode:31&e[0]};switch(t.nalUnitTypeCode){case 5:t.nalUnitType="slice_layer_without_partitioning_rbsp_idr";break;case 6:t.nalUnitType="sei_rbsp",t.escapedRBSP=a(e.subarray(1));break;case 7:t.nalUnitType="seq_parameter_set_rbsp",t.escapedRBSP=a(e.subarray(1)),t.config=s(t.escapedRBSP);break;case 8:t.nalUnitType="pic_parameter_set_rbsp";break;case 9:t.nalUnitType="access_unit_delimiter_rbsp"}n.trigger("data",t)}),t.on("done",function(){n.trigger("done")}),t.on("partialdone",function(){n.trigger("partialdone")}),t.on("reset",function(){n.trigger("reset")}),t.on("endedtimeline",function(){n.trigger("endedtimeline")}),this.flush=function(){t.flush()},this.partialFlush=function(){t.partialFlush()},this.reset=function(){t.reset()},this.endTimeline=function(){t.endTimeline()},m=function(e,t){for(var n=8,r=8,i=0;i<e;i++)0!==r&&(r=(n+t.readExpGolomb()+256)%256),n=0===r?n:r},a=function(e){for(var t,n,r=e.byteLength,i=[],o=1;o<r-2;)0===e[o]&&0===e[o+1]&&3===e[o+2]?(i.push(o+2),o+=2):o++;if(0===i.length)return e;t=r-i.length,n=new Uint8Array(t);for(var a=0,o=0;o<t;a++,o++)a===i[0]&&(a++,i.shift()),n[o]=e[a];return n},s=function(e){var t,n,r,i,o,a,s,u=0,l=0,c=0,d=0,f=[1,1],h=(t=new _(e)).readUnsignedByte(),p=t.readUnsignedByte(),g=t.readUnsignedByte();if(t.skipUnsignedExpGolomb(),x[h]&&(3===(n=t.readUnsignedExpGolomb())&&t.skipBits(1),t.skipUnsignedExpGolomb(),t.skipUnsignedExpGolomb(),t.skipBits(1),t.readBoolean()))for(a=3!==n?8:12,s=0;s<a;s++)t.readBoolean()&&m(s<6?16:64,t);if(t.skipUnsignedExpGolomb(),0===(o=t.readUnsignedExpGolomb()))t.readUnsignedExpGolomb();else if(1===o)for(t.skipBits(1),t.skipExpGolomb(),t.skipExpGolomb(),r=t.readUnsignedExpGolomb(),s=0;s<r;s++)t.skipExpGolomb();if(t.skipUnsignedExpGolomb(),t.skipBits(1),i=t.readUnsignedExpGolomb(),e=t.readUnsignedExpGolomb(),0===(o=t.readBits(1))&&t.skipBits(1),t.skipBits(1),t.readBoolean()&&(u=t.readUnsignedExpGolomb(),l=t.readUnsignedExpGolomb(),c=t.readUnsignedExpGolomb(),d=t.readUnsignedExpGolomb()),t.readBoolean()&&t.readBoolean()){switch(t.readUnsignedByte()){case 1:f=[1,1];break;case 2:f=[12,11];break;case 3:f=[10,11];break;case 4:f=[16,11];break;case 5:f=[40,33];break;case 6:f=[24,11];break;case 7:f=[20,11];break;case 8:f=[32,11];break;case 9:f=[80,33];break;case 10:f=[18,11];break;case 11:f=[15,11];break;case 12:f=[64,33];break;case 13:f=[160,99];break;case 14:f=[4,3];break;case 15:f=[3,2];break;case 16:f=[2,1];break;case 255:f=[t.readUnsignedByte()<<8|t.readUnsignedByte(),t.readUnsignedByte()<<8|t.readUnsignedByte()]}f&&(f[0],f[1])}return{profileIdc:h,levelIdc:g,profileCompatibility:p,width:16*(i+1)-2*u-2*l,height:(2-o)*(e+1)*16-2*c-2*d,sarRatio:f}}}).prototype=new u;var T,k,E,C,A,D,I,R,O,P,L,F,U,M,N,B,j,q,G,H,z,W,V,$,Y,X,Q,K,J,Z,ee,te,ne,re,ie,oe,ae,se,ue,le={H264Stream:b,NalByteStream:w},ce={Adts:S,h264:le},de=Math.pow(2,32),fe={getUint64:function(e){var t=new DataView(e.buffer,e.byteOffset,e.byteLength);return t.getBigUint64?(e=t.getBigUint64(0))<Number.MAX_SAFE_INTEGER?Number(e):e:t.getUint32(0)*de+t.getUint32(4)},MAX_UINT32:de},he=h,pe=fe.MAX_UINT32,ge=4294967295;!function(){if(z={avc1:[],avcC:[],btrt:[],dinf:[],dref:[],esds:[],ftyp:[],hdlr:[],mdat:[],mdhd:[],mdia:[],mfhd:[],minf:[],moof:[],moov:[],mp4a:[],mvex:[],mvhd:[],pasp:[],sdtp:[],smhd:[],stbl:[],stco:[],stsc:[],stsd:[],stsz:[],stts:[],styp:[],tfdt:[],tfhd:[],traf:[],trak:[],trun:[],trex:[],tkhd:[],vmhd:[]},"undefined"!=typeof Uint8Array){for(var e in z)z.hasOwnProperty(e)&&(z[e]=[e.charCodeAt(0),e.charCodeAt(1),e.charCodeAt(2),e.charCodeAt(3)]);W=new Uint8Array(["i".charCodeAt(0),"s".charCodeAt(0),"o".charCodeAt(0),"m".charCodeAt(0)]),$=new Uint8Array(["a".charCodeAt(0),"v".charCodeAt(0),"c".charCodeAt(0),"1".charCodeAt(0)]),V=new Uint8Array([0,0,0,1]),Y=new Uint8Array([0,0,0,0,0,0,0,0,118,105,100,101,0,0,0,0,0,0,0,0,0,0,0,0,86,105,100,101,111,72,97,110,100,108,101,114,0]),X=new Uint8Array([0,0,0,0,0,0,0,0,115,111,117,110,0,0,0,0,0,0,0,0,0,0,0,0,83,111,117,110,100,72,97,110,100,108,101,114,0]),Q={video:Y,audio:X},Z=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,12,117,114,108,32,0,0,0,1]),J=new Uint8Array([0,0,0,0,0,0,0,0]),ee=new Uint8Array([0,0,0,0,0,0,0,0]),te=ee,ne=new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0]),re=ee,K=new Uint8Array([0,0,0,1,0,0,0,0,0,0,0,0])}}(),T=function(e){for(var t,n=[],r=0,i=1;i<arguments.length;i++)n.push(arguments[i]);for(i=n.length;i--;)r+=n[i].byteLength;for(t=new Uint8Array(r+8),new DataView(t.buffer,t.byteOffset,t.byteLength).setUint32(0,t.byteLength),t.set(e,4),i=0,r=8;i<n.length;i++)t.set(n[i],r),r+=n[i].byteLength;return t},k=function(){return T(z.dinf,T(z.dref,Z))},E=function(e){return T(z.esds,new Uint8Array([0,0,0,0,3,25,0,0,0,4,17,64,21,0,6,0,0,0,218,192,0,0,218,192,5,2,e.audioobjecttype<<3|e.samplingfrequencyindex>>>1,e.samplingfrequencyindex<<7|e.channelcount<<3,6,1,2]))},M=function(e){return T(z.hdlr,Q[e])},U=function(e){var t=new Uint8Array([0,0,0,0,0,0,0,2,0,0,0,3,0,1,95,144,e.duration>>>24&255,e.duration>>>16&255,e.duration>>>8&255,255&e.duration,85,196,0,0]);return e.samplerate&&(t[12]=e.samplerate>>>24&255,t[13]=e.samplerate>>>16&255,t[14]=e.samplerate>>>8&255,t[15]=255&e.samplerate),T(z.mdhd,t)},F=function(e){return T(z.mdia,U(e),M(e.type),D(e))},A=function(e){return T(z.mfhd,new Uint8Array([0,0,0,0,(4278190080&e)>>24,(16711680&e)>>16,(65280&e)>>8,255&e]))},D=function(e){return T(z.minf,"video"===e.type?T(z.vmhd,K):T(z.smhd,J),k(),B(e))},yt=function(e,t){for(var n=[],r=t.length;r--;)n[r]=q(t[r]);return T.apply(null,[z.moof,A(e)].concat(n))},I=function(e){for(var t=e.length,n=[];t--;)n[t]=P(e[t]);return T.apply(null,[z.moov,O(ge)].concat(n).concat(R(e)))},R=function(e){for(var t=e.length,n=[];t--;)n[t]=G(e[t]);return T.apply(null,[z.mvex].concat(n))},O=function(e){e=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,2,0,1,95,144,(4278190080&e)>>24,(16711680&e)>>16,(65280&e)>>8,255&e,0,1,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,255]);return T(z.mvhd,e)},N=function(e){for(var t,n=e.samples||[],r=new Uint8Array(4+n.length),i=0;i<n.length;i++)t=n[i].flags,r[i+4]=t.dependsOn<<4|t.isDependedOn<<2|t.hasRedundancy;return T(z.sdtp,r)},B=function(e){return T(z.stbl,j(e),T(z.stts,re),T(z.stsc,te),T(z.stsz,ne),T(z.stco,ee))},j=function(e){return T(z.stsd,new Uint8Array([0,0,0,0,0,0,0,1]),("video"===e.type?ie:oe)(e))},ie=function(e){for(var t=e.sps||[],n=e.pps||[],r=[],i=[],o=0;o<t.length;o++)r.push((65280&t[o].byteLength)>>>8),r.push(255&t[o].byteLength),r=r.concat(Array.prototype.slice.call(t[o]));for(o=0;o<n.length;o++)i.push((65280&n[o].byteLength)>>>8),i.push(255&n[o].byteLength),i=i.concat(Array.prototype.slice.call(n[o]));var a,s=[z.avc1,new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,(65280&e.width)>>8,255&e.width,(65280&e.height)>>8,255&e.height,0,72,0,0,0,72,0,0,0,0,0,0,0,1,19,118,105,100,101,111,106,115,45,99,111,110,116,114,105,98,45,104,108,115,0,0,0,0,0,0,0,0,0,0,0,0,0,24,17,17]),T(z.avcC,new Uint8Array([1,e.profileIdc,e.profileCompatibility,e.levelIdc,255].concat([t.length],r,[n.length],i))),T(z.btrt,new Uint8Array([0,28,156,128,0,45,198,192,0,45,198,192]))];return e.sarRatio&&(a=e.sarRatio[0],e=e.sarRatio[1],s.push(T(z.pasp,new Uint8Array([(4278190080&a)>>24,(16711680&a)>>16,(65280&a)>>8,255&a,(4278190080&e)>>24,(16711680&e)>>16,(65280&e)>>8,255&e])))),T.apply(null,s)},oe=function(e){return T(z.mp4a,new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,(65280&e.channelcount)>>8,255&e.channelcount,(65280&e.samplesize)>>8,255&e.samplesize,0,0,0,0,(65280&e.samplerate)>>8,255&e.samplerate,0,0]),E(e))},L=function(e){e=new Uint8Array([0,0,0,7,0,0,0,0,0,0,0,0,(4278190080&e.id)>>24,(16711680&e.id)>>16,(65280&e.id)>>8,255&e.id,0,0,0,0,(4278190080&e.duration)>>24,(16711680&e.duration)>>16,(65280&e.duration)>>8,255&e.duration,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,(65280&e.width)>>8,255&e.width,0,0,(65280&e.height)>>8,255&e.height,0,0]);return T(z.tkhd,e)},q=function(e){var t,n=T(z.tfhd,new Uint8Array([0,0,0,58,(4278190080&e.id)>>24,(16711680&e.id)>>16,(65280&e.id)>>8,255&e.id,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0])),r=Math.floor(e.baseMediaDecodeTime/pe),i=Math.floor(e.baseMediaDecodeTime%pe),r=T(z.tfdt,new Uint8Array([1,0,0,0,r>>>24&255,r>>>16&255,r>>>8&255,255&r,i>>>24&255,i>>>16&255,i>>>8&255,255&i]));return"audio"===e.type?(t=H(e,92),T(z.traf,n,r,t)):(i=N(e),t=H(e,i.length+92),T(z.traf,n,r,t,i))},P=function(e){return e.duration=e.duration||4294967295,T(z.trak,L(e),F(e))},G=function(e){var t=new Uint8Array([0,0,0,0,(4278190080&e.id)>>24,(16711680&e.id)>>16,(65280&e.id)>>8,255&e.id,0,0,0,1,0,0,0,0,0,0,0,0,0,1,0,1]);return"video"!==e.type&&(t[t.length-1]=0),T(z.trex,t)},ue=function(e,t){var n=0,r=0,i=0,o=0;return e.length&&(void 0!==e[0].duration&&(n=1),void 0!==e[0].size&&(r=2),void 0!==e[0].flags&&(i=4),void 0!==e[0].compositionTimeOffset&&(o=8)),[0,0,n|r|i|o,1,(4278190080&e.length)>>>24,(16711680&e.length)>>>16,(65280&e.length)>>>8,255&e.length,(4278190080&t)>>>24,(16711680&t)>>>16,(65280&t)>>>8,255&t]},se=function(e,t){var n,r,i,o,a;for(t+=20+16*(i=e.samples||[]).length,t=ue(i,t),(r=new Uint8Array(t.length+16*i.length)).set(t),n=t.length,a=0;a<i.length;a++)o=i[a],r[n++]=(4278190080&o.duration)>>>24,r[n++]=(16711680&o.duration)>>>16,r[n++]=(65280&o.duration)>>>8,r[n++]=255&o.duration,r[n++]=(4278190080&o.size)>>>24,r[n++]=(16711680&o.size)>>>16,r[n++]=(65280&o.size)>>>8,r[n++]=255&o.size,r[n++]=o.flags.isLeading<<2|o.flags.dependsOn,r[n++]=o.flags.isDependedOn<<6|o.flags.hasRedundancy<<4|o.flags.paddingValue<<1|o.flags.isNonSyncSample,r[n++]=61440&o.flags.degradationPriority,r[n++]=15&o.flags.degradationPriority,r[n++]=(4278190080&o.compositionTimeOffset)>>>24,r[n++]=(16711680&o.compositionTimeOffset)>>>16,r[n++]=(65280&o.compositionTimeOffset)>>>8,r[n++]=255&o.compositionTimeOffset;return T(z.trun,r)},ae=function(e,t){var n,r,i,o,a;for(t+=20+8*(i=e.samples||[]).length,t=ue(i,t),(n=new Uint8Array(t.length+8*i.length)).set(t),r=t.length,a=0;a<i.length;a++)o=i[a],n[r++]=(4278190080&o.duration)>>>24,n[r++]=(16711680&o.duration)>>>16,n[r++]=(65280&o.duration)>>>8,n[r++]=255&o.duration,n[r++]=(4278190080&o.size)>>>24,n[r++]=(16711680&o.size)>>>16,n[r++]=(65280&o.size)>>>8,n[r++]=255&o.size;return T(z.trun,n)},H=function(e,t){return("audio"===e.type?ae:se)(e,t)};function me(e){var t="";return t+=String.fromCharCode(e[0]),t+=String.fromCharCode(e[1]),(t+=String.fromCharCode(e[2]))+String.fromCharCode(e[3])}function ye(e,t){var n,r,i,o=[];if(!t.length)return null;for(n=0;n<e.byteLength;)r=Ve(e[n]<<24|e[n+1]<<16|e[n+2]<<8|e[n+3]),i=me(e.subarray(n+4,n+8)),r=1<r?n+r:e.byteLength,i===t[0]&&(1===t.length?o.push(e.subarray(n+8,r)):(i=ye(e.subarray(n+8,r),t.slice(1))).length&&(o=o.concat(i))),n=r;return o}function ve(e){for(var t=0,n=String.fromCharCode(e[t]),r="";"\0"!==n;)r+=n,t++,n=String.fromCharCode(e[t]);return r+n}function _e(e){var t,n,r,i,o,a,s=4,u=e[0];0===u?(s+=(t=ve(e.subarray(s))).length,s+=(n=ve(e.subarray(s))).length,a=(o=new DataView(e.buffer)).getUint32(s),s+=4,i=o.getUint32(s),s+=4,c=o.getUint32(s),s+=4,l=o.getUint32(s),s+=4):1===u&&(a=(o=new DataView(e.buffer)).getUint32(s),s+=4,r=$e(e.subarray(s)),s+=8,c=o.getUint32(s),s+=4,l=o.getUint32(s),s+=4,s+=(t=ve(e.subarray(s))).length,s+=(n=ve(e.subarray(s))).length);var l,c={scheme_id_uri:t,value:n,timescale:a||1,presentation_time:r,presentation_time_delta:i,event_duration:c,id:l,message_data:new Uint8Array(e.subarray(s,e.byteLength))};return s="\0"!==(l=c).scheme_id_uri,u=0===(e=u)&&Ye(l.presentation_time_delta)&&s,s=1===e&&Ye(l.presentation_time)&&s,!(1<e)&&u||s?c:void 0}function be(e,t,n,r){return e||0===e?e/t:r+n/t}function we(e){var t=new DataView(e.buffer,e.byteOffset,e.byteLength),n={version:e[0],flags:new Uint8Array(e.subarray(1,4)),trackId:t.getUint32(4)},r=1&n.flags[2],i=2&n.flags[2],o=8&n.flags[2],a=16&n.flags[2],s=32&n.flags[2],u=65536&n.flags[0],l=131072&n.flags[0],e=8;return r&&(e+=4,n.baseDataOffset=t.getUint32(12),e+=4),i&&(n.sampleDescriptionIndex=t.getUint32(e),e+=4),o&&(n.defaultSampleDuration=t.getUint32(e),e+=4),a&&(n.defaultSampleSize=t.getUint32(e),e+=4),s&&(n.defaultSampleFlags=t.getUint32(e)),u&&(n.durationIsEmpty=!0),!r&&l&&(n.baseDataOffsetIsMoof=!0),n}function xe(e){return{isLeading:(12&e[0])>>>2,dependsOn:3&e[0],isDependedOn:(192&e[1])>>>6,hasRedundancy:(48&e[1])>>>4,paddingValue:(14&e[1])>>>1,isNonSyncSample:1&e[1],degradationPriority:e[2]<<8|e[3]}}function Se(e){var t,n={version:e[0],flags:new Uint8Array(e.subarray(1,4)),samples:[]},r=new DataView(e.buffer,e.byteOffset,e.byteLength),i=1&n.flags[2],o=4&n.flags[2],a=1&n.flags[1],s=2&n.flags[1],u=4&n.flags[1],l=8&n.flags[1],c=r.getUint32(4),d=8;for(i&&(n.dataOffset=r.getInt32(d),d+=4),o&&c&&(t={flags:xe(e.subarray(d,d+4))},d+=4,a&&(t.duration=r.getUint32(d),d+=4),s&&(t.size=r.getUint32(d),d+=4),l&&(1===n.version?t.compositionTimeOffset=r.getInt32(d):t.compositionTimeOffset=r.getUint32(d),d+=4),n.samples.push(t),c--);c--;)t={},a&&(t.duration=r.getUint32(d),d+=4),s&&(t.size=r.getUint32(d),d+=4),u&&(t.flags=xe(e.subarray(d,d+4)),d+=4),l&&(1===n.version?t.compositionTimeOffset=r.getInt32(d):t.compositionTimeOffset=r.getUint32(d),d+=4),n.samples.push(t);return n}function Te(e){var t={version:e[0],flags:new Uint8Array(e.subarray(1,4))};return 1===t.version?t.baseMediaDecodeTime=Qe(e.subarray(4)):t.baseMediaDecodeTime=Xe(e[4]<<24|e[5]<<16|e[6]<<8|e[7]),t}function ke(e,t,n){if(!e)return-1;for(var r=n;r<e.length;r++)if(e[r]===t)return r;return-1}function Ee(e,t,n){for(var r="",i=t;i<n;i++)r+="%"+("00"+e[i].toString(16)).slice(-2);return r}function Ce(e,t,n){return decodeURIComponent(Ee(e,t,n))}function Ae(e,t,n){return unescape(Ee(e,t,n))}function De(e){return e[0]<<21|e[1]<<14|e[2]<<7|e[3]}function Ie(e){return("00"+e.toString(16)).slice(-2)}function Re(e,t){var n={size:0,flags:{isLeading:0,dependsOn:1,isDependedOn:0,hasRedundancy:0,degradationPriority:0,isNonSyncSample:1}};return n.dataOffset=t,n.compositionTimeOffset=e.pts-e.dts,n.duration=e.duration,n.size=4*e.length,n.size+=e.byteLength,e.keyFrame&&(n.flags.dependsOn=2,n.flags.isNonSyncSample=0),n}function Oe(e){var t,n,r=[],i=[];for(i.byteLength=0,i.nalCount=0,i.duration=0,t=r.byteLength=0;t<e.length;t++)"access_unit_delimiter_rbsp"===(n=e[t]).nalUnitType?(r.length&&(r.duration=n.dts-r.dts,i.byteLength+=r.byteLength,i.nalCount+=r.length,i.duration+=r.duration,i.push(r)),(r=[n]).byteLength=n.data.byteLength,r.pts=n.pts,r.dts=n.dts):("slice_layer_without_partitioning_rbsp_idr"===n.nalUnitType&&(r.keyFrame=!0),r.duration=n.dts-r.dts,r.byteLength+=n.data.byteLength,r.push(n));return i.length&&(!r.duration||r.duration<=0)&&(r.duration=i[i.length-1].duration),i.byteLength+=r.byteLength,i.nalCount+=r.length,i.duration+=r.duration,i.push(r),i}function Pe(e){var t,n,r=[],i=[];for(r.byteLength=0,r.nalCount=0,r.duration=0,r.pts=e[0].pts,r.dts=e[0].dts,i.byteLength=0,i.nalCount=0,i.duration=0,i.pts=e[0].pts,i.dts=e[0].dts,t=0;t<e.length;t++)(n=e[t]).keyFrame?(r.length&&(i.push(r),i.byteLength+=r.byteLength,i.nalCount+=r.nalCount,i.duration+=r.duration),(r=[n]).nalCount=n.length,r.byteLength=n.byteLength,r.pts=n.pts,r.dts=n.dts,r.duration=n.duration):(r.duration+=n.duration,r.nalCount+=n.length,r.byteLength+=n.byteLength,r.push(n));return i.length&&r.duration<=0&&(r.duration=i[i.length-1].duration),i.byteLength+=r.byteLength,i.nalCount+=r.nalCount,i.duration+=r.duration,i.push(r),i}function Le(e){var t;return!e[0][0].keyFrame&&1<e.length&&(t=e.shift(),e.byteLength-=t.byteLength,e.nalCount-=t.nalCount,e[0][0].dts=t.dts,e[0][0].pts=t.pts,e[0][0].duration+=t.duration),e}function Fe(e){for(var t=[];e--;)t.push(0);return t}function Ue(e,t,n,r){var i,o,a,s,u,l,c=0,d=0;if(t.length&&(i=g(e.baseMediaDecodeTime,e.samplerate),o=Math.ceil(9e4/(e.samplerate/1024)),n&&r&&(r=i-Math.max(n,r),d=(c=Math.floor(r/o))*o),!(c<1||45e3<d))){for(ze||(l={96e3:[nt,[227,64],Fe(154),[56]],88200:[nt,[231],Fe(170),[56]],64e3:[nt,[248,192],Fe(240),[56]],48e3:[nt,[255,192],Fe(268),[55,148,128],Fe(54),[112]],44100:[nt,[255,192],Fe(268),[55,163,128],Fe(84),[112]],32e3:[nt,[255,192],Fe(268),[55,234],Fe(226),[112]],24e3:[nt,[255,192],Fe(268),[55,255,128],Fe(268),[111,112],Fe(126),[224]],16e3:[nt,[255,192],Fe(268),[55,255,128],Fe(268),[111,255],Fe(269),[223,108],Fe(195),[1,192]],12e3:[rt,Fe(268),[3,127,248],Fe(268),[6,255,240],Fe(268),[13,255,224],Fe(268),[27,253,128],Fe(259),[56]],11025:[rt,Fe(268),[3,127,248],Fe(268),[6,255,240],Fe(268),[13,255,224],Fe(268),[27,255,192],Fe(268),[55,175,128],Fe(108),[112]],8e3:[rt,Fe(268),[3,121,16],Fe(47),[7]]},ze=Object.keys(l).reduce(function(e,t){return e[t]=new Uint8Array(l[t].reduce(function(e,t){return e.concat(t)},[])),e},{})),(a=ze[e.samplerate])||(a=t[0].data),s=0;s<c;s++)u=t[0],t.splice(0,0,{data:a,dts:u.dts-o,pts:u.pts-o});return e.baseMediaDecodeTime-=Math.floor(m(d,e.samplerate)),d}}function Me(e,t,n){return t.minSegmentDts>=n?e:(t.minSegmentDts=1/0,e.filter(function(e){return e.dts>=n&&(t.minSegmentDts=Math.min(t.minSegmentDts,e.dts),t.minSegmentPts=t.minSegmentDts,!0)}))}function Ne(e){for(var t,n=[],r=0;r<e.length;r++)t=e[r],n.push({size:t.data.byteLength,duration:1024});return n}function Be(e){for(var t,n=0,r=new Uint8Array(function(e){for(var t=0,n=0;n<e.length;n++)t+=e[n].data.byteLength;return t}(e)),i=0;i<e.length;i++)t=e[i],r.set(t.data,n),n+=t.data.byteLength;return r}function je(e){delete e.minSegmentDts,delete e.maxSegmentDts,delete e.minSegmentPts,delete e.maxSegmentPts}function qe(e,t){var n=e.minSegmentDts;return t||(n-=e.timelineStartInfo.dts),t=e.timelineStartInfo.baseMediaDecodeTime,t+=n,t=Math.max(0,t),"audio"===e.type&&(t*=e.samplerate/9e4,t=Math.floor(t)),t}function Ge(e,t){"number"==typeof t.pts&&(void 0===e.timelineStartInfo.pts&&(e.timelineStartInfo.pts=t.pts),void 0===e.minSegmentPts?e.minSegmentPts=t.pts:e.minSegmentPts=Math.min(e.minSegmentPts,t.pts),void 0===e.maxSegmentPts?e.maxSegmentPts=t.pts:e.maxSegmentPts=Math.max(e.maxSegmentPts,t.pts)),"number"==typeof t.dts&&(void 0===e.timelineStartInfo.dts&&(e.timelineStartInfo.dts=t.dts),void 0===e.minSegmentDts?e.minSegmentDts=t.dts:e.minSegmentDts=Math.min(e.minSegmentDts,t.dts),void 0===e.maxSegmentDts?e.maxSegmentDts=t.dts:e.maxSegmentDts=Math.max(e.maxSegmentDts,t.dts))}var He,ze,We={ftyp:C=function(){return T(z.ftyp,W,V,W,$)},mdat:function(e){return T(z.mdat,e)},moof:yt,moov:I,setDuration:function(e){e&&(ge=he(e))},initSegment:function(e){var t=C(),n=I(e);return(e=new Uint8Array(t.byteLength+n.byteLength)).set(t),e.set(n,t.byteLength),e}},n=function(e){return e>>>0},Ve=n,$e=fe.getUint64,Ye=function(e){return void 0!==e||null!==e},Xe=n,Qe=fe.getUint64,Ke={APIC:function(e){var t,n=1;3===e.data[0]&&((t=ke(e.data,0,n))<0||(e.mimeType=Ae(e.data,n,t),n=t+1,e.pictureType=e.data[n],n++,(t=ke(e.data,0,n))<0||(e.description=Ce(e.data,n,t),n=t+1,"--\x3e"===e.mimeType?e.url=Ae(e.data,n,e.data.length):e.pictureData=e.data.subarray(n,e.data.length))))},"T*":function(e){3===e.data[0]&&(e.value=Ce(e.data,1,e.data.length).replace(/\0*$/,""),e.values=e.value.split("\0"))},TXXX:function(e){var t;3===e.data[0]&&-1!==(t=ke(e.data,0,1))&&(e.description=Ce(e.data,1,t),e.value=Ce(e.data,t+1,e.data.length).replace(/\0*$/,""),e.data=e.value)},"W*":function(e){e.url=Ae(e.data,0,e.data.length).replace(/\0.*$/,"")},WXXX:function(e){var t;3===e.data[0]&&-1!==(t=ke(e.data,0,1))&&(e.description=Ce(e.data,1,t),e.url=Ae(e.data,t+1,e.data.length).replace(/\0.*$/,""))},PRIV:function(e){for(var t=0;t<e.data.length;t++)if(0===e.data[t]){e.owner=Ae(e.data,0,t);break}e.privateData=e.data.subarray(t+1),e.data=e.privateData}},Je={parseId3Frames:function(e){var t,n=10,r=0,i=[];if(!(e.length<10||e[0]!=="I".charCodeAt(0)||e[1]!=="D".charCodeAt(0)||e[2]!=="3".charCodeAt(0))){r=De(e.subarray(6,10)),r+=10,64&e[5]&&(n+=4,n+=De(e.subarray(10,14)),r-=De(e.subarray(16,20)));do{if((t=De(e.subarray(n+4,n+8)))<1)break;var o={id:String.fromCharCode(e[n],e[n+1],e[n+2],e[n+3]),data:e.subarray(n+10,n+t+10)}}while(o.key=o.id,Ke[o.id]?Ke[o.id](o):"T"===o.id[0]?Ke["T*"](o):"W"===o.id[0]&&Ke["W*"](o),i.push(o),n+=10,(n+=t)<r);return i}},parseSyncSafeInteger:De,frameParsers:Ke},Ze=n,et=fe.getUint64,tt=Je.parseId3Frames,l={findBox:ye,parseType:me,timescale:function(e){return ye(e,["moov","trak"]).reduce(function(e,t){var n,r,i;return(i=ye(t,["tkhd"])[0])?(n=i[0],i=Ze(i[r=0===n?12:20]<<24|i[1+r]<<16|i[2+r]<<8|i[3+r]),(t=ye(t,["mdia","mdhd"])[0])?(r=0===t[0]?12:20,e[i]=Ze(t[r]<<24|t[1+r]<<16|t[2+r]<<8|t[3+r]),e):null):null},{})},startTime:function(o,e){e=ye(e,["moof","traf"]).reduce(function(e,t){var n,r=ye(t,["tfhd"])[0],i=Ze(r[4]<<24|r[5]<<16|r[6]<<8|r[7]),r=o[i]||9e4,i=ye(t,["tfdt"])[0],t=new DataView(i.buffer,i.byteOffset,i.byteLength);return"bigint"==typeof(t=1===i[0]?et(i.subarray(4,12)):t.getUint32(4))?n=t/f.default.BigInt(r):"number"!=typeof t||isNaN(t)||(n=t/r),n<Number.MAX_SAFE_INTEGER&&(n=Number(n)),n<e&&(e=n),e},1/0);return"bigint"==typeof e||isFinite(e)?e:0},compositionStartTime:function(e,t){var n,r=ye(t,["moof","traf"]),i=0,o=0;r&&r.length&&(n=ye(r[0],["tfhd"])[0],t=ye(r[0],["trun"])[0],r=ye(r[0],["tfdt"])[0],n&&(a=we(n).trackId),r&&(i=Te(r).baseMediaDecodeTime),!t||(t=Se(t)).samples&&t.samples.length&&(o=t.samples[0].compositionTimeOffset||0));var a=e[a]||9e4;"bigint"==typeof i&&(o=f.default.BigInt(o),a=f.default.BigInt(a));a=(i+o)/a;return"bigint"==typeof a&&a<Number.MAX_SAFE_INTEGER&&(a=Number(a)),a},videoTrackIds:function(e){var e=ye(e,["moov","trak"]),r=[];return e.forEach(function(e){var t=ye(e,["mdia","hdlr"]),n=ye(e,["tkhd"]);t.forEach(function(e,t){var e=me(e.subarray(8,12)),t=n[t];"vide"===e&&(t=0===(t=new DataView(t.buffer,t.byteOffset,t.byteLength)).getUint8(0)?t.getUint32(12):t.getUint32(20),r.push(t))})}),r},tracks:function(e){var e=ye(e,["moov","trak"]),a=[];return e.forEach(function(e){var t={},n=ye(e,["tkhd"])[0];n&&(n=(r=new DataView(n.buffer,n.byteOffset,n.byteLength)).getUint8(0),t.id=0===n?r.getUint32(12):r.getUint32(20));var r=ye(e,["mdia","hdlr"])[0];r&&(o=me(r.subarray(8,12)),t.type="vide"===o?"video":"soun"===o?"audio":o);var i,o=ye(e,["mdia","minf","stbl","stsd"])[0];o&&(o=o.subarray(8),t.codec=me(o.subarray(4,8)),(o=ye(o,[t.codec])[0])&&(/^[asm]vc[1-9]$/i.test(t.codec)?(i=o.subarray(78),"avcC"===me(i.subarray(4,8))&&11<i.length?(t.codec+=".",t.codec+=Ie(i[9]),t.codec+=Ie(i[10]),t.codec+=Ie(i[11])):t.codec="avc1.4d400d"):/^mp4[a,v]$/i.test(t.codec)?(i=o.subarray(28),"esds"===me(i.subarray(4,8))&&20<i.length&&0!==i[19]?(t.codec+="."+Ie(i[19]),t.codec+="."+Ie(i[20]>>>2&63).replace(/^0/,"")):t.codec="mp4a.40.2"):t.codec=t.codec.toLowerCase()));e=ye(e,["mdia","mdhd"])[0];e&&(t.timescale=He(e)),a.push(t)}),a},getTimescaleFromMediaHeader:He=function(e){var t=0===e[0]?12:20;return Ze(e[t]<<24|e[1+t]<<16|e[2+t]<<8|e[3+t])},getEmsgID3:function(e,n){return void 0===n&&(n=0),ye(e,["emsg"]).map(function(e){var t=_e(new Uint8Array(e)),e=tt(t.message_data);return{cueTime:be(t.presentation_time,t.timescale,t.presentation_time_delta,n),duration:be(t.event_duration,t.timescale),frames:e}})}},nt=[33,16,5,32,164,27],rt=[33,65,108,84,1,2,4,8,168,2,4,8,17,191,252],c=function e(t){t=t||{},e.prototype.init.call(this),this.parse708captions_="boolean"!=typeof t.parse708captions||t.parse708captions,this.captionPackets_=[],this.ccStreams_=[new ht(0,0),new ht(0,1),new ht(1,0),new ht(1,1)],this.parse708captions_&&(this.cc708Stream_=new ut({captionServices:t.captionServices})),this.reset(),this.ccStreams_.forEach(function(e){e.on("data",this.trigger.bind(this,"data")),e.on("partialdone",this.trigger.bind(this,"partialdone")),e.on("done",this.trigger.bind(this,"done"))},this),this.parse708captions_&&(this.cc708Stream_.on("data",this.trigger.bind(this,"data")),this.cc708Stream_.on("partialdone",this.trigger.bind(this,"partialdone")),this.cc708Stream_.on("done",this.trigger.bind(this,"done")))};(c.prototype=new u).push=function(e){var t,n;if("sei_rbsp"===e.nalUnitType&&(t=function(e){for(var t=0,n={payloadType:-1,payloadSize:0},r=0,i=0;t<e.byteLength&&128!==e[t];){for(;255===e[t];)r+=255,t++;for(r+=e[t++];255===e[t];)i+=255,t++;if(i+=e[t++],!n.payload&&4===r){if("GA94"===String.fromCharCode(e[t+3],e[t+4],e[t+5],e[t+6])){n.payloadType=r,n.payloadSize=i,n.payload=e.subarray(t,t+i);break}n.payload=void 0}t+=i,i=r=0}return n}(e.escapedRBSP)).payload&&4===t.payloadType&&(n=181!==(n=t).payload[0]||49!=(n.payload[1]<<8|n.payload[2])||"GA94"!==String.fromCharCode(n.payload[3],n.payload[4],n.payload[5],n.payload[6])||3!==n.payload[7]?null:n.payload.subarray(8,n.payload.length-1)))if(e.dts<this.latestDts_)this.ignoreNextEqualDts_=!0;else{if(e.dts===this.latestDts_&&this.ignoreNextEqualDts_)return this.numSameDts_--,void(this.numSameDts_||(this.ignoreNextEqualDts_=!1));n=function(e,t){var n,r,i,o,a=[];if(!(64&t[0]))return a;for(r=31&t[0],n=0;n<r;n++)o={type:3&t[2+(i=3*n)],pts:e},4&t[2+i]&&(o.ccData=t[3+i]<<8|t[4+i],a.push(o));return a}(e.pts,n),this.captionPackets_=this.captionPackets_.concat(n),this.latestDts_!==e.dts&&(this.numSameDts_=0),this.numSameDts_++,this.latestDts_=e.dts}},c.prototype.flushCCStreams=function(t){this.ccStreams_.forEach(function(e){return"flush"===t?e.flush():e.partialFlush()},this)},c.prototype.flushStream=function(e){this.captionPackets_.length&&(this.captionPackets_.forEach(function(e,t){e.presortIndex=t}),this.captionPackets_.sort(function(e,t){return e.pts===t.pts?e.presortIndex-t.presortIndex:e.pts-t.pts}),this.captionPackets_.forEach(function(e){e.type<2?this.dispatchCea608Packet(e):this.dispatchCea708Packet(e)},this),this.captionPackets_.length=0),this.flushCCStreams(e)},c.prototype.flush=function(){return this.flushStream("flush")},c.prototype.partialFlush=function(){return this.flushStream("partialFlush")},c.prototype.reset=function(){this.latestDts_=null,this.ignoreNextEqualDts_=!1,this.numSameDts_=0,this.activeCea608Channel_=[null,null],this.ccStreams_.forEach(function(e){e.reset()})},c.prototype.dispatchCea608Packet=function(e){this.setsTextOrXDSActive(e)?this.activeCea608Channel_[e.type]=null:this.setsChannel1Active(e)?this.activeCea608Channel_[e.type]=0:this.setsChannel2Active(e)&&(this.activeCea608Channel_[e.type]=1),null!==this.activeCea608Channel_[e.type]&&this.ccStreams_[(e.type<<1)+this.activeCea608Channel_[e.type]].push(e)},c.prototype.setsChannel1Active=function(e){return 4096==(30720&e.ccData)},c.prototype.setsChannel2Active=function(e){return 6144==(30720&e.ccData)},c.prototype.setsTextOrXDSActive=function(e){return 256==(28928&e.ccData)||4138==(30974&e.ccData)||6186==(30974&e.ccData)},c.prototype.dispatchCea708Packet=function(e){this.parse708captions_&&this.cc708Stream_.push(e)};function it(e){return 32<=e&&e<=127||160<=e&&e<=255}function ot(e){this.windowNum=e,this.reset()}var at={127:9834,4128:32,4129:160,4133:8230,4138:352,4140:338,4144:9608,4145:8216,4146:8217,4147:8220,4148:8221,4149:8226,4153:8482,4154:353,4156:339,4157:8480,4159:376,4214:8539,4215:8540,4216:8541,4217:8542,4218:9168,4219:9124,4220:9123,4221:9135,4222:9126,4223:9121,4256:12600};ot.prototype.reset=function(){this.clearText(),this.pendingNewLine=!1,this.winAttr={},this.penAttr={},this.penLoc={},this.penColor={},this.visible=0,this.rowLock=0,this.columnLock=0,this.priority=0,this.relativePositioning=0,this.anchorVertical=0,this.anchorHorizontal=0,this.anchorPoint=0,this.rowCount=1,this.virtualRowCount=this.rowCount+1,this.columnCount=41,this.windowStyle=0,this.penStyle=0},ot.prototype.getText=function(){return this.rows.join("\n")},ot.prototype.clearText=function(){this.rows=[""],this.rowIdx=0},ot.prototype.newLine=function(e){for(this.rows.length>=this.virtualRowCount&&"function"==typeof this.beforeRowOverflow&&this.beforeRowOverflow(e),0<this.rows.length&&(this.rows.push(""),this.rowIdx++);this.rows.length>this.virtualRowCount;)this.rows.shift(),this.rowIdx--},ot.prototype.isEmpty=function(){return 0===this.rows.length||1===this.rows.length&&""===this.rows[0]},ot.prototype.addText=function(e){this.rows[this.rowIdx]+=e},ot.prototype.backspace=function(){var e;this.isEmpty()||(e=this.rows[this.rowIdx],this.rows[this.rowIdx]=e.substr(0,e.length-1))};function st(e,t,n){this.serviceNum=e,this.text="",this.currentWindow=new ot(-1),this.windows=[],this.stream=n,"string"==typeof t&&this.createTextDecoder(t)}st.prototype.init=function(e,t){this.startPts=e;for(var n=0;n<8;n++)this.windows[n]=new ot(n),"function"==typeof t&&(this.windows[n].beforeRowOverflow=t)},st.prototype.setCurrentWindow=function(e){this.currentWindow=this.windows[e]},st.prototype.createTextDecoder=function(t){if("undefined"==typeof TextDecoder)this.stream.trigger("log",{level:"warn",message:"The `encoding` option is unsupported without TextDecoder support"});else try{this.textDecoder_=new TextDecoder(t)}catch(e){this.stream.trigger("log",{level:"warn",message:"TextDecoder could not be created with "+t+" encoding. "+e})}};var ut=function e(t){t=t||{},e.prototype.init.call(this);var n,r=this,i=t.captionServices||{},o={};Object.keys(i).forEach(function(e){n=i[e],/^SERVICE/.test(e)&&(o[e]=n.encoding)}),this.serviceEncodings=o,this.current708Packet=null,this.services={},this.push=function(e){3!==e.type&&null!==r.current708Packet||r.new708Packet(),r.add708Bytes(e)}};ut.prototype=new u,ut.prototype.new708Packet=function(){null!==this.current708Packet&&this.push708Packet(),this.current708Packet={data:[],ptsVals:[]}},ut.prototype.add708Bytes=function(e){var t=e.ccData,n=t>>>8,t=255&t;this.current708Packet.ptsVals.push(e.pts),this.current708Packet.data.push(n),this.current708Packet.data.push(t)},ut.prototype.push708Packet=function(){var e,t=this.current708Packet,n=t.data,r=null,i=0,o=n[i++];for(t.seq=o>>6,t.sizeCode=63&o;i<n.length;i++)e=31&(o=n[i++]),7==(r=o>>5)&&0<e&&(r=n[i++]),this.pushServiceBlock(r,i,e),0<e&&(i+=e-1)},ut.prototype.pushServiceBlock=function(e,t,n){for(var r,i=t,o=this.current708Packet.data,a=(a=this.services[e])||this.initService(e,i);i<t+n&&i<o.length;i++)r=o[i],it(r)?i=this.handleText(i,a):24===r?i=this.multiByteCharacter(i,a):16===r?i=this.extendedCommands(i,a):128<=r&&r<=135?i=this.setCurrentWindow(i,a):152<=r&&r<=159?i=this.defineWindow(i,a):136===r?i=this.clearWindows(i,a):140===r?i=this.deleteWindows(i,a):137===r?i=this.displayWindows(i,a):138===r?i=this.hideWindows(i,a):139===r?i=this.toggleWindows(i,a):151===r?i=this.setWindowAttributes(i,a):144===r?i=this.setPenAttributes(i,a):145===r?i=this.setPenColor(i,a):146===r?i=this.setPenLocation(i,a):143===r?a=this.reset(i,a):8===r?a.currentWindow.backspace():12===r?a.currentWindow.clearText():13===r?a.currentWindow.pendingNewLine=!0:14===r?a.currentWindow.clearText():141===r&&i++},ut.prototype.extendedCommands=function(e,t){var n=this.current708Packet.data[++e];return it(n)&&(e=this.handleText(e,t,{isExtended:!0})),e},ut.prototype.getPts=function(e){return this.current708Packet.ptsVals[Math.floor(e/2)]},ut.prototype.initService=function(t,e){var n,r,i=this;return(n="SERVICE"+t)in this.serviceEncodings&&(r=this.serviceEncodings[n]),this.services[t]=new st(t,r,i),this.services[t].init(this.getPts(e),function(e){i.flushDisplayed(e,i.services[t])}),this.services[t]},ut.prototype.handleText=function(e,t,n){var r,i,o=n&&n.isExtended,a=n&&n.isMultiByte,s=this.current708Packet.data,u=o?4096:0,l=s[e],n=s[e+1],s=t.currentWindow;return a?(r=[l,n],e++):r=[l],i=t.textDecoder_&&!o?t.textDecoder_.decode(new Uint8Array(r)):a?(r=r.map(function(e){return("0"+(255&e).toString(16)).slice(-2)}).join(""),String.fromCharCode(parseInt(r,16))):(i=at[l=u|l]||l,4096&l&&l===i?"":String.fromCharCode(i)),s.pendingNewLine&&!s.isEmpty()&&s.newLine(this.getPts(e)),s.pendingNewLine=!1,s.addText(i),e},ut.prototype.multiByteCharacter=function(e,t){var n=this.current708Packet.data,r=n[e+1],n=n[e+2];return it(r)&&it(n)&&(e=this.handleText(++e,t,{isMultiByte:!0})),e},ut.prototype.setCurrentWindow=function(e,t){var n=7&this.current708Packet.data[e];return t.setCurrentWindow(n),e},ut.prototype.defineWindow=function(e,t){var n=this.current708Packet.data,r=7&(i=n[e]);t.setCurrentWindow(r);var t=t.currentWindow,i=n[++e];return t.visible=(32&i)>>5,t.rowLock=(16&i)>>4,t.columnLock=(8&i)>>3,t.priority=7&i,i=n[++e],t.relativePositioning=(128&i)>>7,t.anchorVertical=127&i,i=n[++e],t.anchorHorizontal=i,i=n[++e],t.anchorPoint=(240&i)>>4,t.rowCount=15&i,i=n[++e],t.columnCount=63&i,i=n[++e],t.windowStyle=(56&i)>>3,t.penStyle=7&i,t.virtualRowCount=t.rowCount+1,e},ut.prototype.setWindowAttributes=function(e,t){var n=this.current708Packet.data,r=n[e],t=t.currentWindow.winAttr,r=n[++e];return t.fillOpacity=(192&r)>>6,t.fillRed=(48&r)>>4,t.fillGreen=(12&r)>>2,t.fillBlue=3&r,r=n[++e],t.borderType=(192&r)>>6,t.borderRed=(48&r)>>4,t.borderGreen=(12&r)>>2,t.borderBlue=3&r,r=n[++e],t.borderType+=(128&r)>>5,t.wordWrap=(64&r)>>6,t.printDirection=(48&r)>>4,t.scrollDirection=(12&r)>>2,t.justify=3&r,r=n[++e],t.effectSpeed=(240&r)>>4,t.effectDirection=(12&r)>>2,t.displayEffect=3&r,e},ut.prototype.flushDisplayed=function(e,t){for(var n=[],r=0;r<8;r++)t.windows[r].visible&&!t.windows[r].isEmpty()&&n.push(t.windows[r].getText());t.endPts=e,t.text=n.join("\n\n"),this.pushCaption(t),t.startPts=e},ut.prototype.pushCaption=function(e){""!==e.text&&(this.trigger("data",{startPts:e.startPts,endPts:e.endPts,text:e.text,stream:"cc708_"+e.serviceNum}),e.text="",e.startPts=e.endPts)},ut.prototype.displayWindows=function(e,t){var n=this.current708Packet.data[++e],r=this.getPts(e);this.flushDisplayed(r,t);for(var i=0;i<8;i++)n&1<<i&&(t.windows[i].visible=1);return e},ut.prototype.hideWindows=function(e,t){var n=this.current708Packet.data[++e],r=this.getPts(e);this.flushDisplayed(r,t);for(var i=0;i<8;i++)n&1<<i&&(t.windows[i].visible=0);return e},ut.prototype.toggleWindows=function(e,t){var n=this.current708Packet.data[++e],r=this.getPts(e);this.flushDisplayed(r,t);for(var i=0;i<8;i++)n&1<<i&&(t.windows[i].visible^=1);return e},ut.prototype.clearWindows=function(e,t){var n=this.current708Packet.data[++e],r=this.getPts(e);this.flushDisplayed(r,t);for(var i=0;i<8;i++)n&1<<i&&t.windows[i].clearText();return e},ut.prototype.deleteWindows=function(e,t){var n=this.current708Packet.data[++e],r=this.getPts(e);this.flushDisplayed(r,t);for(var i=0;i<8;i++)n&1<<i&&t.windows[i].reset();return e},ut.prototype.setPenAttributes=function(e,t){var n=this.current708Packet.data,r=n[e],t=t.currentWindow.penAttr,r=n[++e];return t.textTag=(240&r)>>4,t.offset=(12&r)>>2,t.penSize=3&r,r=n[++e],t.italics=(128&r)>>7,t.underline=(64&r)>>6,t.edgeType=(56&r)>>3,t.fontStyle=7&r,e},ut.prototype.setPenColor=function(e,t){var n=this.current708Packet.data,r=n[e],t=t.currentWindow.penColor,r=n[++e];return t.fgOpacity=(192&r)>>6,t.fgRed=(48&r)>>4,t.fgGreen=(12&r)>>2,t.fgBlue=3&r,r=n[++e],t.bgOpacity=(192&r)>>6,t.bgRed=(48&r)>>4,t.bgGreen=(12&r)>>2,t.bgBlue=3&r,r=n[++e],t.edgeRed=(48&r)>>4,t.edgeGreen=(12&r)>>2,t.edgeBlue=3&r,e},ut.prototype.setPenLocation=function(e,t){var n=this.current708Packet.data,r=n[e],i=t.currentWindow.penLoc;return t.currentWindow.pendingNewLine=!0,r=n[++e],i.row=15&r,r=n[++e],i.column=63&r,e},ut.prototype.reset=function(e,t){var n=this.getPts(e);return this.flushDisplayed(n,t),this.initService(t.serviceNum,e)};function lt(e){return null===e?"":(e=dt[e]||e,String.fromCharCode(e))}function ct(){for(var e=[],t=15;t--;)e.push({text:"",indent:0,offset:0});return e}var dt={42:225,92:233,94:237,95:243,96:250,123:231,124:247,125:209,126:241,127:9608,304:174,305:176,306:189,307:191,308:8482,309:162,310:163,311:9834,312:224,313:160,314:232,315:226,316:234,317:238,318:244,319:251,544:193,545:201,546:211,547:218,548:220,549:252,550:8216,551:161,552:42,553:39,554:8212,555:169,556:8480,557:8226,558:8220,559:8221,560:192,561:194,562:199,563:200,564:202,565:203,566:235,567:206,568:207,569:239,570:212,571:217,572:249,573:219,574:171,575:187,800:195,801:227,802:205,803:204,804:236,805:210,806:242,807:213,808:245,809:123,810:125,811:92,812:94,813:95,814:124,815:126,816:196,817:228,818:214,819:246,820:223,821:165,822:164,823:9474,824:197,825:229,826:216,827:248,828:9484,829:9488,830:9492,831:9496},ft=[4352,4384,4608,4640,5376,5408,5632,5664,5888,5920,4096,4864,4896,5120,5152],ht=function e(t,n){e.prototype.init.call(this),this.field_=t||0,this.dataChannel_=n||0,this.name_="CC"+(1+(this.field_<<1|this.dataChannel_)),this.setConstants(),this.reset(),this.push=function(e){var t,n,r,i,o,a;(a=32639&e.ccData)!==this.lastControlCode_?(4096==(61440&a)?this.lastControlCode_=a:a!==this.PADDING_&&(this.lastControlCode_=null),n=a>>>8,r=255&a,a!==this.PADDING_&&(a===this.RESUME_CAPTION_LOADING_?this.mode_="popOn":a===this.END_OF_CAPTION_?(this.mode_="popOn",this.clearFormatting(e.pts),this.flushDisplayed(e.pts),t=this.displayed_,this.displayed_=this.nonDisplayed_,this.nonDisplayed_=t,this.startPts_=e.pts):a===this.ROLL_UP_2_ROWS_?(this.rollUpRows_=2,this.setRollUp(e.pts)):a===this.ROLL_UP_3_ROWS_?(this.rollUpRows_=3,this.setRollUp(e.pts)):a===this.ROLL_UP_4_ROWS_?(this.rollUpRows_=4,this.setRollUp(e.pts)):a===this.CARRIAGE_RETURN_?(this.clearFormatting(e.pts),this.flushDisplayed(e.pts),this.shiftRowsUp_(),this.startPts_=e.pts):a===this.BACKSPACE_?"popOn"===this.mode_?this.nonDisplayed_[this.row_].text=this.nonDisplayed_[this.row_].text.slice(0,-1):this.displayed_[this.row_].text=this.displayed_[this.row_].text.slice(0,-1):a===this.ERASE_DISPLAYED_MEMORY_?(this.flushDisplayed(e.pts),this.displayed_=ct()):a===this.ERASE_NON_DISPLAYED_MEMORY_?this.nonDisplayed_=ct():a===this.RESUME_DIRECT_CAPTIONING_?("paintOn"!==this.mode_&&(this.flushDisplayed(e.pts),this.displayed_=ct()),this.mode_="paintOn",this.startPts_=e.pts):this.isSpecialCharacter(n,r)?(i=lt((n=(3&n)<<8)|r),this[this.mode_](e.pts,i),this.column_++):this.isExtCharacter(n,r)?("popOn"===this.mode_?this.nonDisplayed_[this.row_].text=this.nonDisplayed_[this.row_].text.slice(0,-1):this.displayed_[this.row_].text=this.displayed_[this.row_].text.slice(0,-1),i=lt((n=(3&n)<<8)|r),this[this.mode_](e.pts,i),this.column_++):this.isMidRowCode(n,r)?(this.clearFormatting(e.pts),this[this.mode_](e.pts," "),this.column_++,14==(14&r)&&this.addFormatting(e.pts,["i"]),1==(1&r)&&this.addFormatting(e.pts,["u"])):this.isOffsetControlCode(n,r)?(o=3&r,this.nonDisplayed_[this.row_].offset=o,this.column_+=o):this.isPAC(n,r)?(o=ft.indexOf(7968&a),"rollUp"===this.mode_&&(o-this.rollUpRows_+1<0&&(o=this.rollUpRows_-1),this.setRollUp(e.pts,o)),o!==this.row_&&0<=o&&o<=14&&(this.clearFormatting(e.pts),this.row_=o),1&r&&-1===this.formatting_.indexOf("u")&&this.addFormatting(e.pts,["u"]),16==(16&a)&&(a=(14&a)>>1,this.column_=4*a,this.nonDisplayed_[this.row_].indent+=a),this.isColorPAC(r)&&14==(14&r)&&this.addFormatting(e.pts,["i"])):this.isNormalChar(n)&&(0===r&&(r=null),i=lt(n),i+=lt(r),this[this.mode_](e.pts,i),this.column_+=i.length))):this.lastControlCode_=null}};ht.prototype=new u,ht.prototype.flushDisplayed=function(e){function n(e){t.trigger("log",{level:"warn",message:"Skipping a malformed 608 caption at index "+e+"."})}var t=this,r=[];this.displayed_.forEach(function(e,t){if(e&&e.text&&e.text.length){try{e.text=e.text.trim()}catch(e){n(t)}e.text.length&&r.push({text:e.text,line:t+1,position:10+Math.min(70,10*e.indent)+2.5*e.offset})}else null==e&&n(t)}),r.length&&this.trigger("data",{startPts:this.startPts_,endPts:e,content:r,stream:this.name_})},ht.prototype.reset=function(){this.mode_="popOn",this.topRow_=0,this.startPts_=0,this.displayed_=ct(),this.nonDisplayed_=ct(),this.lastControlCode_=null,this.column_=0,this.row_=14,this.rollUpRows_=2,this.formatting_=[]},ht.prototype.setConstants=function(){0===this.dataChannel_?(this.BASE_=16,this.EXT_=17,this.CONTROL_=(20|this.field_)<<8,this.OFFSET_=23):1===this.dataChannel_&&(this.BASE_=24,this.EXT_=25,this.CONTROL_=(28|this.field_)<<8,this.OFFSET_=31),this.PADDING_=0,this.RESUME_CAPTION_LOADING_=32|this.CONTROL_,this.END_OF_CAPTION_=47|this.CONTROL_,this.ROLL_UP_2_ROWS_=37|this.CONTROL_,this.ROLL_UP_3_ROWS_=38|this.CONTROL_,this.ROLL_UP_4_ROWS_=39|this.CONTROL_,this.CARRIAGE_RETURN_=45|this.CONTROL_,this.RESUME_DIRECT_CAPTIONING_=41|this.CONTROL_,this.BACKSPACE_=33|this.CONTROL_,this.ERASE_DISPLAYED_MEMORY_=44|this.CONTROL_,this.ERASE_NON_DISPLAYED_MEMORY_=46|this.CONTROL_},ht.prototype.isSpecialCharacter=function(e,t){return e===this.EXT_&&48<=t&&t<=63},ht.prototype.isExtCharacter=function(e,t){return(e===this.EXT_+1||e===this.EXT_+2)&&32<=t&&t<=63},ht.prototype.isMidRowCode=function(e,t){return e===this.EXT_&&32<=t&&t<=47},ht.prototype.isOffsetControlCode=function(e,t){return e===this.OFFSET_&&33<=t&&t<=35},ht.prototype.isPAC=function(e,t){return e>=this.BASE_&&e<this.BASE_+8&&64<=t&&t<=127},ht.prototype.isColorPAC=function(e){return 64<=e&&e<=79||96<=e&&e<=127},ht.prototype.isNormalChar=function(e){return 32<=e&&e<=127},ht.prototype.setRollUp=function(e,t){if("rollUp"!==this.mode_&&(this.row_=14,this.mode_="rollUp",this.flushDisplayed(e),this.nonDisplayed_=ct(),this.displayed_=ct()),void 0!==t&&t!==this.row_)for(var n=0;n<this.rollUpRows_;n++)this.displayed_[t-n]=this.displayed_[this.row_-n],this.displayed_[this.row_-n]={text:"",indent:0,offset:0};void 0===t&&(t=this.row_),this.topRow_=t-this.rollUpRows_+1},ht.prototype.addFormatting=function(e,t){this.formatting_=this.formatting_.concat(t);t=t.reduce(function(e,t){return e+"<"+t+">"},"");this[this.mode_](e,t)},ht.prototype.clearFormatting=function(e){var t;this.formatting_.length&&(t=this.formatting_.reverse().reduce(function(e,t){return e+"</"+t+">"},""),this.formatting_=[],this[this.mode_](e,t))},ht.prototype.popOn=function(e,t){var n=this.nonDisplayed_[this.row_].text;n+=t,this.nonDisplayed_[this.row_].text=n},ht.prototype.rollUp=function(e,t){var n=this.displayed_[this.row_].text;n+=t,this.displayed_[this.row_].text=n},ht.prototype.shiftRowsUp_=function(){for(var e=0;e<this.topRow_;e++)this.displayed_[e]={text:"",indent:0,offset:0};for(e=this.row_+1;e<15;e++)this.displayed_[e]={text:"",indent:0,offset:0};for(e=this.topRow_;e<this.row_;e++)this.displayed_[e]=this.displayed_[e+1];this.displayed_[this.row_]={text:"",indent:0,offset:0}},ht.prototype.paintOn=function(e,t){var n=this.displayed_[this.row_].text;n+=t,this.displayed_[this.row_].text=n};function pt(e,t){var n=1;for(t<e&&(n=-1);4294967296<Math.abs(t-e);)e+=8589934592*n;return e}var d={CaptionStream:c,Cea608Stream:ht,Cea708Stream:ut},gt={H264_STREAM_TYPE:27,ADTS_STREAM_TYPE:15,METADATA_STREAM_TYPE:21},h=function e(t){var n,r;e.prototype.init.call(this),this.type_=t||"shared",this.push=function(e){"metadata"!==e.type?"shared"!==this.type_&&e.type!==this.type_||(void 0===r&&(r=e.dts),e.dts=pt(e.dts,r),e.pts=pt(e.pts,r),n=e.dts,this.trigger("data",e)):this.trigger("data",e)},this.flush=function(){r=n,this.trigger("done")},this.endTimeline=function(){this.flush(),this.trigger("endedtimeline")},this.discontinuity=function(){n=r=void 0},this.reset=function(){this.discontinuity(),this.trigger("reset")}};h.prototype=new u;var mt,yt=h,n=pt;(mt=function(e){var t,n={descriptor:e&&e.descriptor},u=0,l=[],c=0;if(mt.prototype.init.call(this),this.dispatchType=gt.METADATA_STREAM_TYPE.toString(16),n.descriptor)for(t=0;t<n.descriptor.length;t++)this.dispatchType+=("00"+n.descriptor[t].toString(16)).slice(-2);this.push=function(e){var t,n,r,i,o,a,s;if("timed-metadata"===e.type)if(e.dataAlignmentIndicator&&(c=0,l.length=0),0===l.length&&(e.data.length<10||e.data[0]!=="I".charCodeAt(0)||e.data[1]!=="D".charCodeAt(0)||e.data[2]!=="3".charCodeAt(0)))this.trigger("log",{level:"warn",message:"Skipping unrecognized metadata packet"});else if(l.push(e),c+=e.data.byteLength,1===l.length&&(u=Je.parseSyncSafeInteger(e.data.subarray(6,10)),u+=10),!(c<u)){for(t={data:new Uint8Array(u),frames:[],pts:l[0].pts,dts:l[0].dts},o=0;o<u;)t.data.set(l[0].data.subarray(0,u-o),o),o+=l[0].data.byteLength,c-=l[0].data.byteLength,l.shift();n=10,64&t.data[5]&&(n+=4,n+=Je.parseSyncSafeInteger(t.data.subarray(10,14)),u-=Je.parseSyncSafeInteger(t.data.subarray(16,20)));do{if((r=Je.parseSyncSafeInteger(t.data.subarray(n+4,n+8)))<1){this.trigger("log",{level:"warn",message:"Malformed ID3 frame encountered. Skipping remaining metadata parsing."});break}}while((i={id:String.fromCharCode(t.data[n],t.data[n+1],t.data[n+2],t.data[n+3]),data:t.data.subarray(n+10,n+r+10)}).key=i.id,Je.frameParsers[i.id]?Je.frameParsers[i.id](i):"T"===i.id[0]?Je.frameParsers["T*"](i):"W"===i.id[0]&&Je.frameParsers["W*"](i),"com.apple.streaming.transportStreamTimestamp"===i.owner&&(s=(1&(a=i.data)[3])<<30|a[4]<<22|a[5]<<14|a[6]<<6|a[7]>>>2,s*=4,s+=3&a[7],i.timeStamp=s,void 0===t.pts&&void 0===t.dts&&(t.pts=i.timeStamp,t.dts=i.timeStamp),this.trigger("timestamp",i)),t.frames.push(i),n+=10,(n+=r)<u);this.trigger("data",t)}}}).prototype=new u;var vt,_t,bt,c=mt,h=yt;(vt=function(){var i=new Uint8Array(188),o=0;vt.prototype.init.call(this),this.push=function(e){var t,n=0,r=188;for(o?((t=new Uint8Array(e.byteLength+o)).set(i.subarray(0,o)),t.set(e,o),o=0):t=e;r<t.byteLength;)71!==t[n]||71!==t[r]?(n++,r++):(this.trigger("data",t.subarray(n,r)),n+=188,r+=188);n<t.byteLength&&(i.set(t.subarray(n),0),o=t.byteLength-n)},this.flush=function(){188===o&&71===i[0]&&(this.trigger("data",i),o=0),this.trigger("done")},this.endTimeline=function(){this.flush(),this.trigger("endedtimeline")},this.reset=function(){o=0,this.trigger("reset")}}).prototype=new u,(_t=function(){var r,i,o,a;_t.prototype.init.call(this),(a=this).packetsWaitingForPmt=[],this.programMapTable=void 0,r=function(e,t){var n=0;t.payloadUnitStartIndicator&&(n+=e[n]+1),("pat"===t.type?i:o)(e.subarray(n),t)},i=function(e,t){t.section_number=e[7],t.last_section_number=e[8],a.pmtPid=(31&e[10])<<8|e[11],t.pmtPid=a.pmtPid},o=function(e,t){var n,r;if(1&e[5]){for(a.programMapTable={video:null,audio:null,"timed-metadata":{}},n=3+((15&e[1])<<8|e[2])-4,r=12+((15&e[10])<<8|e[11]);r<n;){var i=e[r],o=(31&e[r+1])<<8|e[r+2];i===gt.H264_STREAM_TYPE&&null===a.programMapTable.video?a.programMapTable.video=o:i===gt.ADTS_STREAM_TYPE&&null===a.programMapTable.audio?a.programMapTable.audio=o:i===gt.METADATA_STREAM_TYPE&&(a.programMapTable["timed-metadata"][o]=i),r+=5+((15&e[r+3])<<8|e[r+4])}t.programMapTable=a.programMapTable}},this.push=function(e){var t={},n=4;if(t.payloadUnitStartIndicator=!!(64&e[1]),t.pid=31&e[1],t.pid<<=8,t.pid|=e[2],1<(48&e[3])>>>4&&(n+=e[n]+1),0===t.pid)t.type="pat",r(e.subarray(n),t),this.trigger("data",t);else if(t.pid===this.pmtPid)for(t.type="pmt",r(e.subarray(n),t),this.trigger("data",t);this.packetsWaitingForPmt.length;)this.processPes_.apply(this,this.packetsWaitingForPmt.shift());else void 0===this.programMapTable?this.packetsWaitingForPmt.push([e,n,t]):this.processPes_(e,n,t)},this.processPes_=function(e,t,n){n.pid===this.programMapTable.video?n.streamType=gt.H264_STREAM_TYPE:n.pid===this.programMapTable.audio?n.streamType=gt.ADTS_STREAM_TYPE:n.streamType=this.programMapTable["timed-metadata"][n.pid],n.type="pes",n.data=e.subarray(t),this.trigger("data",n)}}).prototype=new u,_t.STREAM_TYPES={h264:27,adts:15},(bt=function(){function r(e,t,n){var r,i=new Uint8Array(e.size),o={type:t},a=0,s=0;if(e.data.length&&!(e.size<9)){for(o.trackId=e.data[0].pid,a=0;a<e.data.length;a++)r=e.data[a],i.set(r.data,s),s+=r.data.byteLength;var u,l=o,c=(u=i)[0]<<16|u[1]<<8|u[2];l.data=new Uint8Array,1==c&&(l.packetLength=6+(u[4]<<8|u[5]),l.dataAlignmentIndicator=0!=(4&u[6]),192&(c=u[7])&&(l.pts=(14&u[9])<<27|(255&u[10])<<20|(254&u[11])<<12|(255&u[12])<<5|(254&u[13])>>>3,l.pts*=4,l.pts+=(6&u[13])>>>1,l.dts=l.pts,64&c&&(l.dts=(14&u[14])<<27|(255&u[15])<<20|(254&u[16])<<12|(255&u[17])<<5|(254&u[18])>>>3,l.dts*=4,l.dts+=(6&u[18])>>>1)),l.data=u.subarray(9+u[8])),t="video"===t||o.packetLength<=e.size,(n||t)&&(e.size=0,e.data.length=0),t&&d.trigger("data",o)}}var t,d=this,i=!1,o={data:[],size:0},a={data:[],size:0},s={data:[],size:0};bt.prototype.init.call(this),this.push=function(n){({pat:function(){},pes:function(){var e,t;switch(n.streamType){case gt.H264_STREAM_TYPE:e=o,t="video";break;case gt.ADTS_STREAM_TYPE:e=a,t="audio";break;case gt.METADATA_STREAM_TYPE:e=s,t="timed-metadata";break;default:return}n.payloadUnitStartIndicator&&r(e,t,!0),e.data.push(n),e.size+=n.data.byteLength},pmt:function(){var e={type:"metadata",tracks:[]};null!==(t=n.programMapTable).video&&e.tracks.push({timelineStartInfo:{baseMediaDecodeTime:0},id:+t.video,codec:"avc",type:"video"}),null!==t.audio&&e.tracks.push({timelineStartInfo:{baseMediaDecodeTime:0},id:+t.audio,codec:"adts",type:"audio"}),i=!0,d.trigger("data",e)}})[n.type]()},this.reset=function(){o.size=0,o.data.length=0,a.size=0,a.data.length=0,this.trigger("reset")},this.flushStreams_=function(){r(o,"video"),r(a,"audio"),r(s,"timed-metadata")},this.flush=function(){var e;!i&&t&&(e={type:"metadata",tracks:[]},null!==t.video&&e.tracks.push({timelineStartInfo:{baseMediaDecodeTime:0},id:+t.video,codec:"avc",type:"video"}),null!==t.audio&&e.tracks.push({timelineStartInfo:{baseMediaDecodeTime:0},id:+t.audio,codec:"adts",type:"audio"}),d.trigger("data",e)),i=!1,this.flushStreams_(),this.trigger("done")}}).prototype=new u;var wt,xt={PAT_PID:0,MP2T_PACKET_LENGTH:188,TransportPacketStream:vt,TransportParseStream:_t,ElementaryStream:bt,TimestampRolloverStream:h,CaptionStream:d.CaptionStream,Cea608Stream:d.Cea608Stream,Cea708Stream:d.Cea708Stream,MetadataStream:c};for(wt in gt)gt.hasOwnProperty(wt)&&(xt[wt]=gt[wt]);function St(e,t){var n=0<=(n=e[t+6]<<21|e[t+7]<<14|e[t+8]<<7|e[t+9])?n:0;return(16&e[t+5])>>4?n+20:n+10}function Tt(e,t){return e.length-t<10||e[t]!=="I".charCodeAt(0)||e[t+1]!=="D".charCodeAt(0)||e[t+2]!=="3".charCodeAt(0)?t:Tt(e,t+=St(e,t))}function kt(e){return e[0]<<21|e[1]<<14|e[2]<<7|e[3]}var Et,Ct=xt,At=[96e3,88200,64e3,48e3,44100,32e3,24e3,22050,16e3,12e3,11025,8e3,7350],Dt={isLikelyAacData:function(e){var t=Tt(e,0);return e.length>=t+2&&255==(255&e[t])&&240==(240&e[t+1])&&16==(22&e[t+1])},parseId3TagSize:St,parseAdtsSize:function(e,t){var n=(224&e[t+5])>>5,r=e[t+4]<<3;return 6144&e[t+3]|r|n},parseType:function(e,t){return e[t]==="I".charCodeAt(0)&&e[t+1]==="D".charCodeAt(0)&&e[t+2]==="3".charCodeAt(0)?"timed-metadata":!0&e[t]&&240==(240&e[t+1])?"audio":null},parseSampleRate:function(e){for(var t=0;t+5<e.length;){if(255===e[t]&&240==(246&e[t+1]))return At[(60&e[t+2])>>>2];t++}return null},parseAacTimestamp:function(e){var t,n,r=10;64&e[5]&&(r+=4,r+=kt(e.subarray(10,14)));do{if((t=kt(e.subarray(r+4,r+8)))<1)return null;if("PRIV"===String.fromCharCode(e[r],e[r+1],e[r+2],e[r+3])){n=e.subarray(r+10,r+t+10);for(var i=0;i<n.byteLength;i++)if(0===n[i]){if("com.apple.streaming.transportStreamTimestamp"!==unescape(function(e,t){for(var n="",r=0;r<t;r++)n+="%"+("00"+e[r].toString(16)).slice(-2);return n}(n,i)))break;var o=n.subarray(i+1),a=(1&o[3])<<30|o[4]<<22|o[5]<<14|o[6]<<6|o[7]>>>2;return(a*=4)+(3&o[7])}}}while(r+=10,(r+=t)<e.byteLength);return null}};(Et=function(){var o=new Uint8Array,a=0;Et.prototype.init.call(this),this.setTimestamp=function(e){a=e},this.push=function(e){var t,n,r=0,i=0;for(o.length?(n=o.length,(o=new Uint8Array(e.byteLength+n)).set(o.subarray(0,n)),o.set(e,n)):o=e;3<=o.length-i;)if(o[i]!=="I".charCodeAt(0)||o[i+1]!=="D".charCodeAt(0)||o[i+2]!=="3".charCodeAt(0))if(255!=(255&o[i])||240!=(240&o[i+1]))i++;else{if(o.length-i<7)break;if(i+(r=Dt.parseAdtsSize(o,i))>o.length)break;t={type:"audio",data:o.subarray(i,i+r),pts:a,dts:a},this.trigger("data",t),i+=r}else{if(o.length-i<10)break;if(i+(r=Dt.parseId3TagSize(o,i))>o.length)break;t={type:"timed-metadata",data:o.subarray(i,i+r)},this.trigger("data",t),i+=r}e=o.length-i,o=0<e?o.subarray(i):new Uint8Array},this.reset=function(){o=new Uint8Array,this.trigger("reset")},this.endTimeline=function(){o=new Uint8Array,this.trigger("endedtimeline")}}).prototype=new u;function It(e,t){t.stream=e,this.trigger("log",t)}function Rt(e,t){for(var n=Object.keys(t),r=0;r<n.length;r++){var i=n[r];"headOfPipeline"!==i&&t[i].on&&t[i].on("log",It.bind(e,i))}}function Ot(e,t){var n;if(e.length===t.length){for(n=0;n<e.length;n++)if(e[n]!==t[n])return;return 1}}function Pt(e,t,n,r,i,o){return{start:{dts:e,pts:e+(n-t)},end:{dts:e+(r-t),pts:e+(i-n)},prependedContentDuration:o,baseMediaDecodeTime:e}}var Lt,Ft,Ut,Mt,Nt=Et,Bt=["audioobjecttype","channelcount","samplerate","samplingfrequencyindex","samplesize"],jt=["width","height","profileIdc","levelIdc","profileCompatibility","sarRatio"],qt=le.H264Stream,Gt=Dt.isLikelyAacData;(Ft=function(o,a){var s=[],u=0,l=0,c=1/0,d=(a=a||{}).firstSequenceNumber||0;Ft.prototype.init.call(this),this.push=function(t){Ge(o,t),o&&Bt.forEach(function(e){o[e]=t[e]}),s.push(t)},this.setEarliestDts=function(e){u=e},this.setVideoBaseMediaDecodeTime=function(e){c=e},this.setAudioAppendStart=function(e){l=e},this.flush=function(){var e,t,n,r,i;0!==s.length&&(e=Me(s,o,u),o.baseMediaDecodeTime=qe(o,a.keepOriginalTimestamps),i=Ue(o,e,l,c),o.samples=Ne(e),t=We.mdat(Be(e)),s=[],r=We.moof(d,[o]),n=new Uint8Array(r.byteLength+t.byteLength),d++,n.set(r),n.set(t,r.byteLength),je(o),r=Math.ceil(9216e4/o.samplerate),e.length&&(r=e.length*r,this.trigger("segmentTimingInfo",Pt(g(o.baseMediaDecodeTime,o.samplerate),e[0].dts,e[0].pts,e[0].dts+r,e[0].pts+r,i||0)),this.trigger("timingInfo",{start:e[0].pts,end:e[0].pts+r})),this.trigger("data",{track:o,boxes:n})),this.trigger("done","AudioSegmentStream")},this.reset=function(){je(o),s=[],this.trigger("reset")}}).prototype=new u,(Lt=function(a,o){var t,n,s=[],l=[],u=(o=o||{}).firstSequenceNumber||0;Lt.prototype.init.call(this),delete a.minPTS,this.gopCache_=[],this.push=function(e){Ge(a,e),"seq_parameter_set_rbsp"!==e.nalUnitType||t||(t=e.config,a.sps=[e.data],jt.forEach(function(e){a[e]=t[e]},this)),"pic_parameter_set_rbsp"!==e.nalUnitType||n||(n=e.data,a.pps=[e.data]),s.push(e)},this.flush=function(){for(var e,t,n,r,i=0;s.length&&"access_unit_delimiter_rbsp"!==s[0].nalUnitType;)s.shift();if(0===s.length)return this.resetStream_(),void this.trigger("done","VideoSegmentStream");if(e=Oe(s),(t=Pe(e))[0][0].keyFrame||((n=this.getGopForFusion_(s[0],a))?(i=n.duration,t.unshift(n),t.byteLength+=n.byteLength,t.nalCount+=n.nalCount,t.pts=n.pts,t.dts=n.dts,t.duration+=n.duration):t=Le(t)),l.length){if(!(r=o.alignGopsAtEnd?this.alignGopsAtEnd_(t):this.alignGopsAtStart_(t)))return this.gopCache_.unshift({gop:t.pop(),pps:a.pps,sps:a.sps}),this.gopCache_.length=Math.min(6,this.gopCache_.length),s=[],this.resetStream_(),void this.trigger("done","VideoSegmentStream");je(a),t=r}Ge(a,t),a.samples=function(e,t){for(var n,r,i,o=t||0,a=[],s=0;s<e.length;s++)for(r=e[s],n=0;n<r.length;n++)i=r[n],o+=(i=Re(i,o)).size,a.push(i);return a}(t),e=We.mdat(function(e){for(var t,n,r,i,o,a=0,s=e.byteLength,u=e.nalCount,l=new Uint8Array(s+4*u),c=new DataView(l.buffer),d=0;d<e.length;d++)for(r=e[d],t=0;t<r.length;t++)for(i=r[t],n=0;n<i.length;n++)o=i[n],c.setUint32(a,o.data.byteLength),a+=4,l.set(o.data,a),a+=o.data.byteLength;return l}(t)),a.baseMediaDecodeTime=qe(a,o.keepOriginalTimestamps),this.trigger("processedGopsInfo",t.map(function(e){return{pts:e.pts,dts:e.dts,byteLength:e.byteLength}})),n=t[0],r=t[t.length-1],this.trigger("segmentTimingInfo",Pt(a.baseMediaDecodeTime,n.dts,n.pts,r.dts+r.duration,r.pts+r.duration,i)),this.trigger("timingInfo",{start:t[0].pts,end:t[t.length-1].pts+t[t.length-1].duration}),this.gopCache_.unshift({gop:t.pop(),pps:a.pps,sps:a.sps}),this.gopCache_.length=Math.min(6,this.gopCache_.length),s=[],this.trigger("baseMediaDecodeTime",a.baseMediaDecodeTime),this.trigger("timelineStartInfo",a.timelineStartInfo),i=We.moof(u,[a]),t=new Uint8Array(i.byteLength+e.byteLength),u++,t.set(i),t.set(e,i.byteLength),this.trigger("data",{track:a,boxes:t}),this.resetStream_(),this.trigger("done","VideoSegmentStream")},this.reset=function(){this.resetStream_(),s=[],this.gopCache_.length=0,l.length=0,this.trigger("reset")},this.resetStream_=function(){je(a),n=t=void 0},this.getGopForFusion_=function(e){for(var t,n,r,i=1/0,o=0;o<this.gopCache_.length;o++)n=(r=this.gopCache_[o]).gop,a.pps&&Ot(a.pps[0],r.pps[0])&&a.sps&&Ot(a.sps[0],r.sps[0])&&(n.dts<a.timelineStartInfo.dts||-1e4<=(n=e.dts-n.dts-n.duration)&&n<=45e3&&(!t||n<i)&&(t=r,i=n));return t?t.gop:null},this.alignGopsAtStart_=function(e){for(var t,n,r,i,o=e.byteLength,a=e.nalCount,s=e.duration,u=t=0;u<l.length&&t<e.length&&(n=l[u],r=e[t],n.pts!==r.pts);)r.pts>n.pts?u++:(t++,o-=r.byteLength,a-=r.nalCount,s-=r.duration);return 0===t?e:t===e.length?null:((i=e.slice(t)).byteLength=o,i.duration=s,i.nalCount=a,i.pts=i[0].pts,i.dts=i[0].dts,i)},this.alignGopsAtEnd_=function(e){for(var t,n,r=l.length-1,i=e.length-1,o=null,a=!1;0<=r&&0<=i;){if(t=l[r],n=e[i],t.pts===n.pts){a=!0;break}t.pts>n.pts?r--:(r===l.length-1&&(o=i),i--)}if(!a&&null===o)return null;if(0===(u=a?i:o))return e;var s=e.slice(u),u=s.reduce(function(e,t){return e.byteLength+=t.byteLength,e.duration+=t.duration,e.nalCount+=t.nalCount,e},{byteLength:0,duration:0,nalCount:0});return s.byteLength=u.byteLength,s.duration=u.duration,s.nalCount=u.nalCount,s.pts=s[0].pts,s.dts=s[0].dts,s},this.alignGopsWith=function(e){l=e}}).prototype=new u,(Mt=function(e,t){this.numberOfTracks=0,this.metadataStream=t,void 0!==(e=e||{}).remux?this.remuxTracks=!!e.remux:this.remuxTracks=!0,"boolean"==typeof e.keepOriginalTimestamps?this.keepOriginalTimestamps=e.keepOriginalTimestamps:this.keepOriginalTimestamps=!1,this.pendingTracks=[],this.videoTrack=null,this.pendingBoxes=[],this.pendingCaptions=[],this.pendingMetadata=[],this.pendingBytes=0,this.emittedTracks=0,Mt.prototype.init.call(this),this.push=function(e){return e.content||e.text?this.pendingCaptions.push(e):e.frames?this.pendingMetadata.push(e):(this.pendingTracks.push(e.track),this.pendingBytes+=e.boxes.byteLength,"video"===e.track.type&&(this.videoTrack=e.track,this.pendingBoxes.push(e.boxes)),void("audio"===e.track.type&&(this.audioTrack=e.track,this.pendingBoxes.unshift(e.boxes))))}}).prototype=new u,Mt.prototype.flush=function(e){var t,n,r,i=0,o={captions:[],captionStreams:{},metadata:[],info:{}},a=0;if(this.pendingTracks.length<this.numberOfTracks){if("VideoSegmentStream"!==e&&"AudioSegmentStream"!==e)return;if(this.remuxTracks)return;if(0===this.pendingTracks.length)return this.emittedTracks++,void(this.emittedTracks>=this.numberOfTracks&&(this.trigger("done"),this.emittedTracks=0))}if(this.videoTrack?(a=this.videoTrack.timelineStartInfo.pts,jt.forEach(function(e){o.info[e]=this.videoTrack[e]},this)):this.audioTrack&&(a=this.audioTrack.timelineStartInfo.pts,Bt.forEach(function(e){o.info[e]=this.audioTrack[e]},this)),this.videoTrack||this.audioTrack){for(1===this.pendingTracks.length?o.type=this.pendingTracks[0].type:o.type="combined",this.emittedTracks+=this.pendingTracks.length,e=We.initSegment(this.pendingTracks),o.initSegment=new Uint8Array(e.byteLength),o.initSegment.set(e),o.data=new Uint8Array(this.pendingBytes),r=0;r<this.pendingBoxes.length;r++)o.data.set(this.pendingBoxes[r],i),i+=this.pendingBoxes[r].byteLength;for(r=0;r<this.pendingCaptions.length;r++)(t=this.pendingCaptions[r]).startTime=y(t.startPts,a,this.keepOriginalTimestamps),t.endTime=y(t.endPts,a,this.keepOriginalTimestamps),o.captionStreams[t.stream]=!0,o.captions.push(t);for(r=0;r<this.pendingMetadata.length;r++)(n=this.pendingMetadata[r]).cueTime=y(n.pts,a,this.keepOriginalTimestamps),o.metadata.push(n);for(o.metadata.dispatchType=this.metadataStream.dispatchType,this.pendingTracks.length=0,this.videoTrack=null,this.pendingBoxes.length=0,this.pendingCaptions.length=0,this.pendingBytes=0,this.pendingMetadata.length=0,this.trigger("data",o),r=0;r<o.captions.length;r++)t=o.captions[r],this.trigger("caption",t);for(r=0;r<o.metadata.length;r++)n=o.metadata[r],this.trigger("id3Frame",n)}this.emittedTracks>=this.numberOfTracks&&(this.trigger("done"),this.emittedTracks=0)},Mt.prototype.setRemux=function(e){this.remuxTracks=e},(Ut=function(r){var i,o,a=this,n=!0;Ut.prototype.init.call(this),r=r||{},this.baseMediaDecodeTime=r.baseMediaDecodeTime||0,this.transmuxPipeline_={},r.duration&&We.setDuration(r.duration),this.setupAacPipeline=function(){var t={};(this.transmuxPipeline_=t).type="aac",t.metadataStream=new Ct.MetadataStream,t.aacStream=new Nt,t.audioTimestampRolloverStream=new Ct.TimestampRolloverStream("audio"),t.timedMetadataTimestampRolloverStream=new Ct.TimestampRolloverStream("timed-metadata"),t.adtsStream=new S,t.coalesceStream=new Mt(r,t.metadataStream),t.headOfPipeline=t.aacStream,t.aacStream.pipe(t.audioTimestampRolloverStream).pipe(t.adtsStream),t.aacStream.pipe(t.timedMetadataTimestampRolloverStream).pipe(t.metadataStream).pipe(t.coalesceStream),t.metadataStream.on("timestamp",function(e){t.aacStream.setTimestamp(e.timeStamp)}),t.aacStream.on("data",function(e){"timed-metadata"!==e.type&&"audio"!==e.type||t.audioSegmentStream||(o=o||{timelineStartInfo:{baseMediaDecodeTime:a.baseMediaDecodeTime},codec:"adts",type:"audio"},t.coalesceStream.numberOfTracks++,t.audioSegmentStream=new Ft(o,r),t.audioSegmentStream.on("log",a.getLogTrigger_("audioSegmentStream")),t.audioSegmentStream.on("timingInfo",a.trigger.bind(a,"audioTimingInfo")),t.adtsStream.pipe(t.audioSegmentStream).pipe(t.coalesceStream),a.trigger("trackinfo",{hasAudio:!!o,hasVideo:!!i}))}),t.coalesceStream.on("data",this.trigger.bind(this,"data")),t.coalesceStream.on("done",this.trigger.bind(this,"done")),Rt(this,t)},this.setupTsPipeline=function(){var n={};(this.transmuxPipeline_=n).type="ts",n.metadataStream=new Ct.MetadataStream,n.packetStream=new Ct.TransportPacketStream,n.parseStream=new Ct.TransportParseStream,n.elementaryStream=new Ct.ElementaryStream,n.timestampRolloverStream=new Ct.TimestampRolloverStream,n.adtsStream=new S,n.h264Stream=new qt,n.captionStream=new Ct.CaptionStream(r),n.coalesceStream=new Mt(r,n.metadataStream),n.headOfPipeline=n.packetStream,n.packetStream.pipe(n.parseStream).pipe(n.elementaryStream).pipe(n.timestampRolloverStream),n.timestampRolloverStream.pipe(n.h264Stream),n.timestampRolloverStream.pipe(n.adtsStream),n.timestampRolloverStream.pipe(n.metadataStream).pipe(n.coalesceStream),n.h264Stream.pipe(n.captionStream).pipe(n.coalesceStream),n.elementaryStream.on("data",function(e){var t;if("metadata"===e.type){for(t=e.tracks.length;t--;)i||"video"!==e.tracks[t].type?o||"audio"!==e.tracks[t].type||((o=e.tracks[t]).timelineStartInfo.baseMediaDecodeTime=a.baseMediaDecodeTime):(i=e.tracks[t]).timelineStartInfo.baseMediaDecodeTime=a.baseMediaDecodeTime;i&&!n.videoSegmentStream&&(n.coalesceStream.numberOfTracks++,n.videoSegmentStream=new Lt(i,r),n.videoSegmentStream.on("log",a.getLogTrigger_("videoSegmentStream")),n.videoSegmentStream.on("timelineStartInfo",function(e){o&&!r.keepOriginalTimestamps&&(o.timelineStartInfo=e,n.audioSegmentStream.setEarliestDts(e.dts-a.baseMediaDecodeTime))}),n.videoSegmentStream.on("processedGopsInfo",a.trigger.bind(a,"gopInfo")),n.videoSegmentStream.on("segmentTimingInfo",a.trigger.bind(a,"videoSegmentTimingInfo")),n.videoSegmentStream.on("baseMediaDecodeTime",function(e){o&&n.audioSegmentStream.setVideoBaseMediaDecodeTime(e)}),n.videoSegmentStream.on("timingInfo",a.trigger.bind(a,"videoTimingInfo")),n.h264Stream.pipe(n.videoSegmentStream).pipe(n.coalesceStream)),o&&!n.audioSegmentStream&&(n.coalesceStream.numberOfTracks++,n.audioSegmentStream=new Ft(o,r),n.audioSegmentStream.on("log",a.getLogTrigger_("audioSegmentStream")),n.audioSegmentStream.on("timingInfo",a.trigger.bind(a,"audioTimingInfo")),n.audioSegmentStream.on("segmentTimingInfo",a.trigger.bind(a,"audioSegmentTimingInfo")),n.adtsStream.pipe(n.audioSegmentStream).pipe(n.coalesceStream)),a.trigger("trackinfo",{hasAudio:!!o,hasVideo:!!i})}}),n.coalesceStream.on("data",this.trigger.bind(this,"data")),n.coalesceStream.on("id3Frame",function(e){e.dispatchType=n.metadataStream.dispatchType,a.trigger("id3Frame",e)}),n.coalesceStream.on("caption",this.trigger.bind(this,"caption")),n.coalesceStream.on("done",this.trigger.bind(this,"done")),Rt(this,n)},this.setBaseMediaDecodeTime=function(e){var t=this.transmuxPipeline_;r.keepOriginalTimestamps||(this.baseMediaDecodeTime=e),o&&(o.timelineStartInfo.dts=void 0,o.timelineStartInfo.pts=void 0,je(o),t.audioTimestampRolloverStream&&t.audioTimestampRolloverStream.discontinuity()),i&&(t.videoSegmentStream&&(t.videoSegmentStream.gopCache_=[]),i.timelineStartInfo.dts=void 0,i.timelineStartInfo.pts=void 0,je(i),t.captionStream.reset()),t.timestampRolloverStream&&t.timestampRolloverStream.discontinuity()},this.setAudioAppendStart=function(e){o&&this.transmuxPipeline_.audioSegmentStream.setAudioAppendStart(e)},this.setRemux=function(e){var t=this.transmuxPipeline_;r.remux=e,t&&t.coalesceStream&&t.coalesceStream.setRemux(e)},this.alignGopsWith=function(e){i&&this.transmuxPipeline_.videoSegmentStream&&this.transmuxPipeline_.videoSegmentStream.alignGopsWith(e)},this.getLogTrigger_=function(t){var n=this;return function(e){e.stream=t,n.trigger("log",e)}},this.push=function(e){var t;n&&((t=Gt(e))&&"aac"!==this.transmuxPipeline_.type?this.setupAacPipeline():t||"ts"===this.transmuxPipeline_.type||this.setupTsPipeline(),n=!1),this.transmuxPipeline_.headOfPipeline.push(e)},this.flush=function(){n=!0,this.transmuxPipeline_.headOfPipeline.flush()},this.endTimeline=function(){this.transmuxPipeline_.headOfPipeline.endTimeline()},this.reset=function(){this.transmuxPipeline_.headOfPipeline&&this.transmuxPipeline_.headOfPipeline.reset()},this.resetCaptions=function(){this.transmuxPipeline_.captionStream&&this.transmuxPipeline_.captionStream.reset()}}).prototype=new u;function Ht(e,c){var n=ye(e,["moof","traf"]),e=ye(e,["mdat"]),d={},r=[];return e.forEach(function(e,t){t=n[t];r.push({mdat:e,traf:t})}),r.forEach(function(e){var t,n,r,i,o,a=e.mdat,s=e.traf,u=ye(s,["tfhd"]),l=we(u[0]),e=l.trackId,u=ye(s,["tfdt"]),u=0<u.length?Te(u[0]).baseMediaDecodeTime:0,s=ye(s,["trun"]);c===e&&0<s.length&&(s=function(e,t,n){for(var r,i,o=new DataView(e.buffer,e.byteOffset,e.byteLength),a={logs:[],seiNals:[]},s=0;s+4<e.length;s+=r)if(r=o.getUint32(s),s+=4,!(r<=0))switch(31&e[s]){case 6:var u=e.subarray(s+1,s+1+r),l=function(e,t){for(var n=e,r=0;r<t.length;r++){var i=t[r];if(n<i.size)return i;n-=i.size}return null}(s,t),u={nalUnitType:"sei_rbsp",size:r,data:u,escapedRBSP:Wt(u),trackId:n};if(l)u.pts=l.pts,u.dts=l.dts,i=l;else{if(!i){a.logs.push({level:"warn",message:"We've encountered a nal unit without data at "+s+" for trackId "+n+". See mux.js#223."});break}u.pts=i.pts,u.dts=i.dts}a.seiNals.push(u)}return a}(a,(t=u,n=l.defaultSampleDuration||0,r=l.defaultSampleSize||0,i=l.trackId,o=[],s.forEach(function(e){e=Se(e).samples;e.forEach(function(e){void 0===e.duration&&(e.duration=n),void 0===e.size&&(e.size=r),e.trackId=i,e.dts=t,void 0===e.compositionTimeOffset&&(e.compositionTimeOffset=0),"bigint"==typeof t?(e.pts=t+f.default.BigInt(e.compositionTimeOffset),t+=f.default.BigInt(e.duration)):(e.pts=t+e.compositionTimeOffset,t+=e.duration)}),o=o.concat(e)}),o),e),d[e]||(d[e]={seiNals:[],logs:[]}),d[e].seiNals=d[e].seiNals.concat(s.seiNals),d[e].logs=d[e].logs.concat(s.logs))}),d}var zt,yt={Transmuxer:Ut,VideoSegmentStream:Lt,AudioSegmentStream:Ft,AUDIO_PROPERTIES:Bt,VIDEO_PROPERTIES:jt,generateSegmentTimingInfo:Pt},Wt=function(e){for(var t,n,r=e.byteLength,i=[],o=1;o<r-2;)0===e[o]&&0===e[o+1]&&3===e[o+2]?(i.push(o+2),o+=2):o++;if(0===i.length)return e;t=r-i.length,n=new Uint8Array(t);for(var a=0,o=0;o<t;a++,o++)a===i[0]&&(a++,i.shift()),n[o]=e[a];return n},Vt=d.CaptionStream,h={generator:We,probe:l,Transmuxer:yt.Transmuxer,AudioSegmentStream:yt.AudioSegmentStream,VideoSegmentStream:yt.VideoSegmentStream,CaptionParser:function(){var t,o,a,s,u,n,r=!1;this.isInitialized=function(){return r},this.init=function(e){t=new Vt,r=!0,n=!!e&&e.isPartial,t.on("data",function(e){e.startTime=e.startPts/s,e.endTime=e.endPts/s,u.captions.push(e),u.captionStreams[e.stream]=!0}),t.on("log",function(e){u.logs.push(e)})},this.isNewInit=function(e,t){return!(e&&0===e.length||t&&"object"==_typeof(t)&&0===Object.keys(t).length||a===e[0]&&s===t[a])},this.parse=function(n,e,t){var r;if(!this.isInitialized())return null;if(!e||!t)return null;if(this.isNewInit(e,t))a=e[0],s=t[a];else if(null===a||!s)return o.push(n),null;for(;0<o.length;){var i=o.shift();this.parse(i,e,t)}return(r=function(e,t){if(null===e)return null;e=Ht(n,e)[e]||{};return{seiNals:e.seiNals,logs:e.logs,timescale:t}}(a,s))&&r.logs&&(u.logs=u.logs.concat(r.logs)),null!==r&&r.seiNals?(this.pushNals(r.seiNals),this.flushStream(),u):u.logs.length?{logs:u.logs,captions:[],captionStreams:[]}:null},this.pushNals=function(e){if(!this.isInitialized()||!e||0===e.length)return null;e.forEach(function(e){t.push(e)})},this.flushStream=function(){if(!this.isInitialized())return null;n?t.partialFlush():t.flush()},this.clearParsedCaptions=function(){u.captions=[],u.captionStreams={},u.logs=[]},this.resetCaptionStream=function(){if(!this.isInitialized())return null;t.reset()},this.clearAllCaptions=function(){this.clearParsedCaptions(),this.resetCaptionStream()},this.reset=function(){o=[],s=a=null,u?this.clearParsedCaptions():u={captions:[],captionStreams:{},logs:[]},this.resetCaptionStream()},this.reset()}};(zt=function(e,n){function i(e,t){(t=e.position+t)<e.bytes.byteLength||((t=new Uint8Array(2*t)).set(e.bytes.subarray(0,e.position),0),e.bytes=t,e.view=new DataView(e.bytes.buffer))}var t,r=0,o=16384,a=zt.widthBytes||new Uint8Array("width".length),s=zt.heightBytes||new Uint8Array("height".length),u=zt.videocodecidBytes||new Uint8Array("videocodecid".length);if(!zt.widthBytes){for(t=0;t<"width".length;t++)a[t]="width".charCodeAt(t);for(t=0;t<"height".length;t++)s[t]="height".charCodeAt(t);for(t=0;t<"videocodecid".length;t++)u[t]="videocodecid".charCodeAt(t);zt.widthBytes=a,zt.heightBytes=s,zt.videocodecidBytes=u}switch(this.keyFrame=!1,e){case zt.VIDEO_TAG:this.length=16,o*=6;break;case zt.AUDIO_TAG:this.length=13,this.keyFrame=!0;break;case zt.METADATA_TAG:this.length=29,this.keyFrame=!0;break;default:throw new Error("Unknown FLV tag type")}this.bytes=new Uint8Array(o),this.view=new DataView(this.bytes.buffer),this.bytes[0]=e,this.position=this.length,this.keyFrame=n,this.pts=0,this.dts=0,this.writeBytes=function(e,t,n){var r=t||0,t=r+(n=n||e.byteLength);i(this,n),this.bytes.set(e.subarray(r,t),this.position),this.position+=n,this.length=Math.max(this.length,this.position)},this.writeByte=function(e){i(this,1),this.bytes[this.position]=e,this.position++,this.length=Math.max(this.length,this.position)},this.writeShort=function(e){i(this,2),this.view.setUint16(this.position,e),this.position+=2,this.length=Math.max(this.length,this.position)},this.negIndex=function(e){return this.bytes[this.length-e]},this.nalUnitSize=function(){return 0===r?0:this.length-(r+4)},this.startNalUnit=function(){if(0<r)throw new Error("Attempted to create new NAL wihout closing the old one");r=this.length,this.length+=4,this.position=this.length},this.endNalUnit=function(e){var t,n;this.length===r+4?this.length-=4:0<r&&(t=r+4,n=this.length-t,this.position=r,this.view.setUint32(this.position,n),this.position=this.length,e&&e.push(this.bytes.subarray(t,t+n))),r=0},this.writeMetaDataDouble=function(e,t){var n;if(i(this,2+e.length+9),this.view.setUint16(this.position,e.length),this.position+=2,"width"===e)this.bytes.set(a,this.position),this.position+=5;else if("height"===e)this.bytes.set(s,this.position),this.position+=6;else if("videocodecid"===e)this.bytes.set(u,this.position),this.position+=12;else for(n=0;n<e.length;n++)this.bytes[this.position]=e.charCodeAt(n),this.position++;this.position++,this.view.setFloat64(this.position,t),this.position+=8,this.length=Math.max(this.length,this.position),++r},this.writeMetaDataBoolean=function(e,t){var n;for(i(this,2),this.view.setUint16(this.position,e.length),this.position+=2,n=0;n<e.length;n++)i(this,1),this.bytes[this.position]=e.charCodeAt(n),this.position++;i(this,2),this.view.setUint8(this.position,1),this.position++,this.view.setUint8(this.position,t?1:0),this.position++,this.length=Math.max(this.length,this.position),++r},this.finalize=function(){var e,t;switch(this.bytes[0]){case zt.VIDEO_TAG:this.bytes[11]=7|(this.keyFrame||n?16:32),this.bytes[12]=n?0:1,e=this.pts-this.dts,this.bytes[13]=(16711680&e)>>>16,this.bytes[14]=(65280&e)>>>8,this.bytes[15]=(255&e)>>>0;break;case zt.AUDIO_TAG:this.bytes[11]=175,this.bytes[12]=n?0:1;break;case zt.METADATA_TAG:this.position=11,this.view.setUint8(this.position,2),this.position++,this.view.setUint16(this.position,10),this.position+=2,this.bytes.set([111,110,77,101,116,97,68,97,116,97],this.position),this.position+=10,this.bytes[this.position]=8,this.position++,this.view.setUint32(this.position,r),this.position=this.length,this.bytes.set([0,0,9],this.position),this.position+=3,this.length=this.position}return t=this.length-11,this.bytes[1]=(16711680&t)>>>16,this.bytes[2]=(65280&t)>>>8,this.bytes[3]=(255&t)>>>0,this.bytes[4]=(16711680&this.dts)>>>16,this.bytes[5]=(65280&this.dts)>>>8,this.bytes[6]=(255&this.dts)>>>0,this.bytes[7]=(4278190080&this.dts)>>>24,this.bytes[8]=0,this.bytes[9]=0,this.bytes[10]=0,i(this,4),this.view.setUint32(this.length,this.length),this.length+=4,this.position+=4,this.bytes=this.bytes.subarray(0,this.length),this.frameTime=zt.frameTime(this.bytes),this}}).AUDIO_TAG=8,zt.VIDEO_TAG=9,zt.METADATA_TAG=18,zt.isAudioFrame=function(e){return zt.AUDIO_TAG===e[0]},zt.isVideoFrame=function(e){return zt.VIDEO_TAG===e[0]},zt.isMetaData=function(e){return zt.METADATA_TAG===e[0]},zt.isKeyFrame=function(e){return zt.isVideoFrame(e)?23===e[11]:!!zt.isAudioFrame(e)||!!zt.isMetaData(e)},zt.frameTime=function(e){var t=e[4]<<16;return t|=e[5]<<8,(t|=e[6]<<0)|e[7]<<24};var $t=zt,c=function e(t){this.numberOfTracks=0,this.metadataStream=t.metadataStream,this.videoTags=[],this.audioTags=[],this.videoTrack=null,this.audioTrack=null,this.pendingCaptions=[],this.pendingMetadata=[],this.pendingTracks=0,this.processedTracks=0,e.prototype.init.call(this),this.push=function(e){return e.content||e.text?this.pendingCaptions.push(e):e.frames?this.pendingMetadata.push(e):("video"===e.track.type&&(this.videoTrack=e.track,this.videoTags=e.tags,this.pendingTracks++),void("audio"===e.track.type&&(this.audioTrack=e.track,this.audioTags=e.tags,this.pendingTracks++)))}};(c.prototype=new u).flush=function(e){var t,n,r,i,o={tags:{},captions:[],captionStreams:{},metadata:[]};if(this.pendingTracks<this.numberOfTracks){if("VideoSegmentStream"!==e&&"AudioSegmentStream"!==e)return;if(0===this.pendingTracks&&(this.processedTracks++,this.processedTracks<this.numberOfTracks))return}if(this.processedTracks+=this.pendingTracks,this.pendingTracks=0,!(this.processedTracks<this.numberOfTracks)){for(this.videoTrack?i=this.videoTrack.timelineStartInfo.pts:this.audioTrack&&(i=this.audioTrack.timelineStartInfo.pts),o.tags.videoTags=this.videoTags,o.tags.audioTags=this.audioTags,r=0;r<this.pendingCaptions.length;r++)(n=this.pendingCaptions[r]).startTime=n.startPts-i,n.startTime/=9e4,n.endTime=n.endPts-i,n.endTime/=9e4,o.captionStreams[n.stream]=!0,o.captions.push(n);for(r=0;r<this.pendingMetadata.length;r++)(t=this.pendingMetadata[r]).cueTime=t.pts-i,t.cueTime/=9e4,o.metadata.push(t);o.metadata.dispatchType=this.metadataStream.dispatchType,this.videoTrack=null,this.audioTrack=null,this.videoTags=[],this.audioTags=[],this.pendingCaptions.length=0,this.pendingMetadata.length=0,this.pendingTracks=0,this.processedTracks=0,this.trigger("data",o),this.trigger("done")}};function Yt(){var e=this;this.list=[],this.push=function(e){this.list.push({bytes:e.bytes,dts:e.dts,pts:e.pts,keyFrame:e.keyFrame,metaDataTag:e.metaDataTag})},Object.defineProperty(this,"length",{get:function(){return e.list.length}})}var Xt,Qt,Kt,Jt=c,Zt=le.H264Stream,en=function(e,t){"number"==typeof t.pts&&(void 0===e.timelineStartInfo.pts?e.timelineStartInfo.pts=t.pts:e.timelineStartInfo.pts=Math.min(e.timelineStartInfo.pts,t.pts)),"number"==typeof t.dts&&(void 0===e.timelineStartInfo.dts?e.timelineStartInfo.dts=t.dts:e.timelineStartInfo.dts=Math.min(e.timelineStartInfo.dts,t.dts))},tn=function(e,t){var n=new $t($t.METADATA_TAG);return n.dts=t,n.pts=t,n.writeMetaDataDouble("videocodecid",7),n.writeMetaDataDouble("width",e.width),n.writeMetaDataDouble("height",e.height),n},nn=function(e,t){var n,r=new $t($t.VIDEO_TAG,!0);for(r.dts=t,r.pts=t,r.writeByte(1),r.writeByte(e.profileIdc),r.writeByte(e.profileCompatibility),r.writeByte(e.levelIdc),r.writeByte(255),r.writeByte(225),r.writeShort(e.sps[0].length),r.writeBytes(e.sps[0]),r.writeByte(e.pps.length),n=0;n<e.pps.length;++n)r.writeShort(e.pps[n].length),r.writeBytes(e.pps[n]);return r};(Kt=function(i){var o,a=[],s=[];Kt.prototype.init.call(this),this.push=function(e){en(i,e),i&&(i.audioobjecttype=e.audioobjecttype,i.channelcount=e.channelcount,i.samplerate=e.samplerate,i.samplingfrequencyindex=e.samplingfrequencyindex,i.samplesize=e.samplesize,i.extraData=i.audioobjecttype<<11|i.samplingfrequencyindex<<7|i.channelcount<<3),e.pts=Math.round(e.pts/90),e.dts=Math.round(e.dts/90),a.push(e)},this.flush=function(){var e,t,n,r=new Yt;if(0!==a.length){for(n=-1/0;a.length;)e=a.shift(),s.length&&e.pts>=s[0]&&(n=s.shift(),this.writeMetaDataTags(r,n)),(i.extraData!==o||1e3<=e.pts-n)&&(this.writeMetaDataTags(r,e.pts),o=i.extraData,n=e.pts),(t=new $t($t.AUDIO_TAG)).pts=e.pts,t.dts=e.dts,t.writeBytes(e.data),r.push(t.finalize());s.length=0,o=null,this.trigger("data",{track:i,tags:r.list}),this.trigger("done","AudioSegmentStream")}else this.trigger("done","AudioSegmentStream")},this.writeMetaDataTags=function(e,t){var n;(n=new $t($t.METADATA_TAG)).pts=t,n.dts=t,n.writeMetaDataDouble("audiocodecid",10),n.writeMetaDataBoolean("stereo",2===i.channelcount),n.writeMetaDataDouble("audiosamplerate",i.samplerate),n.writeMetaDataDouble("audiosamplesize",16),e.push(n.finalize()),(n=new $t($t.AUDIO_TAG,!0)).pts=t,n.dts=t,n.view.setUint16(n.position,i.extraData),n.position+=2,n.length=Math.max(n.length,n.position),e.push(n.finalize())},this.onVideoKeyFrame=function(e){s.push(e)}}).prototype=new u,(Qt=function(i){var o,a,n=[];Qt.prototype.init.call(this),this.finishFrame=function(e,t){var n,r;t&&(o&&i&&i.newMetadata&&(t.keyFrame||0===e.length)&&(n=tn(o,t.dts).finalize(),r=nn(i,t.dts).finalize(),n.metaDataTag=r.metaDataTag=!0,e.push(n),e.push(r),i.newMetadata=!1,this.trigger("keyframe",t.dts)),t.endNalUnit(),e.push(t.finalize()),a=null)},this.push=function(e){en(i,e),e.pts=Math.round(e.pts/90),e.dts=Math.round(e.dts/90),n.push(e)},this.flush=function(){for(var e,t=new Yt;n.length&&"access_unit_delimiter_rbsp"!==n[0].nalUnitType;)n.shift();if(0!==n.length){for(;n.length;)"seq_parameter_set_rbsp"===(e=n.shift()).nalUnitType?(i.newMetadata=!0,o=e.config,i.width=o.width,i.height=o.height,i.sps=[e.data],i.profileIdc=o.profileIdc,i.levelIdc=o.levelIdc,i.profileCompatibility=o.profileCompatibility,a.endNalUnit()):"pic_parameter_set_rbsp"===e.nalUnitType?(i.newMetadata=!0,i.pps=[e.data],a.endNalUnit()):"access_unit_delimiter_rbsp"===e.nalUnitType?(a&&this.finishFrame(t,a),(a=new $t($t.VIDEO_TAG)).pts=e.pts,a.dts=e.dts):("slice_layer_without_partitioning_rbsp_idr"===e.nalUnitType&&(a.keyFrame=!0),a.endNalUnit()),a.startNalUnit(),a.writeBytes(e.data);a&&this.finishFrame(t,a),this.trigger("data",{track:i,tags:t.list}),this.trigger("done","VideoSegmentStream")}else this.trigger("done","VideoSegmentStream")}}).prototype=new u,(Xt=function(e){var t,n,r,i,o,a,s,u,l,c,d,f,h=this;Xt.prototype.init.call(this),e=e||{},this.metadataStream=new Ct.MetadataStream,e.metadataStream=this.metadataStream,t=new Ct.TransportPacketStream,n=new Ct.TransportParseStream,r=new Ct.ElementaryStream,i=new Ct.TimestampRolloverStream("video"),o=new Ct.TimestampRolloverStream("audio"),a=new Ct.TimestampRolloverStream("timed-metadata"),s=new S,u=new Zt,f=new Jt(e),t.pipe(n).pipe(r),r.pipe(i).pipe(u),r.pipe(o).pipe(s),r.pipe(a).pipe(this.metadataStream).pipe(f),d=new Ct.CaptionStream(e),u.pipe(d).pipe(f),r.on("data",function(e){var t,n,r;if("metadata"===e.type){for(t=e.tracks.length;t--;)"video"===e.tracks[t].type?n=e.tracks[t]:"audio"===e.tracks[t].type&&(r=e.tracks[t]);n&&!l&&(f.numberOfTracks++,l=new Qt(n),u.pipe(l).pipe(f)),r&&!c&&(f.numberOfTracks++,c=new Kt(r),s.pipe(c).pipe(f),l&&l.on("keyframe",c.onVideoKeyFrame))}}),this.push=function(e){t.push(e)},this.flush=function(){t.flush()},this.resetCaptions=function(){d.reset()},f.on("data",function(e){h.trigger("data",e)}),f.on("done",function(){h.trigger("done")})}).prototype=new u;d={tag:$t,Transmuxer:Xt,getFlvHeader:function(e,t,n){var r,i=new Uint8Array(9),o=new DataView(i.buffer);return e=e||0,t=void 0===t||t,n=void 0===n||n,o.setUint8(0,70),o.setUint8(1,76),o.setUint8(2,86),o.setUint8(3,1),o.setUint8(4,(t?4:0)|(n?1:0)),o.setUint32(5,i.byteLength),e<=0?((r=new Uint8Array(i.byteLength+4)).set(i),r.set([0,0,0,0],i.byteLength)):((n=new $t($t.METADATA_TAG)).pts=n.dts=0,n.writeMetaDataDouble("duration",e),n=n.finalize().length,(r=new Uint8Array(i.byteLength+n)).set(i),r.set(o.byteLength,n)),r}},l=Ct,yt=function e(i,o){var a=[],s=0,u=0,l=0,c=1/0,d=null,f=null;o=o||{},e.prototype.init.call(this),this.push=function(t){Ge(i,t),i&&Bt.forEach(function(e){i[e]=t[e]}),a.push(t)},this.setEarliestDts=function(e){u=e},this.setVideoBaseMediaDecodeTime=function(e){c=e},this.setAudioAppendStart=function(e){l=e},this.processFrames_=function(){var e,t,n,r;0!==a.length&&0!==(r=Me(a,i,u)).length&&(i.baseMediaDecodeTime=qe(i,o.keepOriginalTimestamps),Ue(i,r,l,c),i.samples=Ne(r),t=We.mdat(Be(r)),a=[],e=We.moof(s,[i]),s++,i.initSegment=We.initSegment([i]),(n=new Uint8Array(e.byteLength+t.byteLength)).set(e),n.set(t,e.byteLength),je(i),null===d&&(f=d=r[0].pts),f+=r.length*(9216e4/i.samplerate),r={start:d},this.trigger("timingInfo",r),this.trigger("data",{track:i,boxes:n}))},this.flush=function(){this.processFrames_(),this.trigger("timingInfo",{start:d,end:f}),this.resetTiming_(),this.trigger("done","AudioSegmentStream")},this.partialFlush=function(){this.processFrames_(),this.trigger("partialdone","AudioSegmentStream")},this.endTimeline=function(){this.flush(),this.trigger("endedtimeline","AudioSegmentStream")},this.resetTiming_=function(){je(i),f=d=null},this.reset=function(){this.resetTiming_(),a=[],this.trigger("reset")}};yt.prototype=new u;var rn=yt,c=function e(s,u){var t,n,l,c=0,d=[],f=[],h=null,p=null,g=!0;u=u||{},e.prototype.init.call(this),this.push=function(e){Ge(s,e),void 0===s.timelineStartInfo.dts&&(s.timelineStartInfo.dts=e.dts),"seq_parameter_set_rbsp"!==e.nalUnitType||t||(t=e.config,s.sps=[e.data],jt.forEach(function(e){s[e]=t[e]},this)),"pic_parameter_set_rbsp"!==e.nalUnitType||n||(n=e.data,s.pps=[e.data]),d.push(e)},this.processNals_=function(e){var t;for(d=f.concat(d);d.length&&"access_unit_delimiter_rbsp"!==d[0].nalUnitType;)d.shift();if(0!==d.length){var n=Oe(d);if(n.length)if(f=n[n.length-1],e&&(n.pop(),n.duration-=f.duration,n.nalCount-=f.length,n.byteLength-=f.byteLength),n.length){if(this.trigger("timelineStartInfo",s.timelineStartInfo),g){if(!(l=Pe(n))[0][0].keyFrame){if(!(l=Le(l))[0][0].keyFrame)return d=[].concat.apply([],n).concat(f),void(f=[]);(n=[].concat.apply([],l)).duration=l.duration}g=!1}for(null===h&&(h=n[0].pts,p=h),p+=n.duration,this.trigger("timingInfo",{start:h,end:p}),t=0;t<n.length;t++){var r=n[t];s.samples=(a=o=void 0,a=[],o=Re(r,o||0),a.push(o),a);var i=We.mdat(function(e){for(var t,n=0,r=e.byteLength,i=e.length,o=new Uint8Array(r+4*i),a=new DataView(o.buffer),s=0;s<e.length;s++)t=e[s],a.setUint32(n,t.data.byteLength),n+=4,o.set(t.data,n),n+=t.data.byteLength;return o}(r));je(s),Ge(s,r),s.baseMediaDecodeTime=qe(s,u.keepOriginalTimestamps);var o=We.moof(c,[s]);c++,s.initSegment=We.initSegment([s]);var a=new Uint8Array(o.byteLength+i.byteLength);a.set(o),a.set(i,o.byteLength),this.trigger("data",{track:s,boxes:a,sequence:c,videoFrameDts:r.dts,videoFramePts:r.pts})}d=[]}else d=[]}},this.resetTimingAndConfig_=function(){n=t=void 0,p=h=null},this.partialFlush=function(){this.processNals_(!0),this.trigger("partialdone","VideoSegmentStream")},this.flush=function(){this.processNals_(!1),this.resetTimingAndConfig_(),this.trigger("done","VideoSegmentStream")},this.endTimeline=function(){this.flush(),this.trigger("endedtimeline","VideoSegmentStream")},this.reset=function(){this.resetTimingAndConfig_(),f=[],d=[],g=!0,this.trigger("reset")}};c.prototype=new u;function on(e){return e.prototype=new u,e.prototype.init.call(e),e}function an(t,n){t.on("data",n.trigger.bind(n,"data")),t.on("done",n.trigger.bind(n,"done")),t.on("partialdone",n.trigger.bind(n,"partialdone")),t.on("endedtimeline",n.trigger.bind(n,"endedtimeline")),t.on("audioTimingInfo",n.trigger.bind(n,"audioTimingInfo")),t.on("videoTimingInfo",n.trigger.bind(n,"videoTimingInfo")),t.on("trackinfo",n.trigger.bind(n,"trackinfo")),t.on("id3Frame",function(e){e.dispatchType=t.metadataStream.dispatchType,e.cueTime=p(e.pts),n.trigger("id3Frame",e)}),t.on("caption",function(e){n.trigger("caption",e)})}var sn=c,un=Dt.isLikelyAacData,le=function e(a){var s=null,u=!0;a=a||{},e.prototype.init.call(this),a.baseMediaDecodeTime=a.baseMediaDecodeTime||0,this.push=function(e){var t,n,r,i,o;u&&(!(t=un(e))||s&&"aac"===s.type?t||s&&"ts"===s.type||(i=a,(o={type:"ts",tracks:{audio:null,video:null},packet:new Ct.TransportPacketStream,parse:new Ct.TransportParseStream,elementary:new Ct.ElementaryStream,timestampRollover:new Ct.TimestampRolloverStream,adts:new ce.Adts,h264:new ce.h264.H264Stream,captionStream:new Ct.CaptionStream(i),metadataStream:new Ct.MetadataStream}).headOfPipeline=o.packet,o.packet.pipe(o.parse).pipe(o.elementary).pipe(o.timestampRollover),o.timestampRollover.pipe(o.h264),o.h264.pipe(o.captionStream),o.timestampRollover.pipe(o.metadataStream),o.timestampRollover.pipe(o.adts),o.elementary.on("data",function(e){if("metadata"===e.type){for(var t=0;t<e.tracks.length;t++)o.tracks[e.tracks[t].type]||(o.tracks[e.tracks[t].type]=e.tracks[t],o.tracks[e.tracks[t].type].timelineStartInfo.baseMediaDecodeTime=i.baseMediaDecodeTime);o.tracks.video&&!o.videoSegmentStream&&(o.videoSegmentStream=new sn(o.tracks.video,i),o.videoSegmentStream.on("timelineStartInfo",function(e){o.tracks.audio&&!i.keepOriginalTimestamps&&o.audioSegmentStream.setEarliestDts(e.dts-i.baseMediaDecodeTime)}),o.videoSegmentStream.on("timingInfo",o.trigger.bind(o,"videoTimingInfo")),o.videoSegmentStream.on("data",function(e){o.trigger("data",{type:"video",data:e})}),o.videoSegmentStream.on("done",o.trigger.bind(o,"done")),o.videoSegmentStream.on("partialdone",o.trigger.bind(o,"partialdone")),o.videoSegmentStream.on("endedtimeline",o.trigger.bind(o,"endedtimeline")),o.h264.pipe(o.videoSegmentStream)),o.tracks.audio&&!o.audioSegmentStream&&(o.audioSegmentStream=new rn(o.tracks.audio,i),o.audioSegmentStream.on("data",function(e){o.trigger("data",{type:"audio",data:e})}),o.audioSegmentStream.on("done",o.trigger.bind(o,"done")),o.audioSegmentStream.on("partialdone",o.trigger.bind(o,"partialdone")),o.audioSegmentStream.on("endedtimeline",o.trigger.bind(o,"endedtimeline")),o.audioSegmentStream.on("timingInfo",o.trigger.bind(o,"audioTimingInfo")),o.adts.pipe(o.audioSegmentStream)),o.trigger("trackinfo",{hasAudio:!!o.tracks.audio,hasVideo:!!o.tracks.video})}}),o.captionStream.on("data",function(e){var t=o.tracks.video&&o.tracks.video.timelineStartInfo.pts||0;e.startTime=y(e.startPts,t,i.keepOriginalTimestamps),e.endTime=y(e.endPts,t,i.keepOriginalTimestamps),o.trigger("caption",e)}),(o=on(o)).metadataStream.on("data",o.trigger.bind(o,"id3Frame")),an(s=o,this)):(n=a,(r={type:"aac",tracks:{audio:null},metadataStream:new Ct.MetadataStream,aacStream:new Nt,audioRollover:new Ct.TimestampRolloverStream("audio"),timedMetadataRollover:new Ct.TimestampRolloverStream("timed-metadata"),adtsStream:new S(!0)}).headOfPipeline=r.aacStream,r.aacStream.pipe(r.audioRollover).pipe(r.adtsStream),r.aacStream.pipe(r.timedMetadataRollover).pipe(r.metadataStream),r.metadataStream.on("timestamp",function(e){r.aacStream.setTimestamp(e.timeStamp)}),r.aacStream.on("data",function(e){"timed-metadata"!==e.type&&"audio"!==e.type||r.audioSegmentStream||(r.tracks.audio=r.tracks.audio||{timelineStartInfo:{baseMediaDecodeTime:n.baseMediaDecodeTime},codec:"adts",type:"audio"},r.audioSegmentStream=new rn(r.tracks.audio,n),r.audioSegmentStream.on("data",function(e){r.trigger("data",{type:"audio",data:e})}),r.audioSegmentStream.on("partialdone",r.trigger.bind(r,"partialdone")),r.audioSegmentStream.on("done",r.trigger.bind(r,"done")),r.audioSegmentStream.on("endedtimeline",r.trigger.bind(r,"endedtimeline")),r.audioSegmentStream.on("timingInfo",r.trigger.bind(r,"audioTimingInfo")),r.adtsStream.pipe(r.audioSegmentStream),r.trigger("trackinfo",{hasAudio:!!r.tracks.audio,hasVideo:!!r.tracks.video}))}),(r=on(r)).metadataStream.on("data",r.trigger.bind(r,"id3Frame")),an(s=r,this)),u=!1),s.headOfPipeline.push(e)},this.flush=function(){s&&(u=!0,s.headOfPipeline.flush())},this.partialFlush=function(){s&&s.headOfPipeline.partialFlush()},this.endTimeline=function(){s&&s.headOfPipeline.endTimeline()},this.reset=function(){s&&s.headOfPipeline.reset()},this.setBaseMediaDecodeTime=function(e){a.keepOriginalTimestamps||(a.baseMediaDecodeTime=e),s&&(s.tracks.audio&&(s.tracks.audio.timelineStartInfo.dts=void 0,s.tracks.audio.timelineStartInfo.pts=void 0,je(s.tracks.audio),s.audioRollover&&s.audioRollover.discontinuity()),s.tracks.video&&(s.videoSegmentStream&&(s.videoSegmentStream.gopCache_=[]),s.tracks.video.timelineStartInfo.dts=void 0,s.tracks.video.timelineStartInfo.pts=void 0,je(s.tracks.video)),s.timestampRollover&&s.timestampRollover.discontinuity())},this.setRemux=function(e){a.remux=e,s&&s.coalesceStream&&s.coalesceStream.setRemux(e)},this.setAudioAppendStart=function(e){s&&s.tracks.audio&&s.audioSegmentStream&&s.audioSegmentStream.setAudioAppendStart(e)},this.alignGopsWith=function(e){}};le.prototype=new u;function ln(e){return new Date(1e3*e-20828448e5)}function cn(e){for(var t=[];0<e.byteLength;)t.push("0x"+("00"+e[0].toString(16)).slice(-2).toUpperCase()),e=e.subarray(1);return t.join(" ")}function dn(e){var t,n,r,i,o={tagType:wn[e[0]],dataSize:e[1]<<16|e[2]<<8|e[3],timestamp:e[7]<<24|e[4]<<16|e[5]<<8|e[6],streamID:e[8]<<16|e[9]<<8|e[10]};switch(e[0]){case 8:n=e.subarray(11),r=o,i=(n[0]&parseInt("11110000",2))>>>4,(r=r||{}).soundFormat=["Linear PCM, platform endian","ADPCM","MP3","Linear PCM, little endian","Nellymoser 16-kHz mono","Nellymoser 8-kHz mono","Nellymoser","G.711 A-law logarithmic PCM","G.711 mu-law logarithmic PCM","reserved","AAC","Speex","MP3 8-Khz","Device-specific sound"][i],r.soundRate=["5.5-kHz","11-kHz","22-kHz","44-kHz"][(n[0]&parseInt("00001100",2))>>>2],r.soundSize=(n[0]&parseInt("00000010",2))>>>1?"16-bit":"8-bit",r.soundType=n[0]&parseInt("00000001",2)?"Stereo":"Mono",10==i&&(i=n.subarray(1),(n=(n=r)||{}).aacPacketType=["AAC Sequence Header","AAC Raw"][i[0]],n.data=cn(i.subarray(1)));break;case 9:t=e.subarray(11),r=o,n=t[0]&parseInt("00001111",2),(r=r||{}).frameType=["Unknown","Keyframe (for AVC, a seekable frame)","Inter frame (for AVC, a nonseekable frame)","Disposable inter frame (H.263 only)","Generated keyframe (reserved for server use only)","Video info/command frame"][(t[0]&parseInt("11110000",2))>>>4],7==(r.codecID=n)&&(i=t.subarray(1),n=r,t=i[1]&parseInt("01111111",2)<<16|i[2]<<8|i[3],(n=n||{}).avcPacketType=["AVC Sequence Header","AVC NALU","AVC End-of-Sequence"][i[0]],n.CompositionTime=i[1]&parseInt("10000000",2)?-t:t,1===i[0]?n.nalUnitTypeRaw=cn(i.subarray(4,100)):n.data=cn(i.subarray(4)))}return o}function fn(e){var t=31&e[1];return(t<<=8)|e[2]}function hn(e){return!!(64&e[1])}function pn(e){var t=0;return 1<(48&e[3])>>>4&&(t+=e[4]+1),t}function gn(e){switch(e){case 5:return"slice_layer_without_partitioning_rbsp_idr";case 6:return"sei_rbsp";case 7:return"seq_parameter_set_rbsp";case 8:return"pic_parameter_set_rbsp";case 9:return"access_unit_delimiter_rbsp";default:return null}}var mn,yn,yt={Transmuxer:le},vn=fe.getUint64,_n=fe.getUint64,bn={avc1:function(e){var t=new DataView(e.buffer,e.byteOffset,e.byteLength);return{dataReferenceIndex:t.getUint16(6),width:t.getUint16(24),height:t.getUint16(26),horizresolution:t.getUint16(28)+t.getUint16(30)/16,vertresolution:t.getUint16(32)+t.getUint16(34)/16,frameCount:t.getUint16(40),depth:t.getUint16(74),config:mn(e.subarray(78,e.byteLength))}},avcC:function(e){for(var t,n,r=new DataView(e.buffer,e.byteOffset,e.byteLength),i={configurationVersion:e[0],avcProfileIndication:e[1],profileCompatibility:e[2],avcLevelIndication:e[3],lengthSizeMinusOne:3&e[4],sps:[],pps:[]},o=31&e[5],a=6,s=0;s<o;s++)n=r.getUint16(a),a+=2,i.sps.push(new Uint8Array(e.subarray(a,a+n))),a+=n;for(t=e[a],a++,s=0;s<t;s++)n=r.getUint16(a),a+=2,i.pps.push(new Uint8Array(e.subarray(a,a+n))),a+=n;return i},btrt:function(e){e=new DataView(e.buffer,e.byteOffset,e.byteLength);return{bufferSizeDB:e.getUint32(0),maxBitrate:e.getUint32(4),avgBitrate:e.getUint32(8)}},edts:function(e){return{boxes:mn(e)}},elst:function(e){for(var t=new DataView(e.buffer,e.byteOffset,e.byteLength),n={version:t.getUint8(0),flags:new Uint8Array(e.subarray(1,4)),edits:[]},r=t.getUint32(4),i=8;r;r--)0===n.version?(n.edits.push({segmentDuration:t.getUint32(i),mediaTime:t.getInt32(i+4),mediaRate:t.getUint16(i+8)+t.getUint16(i+10)/65536}),i+=12):(n.edits.push({segmentDuration:_n(e.subarray(i)),mediaTime:_n(e.subarray(i+8)),mediaRate:t.getUint16(i+16)+t.getUint16(i+18)/65536}),i+=20);return n},esds:function(e){return{version:e[0],flags:new Uint8Array(e.subarray(1,4)),esId:e[6]<<8|e[7],streamPriority:31&e[8],decoderConfig:{objectProfileIndication:e[11],streamType:e[12]>>>2&63,bufferSize:e[13]<<16|e[14]<<8|e[15],maxBitrate:e[16]<<24|e[17]<<16|e[18]<<8|e[19],avgBitrate:e[20]<<24|e[21]<<16|e[22]<<8|e[23],decoderConfigDescriptor:{tag:e[24],length:e[25],audioObjectType:e[26]>>>3&31,samplingFrequencyIndex:(7&e[26])<<1|e[27]>>>7&1,channelConfiguration:e[27]>>>3&15}}}},ftyp:function(e){for(var t=new DataView(e.buffer,e.byteOffset,e.byteLength),n={majorBrand:me(e.subarray(0,4)),minorVersion:t.getUint32(4),compatibleBrands:[]},r=8;r<e.byteLength;)n.compatibleBrands.push(me(e.subarray(r,r+4))),r+=4;return n},dinf:function(e){return{boxes:mn(e)}},dref:function(e){return{version:e[0],flags:new Uint8Array(e.subarray(1,4)),dataReferences:mn(e.subarray(8))}},hdlr:function(e){for(var t={version:new DataView(e.buffer,e.byteOffset,e.byteLength).getUint8(0),flags:new Uint8Array(e.subarray(1,4)),handlerType:me(e.subarray(8,12)),name:""},n=8,n=24;n<e.byteLength;n++){if(0===e[n]){n++;break}t.name+=String.fromCharCode(e[n])}return t.name=decodeURIComponent(escape(t.name)),t},mdat:function(e){return{byteLength:e.byteLength,nals:function(e){for(var t,n=new DataView(e.buffer,e.byteOffset,e.byteLength),r=[],i=0;i+4<e.length;i+=t)if(t=n.getUint32(i),i+=4,t<=0)r.push("<span style='color:red;'>MALFORMED DATA</span>");else switch(31&e[i]){case 1:r.push("slice_layer_without_partitioning_rbsp");break;case 5:r.push("slice_layer_without_partitioning_rbsp_idr");break;case 6:r.push("sei_rbsp");break;case 7:r.push("seq_parameter_set_rbsp");break;case 8:r.push("pic_parameter_set_rbsp");break;case 9:r.push("access_unit_delimiter_rbsp");break;default:r.push("UNKNOWN NAL - "+e[i]&31)}return r}(e)}},mdhd:function(e){var t=new DataView(e.buffer,e.byteOffset,e.byteLength),n=4,e={version:t.getUint8(0),flags:new Uint8Array(e.subarray(1,4)),language:""};return 1===e.version?(n+=4,e.creationTime=ln(t.getUint32(n)),n+=8,e.modificationTime=ln(t.getUint32(n)),n+=4,e.timescale=t.getUint32(n),n+=8):(e.creationTime=ln(t.getUint32(n)),n+=4,e.modificationTime=ln(t.getUint32(n)),n+=4,e.timescale=t.getUint32(n),n+=4),e.duration=t.getUint32(n),n+=4,n=t.getUint16(n),e.language+=String.fromCharCode(96+(n>>10)),e.language+=String.fromCharCode(96+((992&n)>>5)),e.language+=String.fromCharCode(96+(31&n)),e},mdia:function(e){return{boxes:mn(e)}},mfhd:function(e){return{version:e[0],flags:new Uint8Array(e.subarray(1,4)),sequenceNumber:e[4]<<24|e[5]<<16|e[6]<<8|e[7]}},minf:function(e){return{boxes:mn(e)}},mp4a:function(e){var t=new DataView(e.buffer,e.byteOffset,e.byteLength),t={dataReferenceIndex:t.getUint16(6),channelcount:t.getUint16(16),samplesize:t.getUint16(18),samplerate:t.getUint16(24)+t.getUint16(26)/65536};return 28<e.byteLength&&(t.streamDescriptor=mn(e.subarray(28))[0]),t},moof:function(e){return{boxes:mn(e)}},moov:function(e){return{boxes:mn(e)}},mvex:function(e){return{boxes:mn(e)}},mvhd:function(e){var t=new DataView(e.buffer,e.byteOffset,e.byteLength),n=4,r={version:t.getUint8(0),flags:new Uint8Array(e.subarray(1,4))};return 1===r.version?(n+=4,r.creationTime=ln(t.getUint32(n)),n+=8,r.modificationTime=ln(t.getUint32(n)),n+=4,r.timescale=t.getUint32(n),n+=8):(r.creationTime=ln(t.getUint32(n)),n+=4,r.modificationTime=ln(t.getUint32(n)),n+=4,r.timescale=t.getUint32(n),n+=4),r.duration=t.getUint32(n),n+=4,r.rate=t.getUint16(n)+t.getUint16(n+2)/16,n+=4,r.volume=t.getUint8(n)+t.getUint8(n+1)/8,n+=2,n+=2,n+=8,r.matrix=new Uint32Array(e.subarray(n,n+36)),n+=36,n+=24,r.nextTrackId=t.getUint32(n),r},pdin:function(e){var t=new DataView(e.buffer,e.byteOffset,e.byteLength);return{version:t.getUint8(0),flags:new Uint8Array(e.subarray(1,4)),rate:t.getUint32(4),initialDelay:t.getUint32(8)}},sdtp:function(e){for(var t={version:e[0],flags:new Uint8Array(e.subarray(1,4)),samples:[]},n=4;n<e.byteLength;n++)t.samples.push({dependsOn:(48&e[n])>>4,isDependedOn:(12&e[n])>>2,hasRedundancy:3&e[n]});return t},sidx:function(e){var t=new DataView(e.buffer,e.byteOffset,e.byteLength),n={version:e[0],flags:new Uint8Array(e.subarray(1,4)),references:[],referenceId:t.getUint32(4),timescale:t.getUint32(8)},r=12;0===n.version?(n.earliestPresentationTime=t.getUint32(r),n.firstOffset=t.getUint32(r+4),r+=8):(n.earliestPresentationTime=vn(e.subarray(r)),n.firstOffset=vn(e.subarray(r+8)),r+=16),r+=2;var i=t.getUint16(r);for(r+=2;0<i;r+=12,i--)n.references.push({referenceType:(128&e[r])>>>7,referencedSize:2147483647&t.getUint32(r),subsegmentDuration:t.getUint32(r+4),startsWithSap:!!(128&e[r+8]),sapType:(112&e[r+8])>>>4,sapDeltaTime:268435455&t.getUint32(r+8)});return n},smhd:function(e){return{version:e[0],flags:new Uint8Array(e.subarray(1,4)),balance:e[4]+e[5]/256}},stbl:function(e){return{boxes:mn(e)}},ctts:function(e){for(var t=new DataView(e.buffer,e.byteOffset,e.byteLength),n={version:t.getUint8(0),flags:new Uint8Array(e.subarray(1,4)),compositionOffsets:[]},r=t.getUint32(4),i=8;r;i+=8,r--)n.compositionOffsets.push({sampleCount:t.getUint32(i),sampleOffset:t[0===n.version?"getUint32":"getInt32"](i+4)});return n},stss:function(e){for(var t=new DataView(e.buffer,e.byteOffset,e.byteLength),n={version:t.getUint8(0),flags:new Uint8Array(e.subarray(1,4)),syncSamples:[]},r=t.getUint32(4),i=8;r;i+=4,r--)n.syncSamples.push(t.getUint32(i));return n},stco:function(e){for(var t=new DataView(e.buffer,e.byteOffset,e.byteLength),n={version:e[0],flags:new Uint8Array(e.subarray(1,4)),chunkOffsets:[]},r=t.getUint32(4),i=8;r;i+=4,r--)n.chunkOffsets.push(t.getUint32(i));return n},stsc:function(e){for(var t=new DataView(e.buffer,e.byteOffset,e.byteLength),n=t.getUint32(4),r={version:e[0],flags:new Uint8Array(e.subarray(1,4)),sampleToChunks:[]},i=8;n;i+=12,n--)r.sampleToChunks.push({firstChunk:t.getUint32(i),samplesPerChunk:t.getUint32(i+4),sampleDescriptionIndex:t.getUint32(i+8)});return r},stsd:function(e){return{version:e[0],flags:new Uint8Array(e.subarray(1,4)),sampleDescriptions:mn(e.subarray(8))}},stsz:function(e){for(var t=new DataView(e.buffer,e.byteOffset,e.byteLength),n={version:e[0],flags:new Uint8Array(e.subarray(1,4)),sampleSize:t.getUint32(4),entries:[]},r=12;r<e.byteLength;r+=4)n.entries.push(t.getUint32(r));return n},stts:function(e){for(var t=new DataView(e.buffer,e.byteOffset,e.byteLength),n={version:e[0],flags:new Uint8Array(e.subarray(1,4)),timeToSamples:[]},r=t.getUint32(4),i=8;r;i+=8,r--)n.timeToSamples.push({sampleCount:t.getUint32(i),sampleDelta:t.getUint32(i+4)});return n},styp:function(e){return bn.ftyp(e)},tfdt:Te,tfhd:we,tkhd:function(e){var t=new DataView(e.buffer,e.byteOffset,e.byteLength),n=4,r={version:t.getUint8(0),flags:new Uint8Array(e.subarray(1,4))};return 1===r.version?(n+=4,r.creationTime=ln(t.getUint32(n)),n+=8,r.modificationTime=ln(t.getUint32(n)),n+=4,r.trackId=t.getUint32(n),n+=4,n+=8):(r.creationTime=ln(t.getUint32(n)),n+=4,r.modificationTime=ln(t.getUint32(n)),n+=4,r.trackId=t.getUint32(n),n+=4,n+=4),r.duration=t.getUint32(n),n+=4,n+=8,r.layer=t.getUint16(n),n+=2,r.alternateGroup=t.getUint16(n),n+=2,r.volume=t.getUint8(n)+t.getUint8(n+1)/8,n+=2,n+=2,r.matrix=new Uint32Array(e.subarray(n,n+36)),n+=36,r.width=t.getUint16(n)+t.getUint16(n+2)/65536,n+=4,r.height=t.getUint16(n)+t.getUint16(n+2)/65536,r},traf:function(e){return{boxes:mn(e)}},trak:function(e){return{boxes:mn(e)}},trex:function(e){var t=new DataView(e.buffer,e.byteOffset,e.byteLength);return{version:e[0],flags:new Uint8Array(e.subarray(1,4)),trackId:t.getUint32(4),defaultSampleDescriptionIndex:t.getUint32(8),defaultSampleDuration:t.getUint32(12),defaultSampleSize:t.getUint32(16),sampleDependsOn:3&e[20],sampleIsDependedOn:(192&e[21])>>6,sampleHasRedundancy:(48&e[21])>>4,samplePaddingValue:(14&e[21])>>1,sampleIsDifferenceSample:!!(1&e[21]),sampleDegradationPriority:t.getUint16(22)}},trun:Se,"url ":function(e){return{version:e[0],flags:new Uint8Array(e.subarray(1,4))}},vmhd:function(e){var t=new DataView(e.buffer,e.byteOffset,e.byteLength);return{version:e[0],flags:new Uint8Array(e.subarray(1,4)),graphicsmode:t.getUint16(4),opcolor:new Uint16Array([t.getUint16(6),t.getUint16(8),t.getUint16(10)])}}},c={inspect:mn=function(e){for(var t,n,r,i,o,a=0,s=[],u=new ArrayBuffer(e.length),l=new Uint8Array(u),c=0;c<e.length;++c)l[c]=e[c];for(t=new DataView(u);a<e.byteLength;)n=t.getUint32(a),r=me(e.subarray(a+4,a+8)),i=1<n?a+n:e.byteLength,(o=(bn[r]||function(e){return{data:e}})(e.subarray(a+8,i))).size=n,o.type=r,s.push(o),a=i;return s},textify:yn=function(e,t){var i;return t=t||0,i=new Array(2*t+1).join(" "),e.map(function(r,e){return i+r.type+"\n"+Object.keys(r).filter(function(e){return"type"!==e&&"boxes"!==e}).map(function(e){var t=i+"  "+e+": ",n=r[e];if(n instanceof Uint8Array||n instanceof Uint32Array){e=Array.prototype.slice.call(new Uint8Array(n.buffer,n.byteOffset,n.byteLength)).map(function(e){return" "+("00"+e.toString(16)).slice(-2)}).join("").match(/.{1,24}/g);return e?1===e.length?t+"<"+e.join("").slice(1)+">":t+"<\n"+e.map(function(e){return i+"  "+e}).join("\n")+"\n"+i+"  >":t+"<>"}return t+JSON.stringify(n,null,2).split("\n").map(function(e,t){return 0===t?e:i+"  "+e}).join("\n")}).join("\n")+(r.boxes?"\n"+yn(r.boxes,t+1):"")}).join("\n")},parseType:me,findBox:ye,parseTraf:bn.traf,parseTfdt:bn.tfdt,parseHdlr:bn.hdlr,parseTfhd:bn.tfhd,parseTrun:bn.trun,parseSidx:bn.sidx},wn={8:"audio",9:"video",18:"metadata"},le={inspectTag:dn,inspect:function(e){var t,n,r=9,i=[];for(r+=4;r<e.byteLength;)t=e[r+1]<<16,t|=e[r+2]<<8,t|=e[r+3],t+=11,n=e.subarray(r,r+t),i.push(dn(n)),r+=4+t;return i},textify:function(e){return JSON.stringify(e,null,2)}},fe={parseType:function(e,t){e=fn(e);return 0===e?"pat":e===t?"pmt":t?"pes":null},parsePat:function(e){var t=hn(e),n=4+pn(e);return t&&(n+=e[n]+1),(31&e[n+10])<<8|e[n+11]},parsePmt:function(e){var t={},n=hn(e),r=4+pn(e);if(n&&(r+=e[r]+1),1&e[r+5]){for(var i=3+((15&e[r+1])<<8|e[r+2])-4,o=12+((15&e[r+10])<<8|e[r+11]);o<i;){var a=r+o;t[(31&e[a+1])<<8|e[a+2]]=e[a],o+=5+((15&e[a+3])<<8|e[a+4])}return t}},parsePayloadUnitStartIndicator:hn,parsePesType:function(e,t){switch(t[fn(e)]){case gt.H264_STREAM_TYPE:return"video";case gt.ADTS_STREAM_TYPE:return"audio";case gt.METADATA_STREAM_TYPE:return"timed-metadata";default:return null}},parsePesTime:function(e){if(!hn(e))return null;var t=4+pn(e);if(t>=e.byteLength)return null;var n,r=null;return 192&(n=e[t+7])&&((r={}).pts=(14&e[t+9])<<27|(255&e[t+10])<<20|(254&e[t+11])<<12|(255&e[t+12])<<5|(254&e[t+13])>>>3,r.pts*=4,r.pts+=(6&e[t+13])>>>1,r.dts=r.pts,64&n&&(r.dts=(14&e[t+14])<<27|(255&e[t+15])<<20|(254&e[t+16])<<12|(255&e[t+17])<<5|(254&e[t+18])>>>3,r.dts*=4,r.dts+=(6&e[t+18])>>>1)),r},videoPacketContainsKeyFrame:function(e){for(var t=4+pn(e),n=e.subarray(t),r=0,i=0,o=!1;i<n.byteLength-3;i++)if(1===n[i+2]){r=i+5;break}for(;r<n.byteLength;)switch(n[r]){case 0:if(0!==n[r-1]){r+=2;break}if(0!==n[r-2]){r++;break}for(i+3!==r-2&&"slice_layer_without_partitioning_rbsp_idr"===gn(31&n[i+3])&&(o=!0);r++,1!==n[r]&&r<n.length;);i=r-2,r+=3;break;case 1:if(0!==n[r-1]||0!==n[r-2]){r+=3;break}"slice_layer_without_partitioning_rbsp_idr"===gn(31&n[i+3])&&(o=!0),i=r-2,r+=3;break;default:r+=3}return n=n.subarray(i),r-=i,i=0,n&&3<n.byteLength&&"slice_layer_without_partitioning_rbsp_idr"===gn(31&n[i+3])&&(o=!0),o}},xn=n,Sn={};Sn.ts=fe,Sn.aac=Dt;function Tn(e,t,n){for(var r,i,o,a,s=0,u=188,l=!1;u<=e.byteLength;)if(71!==e[s]||71!==e[u]&&u!==e.byteLength)s++,u++;else{if("pes"===(r=e.subarray(s,u),Sn.ts.parseType(r,t.pid))&&(i=Sn.ts.parsePesType(r,t.table),o=Sn.ts.parsePayloadUnitStartIndicator(r),"audio"===i&&o&&(a=Sn.ts.parsePesTime(r))&&(a.type="audio",n.audio.push(a),l=!0)),l)break;s+=188,u+=188}for(s=(u=e.byteLength)-188,l=!1;0<=s;)if(71!==e[s]||71!==e[u]&&u!==e.byteLength)s--,u--;else{if("pes"===(r=e.subarray(s,u),Sn.ts.parseType(r,t.pid))&&(i=Sn.ts.parsePesType(r,t.table),o=Sn.ts.parsePayloadUnitStartIndicator(r),"audio"===i&&o&&(a=Sn.ts.parsePesTime(r))&&(a.type="audio",n.audio.push(a),l=!0)),l)break;s-=188,u-=188}}function kn(e){var t,n={pid:null,table:null},r={};for(t in function(e,t){for(var n,r=0,i=188;i<e.byteLength;)if(71!==e[r]||71!==e[i])r++,i++;else{switch(n=e.subarray(r,i),Sn.ts.parseType(n,t.pid)){case"pat":t.pid=Sn.ts.parsePat(n);break;case"pmt":var o=Sn.ts.parsePmt(n);t.table=t.table||{},Object.keys(o).forEach(function(e){t.table[e]=o[e]})}r+=188,i+=188}}(e,n),n.table)if(n.table.hasOwnProperty(t))switch(n.table[t]){case gt.H264_STREAM_TYPE:r.video=[],function(e,t,n){for(var r,i,o,a,s,u,l,c,d=0,f=188,h=!1,p={data:[],size:0};f<e.byteLength;)if(71!==e[d]||71!==e[f])d++,f++;else{if("pes"===(r=e.subarray(d,f),Sn.ts.parseType(r,t.pid)))if(i=Sn.ts.parsePesType(r,t.table),o=Sn.ts.parsePayloadUnitStartIndicator(r),"video"===i&&(o&&!h&&(a=Sn.ts.parsePesTime(r))&&(a.type="video",n.video.push(a),h=!0),!n.firstKeyFrame)){if(o&&0!==p.size){for(s=new Uint8Array(p.size),u=0;p.data.length;)l=p.data.shift(),s.set(l,u),u+=l.byteLength;!Sn.ts.videoPacketContainsKeyFrame(s)||(c=Sn.ts.parsePesTime(s))&&(n.firstKeyFrame=c,n.firstKeyFrame.type="video"),p.size=0}p.data.push(r),p.size+=r.byteLength}if(h&&n.firstKeyFrame)break;d+=188,f+=188}for(d=(f=e.byteLength)-188,h=!1;0<=d;)if(71!==e[d]||71!==e[f])d--,f--;else{if("pes"===(r=e.subarray(d,f),Sn.ts.parseType(r,t.pid))&&(i=Sn.ts.parsePesType(r,t.table),o=Sn.ts.parsePayloadUnitStartIndicator(r),"video"===i&&o&&(a=Sn.ts.parsePesTime(r))&&(a.type="video",n.video.push(a),h=!0)),h)break;d-=188,f-=188}}(e,n,r),0===r.video.length&&delete r.video;break;case gt.ADTS_STREAM_TYPE:r.audio=[],Tn(e,n,r),0===r.audio.length&&delete r.audio}return r}fe={inspect:function(e,t){var n,r,i;return(n=(Sn.aac.isLikelyAacData(e)?function(e){for(var t,n=!1,r=0,i=null,o=null,a=0,s=0;3<=e.length-s;){switch(Sn.aac.parseType(e,s)){case"timed-metadata":if(e.length-s<10){n=!0;break}if((a=Sn.aac.parseId3TagSize(e,s))>e.length){n=!0;break}null===o&&(t=e.subarray(s,s+a),o=Sn.aac.parseAacTimestamp(t)),s+=a;break;case"audio":if(e.length-s<7){n=!0;break}if((a=Sn.aac.parseAdtsSize(e,s))>e.length){n=!0;break}null===i&&(t=e.subarray(s,s+a),i=Sn.aac.parseSampleRate(t)),r++,s+=a;break;default:s++}if(n)return null}if(null===i||null===o)return null;var u=9e4/i;return{audio:[{type:"audio",dts:o,pts:o},{type:"audio",dts:o+1024*r*u,pts:o+1024*r*u}]}}:kn)(e))&&(n.audio||n.video)?(e=t,(t=n).audio&&t.audio.length&&(void 0!==(r=e)&&!isNaN(r)||(r=t.audio[0].dts),t.audio.forEach(function(e){e.dts=xn(e.dts,r),e.pts=xn(e.pts,r),e.dtsTime=e.dts/9e4,e.ptsTime=e.pts/9e4})),t.video&&t.video.length&&(void 0!==(i=e)&&!isNaN(i)||(i=t.video[0].dts),t.video.forEach(function(e){e.dts=xn(e.dts,i),e.pts=xn(e.pts,i),e.dtsTime=e.dts/9e4,e.ptsTime=e.pts/9e4}),t.firstKeyFrame&&((t=t.firstKeyFrame).dts=xn(t.dts,i),t.pts=xn(t.pts,i),t.dtsTime=t.dts/9e4,t.ptsTime=t.pts/9e4)),n):null},parseAudioPes_:Tn},yt={codecs:ce,mp4:h,flv:d,mp2t:l,partial:yt};return yt.mp4.tools=c,yt.flv.tools=le,yt.mp2t.tools=fe,yt}),window.DownloadBuffers=(_class3=function(){"use strict";function l(e){_classCallCheck(this,l),_defineProperty(this,"buffers",[]),_defineProperty(this,"bufferSize",0),_defineProperty(this,"inited",!1);var t=e.directory,n=e.tabId,r=e.tabUrl,i=e.uniqueId,o=e.title,e=e.extension;t?(this.directory=t,this.bufferType=l.BUFFER_TYPE.USER_DIR):this.bufferType=l.BUFFER_TYPE.BUFFER_TYPE,this.tabId=n,this.uniqueId=i,this.tabUrl=r||location.href,this.title=middleTruncate(sanitizeFilename(o)),this.extension=e,this.dirName=".t".concat(this.tabId,"-e").concat(browser.runtime.id.substr(0,8),"-u").concat(this.uniqueId,"-d").concat(Date.now())}var e,t,n,r,i,o,a,s,u,c,d,f,h;return _createClass(l,[{key:"tempFileName",value:function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:null,t=1<arguments.length&&void 0!==arguments[1]&&arguments[1];return(null==e?"".concat(this.title,"."):"".concat(this.title,"-").concat(e,".")).concat(t?"raw.":"").concat(this.extension)}},{key:"opfsHsEnoughSpace",value:(h=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var n,r;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,navigator.storage.estimate();case 3:if(r=e.sent,n=r.quota,n=void 0===n?0:n,r=r.usage,1.5*t<n-(void 0===r?0:r))return e.abrupt("return",!0);e.next=10;break;case 10:e.next=15;break;case 12:e.prev=12,e.t0=e.catch(0);case 15:return e.abrupt("return",!1);case 16:case"end":return e.stop()}},e,null,[[0,12]])})),function(e){return h.apply(this,arguments)})},{key:"init",value:(f=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t,n,r=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(t=0<r.length&&void 0!==r[0]?r[0]:0,this.inited)return e.abrupt("return");e.next=4;break;case 4:if(this.inited=!0,!(0<(this.estimatedSize=t)&&t<524288e3)){e.next=10;break}0==this.bufferSize&&(this.bufferType=l.BUFFER_TYPE.BUFFERS),e.next=44;break;case 10:if(this.directory)return e.next=13,this.directory.getDirectoryHandle(this.dirName,{create:!0});e.next=16;break;case 13:this.subDirectory=e.sent,e.next=44;break;case 16:return e.next=18,this.opfsHsEnoughSpace(t);case 18:if(n=e.sent){e.next=25;break}return e.next=22,l.clearOldTempFiles(this.directory,2);case 22:return e.next=24,this.opfsHsEnoughSpace(t);case 24:n=e.sent;case 25:if(n)return e.prev=26,e.next=29,navigator.storage.getDirectory();e.next=42;break;case 29:return this.directory=e.sent,e.next=32,this.directory.getDirectoryHandle(this.dirName,{create:!0});case 32:this.subDirectory=e.sent,this.bufferType=l.BUFFER_TYPE.OPFS_DIR,e.next=40;break;case 36:e.prev=36,e.t0=e.catch(26),this.bufferType=l.BUFFER_TYPE.BUFFERS;case 40:e.next=44;break;case 42:this.bufferType=l.BUFFER_TYPE.BUFFERS;case 44:case"end":return e.stop()}},e,this,[[26,36]])})),function(){return f.apply(this,arguments)})},{key:"writeFile",value:(d=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,n){var r,i,o=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return r=2<o.length&&void 0!==o[2]&&o[2],e.next=3,this.subDirectory.getFileHandle(this.tempFileName(t,r),{create:!0});case 3:return r=e.sent,e.next=6,r.createWritable();case 6:return i=e.sent,e.next=9,i.write(n);case 9:return e.next=11,i.close();case 11:case"end":return e.stop()}},e,this)})),function(e,t){return d.apply(this,arguments)})},{key:"push",value:(c=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,n){var r,i=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(r=2<i.length&&void 0!==i[2]&&i[2],this.bufferType!=l.BUFFER_TYPE.BUFFERS){e.next=5;break}this.buffers[t]=n,e.next=12;break;case 5:if(this.directory)return e.next=8,this.writeFile(t,n,r);e.next=11;break;case 8:this.buffers[t]=!0,e.next=12;break;case 11:this.buffers[t]=n;case 12:this.bufferSize+=n.byteLength;case 13:case"end":return e.stop()}},e,this)})),function(e,t){return c.apply(this,arguments)})},{key:"count",value:function(){return this.buffers.filter(function(e){return null!==e}).length}},{key:"check",value:function(e){return!!this.buffers[e]}},{key:"readFilesTo",value:(u=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var n,r,i,o,a=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:n=1<a.length&&void 0!==a[1]?a[1]:0,r=!(2<a.length&&void 0!==a[2])||a[2];case 2:if(n<this.buffers.length)return e.next=5,this.subDirectory.getFileHandle(this.tempFileName(n));e.next=20;break;case 5:return i=e.sent,e.next=8,i.getFile();case 8:return o=e.sent,e.next=11,o.arrayBuffer();case 11:return o=e.sent,e.next=14,t(o,n);case 14:if(r)return e.next=17,this.subDirectory.removeEntry(this.tempFileName(n));e.next=17;break;case 17:n++,e.next=2;break;case 20:case"end":return e.stop()}},e,this)})),function(e){return u.apply(this,arguments)})},{key:"getFile",value:(s=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var n,r,i,o,a,s,u=this;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(n=t?{type:t}:void 0,this.bufferType==l.BUFFER_TYPE.BUFFERS)return e.abrupt("return",new File(this.buffers,this.tempFileName(),n));e.next=3;break;case 3:if(!this.directory){e.next=68;break}if(e.prev=4,1==this.buffers.length)return e.next=8,this.subDirectory.getFileHandle(this.tempFileName(0));e.next=10;break;case 8:return a=e.sent,e.abrupt("return",a.getFile());case 10:if(this.targetFile)return e.abrupt("return",this.targetFile.getFile());e.next=12;break;case 12:return e.next=14,this.ensureStorageSpace(Math.max(524288e3,.5*this.bufferSize));case 14:if(e.sent){e.next=26;break}if(this.bufferSize<1932735283.2)return r=[],e.next=22,this.readFilesTo(function(){var n=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,n){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:r[n]=t;case 1:case"end":return e.stop()}},e)}));return function(e,t){return n.apply(this,arguments)}}());e.next=23;break;case 22:return e.abrupt("return",new File(r,this.tempFileName(),n));case 23:return e.next=25,this.ensureStorageSpace(Math.max(524288e3,.5*this.bufferSize),2);case 25:e.sent;case 26:return e.next=28,this.subDirectory.getFileHandle(this.tempFileName(),{create:!0});case 28:return this.targetFile=e.sent,e.next=31,this.targetFile.createWritable();case 31:return i=e.sent,e.prev=32,e.next=35,this.readFilesTo(function(){var n=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,n){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,i.write(t);case 3:e.next=11;break;case 5:return e.prev=5,e.t0=e.catch(0),e.next=9,l.clearOldTempFiles(u.directory,1);case 9:return e.next=11,i.write(t);case 11:case"end":return e.stop()}},e,null,[[0,5]])}));return function(e,t){return n.apply(this,arguments)}}());case 35:return e.next=37,i.close();case 37:return e.abrupt("return",this.targetFile.getFile());case 40:if(e.prev=40,e.t0=e.catch(32),i.locked)return e.next=45,i.abort();e.next=45;break;case 45:throw e.t0;case 46:e.next=66;break;case 48:return e.prev=48,e.t1=e.catch(4),e.prev=50,e.next=53,navigator.storage.estimate();case 53:s=e.sent,o=s.quota,a=s.usage,s=s.usageDetails,a=o-a,s=(null==s?void 0:s.fileSystem)||0,reportMsg("getFile ".concat(formatBytes(this.bufferSize)," ").concat(formatBytes(s)).concat(formatBytes(o)," ").concat(formatBytes(a)," error: ").concat(null===e.t1||void 0===e.t1?void 0:e.t1.message," ").concat(this.tabUrl," ").concat(this.bufferType,".")),e.next=65;break;case 62:e.prev=62,e.t2=e.catch(50),reportMsg("getFile error: ".concat(null===e.t1||void 0===e.t1?void 0:e.t1.message," ").concat(this.tabUrl," ").concat(this.bufferType));case 65:throw e.t1;case 66:e.next=69;break;case 68:return e.abrupt("return",new File(this.buffers,this.tempFileName(),n));case 69:case"end":return e.stop()}},e,this,[[4,48],[32,40],[50,62]])})),function(e){return s.apply(this,arguments)})},{key:"deleteFiles",value:(a=_asyncToGenerator(_regeneratorRuntime().mark(function e(){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,this.directory)return e.next=4,this.directory.removeEntry(this.dirName,{recursive:!0});e.next=4;break;case 4:e.next=9;break;case 6:e.prev=6,e.t0=e.catch(0);case 9:case"end":return e.stop()}},e,this,[[0,6]])})),function(){return a.apply(this,arguments)})},{key:"getSize",value:function(){return this.bufferSize}},{key:"ensureStorageSpace",value:(o=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var n,r,i,o=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(n=1<o.length&&void 0!==o[1]?o[1]:3,this.directory){e.next=3;break}return e.abrupt("return",!0);case 3:return e.prev=3,e.next=6,navigator.storage.estimate();case 6:if(r=e.sent,i=r.quota,r=r.usage,i-r<t)return e.next=14,l.clearOldTempFiles(this.directory,n);e.next=23;break;case 14:return e.next=16,navigator.storage.estimate();case 16:if(i=e.sent,r=i.quota,i=i.usage,r-i<t)return e.abrupt("return",!1);e.next=23;break;case 23:return e.abrupt("return",!0);case 26:return e.prev=26,e.t0=e.catch(3),e.abrupt("return",!1);case 30:case"end":return e.stop()}},e,this,[[3,26]])})),function(e){return o.apply(this,arguments)})}],[{key:"clearTabTempFiles",value:(i=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var n,r,i,o,a,s,u,l,c=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(n=1<c.length&&void 0!==c[1]?c[1]:null,e.prev=1,n){e.next=14;break}return e.next=5,navigator.storage.estimate();case 5:if(l=e.sent,null!=(u=l.usageDetails)&&u.fileSystem)return e.next=10,navigator.storage.getDirectory();e.next=13;break;case 10:n=e.sent,e.next=14;break;case 13:return e.abrupt("return");case 14:o=i=!(r=[]),e.prev=17,s=_asyncIterator(n.entries());case 19:return e.next=21,s.next();case 21:if(!(i=!(l=e.sent).done)){e.next=29;break}if(u=_slicedToArray(l.value,2),l=u[0],u[1],l.startsWith(".t".concat(t,"-e").concat(browser.runtime.id.substr(0,8),"-")))return e.next=26,n.removeEntry(l,{recursive:!0});e.next=26;break;case 26:i=!1,e.next=19;break;case 29:e.next=35;break;case 31:e.prev=31,e.t0=e.catch(17),o=!0,a=e.t0;case 35:if(e.prev=35,e.prev=36,i&&null!=s.return)return e.next=40,s.return();e.next=40;break;case 40:if(e.prev=40,o)throw a;e.next=43;break;case 43:return e.finish(40);case 44:return e.finish(35);case 45:return e.next=47,Promise.all(r);case 47:r.length,e.next=53;break;case 50:e.prev=50,e.t1=e.catch(1),null===e.t1||void 0===e.t1||e.t1.message.includes("estimate");case 53:case"end":return e.stop()}},e,null,[[1,50],[17,31,35,45],[36,,40,44]])})),function(e){return i.apply(this,arguments)})},{key:"clearTempFilesByUniqueId",value:(r=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var n,r,i,o,a,s,u,l,c=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(n=1<c.length&&void 0!==c[1]?c[1]:null,e.prev=1,n){e.next=14;break}return e.next=5,navigator.storage.estimate();case 5:if(l=e.sent,null!=(u=l.usageDetails)&&u.fileSystem)return e.next=10,navigator.storage.getDirectory();e.next=13;break;case 10:n=e.sent,e.next=14;break;case 13:return e.abrupt("return");case 14:o=i=!(r=[]),e.prev=17,s=_asyncIterator(n.entries());case 19:return e.next=21,s.next();case 21:if(!(i=!(l=e.sent).done)){e.next=29;break}if(u=_slicedToArray(l.value,2),l=u[0],u[1],l.includes("-u".concat(t,"-")))return e.next=26,n.removeEntry(l,{recursive:!0});e.next=26;break;case 26:i=!1,e.next=19;break;case 29:e.next=35;break;case 31:e.prev=31,e.t0=e.catch(17),o=!0,a=e.t0;case 35:if(e.prev=35,e.prev=36,i&&null!=s.return)return e.next=40,s.return();e.next=40;break;case 40:if(e.prev=40,o)throw a;e.next=43;break;case 43:return e.finish(40);case 44:return e.finish(35);case 45:return e.next=47,Promise.all(r);case 47:r.length,e.next=53;break;case 50:e.prev=50,e.t1=e.catch(1),null===e.t1||void 0===e.t1||e.t1.message.includes("estimate");case 53:case"end":return e.stop()}},e,null,[[1,50],[17,31,35,45],[36,,40,44]])})),function(e){return r.apply(this,arguments)})},{key:"clearOldTempFiles",value:(n=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t,n,r,i,o,a,s,u,l,c,d,f=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(t=0<f.length&&void 0!==f[0]?f[0]:null,n=1<f.length&&void 0!==f[1]?f[1]:6,e.prev=2,t){e.next=15;break}return e.next=6,navigator.storage.estimate();case 6:if(u=e.sent,null!=(u=u.usageDetails)&&u.fileSystem)return e.next=11,navigator.storage.getDirectory();e.next=14;break;case 11:t=e.sent,e.next=15;break;case 14:return e.abrupt("return");case 15:o=i=!(r=[]),e.prev=18,s=_asyncIterator(t.entries());case 20:return e.next=22,s.next();case 22:if(!(i=!(u=e.sent).done)){e.next=33;break}if(d=_slicedToArray(u.value,2),l=d[0],d[1],null==(d=l.match("-e".concat(browser.runtime.id.substr(0,8),"-.*-d(?<date>\\d{13})")))||null===(c=d.groups)||void 0===c||!c.date){e.next=30;break}if(new Date(Number(d.groups.date)).getTime()<Date.now()-36e5*n)return e.next=30,t.removeEntry(l,{recursive:!0});e.next=30;break;case 30:i=!1,e.next=20;break;case 33:e.next=39;break;case 35:e.prev=35,e.t0=e.catch(18),o=!0,a=e.t0;case 39:if(e.prev=39,e.prev=40,i&&null!=s.return)return e.next=44,s.return();e.next=44;break;case 44:if(e.prev=44,o)throw a;e.next=47;break;case 47:return e.finish(44);case 48:return e.finish(39);case 49:return e.next=51,Promise.all(r);case 51:r.length,e.next=57;break;case 54:e.prev=54,e.t1=e.catch(2),null===e.t1||void 0===e.t1||e.t1.message.includes("estimate");case 57:case"end":return e.stop()}},e,null,[[2,54],[18,35,39,49],[40,,44,48]])})),function(){return n.apply(this,arguments)})},{key:"clearFilesExceptActiveTabs",value:(t=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var n,r,i,o,a,s,u,l,c=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(n=1<c.length&&void 0!==c[1]?c[1]:null,e.prev=1,n){e.next=14;break}return e.next=5,navigator.storage.estimate();case 5:if(u=e.sent,null!=(l=u.usageDetails)&&l.fileSystem)return e.next=10,navigator.storage.getDirectory();e.next=13;break;case 10:n=e.sent,e.next=14;break;case 13:return e.abrupt("return");case 14:o=i=!(r=[]),e.prev=17,s=_asyncIterator(n.entries());case 19:return e.next=21,s.next();case 21:if(!(i=!(u=e.sent).done)){e.next=28;break}l=_slicedToArray(u.value,2),u=l[0],l[1],(l=u.match(/^\.t(\d+)-/))&&(l=parseInt(l[1],10),t.includes(l)||r.push(n.removeEntry(u,{recursive:!0})));case 25:i=!1,e.next=19;break;case 28:e.next=34;break;case 30:e.prev=30,e.t0=e.catch(17),o=!0,a=e.t0;case 34:if(e.prev=34,e.prev=35,i&&null!=s.return)return e.next=39,s.return();e.next=39;break;case 39:if(e.prev=39,o)throw a;e.next=42;break;case 42:return e.finish(39);case 43:return e.finish(34);case 44:return e.next=46,Promise.all(r);case 46:r.length,e.next=52;break;case 49:e.prev=49,e.t1=e.catch(1);case 52:case"end":return e.stop()}},e,null,[[1,49],[17,30,34,44],[35,,39,43]])})),function(e){return t.apply(this,arguments)})},{key:"printFiles",value:(e=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t,n,r,i,o,a,s=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(t=0<s.length&&void 0!==s[0]?s[0]:null,e.prev=1,t){e.next=14;break}return e.next=5,navigator.storage.estimate();case 5:if(a=e.sent,null!=(a=a.usageDetails)&&a.fileSystem)return e.next=10,navigator.storage.getDirectory();e.next=13;break;case 10:t=e.sent,e.next=14;break;case 13:return e.abrupt("return");case 14:r=n=!1,e.prev=16,o=_asyncIterator(t.entries());case 18:return e.next=20,o.next();case 20:if(!(n=!(a=e.sent).done)){e.next=26;break}a=_slicedToArray(a.value,2),a[0],a[1];case 23:n=!1,e.next=18;break;case 26:e.next=32;break;case 28:e.prev=28,e.t0=e.catch(16),r=!0,i=e.t0;case 32:if(e.prev=32,e.prev=33,n&&null!=o.return)return e.next=37,o.return();e.next=37;break;case 37:if(e.prev=37,r)throw i;e.next=40;break;case 40:return e.finish(37);case 41:return e.finish(32);case 42:e.next=47;break;case 44:e.prev=44,e.t1=e.catch(1);case 47:case"end":return e.stop()}},e,null,[[1,44],[16,28,32,42],[33,,37,41]])})),function(){return e.apply(this,arguments)})}]),l}(),_defineProperty(_class3,"BUFFER_TYPE",{BUFFERS:"buffers",USER_DIR:"userDir",OPFS_DIR:"opfsDir"}),_class3);var DownloadingUi=function(){"use strict";function e(){_classCallCheck(this,e)}return _createClass(e,[{key:"createUi",value:function(){var e='\n            <h5 class="pt-2">'.concat(browser.i18n.getMessage("downloading"),'</h5>\n            <table class="table table-striped">\n                <thead class="bg-primary text-light">\n                    <tr>\n                        <th scope="col">#</th>\n                        <th scope="col">').concat(browser.i18n.getMessage("quality"),'</th>\n                        <th scope="col">').concat(browser.i18n.getMessage("filename"),'</th>\n                        <th scope="col">').concat(browser.i18n.getMessage("status"),'</th>\n                        <th scope="col">').concat(browser.i18n.getMessage("progress"),"</th>\n                    </tr>\n                </thead>\n                <tbody>\n                </tbody>\n            </table>");$("#downloading-content").html(e)}},{key:"createListItem",value:function(e,t,n){t='\n            <tr id="'.concat(e,'">\n                <th scope="row">\n                    <img src="http://www.google.com/s2/favicons?domain=').concat(n,'" width="25" height="25">\n                </th>\n                <td style="width:20%">').concat(getQualityFromVideoLink(t),'</td>\n                <td style="width:50%">').concat(t.title,'</td>\n                <td id="state">').concat(browser.i18n.getMessage("waiting"),'</td>\n                <td id="progress"></td>\n            </tr>');$("#downloading-content tbody").append(t)}},{key:"deleteListItem",value:function(e){e=$('[id="'.concat(e,'"]'));e.length&&e.remove()}},{key:"updateState",value:function(e,t,n){e=$('[id="'.concat(e,'"] [id="state"]'));e.length&&(n.length?(e.addClass("text-danger"),e.attr("title",n)):e.removeAttr("title").removeClass("text-danger"),e.html(t))}},{key:"updateProgress",value:function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{},e=$('[id="'.concat(e,'"] [id="progress"]'));e.length&&(t=null!=(null==n?void 0:n.totalSegs)&&null!=(null==n?void 0:n.totalSegs)?"".concat(t," (").concat(null==n?void 0:n.completedSegs,"/").concat(null==n?void 0:n.totalSegs,")"):t,e.html(t))}}]),e}(),ItemDownloader=function(){"use strict";_inherits(w,AbstractDownloader);var t,r,n,i,o,a,s,u,e,l,c,d,f,h=_createSuper(w);function w(e){_classCallCheck(this,w);var t=e.currentTab,n=e.directory,r=e.tabId,i=e.tabUrl,o=e.uniqueId,a=e.title,s=e.extension,u=e.stateListener,u=void 0===u?null:u,e=e.progressListener,e=void 0===e?null:e;return _defineProperty(_assertThisInitialized(e=h.call(this,u,e)),"videoLink",null),_defineProperty(_assertThisInitialized(e),"encryptionKeys",{}),_defineProperty(_assertThisInitialized(e),"duration",0),_defineProperty(_assertThisInitialized(e),"frameId",0),_defineProperty(_assertThisInitialized(e),"tabId",0),_defineProperty(_assertThisInitialized(e),"tabUrl",""),_defineProperty(_assertThisInitialized(e),"downloadId",null),_defineProperty(_assertThisInitialized(e),"requestMethods",[w.REQUEST_METHOD.CONTENT,w.REQUEST_METHOD.INJECT,w.REQUEST_METHOD.DIRECT,w.REQUEST_METHOD.BG]),_defineProperty(_assertThisInitialized(e),"corsReqMethods",[w.REQUEST_METHOD.DIRECT,w.REQUEST_METHOD.BG,w.REQUEST_METHOD.CONTENT,w.REQUEST_METHOD.INJECT]),_defineProperty(_assertThisInitialized(e),"currentReqMethods",e.requestMethods),e.currentTab=t,e.tabId=r,e.tabUrl=i,e.buffers=new DownloadBuffers({directory:n,tabId:r,tabUrl:i,uniqueId:o,title:a,extension:s}),e}return _createClass(w,[{key:"setRequestMethods",value:function(e,t,n){if(e&&new URL(t).hostname!==new URL(n).hostname)return void(this.currentReqMethods=this.corsReqMethods);this.currentReqMethods=this.requestMethods}},{key:"updateRequestMethods",value:function(e){e!==w.REQUEST_METHOD.DIRECT&&e!==w.REQUEST_METHOD.BG||this.currentReqMethods!==this.corsReqMethods&&(this.currentReqMethods=this.corsReqMethods)}},{key:"download",value:(f=_asyncToGenerator(_regeneratorRuntime().mark(function e(n,t){var r,i,o,a,s,u,l,c,d,f,h,p,g,m,y,v,_,b=this;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(this.videoLink=n,this.downloadId=t,this.frameId=n.frameId||0,n.duration&&(this.duration=n.duration),null!==(r=this.videoLink)&&void 0!==r&&r.streamType){e.next=43;break}if(s=this.videoLink,a=s.init,o=void 0===a?{}:a,a=s.download_url,s=s.size,s=void 0===s?0:s,this.setRequestMethods(null==n?void 0:n.checkIsCors,n.initiator,a),null!=n&&n.checkIsCors||null!=n&&null!==(i=n.init)&&void 0!==i&&i.credentials){e.next=19;break}return e.prev=8,e.next=11,this.getCredentials({url:n.download_url,init:o});case 11:(l=e.sent)&&(n.init.credentials=l),e.next=19;break;case 15:e.prev=15,e.t0=e.catch(8),null!==e.t0&&void 0!==e.t0&&null!==(u=e.t0.message)&&void 0!==u&&u.includes(w.ERRORS.SEND_MESSAGE_ERROR)?["contentReq","injectReq"].includes(this.currentReqMethods[0])&&(this.requestMethods=this.currentReqMethods.filter(function(e){return!["contentReq","injectReq"].includes(e)})):this.currentReqMethods=this.corsReqMethods;case 19:if(!1===(null===(l=this.videoLink)||void 0===l?void 0:l.shouldBeChunked)){e.next=33;break}if(0==s)return(l=_objectSpread({},null==n?void 0:n.init)).headers=_objectSpread(_objectSpread({},l.headers),{},{range:"bytes=0-"}),e.next=25,this.segmentRequest({url:n.download_url,init:l,initiator:null==n?void 0:n.initiator,type:"headers",timeout:5e3},null,!0);e.next=27;break;case 25:d=e.sent,s=getSizeFromReceivedHeader(jsonHeadersToResponseHeaders(d.result));case 27:return this.estimatedSize=s,e.next=30,this.buffers.init(s);case 30:n.segments=this.generateSegments(a,s,4194304),e.next=41;break;case 33:return e.next=35,this.segmentRequest({url:n.download_url,init:null==n?void 0:n.init,initiator:null==n?void 0:n.initiator,type:"arrayBuffer"},function(e){b.handleProgress(b.downloadId,e)});case 35:return c=e.sent,e.next=38,this.buffers.init(c.byteLength);case 38:return e.next=40,this.buffers.push(0,c);case 40:return e.abrupt("return");case 41:e.next=99;break;case 43:this.setRequestMethods(null==n?void 0:n.checkIsCors,n.initiator,n.url||n.manifestUrl);try{n.url&&n.sig&&(n.url=applySig(n.url,n.sig))}catch(e){}if("dash"==n.streamType&&n.url)return e.prev=46,(d=_objectSpread({},null==n?void 0:n.init)).headers=_objectSpread(_objectSpread({},d.headers),{},{range:"bytes=0-"}),e.next=51,this.segmentRequest({url:n.url,init:d,initiator:null==n?void 0:n.initiator,type:"headers",timeout:5e3},null,!0);e.next=67;break;case 51:return f=e.sent,this.updateRequestMethods(f.func),f=getSizeFromReceivedHeader(jsonHeadersToResponseHeaders(f.result)),this.estimatedSize=f,this.isTs=!1,e.next=58,this.buffers.init(f);case 58:n.segments=this.generateSegments(n.url,f,4194304),e.next=67;break;case 61:throw e.prev=61,e.t1=e.catch(46),sendGaException("download failed",{videoLink:n,error:e.t1.message}),this.handleStateChange(this.downloadId,w.STATE.FAILED),new Error("genseg: ".concat((null===e.t1||void 0===e.t1?void 0:e.t1.message)||e.t1));case 67:if(null!=n&&n.segments||!n.url){e.next=98;break}return e.prev=68,e.next=71,this.segmentRequest({url:updatePandavideoToken(n.url),init:n.init,initiator:null==n?void 0:n.initiator,type:"text"});case 71:if(determineStreamType(h=e.sent)){e.next=83;break}return e.next=75,this.segmentRequest({url:n.manifestUrl,init:(null==n?void 0:n.init)||{},initiator:null==n?void 0:n.initiator,type:"text"});case 75:return p=e.sent,e.next=78,getDataFromBg("parse-stream-manifest",{manifest:p,url:n.url,init:n.init,streamType:n.streamType},this.tabUrl);case 78:return p=e.sent,p=p.find(function(e){return(null==e||!e.bandwidth||!n.bandwidth||e.bandwidth===n.bandwidth)&&((null==e||!e.resolution||!n.resolution||e.resolution===n.resolution)&&((null==e||!e.frameRate||!n.frameRate||e.frameRate===n.frameRate)&&((null==e||!e.height||!n.height||e.height===n.height)&&((null==e?void 0:e.bandwidth)&&n.bandwidth||(null==e?void 0:e.resolution)&&n.resolution||(null==e?void 0:e.frameRate)&&n.frameRate||(null==e?void 0:e.height)&&n.height))))}),e.next=82,this.segmentRequest({url:updatePandavideoToken(p.url),init:(null==n?void 0:n.init)||{},initiator:null==n?void 0:n.initiator,type:"text"});case 82:h=e.sent;case 83:if(determineStreamType(h)){e.next=86;break}throw this.handleStateChange(this.downloadId,w.STATE.FAILED),new Error("reqManifest data invalid");case 86:return e.next=88,getDataFromBg("parse-stream-manifest",{manifest:h,url:n.url,init:n.init,streamType:n.streamType},this.tabUrl);case 88:(y=e.sent)[0].segments&&(n.mediaSequence=(null===(m=y[0])||void 0===m?void 0:m.mediaSequence)||0,n.discontinuityStarts=(null===(m=y[0])||void 0===m?void 0:m.discontinuityStarts)||[],n.segments=y[0].segments),e.next=98;break;case 92:throw e.prev=92,e.t2=e.catch(68),sendGaException("get mediaLinks error",{videoLink:n,error:e.t2.message}),this.handleStateChange(this.downloadId,w.STATE.FAILED),new Error("getML: ".concat((null===e.t2||void 0===e.t2?void 0:e.t2.message)||e.t2));case 98:n.duration||(g=0,n.segments.forEach(function(e){var t;g+=(null==e?void 0:e.duration)||0,n.sig&&(e.url=applySig(e.url,n.sig),null!=e&&null!==(t=e.key)&&void 0!==t&&t.url&&(e.key.url=applySig(e.key.url,n.sig)))}),n.duration=g,this.duration=g);case 99:if(this.estimatedSize){e.next=124;break}if(e.prev=100,n.segments.length)return _=_objectSpread({},null==n?void 0:n.init),(v=1<n.segments.length?n.segments[1]:n.segments[0]).byterange&&(m=v.byterange.offset,y=m+v.byterange.length-1,_.headers=_objectSpread(_objectSpread({},_.headers),{},{range:"bytes=".concat(m,"-").concat(y)})),e.next=107,this.segmentRequest({url:v.url,init:_,initiator:null==n?void 0:n.initiator,type:"arrayBuffer",timeout:5e3},null,!0);e.next=116;break;case 107:v=e.sent,this.updateRequestMethods(v.func),_=v.result,v=null==_?void 0:_.byteLength,_=new Uint8Array(_),this.isTs=mediaIsTs(_),v&&(this.estimatedSize=v*n.segments.length),e.next=117;break;case 116:n.duration&&n.bandwidth?this.estimatedSize=n.duration*n.bandwidth/8:n.size&&(this.estimatedSize=n.size);case 117:e.next=124;break;case 119:throw e.prev=119,e.t3=e.catch(100),n.duration&&n.bandwidth&&(this.estimatedSize=n.duration*n.bandwidth/8),new Error("estimateSize: ".concat((null===e.t3||void 0===e.t3?void 0:e.t3.message)||e.t3));case 124:if(this.buffers.inited){e.next=127;break}return e.next=127,this.buffers.init((null==this?void 0:this.estimatedSize)||0);case 127:return e.prev=127,e.next=130,this.parallelDownload(5);case 130:e.next=144;break;case 132:return e.prev=132,e.t4=e.catch(127),e.prev=135,e.next=138,this.serialDownload();case 138:e.next=144;break;case 140:throw e.prev=140,e.t5=e.catch(135),e.t5;case 144:return e.abrupt("return");case 145:case"end":return e.stop()}},e,this,[[8,15],[46,61],[68,92],[100,119],[127,132],[135,140]])})),function(e,t){return f.apply(this,arguments)})},{key:"convertToMp4",value:function(o,a,s){var u=this,l=3<arguments.length&&void 0!==arguments[3]?arguments[3]:1e3;return new Promise(function(n,t){if(0!=(null==u?void 0:u.isTs)){if(void 0===(null==u?void 0:u.isTs)){var e=new Uint8Array(o);if(u.isTs=mediaIsTs(e),!u.isTs)return void n(o)}var r=setTimeout(function(){i.flush(),n(o)},l),i=new muxjs.mp4.Transmuxer({keepOriginalTimestamps:!0,duration:parseInt(s)});i.on("data",function(e){var t;clearTimeout(r),0===a?((t=new Uint8Array(e.initSegment.byteLength+e.data.byteLength)).set(e.initSegment,0),t.set(e.data,e.initSegment.byteLength),n(t.buffer)):n(e.data.buffer)}),i.on("end",function(){}),i.on("error",function(e){clearTimeout(r),t(e)}),i.push(new Uint8Array(o)),i.flush()}else n(o)})}},{key:"generateSegments",value:function(e,t,n){for(var r=[],i=0;i<t;){var o=Math.min(i+n-1,t-1),a={offset:i,length:o-i+1};r.push({url:e,byterange:a}),i=o+1}return r}},{key:"decrypt",value:(d=_asyncToGenerator(_regeneratorRuntime().mark(function e(i,o,a){var s,t=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return s=3<t.length&&void 0!==t[3]&&t[3],e.abrupt("return",new Promise(function(n,t){var e=new DataView(o.buffer),r=new Uint32Array([e.getUint32(0),e.getUint32(4),e.getUint32(8),e.getUint32(12)]);try{new aesDecrypter.Decrypter(i,r,a,function(e,t){n(t.buffer.slice(0,t.byteLength))})}catch(e){s?n(i.buffer):t(e)}}));case 2:case"end":return e.stop()}},e)})),function(e,t,n){return d.apply(this,arguments)})},{key:"handleEncryptedBuffer",value:(c=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,n,r,i,o,a){var s,u;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(n.key.url in this.encryptionKeys){e.next=6;break}return e.next=3,this.segmentRequest({url:n.key.url,init:o,initiator:a,type:"arrayBuffer"});case 3:s=e.sent,u=new Uint8Array(s),this.encryptionKeys[n.key.url]=u;case 6:return s=this.encryptionKeys[n.key.url],u=n.key.iv?new Uint32Array(n.key.iv):new Uint32Array([0,0,0,r+i]),e.next=10,this.decrypt(new Uint8Array(t),s,u,0==r);case 10:return e.abrupt("return",e.sent);case 11:case"end":return e.stop()}},e,this)})),function(e,t,n,r,i,o){return c.apply(this,arguments)})},{key:"serialDownload",value:(l=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var s,t,u,l,c,n,d,f=this;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:n=this.videoLink,s=n.segments,t=n.init,u=void 0===t?{}:t,n=n.mediaSequence,l=void 0===n?0:n,c=this.buffers.count(),n=_regeneratorRuntime().mark(function e(){var t,n,r,i,o,a;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(t=s[d],f.buffers.check(d))return e.abrupt("return",1);e.next=3;break;case 3:return n=_objectSpread({},u),t.byterange&&(r=t.byterange.offset,i=r+t.byterange.length-1,n.headers=_objectSpread(_objectSpread({},n.headers),{},{range:"bytes=".concat(r,"-").concat(i)})),e.next=7,f.segmentRequest({url:t.url,init:n,initiator:null===(n=f.videoLink)||void 0===n?void 0:n.initiator,type:"arrayBuffer"},function(e){e=Math.floor((e/100+c)/s.length*100);f.handleProgress(f.downloadId,e,{completedSegs:c,totalSegs:s.length})});case 7:if(!(o=e.sent)){e.next=23;break}if(c++,t.key)return e.next=13,f.handleEncryptedBuffer(o,t,d,l,u,null===(a=f.videoLink)||void 0===a?void 0:a.initiator);e.next=14;break;case 13:o=e.sent;case 14:return e.next=16,f.convertToMp4(o,d,f.duration).catch(function(e){return o});case 16:return o=e.sent,e.next=19,f.buffers.push(d,o);case 19:a=Math.floor(c/s.length*100),f.handleProgress(f.downloadId,a,{completedSegs:c,totalSegs:s.length}),e.next=26;break;case 23:throw f.handleStateChange(f.downloadId,w.STATE.FAILED),new Error("Request failed");case 26:case"end":return e.stop()}},e)}),d=0;case 4:if(d<s.length)return e.delegateYield(n(),"t0",6);e.next=11;break;case 6:if(e.t0)return e.abrupt("continue",8);e.next=8;break;case 8:d++,e.next=4;break;case 11:case"end":return e.stop()}},e,this)})),function(){return l.apply(this,arguments)})},{key:"parallelDownload",value:(e=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t,u,n,l,r,c,d,f,h,p,g,m,i,o,y=this,a=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:for(t=0<a.length&&void 0!==a[0]?a[0]:5,r=this.videoLink,u=r.segments,n=r.init,l=void 0===n?{}:n,r=r.mediaSequence,c=void 0===r?0:r,d=u.length,h=f=0,p=[],g=new Array(d).fill(0),m=function(){var e=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t,n,r,i,o,a,s;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(d<=(t=h))return e.abrupt("return");e.next=3;break;case 3:return h++,e.prev=4,n=u[t],r=_objectSpread({},l),n.byterange&&(i=n.byterange.offset,o=i+n.byterange.length-1,r.headers=_objectSpread(_objectSpread({},r.headers),{},{range:"bytes=".concat(i,"-").concat(o)})),e.next=10,y.segmentRequest({url:n.url,init:r,initiator:null===(r=y.videoLink)||void 0===r?void 0:r.initiator,type:"arrayBuffer"},function(e){g[t]=e;e=Math.floor(g.reduce(function(e,t){return e+t/100},0)/d*100);y.handleProgress(y.downloadId,e,{completedSegs:f,totalSegs:u.length})});case 10:if(!(a=e.sent)){e.next=26;break}if(n.key)return e.next=15,y.handleEncryptedBuffer(a,n,t,c,l,null===(s=y.videoLink)||void 0===s?void 0:s.initiator);e.next=16;break;case 15:a=e.sent;case 16:return e.next=18,y.convertToMp4(a,t,y.duration).catch(function(e){return a});case 18:return a=e.sent,e.next=21,y.buffers.push(t,a);case 21:f++,s=Math.floor(g.reduce(function(e,t){return e+t/100},0)/d*100),y.handleProgress(y.downloadId,s,{completedSegs:f,totalSegs:u.length}),e.next=27;break;case 26:p.push(new Error("Request failed"));case 27:e.next=32;break;case 29:e.prev=29,e.t0=e.catch(4),p.push(e.t0);case 32:if(e.prev=32,d<=h)return e.abrupt("return");e.next=35;break;case 35:return e.next=37,m();case 37:return e.finish(32);case 38:case"end":return e.stop()}},e,null,[[4,29,32,38]])}));return function(){return e.apply(this,arguments)}}(),i=[],o=0;o<Math.min(t,d);o++)i.push(m());return e.next=12,Promise.all(i);case 12:if(0<p.length)throw p;e.next=14;break;case 14:case"end":return e.stop()}},e,this)})),function(){return e.apply(this,arguments)})},{key:"filterInitForContent",value:function(e){var t,n,r=e.headers,r=void 0===r?{}:r,e=_objectWithoutProperties(e,_excluded2);return e.headers=this.filterHeaders(r),"include"==(null==e?void 0:e.credentials)&&null!==(t=this.videoLink)&&void 0!==t&&t.manifestUrl&&null!==(n=this.videoLink)&&void 0!==n&&n.url&&new URL(null===(n=this.videoLink)||void 0===n?void 0:n.manifestUrl).hostname!=new URL(null===(n=this.videoLink)||void 0===n?void 0:n.url).hostname&&delete e.credentials,e}},{key:"filterHeaders",value:function(){var e,t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},n=["x-client-data","referer","user-agent","origin","cache-control","pragma"],r={};for(e in t)n.includes(e)||(r[e]=t[e]);return r}},{key:"directReq",value:(u=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,n){var r,i,o,a,s,u,l,c,d,f,h,p,g,m,y,v,_,b,w,x,S,T;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return r=t.url,i=t.init,o=t.initiator,l=t.type,a=void 0===l?"arrayBuffer":l,c=t.timeout,s=void 0===c?3e4:c,u=new AbortController,l=u.signal,c=setTimeout(function(){return u.abort()},s),e.next=6,fetch(r,_objectSpread(_objectSpread({},i),{},{signal:l})).catch(function(e){if("AbortError"==e.name)throw e;return null});case 6:if(d=e.sent,clearTimeout(c),d&&d.ok){e.next=29;break}if(f=[],[500,520,429,503].includes(null===(p=d)||void 0===p?void 0:p.status))return e.next=13,delay(1e3+2e3*Math.random());e.next=15;break;case 13:e.next=21;break;case 15:return e.next=17,delay(1e3);case 17:return p=(null==i||null===(h=i.headers)||void 0===h?void 0:h.referer)||o||r,e.next=20,updateSessionRules([r],p,{tabIds:[this.currentTab.id]});case 20:f=e.sent;case 21:return c=setTimeout(function(){return u.abort()},s),e.next=24,fetch(r,_objectSpread(_objectSpread({},i),{},{cache:"no-store",headers:this.filterHeaders(i.headers||{}),signal:l}));case 24:if(d=e.sent,clearTimeout(c),d.ok){e.next=29;break}throw f.length&&browser.declarativeNetRequest.updateSessionRules({removeRuleIds:[1]}),new Error("Not ok, change credentials not working ".concat(d.status));case 29:if("arrayBuffer"!=a){e.next=55;break}g=d.body.getReader(),m=[],y=0;case 33:return e.next=36,g.read();case 36:if(v=e.sent,_=v.done,v=v.value,_)return n&&n(100),e.abrupt("break",49);e.next=42;break;case 42:m.push(v.buffer),y+=v.length,_=y/(getSizeFromReceivedHeader(d.headers)||20*y)*100,n&&n(_),e.next=33;break;case 49:for(b=new Uint8Array(y),x=w=0,S=m;x<S.length;x++)T=S[x],b.set(new Uint8Array(T),w),w+=T.byteLength;return e.abrupt("return",b.buffer);case 55:if("json"==a)return e.next=58,d.json();e.next=61;break;case 58:return e.abrupt("return",e.sent);case 61:if("headers"==a||"HEAD"==(null==i?void 0:i.method))return"HEAD"!=(null==i?void 0:i.method)&&u.abort(),e.abrupt("return",responseHeadersToJson(d.headers));e.next=66;break;case 66:return e.next=68,d.text();case 68:return e.abrupt("return",e.sent);case 69:case"end":return e.stop()}},e,this)})),function(e,t){return u.apply(this,arguments)})},{key:"getCredentials",value:(s=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var n,r,i,o,a=this;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return n=t.url,r=t.init,o=t.frameId,i=void 0===o?this.frameId:o,o=function(n){return browser.tabs.sendMessage(a.tabId,_objectSpread(_objectSpread({},n),{},{message:"get-credentials"}),{frameId:i}).catch(function(e){var t;if(0<(null===(t=a.videoLink)||void 0===t?void 0:t.frameId)&&0<i)return a.frameId=0,browser.tabs.sendMessage(a.tabId,_objectSpread(_objectSpread({},n),{},{message:"get-credentials"}),{frameId:0});throw new Error(w.ERRORS.SEND_MESSAGE_ERROR)})},e.next=4,this.remoteRequest({url:n,init:r,type:"json",timeout:5e3},o);case 4:return e.abrupt("return",e.sent);case 5:case"end":return e.stop()}},e,this)})),function(e){return s.apply(this,arguments)})},{key:"contentReq",value:(a=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,n){var r,i,o=this;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return i=t.frameId,r=void 0===i?this.frameId:i,i=function(n){return browser.tabs.sendMessage(o.tabId,_objectSpread(_objectSpread({},n),{},{message:"content-req"}),{frameId:r}).catch(function(e){var t;if(0<(null===(t=o.videoLink)||void 0===t?void 0:t.frameId)&&0<r)return o.frameId=0,browser.tabs.sendMessage(o.tabId,_objectSpread(_objectSpread({},n),{},{message:"content-req"}),{frameId:0});throw new Error(w.ERRORS.SEND_MESSAGE_ERROR)})},e.next=4,this.remoteRequest(_objectSpread(_objectSpread({},t),{},{init:this.filterInitForContent(null==t?void 0:t.init)}),i,n);case 4:return e.abrupt("return",e.sent);case 5:case"end":return e.stop()}},e,this)})),function(e,t){return a.apply(this,arguments)})},{key:"injectReq",value:(o=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,n){var r,i,o=this;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return i=t.frameId,r=void 0===i?this.frameId:i,i=function(n){return browser.tabs.sendMessage(o.tabId,_objectSpread(_objectSpread({},n),{},{message:"inject-req"}),{frameId:r}).catch(function(e){var t;if(0<(null===(t=o.videoLink)||void 0===t?void 0:t.frameId)&&0<r)return o.frameId=0,browser.tabs.sendMessage(o.tabId,_objectSpread(_objectSpread({},n),{},{message:"inject-req"}),{frameId:0});throw new Error(w.ERRORS.SEND_MESSAGE_ERROR)})},e.next=4,this.remoteRequest(_objectSpread(_objectSpread({},t),{},{init:this.filterInitForContent(null==t?void 0:t.init)}),i,n);case 4:return e.abrupt("return",e.sent);case 5:case"end":return e.stop()}},e,this)})),function(e,t){return o.apply(this,arguments)})},{key:"bgReq",value:(i=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,n){var r;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return r=function(e){return sendMessage(_objectSpread(_objectSpread({},e),{},{message:"ajax-request"}))},e.next=3,this.remoteRequest(t,r,n);case 3:return e.abrupt("return",e.sent);case 4:case"end":return e.stop()}},e,this)})),function(e,t){return i.apply(this,arguments)})},{key:"fetchUint8Array",value:(n=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var n,r;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,fetch(t);case 2:if(null!=(n=e.sent)&&n.ok){e.next=6;break}throw URL.revokeObjectURL(t),new Error("Network response was not ok");case 6:return e.next=8,n.arrayBuffer();case 8:return r=e.sent,URL.revokeObjectURL(t),e.abrupt("return",new Uint8Array(r));case 11:case"end":return e.stop()}},e)})),function(e){return n.apply(this,arguments)})},{key:"remoteRequest",value:(r=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,r,S){var i,o,T,a,n,s,k=this;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return i=t.url,o=t.init,T=t.type,n=t.maxChunkSize,a=void 0===n?1048576:n,n=t.timeout,s=void 0===n?3e4:n,e.abrupt("return",new Promise(function(g,m){function t(){browser.runtime.onMessage.removeListener(x),m(new Error("Message request timed out"))}function y(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:5e3;n&&clearTimeout(n),n=setTimeout(t,e)}function v(){n&&(clearTimeout(n),n=null)}var _="ajax-request-".concat(Date.now(),"-").concat(Math.random()),n=null,b=[],w=0,x=function(){var r=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,n,r){var i,o,a,s,u,l,c,d,f,h,p;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(t.message,i=t.data,u=t.blobUrl,o=void 0===u?null:u,a=t.progress,s=t.error,u=t.done,(null==t?void 0:t.uniqueEventName)!==_){e.next=31;break}if(r(),!s){e.next=9;break}browser.runtime.onMessage.removeListener(x),v(),m(new Error(s)),e.next=31;break;case 9:if("arrayBuffer"!=T){e.next=29;break}if(y(),!i){e.next=19;break}l=_toConsumableArray(i.map(function(e){return new Uint8Array(e)})),b.push.apply(b,_toConsumableArray(l)),l=l.reduce(function(e,t){return e+t.byteLength},0),w+=l,e.next=25;break;case 19:if(o)return e.next=22,k.fetchUint8Array(o);e.next=25;break;case 22:l=e.sent,w+=l.byteLength,b.push(l);case 25:if(a&&S&&S(a),1==u||100===a){for(browser.runtime.onMessage.removeListener(x),c=new Uint8Array(w),f=d=0,h=b;f<h.length;f++)p=h[f],c.set(p,d),d+=p.byteLength;v(),g(c.buffer)}e.next=31;break;case 29:v(),g(i);case 31:case"end":return e.stop()}},e)}));return function(e,t,n){return r.apply(this,arguments)}}();browser.runtime.onMessage.addListener(x),r({url:i,type:T,init:o,uniqueEventName:_,maxChunkSize:a}).then(function(){y()}).catch(function(e){v(),m(e)}),y(s)}));case 2:case"end":return e.stop()}},e)})),function(e,t,n){return r.apply(this,arguments)})},{key:"segmentRequest",value:(t=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var n,r,i,o,a,s,u,l,c,d,f=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(n=1<f.length&&void 0!==f[1]?f[1]:null,r=2<f.length&&void 0!==f[2]&&f[2],i=t.url,u=t.type,o=void 0===u?"arrayBuffer":u,isPrivacyCombrUrl(i))try{t.init.headers=updatePrivacyCombrHeaders(i,t.init.headers)}catch(e){}a=Array.from(this.currentReqMethods),s=0,u=a;case 6:if(s<u.length)return l=u[s],e.prev=8,e.next=12,this[l](_objectSpread(_objectSpread({},t),{},{frameId:this.frameId,type:o}),n);e.next=32;break;case 12:if(c=e.sent,r)return e.abrupt("return",{func:l,result:c});e.next=17;break;case 17:return e.abrupt("return",c);case 18:e.next=29;break;case 20:if(e.prev=20,e.t0=e.catch(8),null!==e.t0&&void 0!==e.t0&&null!==(d=e.t0.message)&&void 0!==d&&d.includes(w.ERRORS.SEND_MESSAGE_ERROR)&&["contentReq","injectReq"].includes(l)&&(this.requestMethods=this.currentReqMethods.filter(function(e){return!["contentReq","injectReq"].includes(e)})),a.indexOf(l)==a.length-1)throw new Error("segReq: ".concat((null===e.t0||void 0===e.t0?void 0:e.t0.message)||e.t0));e.next=25;break;case 25:return sendGaEvent("segmentRequest",{url:i,func:l,error:e.t0.message}),e.next=29,delay(1e3);case 29:s++,e.next=6;break;case 32:case"end":return e.stop()}},e,this,[[8,20]])})),function(e){return t.apply(this,arguments)})}]),w}();_defineProperty(ItemDownloader,"REQUEST_METHOD",{CONTENT:"contentReq",INJECT:"injectReq",DIRECT:"directReq",BG:"bgReq"}),_defineProperty(ItemDownloader,"ERRORS",{SEND_MESSAGE_ERROR:"sendMessage to tab error"});var BgDownloader=function(){"use strict";_inherits(t,LinkDownloader);var e=_createSuper(t);function t(){return _classCallCheck(this,t),e.apply(this,arguments)}return _createClass(t,[{key:"download",value:function(e,t,n){var r=e.title;return new RegExp("."+e.ext+"$").test(e.title)||(r=e.title+"."+e.ext),n.key=t,_backgroundDownload2(e.download_url,r,n)}}]),t}();window.Downloading=(_class5=function(){"use strict";function a(){var n=this;_classCallCheck(this,a),_defineProperty(this,"downloadQueue",[]),_defineProperty(this,"currentDownload",null),_defineProperty(this,"videoLinks",{}),_defineProperty(this,"mergeQueue",[]),_defineProperty(this,"downloadingUi",null),_defineProperty(this,"autoClose",!1),_defineProperty(this,"concurrentDownload",!0),_defineProperty(this,"concurrentDownloads",0),_defineProperty(this,"currentTab",null),_defineProperty(this,"directory",null),_defineProperty(this,"calculateSegmentDuration",function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:.5,t=t/1073741824;if(t<1)return null;n=Math.max(2,Math.ceil(t/n));return Math.max(60,Math.ceil(e/n))}),_defineProperty(this,"mountFfmpegSrcDir",function(){var t=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,n.ffmpeg.listDir("/");case 2:if(e.sent.find(function(e){return"mounted"===e.name})){e.next=6;break}return e.next=6,n.ffmpeg.createDir("/mounted");case 6:return e.next=8,n.ffmpeg.mount("WORKERFS",{files:t},"/mounted");case 8:case"end":return e.stop()}},e)}));return function(e){return t.apply(this,arguments)}}()),this.pageType=this.getPageType(),this.init()}var t,i,n,r,e,o,s,u,l,c,d,f,h,p,g,m,y,v,_;return _createClass(a,[{key:"init",value:(_=_asyncToGenerator(_regeneratorRuntime().mark(function e(){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:localizeHtmlPage(),this.initListener(),this.createUi(),this.setAutoClose(),this.clearOpfsFilesOnInit();case 5:case"end":return e.stop()}},e,this)})),function(){return _.apply(this,arguments)})},{key:"clearOpfsFilesOnInit",value:(v=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t,n,r=this;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,navigator.storage.estimate();case 2:if(t=e.sent,null!=(t=t.usageDetails)&&t.fileSystem)return e.next=7,navigator.storage.getDirectory();e.next=27;break;case 7:if(n=e.sent,this.pageType==a.PAGE_TYPES.CONTENT_PAGE)return e.next=11,sendMessage({message:"get-tab-info"});e.next=17;break;case 11:return this.currentTab=e.sent,e.next=14,DownloadBuffers.clearTabTempFiles(this.currentTab.id,n);case 14:setTimeout(function(){r.clearClosedTabFiles(n),DownloadBuffers.clearOldTempFiles(n)},1e3),e.next=27;break;case 17:if(this.pageType!=a.PAGE_TYPES.CONTENT_IFRAME){e.next=21;break}setTimeout(function(){r.clearClosedTabFiles(n),DownloadBuffers.clearOldTempFiles(n)},1e3),e.next=27;break;case 21:return e.next=23,browser.tabs.getCurrent();case 23:return this.currentTab=e.sent,e.next=26,DownloadBuffers.clearTabTempFiles(this.currentTab.id,n);case 26:setTimeout(function(){DownloadBuffers.clearOldTempFiles(n)},1e3);case 27:case"end":return e.stop()}},e,this)})),function(){return v.apply(this,arguments)})},{key:"getPageType",value:function(){return location.host==browser.runtime.id?window.self==window.top?a.PAGE_TYPES.EXT_PAGE:a.PAGE_TYPES.EXT_IFRAME:window.self==window.top?a.PAGE_TYPES.CONTENT_PAGE:a.PAGE_TYPES.CONTENT_IFRAME}},{key:"setAutoClose",value:(y=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:try{t=new URL(location.href).searchParams.get("t"),!inIframe()&&t&&(this.autoClose=!0)}catch(e){}case 1:case"end":return e.stop()}},e,this)})),function(){return y.apply(this,arguments)})},{key:"initListener",value:function(){var i=this;browser.runtime.onMessage.addListener(function(e,t,n){var r;if("downloading-download-video-stream"===e.message)return null!=e&&e.videoLink&&i.isValidVideolink(e.videoLink)?(n({result:1,key:r=e.key||"sid-".concat(Date.now(),"-").concat(Math.random()),msg:""}),i.download(e.videoLink,r,e.options)):n({result:-1,msg:"Invalid argument"}),!0})}},{key:"stateListener",value:function(e,t){var n,r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:"";try{this.updateStateUi(e,t,r),null!==(n=this.videoLinks)&&void 0!==n&&n[e]&&(this.videoLinks[e].state=t,this.videoLinks[e].errmsg=r,sendMessage({message:"download-changed",key:this.videoLinks[e].key,state:t,errmsg:r}))}catch(e){}}},{key:"progressListener",value:function(e,t){var n,r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{};try{this.updateProgressUi(e,t,r),null!==(n=this.videoLinks)&&void 0!==n&&n[e]&&(this.videoLinks[e].progress=t,this.videoLinks[e].extra=r,sendMessage({message:"progress-changed",key:this.videoLinks[e].key,progress:t}))}catch(e){}}},{key:"createUi",value:function(){inIframe()||(this.downloadingUi=new DownloadingUi,this.downloadingUi.createUi())}},{key:"createListItemUi",value:function(e,t,n){var r;null===(r=this.downloadingUi)||void 0===r||r.createListItem(e,t,n)}},{key:"deleteListItemUi",value:function(e){var t;null===(t=this.downloadingUi)||void 0===t||t.deleteListItem(e)}},{key:"updateStateUi",value:function(e,t,n){var r;null===(r=this.downloadingUi)||void 0===r||r.updateState(e,t,n)}},{key:"updateProgressUi",value:function(e,t){var n,r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{};null===(n=this.downloadingUi)||void 0===n||n.updateProgress(e,t,r)}},{key:"isValidVideolink",value:function(e){return null!=e&&e.streamType?this.isValidPlaylist(e):!(null==e||!e.download_url)}},{key:"isValidPlaylist",value:function(e){if(!e.url&&!e.segments)return!1;if(e.segments){var t,n=_createForOfIteratorHelper(e.segments);try{for(n.s();!(t=n.n()).done;)if(!t.value.url)return!1}catch(e){n.e(e)}finally{n.f()}}return!0}},{key:"loadFfmpeg",value:(m=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var i=this;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(this.ffmpeg||(this.ffmpeg=new FFmpegWASM.FFmpeg,this.ffmpeg.on("log",function(e){e.type;var t,n,r=e.message;null===(n=i.mergingInfo)||void 0===n||!n.downloaderList||null!==(t=i.mergingInfo)&&void 0!==t&&t.downloaderList[0].duration||(e=r.match(/Duration: (\d{2}):(\d{2}):(\d{2}\.\d{2})/))&&(t=(n=_slicedToArray(e,4))[1],e=n[2],n=n[3],n=3600*parseFloat(t)+60*parseFloat(e)+parseFloat(n),i.mergingInfo.downloaderList[0].duration=n),i.getAudioCodec&&i.getAudioCodec(r)}),this.ffmpeg.on("progress",function(e){var t,n=e.progress,e=(e.time,n);null!=i&&null!==(t=i.mergingInfo)&&void 0!==t&&t.downloadId&&(i.mergingInfo.totalSegments&&(n=(i.mergingInfo.segmentIndex+n)/i.mergingInfo.totalSegments),e=i.mergingInfo.segmentIndex+(e<1?0:1),i.progressListener(null==i?void 0:i.mergingInfo.downloadId,Math.floor(100*n),{completedSegs:e,totalSegs:i.mergingInfo.totalSegments}))})),0==this.ffmpeg.loaded)return e.next=4,this.ffmpeg.load({coreURL:"/js/core.js",classWorkerURL:"/js/worker.js"});e.next=4;break;case 4:case"end":return e.stop()}},e,this)})),function(){return m.apply(this,arguments)})},{key:"showMergefailedFailedModal",value:function(e){var t="".concat(browser.i18n.getMessage("MergeAudioFailedDesc1").replace("{{1}}",'"'.concat(e.title,'"'))),n="".concat(browser.i18n.getMessage("MergeAudioFailedRetry"));try{showModal('\n                <h1 style="text-align:center;">'.concat(browser.i18n.getMessage("MergeAudioFailed"),"</h1>\n                <p>").concat(t,"</p>\n                <p>").concat(n,"</p>\n                "))}catch(e){sendMessage({message:"send-notification",content:{type:"basic",title:browser.i18n.getMessage("MergeAudioFailed"),message:"".concat(t,"\n").concat(n)}})}}},{key:"sendNotification",value:function(e,t){t={type:"basic",title:e,message:t,iconUrl:"/images/logo.png"};browser.notifications.create(t)}},{key:"processDownloadQueue",value:(g=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t,n=this;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:t=_regeneratorRuntime().mark(function e(){var t;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(n.currentDownload){e.next=17;break}return n.currentDownload=n.downloadQueue.shift(),e.prev=2,e.next=5,n.startDownload(n.currentDownload);case 5:t=n.currentDownload,setTimeout(function(){return n.deleteListItemUi(t)},3e3),e.next=12;break;case 9:e.prev=9,e.t0=e.catch(2);case 12:return e.prev=12,n.currentDownload=null,e.finish(12);case 15:e.next=18;break;case 17:return e.abrupt("return",1);case 18:case"end":return e.stop()}},e,null,[[2,9,12,15]])});case 1:if(this.downloadQueue.length)return e.delegateYield(t(),"t0",3);e.next=7;break;case 3:if(e.t0)return e.abrupt("break",7);e.next=5;break;case 5:e.next=1;break;case 7:setTimeout(function(){n.autoClose&&0==n.downloadQueue.length&&window.close()},3e3);case 8:case"end":return e.stop()}},e,this)})),function(){return g.apply(this,arguments)})},{key:"enqueueVideoLink",value:function(e){this.downloadQueue.push(e),this.processDownloadQueue()}},{key:"pickDirectory",value:(p=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var i,o,a=this,t=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return i=0<t.length&&void 0!==t[0]?t[0]:"readwrite",o=1<t.length&&void 0!==t[1]?t[1]:"downloads",e.abrupt("return",new Promise(function(n){function r(){return(r=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,n){var r;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(r={mode:n?"readwrite":"readonly",startIn:o},t.requestPermission&&t.queryPermission)return e.next=4,t.queryPermission(r);e.next=13;break;case 4:if(e.t0=e.sent,"granted"===e.t0)return e.abrupt("return",!0);e.next=7;break;case 7:return e.next=9,t.requestPermission(r);case 9:if(e.t1=e.sent,"granted"===e.t1)return e.abrupt("return",!0);e.next=12;break;case 12:return e.abrupt("return",!1);case 13:return e.abrupt("return",!0);case 14:case"end":return e.stop()}},e)}))).apply(this,arguments)}(function(){var e=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,window.showDirectoryPicker({mode:i});case 3:return t=e.sent,e.next=6,function(){return r.apply(this,arguments)}(t,i);case 6:if(!e.sent){e.next=11;break}a.directory=t,n(!0),e.next=12;break;case 11:n(!1);case 12:e.next=17;break;case 14:e.prev=14,e.t0=e.catch(0),n(!1);case 17:case"end":return e.stop()}},e,null,[[0,14]])}));return function(){return e.apply(this,arguments)}})()()}));case 3:case"end":return e.stop()}},e)})),function(){return p.apply(this,arguments)})},{key:"pickOpfsDirectory",value:(h=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var r=this;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",new Promise(function(n){(function(){var e=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,navigator.storage.getDirectory();case 2:t=e.sent,r.directory=t,n();case 5:case"end":return e.stop()}},e)}));return function(){return e.apply(this,arguments)}})()()}));case 1:case"end":return e.stop()}},e)})),function(){return h.apply(this,arguments)})},{key:"getHostTabsByRemote",value:(f=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t,n=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t=0<n.length&&void 0!==n[0]?n[0]:null,e.next=3,browser.runtime.sendMessage(_objectSpread({message:"get-tab-info-by-host"},t?{host:t}:{}));case 3:return e.abrupt("return",e.sent);case 4:case"end":return e.stop()}},e)})),function(){return f.apply(this,arguments)})},{key:"getHostTabsDirect",value:(d=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t,n,r=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if((n=0<r.length&&void 0!==r[0]?r[0]:null)||this.currentTab)return n=new URL(n||(null==this||null===(t=this.currentTab)||void 0===t?void 0:t.url)).host,e.next=5,browser.tabs.query({url:"*://".concat(n,"/*")});e.next=6;break;case 5:return e.abrupt("return",e.sent);case 6:return e.abrupt("return",null);case 7:case"end":return e.stop()}},e,this)})),function(){return d.apply(this,arguments)})},{key:"clearClosedTabFiles",value:(c=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t,n,r,i,o=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(t=0<o.length&&void 0!==o[0]?o[0]:null,n=1<o.length&&void 0!==o[1]?o[1]:null,e.prev=2,r=null,this.pageType==a.PAGE_TYPES.CONTENT_PAGE||this.pageType==a.PAGE_TYPES.CONTENT_IFRAME)return e.next=7,this.getHostTabsByRemote(t);e.next=10;break;case 7:r=e.sent,e.next=13;break;case 10:return e.next=12,this.getHostTabsDirect(t);case 12:r=e.sent;case 13:if(r&&Array.isArray(r)){e.next=16;break}return e.abrupt("return");case 16:return i=r.map(function(e){return e.id}),e.next=19,DownloadBuffers.clearFilesExceptActiveTabs(i,n);case 19:e.next=25;break;case 22:e.prev=22,e.t0=e.catch(2);case 25:case"end":return e.stop()}},e,this,[[2,22]])})),function(){return c.apply(this,arguments)})},{key:"download",value:(l=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,n,r){var i,o,a;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(a=r.skipBgDl,i=void 0!==a&&a,this.currentTab){e.next=5;break}return e.next=4,browser.tabs.getCurrent();case 4:this.currentTab=e.sent;case 5:if(o="did-".concat(Date.now(),"-").concat(Math.random()),this.videoLinks[o]={videoLink:t,key:n,downloads:{},state:"",current:"video",progress:0,options:r},this.createListItemUi(o,t,(null==r?void 0:r.tabUrl)||""),0==i)return this.videoLinks[o].downloads.linkDownloader=new BgDownloader(this.stateListener.bind(this),this.progressListener.bind(this)),e.next=12,this.videoLinks[o].downloads.linkDownloader.download(t,o,{tabId:-1});e.next=19;break;case 12:if(0<(null==(a=e.sent)?void 0:a.result))return e.abrupt("return");e.next=17;break;case 17:this.videoLinks[o].downloads.linkDownloader.dispose(),delete this.videoLinks[o].downloads.linkDownloader;case 19:if(this.concurrentDownload){e.next=23;break}this.enqueueVideoLink(o),e.next=28;break;case 23:return this.concurrentDownloads++,e.next=26,this.startDownload(o);case 26:this.concurrentDownloads--,0==this.concurrentDownloads&&this.autoClose&&window.close();case 28:case"end":return e.stop()}},e,this)})),function(e,t,n){return l.apply(this,arguments)})},{key:"startDownload",value:(u=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var n,r,i,o,a,s,u,l,c=this;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return s=this.videoLinks[t],n=s.videoLink,r=s.options,i=s.downloads,o=genUniqueId(),a=function(e){return{currentTab:c.currentTab,directory:c.directory,title:e.title,extension:e.streamType?"audio"==e.type?"mp3":"mp4":(null==e?void 0:e.ext)||"mp4",tabId:r.tabId,tabUrl:r.tabUrl,uniqueId:o,stateListener:c.stateListener.bind(c),progressListener:c.progressListener.bind(c)}},this.stateListener(t,ItemDownloader.STATE.START),e.prev=4,this.videoLinks[t].state="unknown"!=(null==n?void 0:n.type)?"Video":"Media",null!=i&&i.videoDownloader||(i.videoDownloader=new ItemDownloader(a(n))),e.next=9,i.videoDownloader.download(n,t);case 9:if(n.audioLink)return e.next=12,this.loadFfmpeg();e.next=42;break;case 12:return e.prev=12,this.videoLinks[t].state="Audio",null!=i&&i.audioDownloader||(null!==(s=n.audioLink)&&void 0!==s&&s.title||(n.audioLink.title=n.title),i.audioDownloader=new ItemDownloader(a(n.audioLink))),e.next=17,i.audioDownloader.download(n.audioLink,t);case 17:return this.videoLinks[t].state="Merge",e.next=20,new DownloadBuffers({directory:this.directory,tabId:r.tabId,tabUrl:r.tabUrl,uniqueId:o,title:"".concat(n.title,"-result"),extension:"mp4"});case 20:return i.resultBuffers=e.sent,e.next=23,i.resultBuffers.init(i.videoDownloader.buffers.getSize()+i.audioDownloader.buffers.getSize());case 23:return e.next=25,this.queueMergeVideo({downloaderList:[i.videoDownloader,i.audioDownloader],downloadId:t,resultBuffers:i.resultBuffers});case 25:return e.next=27,this._download(i.resultBuffers,n);case 27:this.stateListener(t,ItemDownloader.STATE.COMPLETE),e.next=40;break;case 30:return e.prev=30,e.t0=e.catch(12),u="".concat(browser.i18n.getMessage("MergeAudioFailedDesc1").replace("{{1}}",'"'.concat(n.title,'"')),"\n").concat(browser.i18n.getMessage("MergeAudioFailedRetry")),this.sendNotification(browser.i18n.getMessage("MergeAudioFailed"),u),this.stateListener(t,ItemDownloader.STATE.COMPLETE),e.next=38,this._download(i.videoDownloader.buffers,n);case 38:u=(null===e.t0||void 0===e.t0?void 0:e.t0.message)||e.t0,reportMsg("startDl MergeAudio: ".concat(null==r?void 0:r.tabUrl," ").concat(u," ").concat(i.videoDownloader.buffers.bufferType," ").concat(formatBytes(i.videoDownloader.buffers.bufferSize)," ").concat(i.audioDownloader.buffers.bufferType," ").concat(formatBytes(i.audioDownloader.buffers.bufferSize)));case 40:e.next=69;break;case 42:if(i.videoDownloader.isTs&&"video"==n.type)return e.prev=43,e.next=46,this.loadFfmpeg();e.next=66;break;case 46:return e.next=48,new DownloadBuffers({directory:this.directory,tabId:r.tabId,tabUrl:r.tabUrl,uniqueId:o,title:"".concat(n.title,"-result"),extension:"mp4"});case 48:return i.resultBuffers=e.sent,e.next=51,i.resultBuffers.init(i.videoDownloader.buffers.getSize());case 51:return e.next=53,this.queueMergeVideo({downloaderList:[i.videoDownloader],downloadId:t,resultBuffers:i.resultBuffers});case 53:return e.next=55,this._download(i.resultBuffers,n);case 55:e.next=64;break;case 57:return e.prev=57,e.t1=e.catch(43),e.next=62,this._download(i.videoDownloader.buffers,n);case 62:l=(null===e.t1||void 0===e.t1?void 0:e.t1.message)||e.t1,reportMsg("startDl mergTs: ".concat(null==r?void 0:r.tabUrl," ").concat(l));case 64:e.next=68;break;case 66:return e.next=68,this._download(i.videoDownloader.buffers,n);case 68:this.stateListener(t,ItemDownloader.STATE.COMPLETE);case 69:e.next=77;break;case 71:e.prev=71,e.t2=e.catch(4),this.stateListener(t,ItemDownloader.STATE.FAILED),l=(null===e.t2||void 0===e.t2?void 0:e.t2.message)||e.t2,reportMsg("startDl error: ".concat(null==r?void 0:r.tabUrl," ").concat(l));case 77:return e.prev=77,i.videoDownloader&&(i.videoDownloader.buffers.deleteFiles(),delete i.videoDownloader),i.audioDownloader&&(i.audioDownloader.buffers.deleteFiles(),delete i.audioDownloader),i.resultBuffers&&i.resultBuffers.deleteFiles(),e.finish(77);case 82:case"end":return e.stop()}},e,this,[[4,71,77,82],[12,30],[43,57]])})),function(e){return u.apply(this,arguments)})},{key:"_download",value:(s=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,n){var r,i,o,a,s;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return r="video/mp4",i="mp4",n.streamType?"audio"==n.type&&(r="audio/mp3",i="mp3"):r=n.type+"/"+((null==n?void 0:n.ext)||"mp4"),e.t0=URL,e.next=6,t.getFile(r);case 6:if(e.t1=e.sent,o=e.t0.createObjectURL.call(e.t0,e.t1),a=sanitizeFilename(n.title,120)+"."+((null==n?void 0:n.ext)||i),"isBrave"==(null===(s=navigator)||void 0===s||null===(s=s.brave)||void 0===s||null===(s=s.isBrave)||void 0===s?void 0:s.name))return _linkClickDownload(o,a),e.next=13,delay(1e4);e.next=16;break;case 13:URL.revokeObjectURL(o),e.next=23;break;case 16:return e.next=18,_backgroundDownload2(o,a);case 18:return 0<(null==(s=e.sent)?void 0:s.result)||_linkClickDownload(o,a),e.next=22,delay(1e3);case 22:URL.revokeObjectURL(o);case 23:case"end":return e.stop()}},e)})),function(e,t){return s.apply(this,arguments)})},{key:"queueMergeVideo",value:(o=_asyncToGenerator(_regeneratorRuntime().mark(function e(r){var i=this;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",new Promise(function(){var n=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,n){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(i.mergeQueue.push({params:r,resolve:t,reject:n}),i.mergeInProgress){e.next=5;break}return e.next=5,i.processMergeQueue();case 5:case"end":return e.stop()}},e)}));return function(e,t){return n.apply(this,arguments)}}()));case 1:case"end":return e.stop()}},e)})),function(e){return o.apply(this,arguments)})},{key:"processMergeQueue",value:(e=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t,n,r;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(0<this.mergeQueue.length)return this.mergeInProgress=!0,n=this.mergeQueue.shift(),r=n.params,t=n.resolve,n=n.reject,e.prev=3,this.mergingInfo=r,e.next=7,this.mergeVideo(r);e.next=21;break;case 7:r=e.sent,this.mergingInfo=null,t(r),e.next=15;break;case 12:e.prev=12,e.t0=e.catch(3),n(e.t0);case 15:return e.prev=15,e.next=18,this.processMergeQueue();case 18:return e.finish(15);case 19:e.next=23;break;case 21:this.mergeInProgress=!1,this.mergingInfo=null;case 23:case"end":return e.stop()}},e,this,[[3,12,15,19]])})),function(){return e.apply(this,arguments)})},{key:"mergeArrayBuffers",value:function(e){var t=e.reduce(function(e,t){return e+t.byteLength},0),n=new Uint8Array(t),r=0;return e.forEach(function(e){n.set(new Uint8Array(e),r),r+=e.byteLength}),n}},{key:"mergeVideo_old",value:(r=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var n,r,i;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return n=t.videoDownloader,r=t.audioDownloader,n=n.buffers,r=r.buffers,n=this.mergeArrayBuffers(n),r=this.mergeArrayBuffers(r),e.next=7,this.ffmpeg.writeFile("input_video.mp4",n);case 7:return e.next=9,this.ffmpeg.writeFile("input_audio.mp3",r);case 9:return e.next=11,this.ffmpeg.exec(["-i","input_video.mp4","-i","input_audio.mp3","-c","copy","output.mp4"]);case 11:return e.next=13,this.ffmpeg.readFile("output.mp4");case 13:return i=e.sent,e.next=16,this.ffmpeg.deleteFile("input_video.mp4");case 16:return e.next=18,this.ffmpeg.deleteFile("input_audio.mp3");case 18:return e.next=20,this.ffmpeg.deleteFile("output.mp4");case 20:return e.abrupt("return",i);case 21:case"end":return e.stop()}},e,this)})),function(e){return r.apply(this,arguments)})},{key:"fileMerge",value:(n=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,n){var r,i;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return(r=t.flatMap(function(e){return["-i","/mounted/".concat(e.name)]})).push("-c","copy","output.mp4"),e.next=4,this.ffmpeg.exec(r);case 4:return e.next=6,this.ffmpeg.readFile("output.mp4");case 6:return i=e.sent,e.next=9,this.ffmpeg.deleteFile("output.mp4");case 9:return e.next=11,n.push(0,i);case 11:return e.abrupt("return",n);case 12:case"end":return e.stop()}},e,this)})),function(e,t){return n.apply(this,arguments)})},{key:"segmentMerge",value:(i=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,n,r,i){var o,a,s,u,l,c,d,f,h,p,g,m,y,v,_;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return o=this.mergingInfo.downloaderList[0].videoLink.segments,a=!(null===(y=o[0])||void 0===y||!y.duration),v=1<t.length?t[1]:t[0],s=null,y=function(e){null!=s||(e=e.match(/Audio:\s+(\w+)/))&&e[1]&&(s=e[1].toLowerCase())},this.getAudioCodec=y,e.next=8,this.ffmpeg.exec(["-v","debug","-i","/mounted/".concat(v.name)]);case 8:if(this.getAudioCodec=null,a){for(c=[],h=f=d=0;h<o.length;h++)d+=o[h].duration,(r<=d||h===o.length-1)&&(c.push({startTime:o.slice(0,f).reduce(function(e,t){return e+t.duration},0),duration:d}),d=0,f=h+1);u=c.length,l=function(e){return c[e].startTime}}else u=Math.ceil(n/r),l=function(e,t){return 0===e?0:t.getNextSegmentStartTime()};p=new MP4Processor(n),this.mergingInfo.totalSegments=u,g=["-c:v","copy","-f","mp4","-movflags","+frag_keyframe+empty_moov+default_base_moof","segment.mp4"],"aac"===s?g.unshift("-bsf:a","aac_adtstoasc"):g.unshift("-c:a","copy"),m=0;case 16:if(m<u)return this.mergingInfo.segmentIndex=m,y=l(m,p),v=Math.min(r,n-y),(_=t.flatMap(function(e){return["-i","/mounted/".concat(e.name)]})).push.apply(_,["-ss",y.toString(),"-t",v.toString()].concat(g)),e.next=24,this.ffmpeg.exec(_);e.next=36;break;case 24:return e.next=26,this.ffmpeg.readFile("segment.mp4");case 26:return _=e.sent,_=p.processMP4Segment(_,m),e.next=30,i.push(m,_);case 30:return e.next=32,this.ffmpeg.deleteFile("segment.mp4");case 32:case 33:m++,e.next=16;break;case 36:return e.abrupt("return",i);case 37:case"end":return e.stop()}},e,this)})),function(e,t,n,r){return i.apply(this,arguments)})},{key:"mergeVideo",value:(t=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var n,r,i,o,a,s;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return n=t.downloaderList,r=t.resultBuffers,e.prev=1,e.next=4,Promise.all(n.map(function(e){return e.buffers.getFile()}));case 4:return i=e.sent,e.next=7,this.mountFfmpegSrcDir(i);case 7:if(n[0].duration){e.next=10;break}return e.next=10,this.ffmpeg.exec(["-v","debug","-i","/mounted/".concat(i[0].name)]);case 10:if(s=n.reduce(function(e,t){return e+t.buffers.getSize()},0),o=n[0].duration,a=this.calculateSegmentDuration(o,s),s=Date.now(),a){e.next=19;break}return e.next=17,this.fileMerge(i,r);case 17:e.next=21;break;case 19:return e.next=21,this.segmentMerge(i,o,a,r);case 21:new Date(Date.now()-s);case 23:return e.prev=23,e.prev=24,e.next=27,this.ffmpeg.unmount("/mounted");case 27:e.next=32;break;case 29:e.prev=29,e.t0=e.catch(24);case 32:return e.finish(23);case 33:return e.abrupt("return",r);case 34:case"end":return e.stop()}},e,this,[[1,,23,33],[24,29]])})),function(e){return t.apply(this,arguments)})}]),a}(),_defineProperty(_class5,"PAGE_TYPES",{EXT_PAGE:"extensionPage",EXT_IFRAME:"extensionIframe",CONTENT_PAGE:"contentPage",CONTENT_IFRAME:"contentIframe"}),_class5),window.downloading=new Downloading;