if(window.ajaxgetListener||(window.ajaxgetListener=!0,window.addEventListener("message",(async e=>{if(e.source!==window||"ajax-get"!==e.data.action)return;const{url:t,type:n,init:a,uniqueEventName:o,timeout:i=1e4}=e.data,s=new AbortController,r=s.signal,u=setTimeout((()=>{s.abort()}),i);try{const e=await fetch(t,{...a,signal:r});clearTimeout(u);let i="";i="arrayBuffer"==n?await e.arrayBuffer():"json"==n?await e.json():await e.text(),window.postMessage({action:"ajax-response",uniqueEventName:o,data:i},"*")}catch(e){window.postMessage({action:"ajax-response",uniqueEventName:o,error:e.message},"*")}}))),!window.ajaxrequestListener){function getRangeInfo(e){const t=e.split(" ");if(2===t.length){const e=t[1].split("/");if(2===e.length){const t=parseInt(e[1]);if(t)return{chunk:e[0],total:t}}}return null}function getSizeFromReceivedHeader(e){let t=e.get("content-length",null),n=e.get("content-range",null);if(t)return parseInt(t);if(n){let e=getRangeInfo(n);if(e)return e.total}return null}window.ajaxrequestListener=!0,window.addEventListener("ajax-request",(async e=>{const{url:t,type:n,init:a,uniqueEventName:o,maxChunkSize:i,timeout:s=3e4}=e.detail,r=new AbortController,u=r.signal;let d=setTimeout((()=>r.abort()),s);const l=e=>new Promise((t=>setTimeout(t,e)));try{let e=await fetch(t,{...a,signal:u}).catch((e=>{if("AbortError"==e.name)throw e;return null}));if(clearTimeout(d),!(e&&e.ok||([500,520,429,503].includes(e?.status)?await l(1e3+2e3*Math.random()):await l(1e3),d=setTimeout((()=>r.abort()),s),e=await fetch(t,{...a,credentials:"include",signal:u}),clearTimeout(d),e.ok)))return void window.dispatchEvent(new CustomEvent("ajax-response",{detail:{uniqueEventName:o,error:`Not ok, change credentials not working ${e.status}`}}));if("arrayBuffer"==n){window.dispatchEvent(new CustomEvent("ajax-response",{detail:{uniqueEventName:o,data:null,progress:0,done:!1}}));const t=e.body.getReader();let n=[],a=0,s=0;for(;;){const{done:r,value:u}=await t.read();if(r)break;if(n.push(u.buffer),a+=u.length,s+=u.length,a>=i){const t=s/(getSizeFromReceivedHeader(e.headers)||20*s)*100;window.dispatchEvent(new CustomEvent("ajax-response",{detail:{uniqueEventName:o,data:n,progress:t,done:r}})),n=[],a=0}}if(n.length>0){const t=s/(e.headers.get("content-length")||s)*100;window.dispatchEvent(new CustomEvent("ajax-response",{detail:{uniqueEventName:o,data:n,progress:t,done:!0}}))}}else"json"==n?window.dispatchEvent(new CustomEvent("ajax-response",{detail:{uniqueEventName:o,data:await e.json()}})):"headers"==n||"HEAD"==a?.method?("HEAD"!=a?.method&&r.abort(),window.dispatchEvent(new CustomEvent("ajax-response",{detail:{uniqueEventName:o,data:(function(e){const t={};for(const[n,a]of e.entries())t[n]=a;return t})(e.headers)}}))):window.dispatchEvent(new CustomEvent("ajax-response",{detail:{uniqueEventName:o,data:await e.text()}}))}catch(e){window.dispatchEvent(new CustomEvent("ajax-response",{detail:{uniqueEventName:o,error:e.message}}))}}))}