var _class;function _createSuper(n){var r=_isNativeReflectConstruct();return function(){var e,t=_getPrototypeOf(n);return _possibleConstructorReturn(this,r?(e=_getPrototypeOf(this).constructor,Reflect.construct(t,arguments,e)):t.apply(this,arguments))}}function _possibleConstructorReturn(e,t){if(t&&("object"===_typeof(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return _assertThisInitialized(e)}function _assertThisInitialized(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(<PERSON><PERSON><PERSON>,[],function(){})),!0}catch(e){return!1}}function _getPrototypeOf(e){return(_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function _regeneratorRuntime(){"use strict";_regeneratorRuntime=function(){return a};var l,a={},e=Object.prototype,c=e.hasOwnProperty,u=Object.defineProperty||function(e,t,n){e[t]=n.value},t="function"==typeof Symbol?Symbol:{},r=t.iterator||"@@iterator",n=t.asyncIterator||"@@asyncIterator",i=t.toStringTag||"@@toStringTag";function o(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{o({},"")}catch(l){o=function(e,t,n){return e[t]=n}}function s(e,t,n,r){var i,o,a,s,t=t&&t.prototype instanceof v?t:v,t=Object.create(t.prototype),r=new S(r||[]);return u(t,"_invoke",{value:(i=e,o=n,a=r,s=f,function(e,t){if(s===h)throw new Error("Generator is already running");if(s===g){if("throw"===e)throw t;return{value:l,done:!0}}for(a.method=e,a.arg=t;;){var n=a.delegate;if(n){var r=function e(t,n){var r=n.method,i=t.iterator[r];if(i===l)return n.delegate=null,"throw"===r&&t.iterator.return&&(n.method="return",n.arg=l,e(t,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),m;i=d(i,t.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,m;var i=i.arg;return i?i.done?(n[t.resultName]=i.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=l),n.delegate=null,m):i:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,m)}(n,a);if(r){if(r===m)continue;return r}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if(s===f)throw s=g,a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);s=h;r=d(i,o,a);if("normal"===r.type){if(s=a.done?g:p,r.arg===m)continue;return{value:r.arg,done:a.done}}"throw"===r.type&&(s=g,a.method="throw",a.arg=r.arg)}})}),t}function d(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}a.wrap=s;var f="suspendedStart",p="suspendedYield",h="executing",g="completed",m={};function v(){}function y(){}function b(){}var w={};o(w,r,function(){return this});t=Object.getPrototypeOf,t=t&&t(t(C([])));t&&t!==e&&c.call(t,r)&&(w=t);var _=b.prototype=v.prototype=Object.create(w);function x(e){["next","throw","return"].forEach(function(t){o(e,t,function(e){return this._invoke(t,e)})})}function k(a,s){var t;u(this,"_invoke",{value:function(n,r){function e(){return new s(function(e,t){!function t(e,n,r,i){e=d(a[e],a,n);if("throw"!==e.type){var o=e.arg,n=o.value;return n&&"object"==_typeof(n)&&c.call(n,"__await")?s.resolve(n.__await).then(function(e){t("next",e,r,i)},function(e){t("throw",e,r,i)}):s.resolve(n).then(function(e){o.value=e,r(o)},function(e){return t("throw",e,r,i)})}i(e.arg)}(n,r,e,t)})}return t=t?t.then(e,e):e()}})}function T(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function E(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function S(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(T,this),this.reset(!0)}function C(t){if(t||""===t){var e=t[r];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,e=function e(){for(;++n<t.length;)if(c.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=l,e.done=!0,e};return e.next=e}}throw new TypeError(_typeof(t)+" is not iterable")}return u(_,"constructor",{value:y.prototype=b,configurable:!0}),u(b,"constructor",{value:y,configurable:!0}),y.displayName=o(b,i,"GeneratorFunction"),a.isGeneratorFunction=function(e){e="function"==typeof e&&e.constructor;return!!e&&(e===y||"GeneratorFunction"===(e.displayName||e.name))},a.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,b):(e.__proto__=b,o(e,i,"GeneratorFunction")),e.prototype=Object.create(_),e},a.awrap=function(e){return{__await:e}},x(k.prototype),o(k.prototype,n,function(){return this}),a.AsyncIterator=k,a.async=function(e,t,n,r,i){void 0===i&&(i=Promise);var o=new k(s(e,t,n,r),i);return a.isGeneratorFunction(t)?o:o.next().then(function(e){return e.done?e.value:o.next()})},x(_),o(_,i,"Generator"),o(_,r,function(){return this}),o(_,"toString",function(){return"[object Generator]"}),a.keys=function(e){var t,n=Object(e),r=[];for(t in n)r.push(t);return r.reverse(),function e(){for(;r.length;){var t=r.pop();if(t in n)return e.value=t,e.done=!1,e}return e.done=!0,e}},a.values=C,S.prototype={constructor:S,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=l,this.done=!1,this.delegate=null,this.method="next",this.arg=l,this.tryEntries.forEach(E),!e)for(var t in this)"t"===t.charAt(0)&&c.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=l)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(n){if(this.done)throw n;var r=this;function e(e,t){return o.type="throw",o.arg=n,r.next=e,t&&(r.method="next",r.arg=l),!!t}for(var t=this.tryEntries.length-1;0<=t;--t){var i=this.tryEntries[t],o=i.completion;if("root"===i.tryLoc)return e("end");if(i.tryLoc<=this.prev){var a=c.call(i,"catchLoc"),s=c.call(i,"finallyLoc");if(a&&s){if(this.prev<i.catchLoc)return e(i.catchLoc,!0);if(this.prev<i.finallyLoc)return e(i.finallyLoc)}else if(a){if(this.prev<i.catchLoc)return e(i.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return e(i.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;0<=n;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&c.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var i=r;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=e,o.arg=t,i?(this.method="next",this.next=i.finallyLoc,m):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),m},finish:function(e){for(var t=this.tryEntries.length-1;0<=t;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),E(n),m}},catch:function(e){for(var t=this.tryEntries.length-1;0<=t;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r,i=n.completion;return"throw"===i.type&&(r=i.arg,E(n)),r}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:C(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=l),m}},a}function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function _defineProperties(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,_toPropertyKey(r.key),r)}}function _createClass(e,t,n){return t&&_defineProperties(e.prototype,t),n&&_defineProperties(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function _createForOfIteratorHelper(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=_unsupportedIterableToArray(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,t=function(){};return{s:t,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:t}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,o=!0,a=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){a=!0,i=e},f:function(){try{o||null==n.return||n.return()}finally{if(a)throw i}}}}function asyncGeneratorStep(e,t,n,r,i,o,a){try{var s=e[o](a),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,i)}function _asyncToGenerator(s){return function(){var e=this,a=arguments;return new Promise(function(t,n){var r=s.apply(e,a);function i(e){asyncGeneratorStep(r,t,n,i,o,"next",e)}function o(e){asyncGeneratorStep(r,t,n,i,o,"throw",e)}i(void 0)})}}function _toConsumableArray(e){return _arrayWithoutHoles(e)||_iterableToArray(e)||_unsupportedIterableToArray(e)||_nonIterableSpread()}function _nonIterableSpread(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _iterableToArray(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function _arrayWithoutHoles(e){if(Array.isArray(e))return _arrayLikeToArray(e)}function _wrapRegExp(){_wrapRegExp=function(e,t){return new r(e,void 0,t)};var i=RegExp.prototype,a=new WeakMap;function r(e,t,n){t=new RegExp(e,t);return a.set(t,n||a.get(e)),_setPrototypeOf(t,r.prototype)}function o(i,e){var o=a.get(e);return Object.keys(o).reduce(function(e,t){var n=o[t];if("number"==typeof n)e[t]=i[n];else{for(var r=0;void 0===i[n[r]]&&r+1<n.length;)r++;e[t]=i[n[r]]}return e},Object.create(null))}return _inherits(r,RegExp),r.prototype.exec=function(e){var t=i.exec.call(this,e);return t&&(t.groups=o(t,this),(e=t.indices)&&(e.groups=o(e,this))),t},r.prototype[Symbol.replace]=function(e,t){if("string"==typeof t){var n=a.get(this);return i[Symbol.replace].call(this,e,t.replace(/\$<([^>]+)>/g,function(e,t){t=n[t];return"$"+(Array.isArray(t)?t.join("$"):t)}))}if("function"!=typeof t)return i[Symbol.replace].call(this,e,t);var r=this;return i[Symbol.replace].call(this,e,function(){var e=arguments;return"object"!=_typeof(e[e.length-1])&&(e=[].slice.call(e)).push(o(e,r)),t.apply(this,e)})},_wrapRegExp.apply(this,arguments)}function _inherits(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&_setPrototypeOf(e,t)}function _setPrototypeOf(e,t){return(_setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function _slicedToArray(e,t){return _arrayWithHoles(e)||_iterableToArrayLimit(e,t)||_unsupportedIterableToArray(e,t)||_nonIterableRest()}function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return _arrayLikeToArray(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?_arrayLikeToArray(e,t):void 0}}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function _iterableToArrayLimit(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,o,a,s=[],l=!0,c=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=o.call(n)).done)&&(s.push(r.value),s.length!==t);l=!0);}catch(e){c=!0,i=e}finally{try{if(!l&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(c)throw i}}return s}}function _arrayWithHoles(e){if(Array.isArray(e))return e}function ownKeys(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}function _objectSpread(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?ownKeys(Object(n),!0).forEach(function(e){_defineProperty(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function _defineProperty(e,t,n){return(t=_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function _toPropertyKey(e){e=_toPrimitive(e,"string");return"symbol"===_typeof(e)?e:String(e)}function _toPrimitive(e,t){if("object"!==_typeof(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0===n)return("string"===t?String:Number)(e);t=n.call(e,t||"default");if("object"!==_typeof(t))return t;throw new TypeError("@@toPrimitive must return a primitive value.")}function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}!function(e,t){"use strict";"object"==("undefined"==typeof module?"undefined":_typeof(module))&&"object"==_typeof(module.exports)?module.exports=e.document?t(e,!0):function(e){if(!e.document)throw new Error("jQuery requires a window with a document");return t(e)}:t(e)}("undefined"!=typeof window?window:this,function(k,e){"use strict";function g(e){return null!=e&&e===e.window}var t=[],n=Object.getPrototypeOf,s=t.slice,m=t.flat?function(e){return t.flat.call(e)}:function(e){return t.concat.apply([],e)},l=t.push,i=t.indexOf,r={},o=r.toString,v=r.hasOwnProperty,a=v.toString,c=a.call(Object),y={},b=function(e){return"function"==typeof e&&"number"!=typeof e.nodeType},T=k.document,u={type:!0,src:!0,nonce:!0,noModule:!0};function w(e,t,n){var r,i,o=(n=n||T).createElement("script");if(o.text=e,t)for(r in u)(i=t[r]||t.getAttribute&&t.getAttribute(r))&&o.setAttribute(r,i);n.head.appendChild(o).parentNode.removeChild(o)}function h(e){return null==e?e+"":"object"==_typeof(e)||"function"==typeof e?r[o.call(e)]||"object":_typeof(e)}var E=function e(t,n){return new e.fn.init(t,n)};function d(e){var t=!!e&&"length"in e&&e.length,n=h(e);return!b(e)&&!g(e)&&("array"===n||0===t||"number"==typeof t&&0<t&&t-1 in e)}E.fn=E.prototype={jquery:"3.5.1",constructor:E,length:0,toArray:function(){return s.call(this)},get:function(e){return null==e?s.call(this):e<0?this[e+this.length]:this[e]},pushStack:function(e){e=E.merge(this.constructor(),e);return e.prevObject=this,e},each:function(e){return E.each(this,e)},map:function(n){return this.pushStack(E.map(this,function(e,t){return n.call(e,t,e)}))},slice:function(){return this.pushStack(s.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack(E.grep(this,function(e,t){return(t+1)%2}))},odd:function(){return this.pushStack(E.grep(this,function(e,t){return t%2}))},eq:function(e){var t=this.length,e=+e+(e<0?t:0);return this.pushStack(0<=e&&e<t?[this[e]]:[])},end:function(){return this.prevObject||this.constructor()},push:l,sort:t.sort,splice:t.splice},E.extend=E.fn.extend=function(){var e,t,n,r,i,o=arguments[0]||{},a=1,s=arguments.length,l=!1;for("boolean"==typeof o&&(l=o,o=arguments[a]||{},a++),"object"==_typeof(o)||b(o)||(o={}),a===s&&(o=this,a--);a<s;a++)if(null!=(e=arguments[a]))for(t in e)n=e[t],"__proto__"!==t&&o!==n&&(l&&n&&(E.isPlainObject(n)||(r=Array.isArray(n)))?(i=o[t],i=r&&!Array.isArray(i)?[]:r||E.isPlainObject(i)?i:{},r=!1,o[t]=E.extend(l,i,n)):void 0!==n&&(o[t]=n));return o},E.extend({expando:"jQuery"+("3.5.1"+Math.random()).replace(/\D/g,""),isReady:!0,error:function(e){throw new Error(e)},noop:function(){},isPlainObject:function(e){return!(!e||"[object Object]"!==o.call(e)||(e=n(e))&&("function"!=typeof(e=v.call(e,"constructor")&&e.constructor)||a.call(e)!==c))},isEmptyObject:function(e){for(var t in e)return!1;return!0},globalEval:function(e,t,n){w(e,{nonce:t&&t.nonce},n)},each:function(e,t){var n,r=0;if(d(e))for(n=e.length;r<n&&!1!==t.call(e[r],r,e[r]);r++);else for(r in e)if(!1===t.call(e[r],r,e[r]))break;return e},makeArray:function(e,t){t=t||[];return null!=e&&(d(Object(e))?E.merge(t,"string"==typeof e?[e]:e):l.call(t,e)),t},inArray:function(e,t,n){return null==t?-1:i.call(t,e,n)},merge:function(e,t){for(var n=+t.length,r=0,i=e.length;r<n;r++)e[i++]=t[r];return e.length=i,e},grep:function(e,t,n){for(var r=[],i=0,o=e.length,a=!n;i<o;i++)!t(e[i],i)!=a&&r.push(e[i]);return r},map:function(e,t,n){var r,i,o=0,a=[];if(d(e))for(r=e.length;o<r;o++)null!=(i=t(e[o],o,n))&&a.push(i);else for(o in e)null!=(i=t(e[o],o,n))&&a.push(i);return m(a)},guid:1,support:y}),"function"==typeof Symbol&&(E.fn[Symbol.iterator]=t[Symbol.iterator]),E.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(e,t){r["[object "+t+"]"]=t.toLowerCase()});var f=function(n){function d(e,t){return e="0x"+e.slice(1)-65536,t||(e<0?String.fromCharCode(65536+e):String.fromCharCode(e>>10|55296,1023&e|56320))}function r(){x()}var e,p,w,o,i,h,f,g,_,l,c,x,k,a,T,m,s,u,v,E="sizzle"+ +new Date,y=n.document,S=0,b=0,C=le(),A=le(),R=le(),L=le(),D=function(e,t){return e===t&&(c=!0),0},I={}.hasOwnProperty,t=[],N=t.pop,M=t.push,O=t.push,j=t.slice,P=function(e,t){for(var n=0,r=e.length;n<r;n++)if(e[n]===t)return n;return-1},F="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",B="[\\x20\\t\\r\\n\\f]",U="(?:\\\\[\\da-fA-F]{1,6}"+B+"?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+",q="\\["+B+"*("+U+")(?:"+B+"*([*^$|!~]?=)"+B+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+U+"))|)"+B+"*\\]",H=":("+U+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+q+")*)|.*)\\)|)",$=new RegExp(B+"+","g"),G=new RegExp("^"+B+"+|((?:^|[^\\\\])(?:\\\\.)*)"+B+"+$","g"),V=new RegExp("^"+B+"*,"+B+"*"),W=new RegExp("^"+B+"*([>+~]|"+B+")"+B+"*"),z=new RegExp(B+"|>"),Q=new RegExp(H),X=new RegExp("^"+U+"$"),Y={ID:new RegExp("^#("+U+")"),CLASS:new RegExp("^\\.("+U+")"),TAG:new RegExp("^("+U+"|[*])"),ATTR:new RegExp("^"+q),PSEUDO:new RegExp("^"+H),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+B+"*(even|odd|(([+-]|)(\\d*)n|)"+B+"*(?:([+-]|)"+B+"*(\\d+)|))"+B+"*\\)|)","i"),bool:new RegExp("^(?:"+F+")$","i"),needsContext:new RegExp("^"+B+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+B+"*((?:-\\d)?\\d*)"+B+"*\\)|)(?=[^-]|$)","i")},K=/HTML$/i,J=/^(?:input|select|textarea|button)$/i,Z=/^h\d$/i,ee=/^[^{]+\{\s*\[native \w/,te=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,ne=/[+~]/,re=new RegExp("\\\\[\\da-fA-F]{1,6}"+B+"?|\\\\([^\\r\\n\\f])","g"),ie=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\0-\x1f\x7f-\uFFFF\w-]/g,oe=function(e,t){return t?"\0"===e?"�":e.slice(0,-1)+"\\"+e.charCodeAt(e.length-1).toString(16)+" ":"\\"+e},ae=ye(function(e){return!0===e.disabled&&"fieldset"===e.nodeName.toLowerCase()},{dir:"parentNode",next:"legend"});try{O.apply(t=j.call(y.childNodes),y.childNodes),t[y.childNodes.length].nodeType}catch(e){O={apply:t.length?function(e,t){M.apply(e,j.call(t))}:function(e,t){for(var n=e.length,r=0;e[n++]=t[r++];);e.length=n-1}}}function se(e,t,n,r){var i,o,a,s,l,c,u,d=t&&t.ownerDocument,f=t?t.nodeType:9;if(n=n||[],"string"!=typeof e||!e||1!==f&&9!==f&&11!==f)return n;if(!r&&(x(t),t=t||k,T)){if(11!==f&&(l=te.exec(e)))if(i=l[1]){if(9===f){if(!(a=t.getElementById(i)))return n;if(a.id===i)return n.push(a),n}else if(d&&(a=d.getElementById(i))&&v(t,a)&&a.id===i)return n.push(a),n}else{if(l[2])return O.apply(n,t.getElementsByTagName(e)),n;if((i=l[3])&&p.getElementsByClassName&&t.getElementsByClassName)return O.apply(n,t.getElementsByClassName(i)),n}if(p.qsa&&!L[e+" "]&&(!m||!m.test(e))&&(1!==f||"object"!==t.nodeName.toLowerCase())){if(u=e,d=t,1===f&&(z.test(e)||W.test(e))){for((d=ne.test(e)&&ge(t.parentNode)||t)===t&&p.scope||((s=t.getAttribute("id"))?s=s.replace(ie,oe):t.setAttribute("id",s=E)),o=(c=h(e)).length;o--;)c[o]=(s?"#"+s:":scope")+" "+ve(c[o]);u=c.join(",")}try{return O.apply(n,d.querySelectorAll(u)),n}catch(t){L(e,!0)}finally{s===E&&t.removeAttribute("id")}}}return g(e.replace(G,"$1"),t,n,r)}function le(){var r=[];return function e(t,n){return r.push(t+" ")>w.cacheLength&&delete e[r.shift()],e[t+" "]=n}}function ce(e){return e[E]=!0,e}function ue(e){var t=k.createElement("fieldset");try{return!!e(t)}catch(e){return!1}finally{t.parentNode&&t.parentNode.removeChild(t),t=null}}function de(e,t){for(var n=e.split("|"),r=n.length;r--;)w.attrHandle[n[r]]=t}function fe(e,t){var n=t&&e,r=n&&1===e.nodeType&&1===t.nodeType&&e.sourceIndex-t.sourceIndex;if(r)return r;if(n)for(;n=n.nextSibling;)if(n===t)return-1;return e?1:-1}function pe(t){return function(e){return"form"in e?e.parentNode&&!1===e.disabled?"label"in e?"label"in e.parentNode?e.parentNode.disabled===t:e.disabled===t:e.isDisabled===t||e.isDisabled!==!t&&ae(e)===t:e.disabled===t:"label"in e&&e.disabled===t}}function he(a){return ce(function(o){return o=+o,ce(function(e,t){for(var n,r=a([],e.length,o),i=r.length;i--;)e[n=r[i]]&&(e[n]=!(t[n]=e[n]))})})}function ge(e){return e&&void 0!==e.getElementsByTagName&&e}for(e in p=se.support={},i=se.isXML=function(e){var t=e.namespaceURI,e=(e.ownerDocument||e).documentElement;return!K.test(t||e&&e.nodeName||"HTML")},x=se.setDocument=function(e){var t,e=e?e.ownerDocument||e:y;return e!=k&&9===e.nodeType&&e.documentElement&&(a=(k=e).documentElement,T=!i(k),y!=k&&(t=k.defaultView)&&t.top!==t&&(t.addEventListener?t.addEventListener("unload",r,!1):t.attachEvent&&t.attachEvent("onunload",r)),p.scope=ue(function(e){return a.appendChild(e).appendChild(k.createElement("div")),void 0!==e.querySelectorAll&&!e.querySelectorAll(":scope fieldset div").length}),p.attributes=ue(function(e){return e.className="i",!e.getAttribute("className")}),p.getElementsByTagName=ue(function(e){return e.appendChild(k.createComment("")),!e.getElementsByTagName("*").length}),p.getElementsByClassName=ee.test(k.getElementsByClassName),p.getById=ue(function(e){return a.appendChild(e).id=E,!k.getElementsByName||!k.getElementsByName(E).length}),p.getById?(w.filter.ID=function(e){var t=e.replace(re,d);return function(e){return e.getAttribute("id")===t}},w.find.ID=function(e,t){if(void 0!==t.getElementById&&T){e=t.getElementById(e);return e?[e]:[]}}):(w.filter.ID=function(e){var t=e.replace(re,d);return function(e){e=void 0!==e.getAttributeNode&&e.getAttributeNode("id");return e&&e.value===t}},w.find.ID=function(e,t){if(void 0!==t.getElementById&&T){var n,r,i,o=t.getElementById(e);if(o){if((n=o.getAttributeNode("id"))&&n.value===e)return[o];for(i=t.getElementsByName(e),r=0;o=i[r++];)if((n=o.getAttributeNode("id"))&&n.value===e)return[o]}return[]}}),w.find.TAG=p.getElementsByTagName?function(e,t){return void 0!==t.getElementsByTagName?t.getElementsByTagName(e):p.qsa?t.querySelectorAll(e):void 0}:function(e,t){var n,r=[],i=0,o=t.getElementsByTagName(e);if("*"!==e)return o;for(;n=o[i++];)1===n.nodeType&&r.push(n);return r},w.find.CLASS=p.getElementsByClassName&&function(e,t){if(void 0!==t.getElementsByClassName&&T)return t.getElementsByClassName(e)},s=[],m=[],(p.qsa=ee.test(k.querySelectorAll))&&(ue(function(e){var t;a.appendChild(e).innerHTML="<a id='"+E+"'></a><select id='"+E+"-\r\\' msallowcapture=''><option selected=''></option></select>",e.querySelectorAll("[msallowcapture^='']").length&&m.push("[*^$]="+B+"*(?:''|\"\")"),e.querySelectorAll("[selected]").length||m.push("\\["+B+"*(?:value|"+F+")"),e.querySelectorAll("[id~="+E+"-]").length||m.push("~="),(t=k.createElement("input")).setAttribute("name",""),e.appendChild(t),e.querySelectorAll("[name='']").length||m.push("\\["+B+"*name"+B+"*="+B+"*(?:''|\"\")"),e.querySelectorAll(":checked").length||m.push(":checked"),e.querySelectorAll("a#"+E+"+*").length||m.push(".#.+[+~]"),e.querySelectorAll("\\\f"),m.push("[\\r\\n\\f]")}),ue(function(e){e.innerHTML="<a href='' disabled='disabled'></a><select disabled='disabled'><option/></select>";var t=k.createElement("input");t.setAttribute("type","hidden"),e.appendChild(t).setAttribute("name","D"),e.querySelectorAll("[name=d]").length&&m.push("name"+B+"*[*^$|!~]?="),2!==e.querySelectorAll(":enabled").length&&m.push(":enabled",":disabled"),a.appendChild(e).disabled=!0,2!==e.querySelectorAll(":disabled").length&&m.push(":enabled",":disabled"),e.querySelectorAll("*,:x"),m.push(",.*:")})),(p.matchesSelector=ee.test(u=a.matches||a.webkitMatchesSelector||a.mozMatchesSelector||a.oMatchesSelector||a.msMatchesSelector))&&ue(function(e){p.disconnectedMatch=u.call(e,"*"),u.call(e,"[s!='']:x"),s.push("!=",H)}),m=m.length&&new RegExp(m.join("|")),s=s.length&&new RegExp(s.join("|")),t=ee.test(a.compareDocumentPosition),v=t||ee.test(a.contains)?function(e,t){var n=9===e.nodeType?e.documentElement:e,t=t&&t.parentNode;return e===t||!(!t||1!==t.nodeType||!(n.contains?n.contains(t):e.compareDocumentPosition&&16&e.compareDocumentPosition(t)))}:function(e,t){if(t)for(;t=t.parentNode;)if(t===e)return!0;return!1},D=t?function(e,t){if(e===t)return c=!0,0;var n=!e.compareDocumentPosition-!t.compareDocumentPosition;return n||(1&(n=(e.ownerDocument||e)==(t.ownerDocument||t)?e.compareDocumentPosition(t):1)||!p.sortDetached&&t.compareDocumentPosition(e)===n?e==k||e.ownerDocument==y&&v(y,e)?-1:t==k||t.ownerDocument==y&&v(y,t)?1:l?P(l,e)-P(l,t):0:4&n?-1:1)}:function(e,t){if(e===t)return c=!0,0;var n,r=0,i=e.parentNode,o=t.parentNode,a=[e],s=[t];if(!i||!o)return e==k?-1:t==k?1:i?-1:o?1:l?P(l,e)-P(l,t):0;if(i===o)return fe(e,t);for(n=e;n=n.parentNode;)a.unshift(n);for(n=t;n=n.parentNode;)s.unshift(n);for(;a[r]===s[r];)r++;return r?fe(a[r],s[r]):a[r]==y?-1:s[r]==y?1:0}),k},se.matches=function(e,t){return se(e,null,null,t)},se.matchesSelector=function(e,t){if(x(e),p.matchesSelector&&T&&!L[t+" "]&&(!s||!s.test(t))&&(!m||!m.test(t)))try{var n=u.call(e,t);if(n||p.disconnectedMatch||e.document&&11!==e.document.nodeType)return n}catch(e){L(t,!0)}return 0<se(t,k,null,[e]).length},se.contains=function(e,t){return(e.ownerDocument||e)!=k&&x(e),v(e,t)},se.attr=function(e,t){(e.ownerDocument||e)!=k&&x(e);var n=w.attrHandle[t.toLowerCase()],n=n&&I.call(w.attrHandle,t.toLowerCase())?n(e,t,!T):void 0;return void 0!==n?n:p.attributes||!T?e.getAttribute(t):(n=e.getAttributeNode(t))&&n.specified?n.value:null},se.escape=function(e){return(e+"").replace(ie,oe)},se.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},se.uniqueSort=function(e){var t,n=[],r=0,i=0;if(c=!p.detectDuplicates,l=!p.sortStable&&e.slice(0),e.sort(D),c){for(;t=e[i++];)t===e[i]&&(r=n.push(i));for(;r--;)e.splice(n[r],1)}return l=null,e},o=se.getText=function(e){var t,n="",r=0,i=e.nodeType;if(i){if(1===i||9===i||11===i){if("string"==typeof e.textContent)return e.textContent;for(e=e.firstChild;e;e=e.nextSibling)n+=o(e)}else if(3===i||4===i)return e.nodeValue}else for(;t=e[r++];)n+=o(t);return n},(w=se.selectors={cacheLength:50,createPseudo:ce,match:Y,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(re,d),e[3]=(e[3]||e[4]||e[5]||"").replace(re,d),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||se.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&se.error(e[0]),e},PSEUDO:function(e){var t,n=!e[6]&&e[2];return Y.CHILD.test(e[0])?null:(e[3]?e[2]=e[4]||e[5]||"":n&&Q.test(n)&&(t=h(n,!0))&&(t=n.indexOf(")",n.length-t)-n.length)&&(e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(re,d).toLowerCase();return"*"===e?function(){return!0}:function(e){return e.nodeName&&e.nodeName.toLowerCase()===t}},CLASS:function(e){var t=C[e+" "];return t||(t=new RegExp("(^|"+B+")"+e+"("+B+"|$)"))&&C(e,function(e){return t.test("string"==typeof e.className&&e.className||void 0!==e.getAttribute&&e.getAttribute("class")||"")})},ATTR:function(t,n,r){return function(e){e=se.attr(e,t);return null==e?"!="===n:!n||(e+="","="===n?e===r:"!="===n?e!==r:"^="===n?r&&0===e.indexOf(r):"*="===n?r&&-1<e.indexOf(r):"$="===n?r&&e.slice(-r.length)===r:"~="===n?-1<(" "+e.replace($," ")+" ").indexOf(r):"|="===n&&(e===r||e.slice(0,r.length+1)===r+"-"))}},CHILD:function(h,e,t,g,m){var v="nth"!==h.slice(0,3),y="last"!==h.slice(-4),b="of-type"===e;return 1===g&&0===m?function(e){return!!e.parentNode}:function(e,t,n){var r,i,o,a,s,l,c=v!=y?"nextSibling":"previousSibling",u=e.parentNode,d=b&&e.nodeName.toLowerCase(),f=!n&&!b,p=!1;if(u){if(v){for(;c;){for(a=e;a=a[c];)if(b?a.nodeName.toLowerCase()===d:1===a.nodeType)return!1;l=c="only"===h&&!l&&"nextSibling"}return!0}if(l=[y?u.firstChild:u.lastChild],y&&f){for(p=(s=(r=(i=(o=(a=u)[E]||(a[E]={}))[a.uniqueID]||(o[a.uniqueID]={}))[h]||[])[0]===S&&r[1])&&r[2],a=s&&u.childNodes[s];a=++s&&a&&a[c]||(p=s=0)||l.pop();)if(1===a.nodeType&&++p&&a===e){i[h]=[S,s,p];break}}else if(f&&(p=s=(r=(i=(o=(a=e)[E]||(a[E]={}))[a.uniqueID]||(o[a.uniqueID]={}))[h]||[])[0]===S&&r[1]),!1===p)for(;(a=++s&&a&&a[c]||(p=s=0)||l.pop())&&((b?a.nodeName.toLowerCase()!==d:1!==a.nodeType)||!++p||(f&&((i=(o=a[E]||(a[E]={}))[a.uniqueID]||(o[a.uniqueID]={}))[h]=[S,p]),a!==e)););return(p-=m)===g||p%g==0&&0<=p/g}}},PSEUDO:function(e,o){var t,a=w.pseudos[e]||w.setFilters[e.toLowerCase()]||se.error("unsupported pseudo: "+e);return a[E]?a(o):1<a.length?(t=[e,e,"",o],w.setFilters.hasOwnProperty(e.toLowerCase())?ce(function(e,t){for(var n,r=a(e,o),i=r.length;i--;)e[n=P(e,r[i])]=!(t[n]=r[i])}):function(e){return a(e,0,t)}):a}},pseudos:{not:ce(function(e){var r=[],i=[],s=f(e.replace(G,"$1"));return s[E]?ce(function(e,t,n,r){for(var i,o=s(e,null,r,[]),a=e.length;a--;)(i=o[a])&&(e[a]=!(t[a]=i))}):function(e,t,n){return r[0]=e,s(r,null,n,i),r[0]=null,!i.pop()}}),has:ce(function(t){return function(e){return 0<se(t,e).length}}),contains:ce(function(t){return t=t.replace(re,d),function(e){return-1<(e.textContent||o(e)).indexOf(t)}}),lang:ce(function(n){return X.test(n||"")||se.error("unsupported lang: "+n),n=n.replace(re,d).toLowerCase(),function(e){var t;do{if(t=T?e.lang:e.getAttribute("xml:lang")||e.getAttribute("lang"))return(t=t.toLowerCase())===n||0===t.indexOf(n+"-")}while((e=e.parentNode)&&1===e.nodeType);return!1}}),target:function(e){var t=n.location&&n.location.hash;return t&&t.slice(1)===e.id},root:function(e){return e===a},focus:function(e){return e===k.activeElement&&(!k.hasFocus||k.hasFocus())&&!!(e.type||e.href||~e.tabIndex)},enabled:pe(!1),disabled:pe(!0),checked:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&!!e.checked||"option"===t&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,!0===e.selected},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeType<6)return!1;return!0},parent:function(e){return!w.pseudos.empty(e)},header:function(e){return Z.test(e.nodeName)},input:function(e){return J.test(e.nodeName)},button:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&"button"===e.type||"button"===t},text:function(e){return"input"===e.nodeName.toLowerCase()&&"text"===e.type&&(null==(e=e.getAttribute("type"))||"text"===e.toLowerCase())},first:he(function(){return[0]}),last:he(function(e,t){return[t-1]}),eq:he(function(e,t,n){return[n<0?n+t:n]}),even:he(function(e,t){for(var n=0;n<t;n+=2)e.push(n);return e}),odd:he(function(e,t){for(var n=1;n<t;n+=2)e.push(n);return e}),lt:he(function(e,t,n){for(var r=n<0?n+t:t<n?t:n;0<=--r;)e.push(r);return e}),gt:he(function(e,t,n){for(var r=n<0?n+t:n;++r<t;)e.push(r);return e})}}).pseudos.nth=w.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})w.pseudos[e]=function(t){return function(e){return"input"===e.nodeName.toLowerCase()&&e.type===t}}(e);for(e in{submit:!0,reset:!0})w.pseudos[e]=function(n){return function(e){var t=e.nodeName.toLowerCase();return("input"===t||"button"===t)&&e.type===n}}(e);function me(){}function ve(e){for(var t=0,n=e.length,r="";t<n;t++)r+=e[t].value;return r}function ye(a,e,t){var s=e.dir,l=e.next,c=l||s,u=t&&"parentNode"===c,d=b++;return e.first?function(e,t,n){for(;e=e[s];)if(1===e.nodeType||u)return a(e,t,n);return!1}:function(e,t,n){var r,i,o=[S,d];if(n){for(;e=e[s];)if((1===e.nodeType||u)&&a(e,t,n))return!0}else for(;e=e[s];)if(1===e.nodeType||u)if(r=(i=e[E]||(e[E]={}))[e.uniqueID]||(i[e.uniqueID]={}),l&&l===e.nodeName.toLowerCase())e=e[s]||e;else{if((i=r[c])&&i[0]===S&&i[1]===d)return o[2]=i[2];if((r[c]=o)[2]=a(e,t,n))return!0}return!1}}function be(i){return 1<i.length?function(e,t,n){for(var r=i.length;r--;)if(!i[r](e,t,n))return!1;return!0}:i[0]}function we(e,t,n,r,i){for(var o,a=[],s=0,l=e.length,c=null!=t;s<l;s++)(o=e[s])&&(n&&!n(o,r,i)||(a.push(o),c&&t.push(s)));return a}function _e(e){for(var r,t,n,i=e.length,o=w.relative[e[0].type],a=o||w.relative[" "],s=o?1:0,l=ye(function(e){return e===r},a,!0),c=ye(function(e){return-1<P(r,e)},a,!0),u=[function(e,t,n){n=!o&&(n||t!==_)||((r=t).nodeType?l:c)(e,t,n);return r=null,n}];s<i;s++)if(t=w.relative[e[s].type])u=[ye(be(u),t)];else{if((t=w.filter[e[s].type].apply(null,e[s].matches))[E]){for(n=++s;n<i&&!w.relative[e[n].type];n++);return function e(p,h,g,m,v,t){return m&&!m[E]&&(m=e(m)),v&&!v[E]&&(v=e(v,t)),ce(function(e,t,n,r){var i,o,a,s=[],l=[],c=t.length,u=e||function(e,t,n){for(var r=0,i=t.length;r<i;r++)se(e,t[r],n);return n}(h||"*",n.nodeType?[n]:n,[]),d=!p||!e&&h?u:we(u,s,p,n,r),f=g?v||(e?p:c||m)?[]:t:d;if(g&&g(d,f,n,r),m)for(i=we(f,l),m(i,[],n,r),o=i.length;o--;)(a=i[o])&&(f[l[o]]=!(d[l[o]]=a));if(e){if(v||p){if(v){for(i=[],o=f.length;o--;)(a=f[o])&&i.push(d[o]=a);v(null,f=[],i,r)}for(o=f.length;o--;)(a=f[o])&&-1<(i=v?P(e,a):s[o])&&(e[i]=!(t[i]=a))}}else f=we(f===t?f.splice(c,f.length):f),v?v(null,t,f,r):O.apply(t,f)})}(1<s&&be(u),1<s&&ve(e.slice(0,s-1).concat({value:" "===e[s-2].type?"*":""})).replace(G,"$1"),t,s<n&&_e(e.slice(s,n)),n<i&&_e(e=e.slice(n)),n<i&&ve(e))}u.push(t)}return be(u)}return me.prototype=w.filters=w.pseudos,w.setFilters=new me,h=se.tokenize=function(e,t){var n,r,i,o,a,s,l,c=A[e+" "];if(c)return t?0:c.slice(0);for(a=e,s=[],l=w.preFilter;a;){for(o in n&&!(r=V.exec(a))||(r&&(a=a.slice(r[0].length)||a),s.push(i=[])),n=!1,(r=W.exec(a))&&(n=r.shift(),i.push({value:n,type:r[0].replace(G," ")}),a=a.slice(n.length)),w.filter)!(r=Y[o].exec(a))||l[o]&&!(r=l[o](r))||(n=r.shift(),i.push({value:n,type:o,matches:r}),a=a.slice(n.length));if(!n)break}return t?a.length:a?se.error(e):A(e,s).slice(0)},f=se.compile=function(e,t){var n,m,v,y,b,r,i=[],o=[],a=R[e+" "];if(!a){for(n=(t=t||h(e)).length;n--;)((a=_e(t[n]))[E]?i:o).push(a);(a=R(e,(m=o,y=0<(v=i).length,b=0<m.length,r=function(e,t,n,r,i){var o,a,s,l=0,c="0",u=e&&[],d=[],f=_,p=e||b&&w.find.TAG("*",i),h=S+=null==f?1:Math.random()||.1,g=p.length;for(i&&(_=t==k||t||i);c!==g&&null!=(o=p[c]);c++){if(b&&o){for(a=0,t||o.ownerDocument==k||(x(o),n=!T);s=m[a++];)if(s(o,t||k,n)){r.push(o);break}i&&(S=h)}y&&((o=!s&&o)&&l--,e&&u.push(o))}if(l+=c,y&&c!==l){for(a=0;s=v[a++];)s(u,d,t,n);if(e){if(0<l)for(;c--;)u[c]||d[c]||(d[c]=N.call(r));d=we(d)}O.apply(r,d),i&&!e&&0<d.length&&1<l+v.length&&se.uniqueSort(r)}return i&&(S=h,_=f),u},y?ce(r):r))).selector=e}return a},g=se.select=function(e,t,n,r){var i,o,a,s,l,c="function"==typeof e&&e,u=!r&&h(e=c.selector||e);if(n=n||[],1===u.length){if(2<(o=u[0]=u[0].slice(0)).length&&"ID"===(a=o[0]).type&&9===t.nodeType&&T&&w.relative[o[1].type]){if(!(t=(w.find.ID(a.matches[0].replace(re,d),t)||[])[0]))return n;c&&(t=t.parentNode),e=e.slice(o.shift().value.length)}for(i=Y.needsContext.test(e)?0:o.length;i--&&(a=o[i],!w.relative[s=a.type]);)if((l=w.find[s])&&(r=l(a.matches[0].replace(re,d),ne.test(o[0].type)&&ge(t.parentNode)||t))){if(o.splice(i,1),!(e=r.length&&ve(o)))return O.apply(n,r),n;break}}return(c||f(e,u))(r,t,!T,n,!t||ne.test(e)&&ge(t.parentNode)||t),n},p.sortStable=E.split("").sort(D).join("")===E,p.detectDuplicates=!!c,x(),p.sortDetached=ue(function(e){return 1&e.compareDocumentPosition(k.createElement("fieldset"))}),ue(function(e){return e.innerHTML="<a href='#'></a>","#"===e.firstChild.getAttribute("href")})||de("type|href|height|width",function(e,t,n){if(!n)return e.getAttribute(t,"type"===t.toLowerCase()?1:2)}),p.attributes&&ue(function(e){return e.innerHTML="<input/>",e.firstChild.setAttribute("value",""),""===e.firstChild.getAttribute("value")})||de("value",function(e,t,n){if(!n&&"input"===e.nodeName.toLowerCase())return e.defaultValue}),ue(function(e){return null==e.getAttribute("disabled")})||de(F,function(e,t,n){if(!n)return!0===e[t]?t.toLowerCase():(t=e.getAttributeNode(t))&&t.specified?t.value:null}),se}(k);E.find=f,E.expr=f.selectors,E.expr[":"]=E.expr.pseudos,E.uniqueSort=E.unique=f.uniqueSort,E.text=f.getText,E.isXMLDoc=f.isXML,E.contains=f.contains,E.escapeSelector=f.escape;function p(e,t,n){for(var r=[],i=void 0!==n;(e=e[t])&&9!==e.nodeType;)if(1===e.nodeType){if(i&&E(e).is(n))break;r.push(e)}return r}function _(e,t){for(var n=[];e;e=e.nextSibling)1===e.nodeType&&e!==t&&n.push(e);return n}var x=E.expr.match.needsContext;function S(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()}var C=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;function A(e,n,r){return b(n)?E.grep(e,function(e,t){return!!n.call(e,t,e)!==r}):n.nodeType?E.grep(e,function(e){return e===n!==r}):"string"!=typeof n?E.grep(e,function(e){return-1<i.call(n,e)!==r}):E.filter(n,e,r)}E.filter=function(e,t,n){var r=t[0];return n&&(e=":not("+e+")"),1===t.length&&1===r.nodeType?E.find.matchesSelector(r,e)?[r]:[]:E.find.matches(e,E.grep(t,function(e){return 1===e.nodeType}))},E.fn.extend({find:function(e){var t,n,r=this.length,i=this;if("string"!=typeof e)return this.pushStack(E(e).filter(function(){for(t=0;t<r;t++)if(E.contains(i[t],this))return!0}));for(n=this.pushStack([]),t=0;t<r;t++)E.find(e,i[t],n);return 1<r?E.uniqueSort(n):n},filter:function(e){return this.pushStack(A(this,e||[],!1))},not:function(e){return this.pushStack(A(this,e||[],!0))},is:function(e){return!!A(this,"string"==typeof e&&x.test(e)?E(e):e||[],!1).length}});var R,L=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/;(E.fn.init=function(e,t,n){if(!e)return this;if(n=n||R,"string"!=typeof e)return e.nodeType?(this[0]=e,this.length=1,this):b(e)?void 0!==n.ready?n.ready(e):e(E):E.makeArray(e,this);if(!(r="<"===e[0]&&">"===e[e.length-1]&&3<=e.length?[null,e,null]:L.exec(e))||!r[1]&&t)return(!t||t.jquery?t||n:this.constructor(t)).find(e);if(r[1]){if(t=t instanceof E?t[0]:t,E.merge(this,E.parseHTML(r[1],t&&t.nodeType?t.ownerDocument||t:T,!0)),C.test(r[1])&&E.isPlainObject(t))for(var r in t)b(this[r])?this[r](t[r]):this.attr(r,t[r]);return this}return(e=T.getElementById(r[2]))&&(this[0]=e,this.length=1),this}).prototype=E.fn,R=E(T);var D=/^(?:parents|prev(?:Until|All))/,I={children:!0,contents:!0,next:!0,prev:!0};function N(e,t){for(;(e=e[t])&&1!==e.nodeType;);return e}E.fn.extend({has:function(e){var t=E(e,this),n=t.length;return this.filter(function(){for(var e=0;e<n;e++)if(E.contains(this,t[e]))return!0})},closest:function(e,t){var n,r=0,i=this.length,o=[],a="string"!=typeof e&&E(e);if(!x.test(e))for(;r<i;r++)for(n=this[r];n&&n!==t;n=n.parentNode)if(n.nodeType<11&&(a?-1<a.index(n):1===n.nodeType&&E.find.matchesSelector(n,e))){o.push(n);break}return this.pushStack(1<o.length?E.uniqueSort(o):o)},index:function(e){return e?"string"==typeof e?i.call(E(e),this[0]):i.call(this,e.jquery?e[0]:e):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){return this.pushStack(E.uniqueSort(E.merge(this.get(),E(e,t))))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}}),E.each({parent:function(e){e=e.parentNode;return e&&11!==e.nodeType?e:null},parents:function(e){return p(e,"parentNode")},parentsUntil:function(e,t,n){return p(e,"parentNode",n)},next:function(e){return N(e,"nextSibling")},prev:function(e){return N(e,"previousSibling")},nextAll:function(e){return p(e,"nextSibling")},prevAll:function(e){return p(e,"previousSibling")},nextUntil:function(e,t,n){return p(e,"nextSibling",n)},prevUntil:function(e,t,n){return p(e,"previousSibling",n)},siblings:function(e){return _((e.parentNode||{}).firstChild,e)},children:function(e){return _(e.firstChild)},contents:function(e){return null!=e.contentDocument&&n(e.contentDocument)?e.contentDocument:(S(e,"template")&&(e=e.content||e),E.merge([],e.childNodes))}},function(r,i){E.fn[r]=function(e,t){var n=E.map(this,i,e);return"Until"!==r.slice(-5)&&(t=e),t&&"string"==typeof t&&(n=E.filter(t,n)),1<this.length&&(I[r]||E.uniqueSort(n),D.test(r)&&n.reverse()),this.pushStack(n)}});var M=/[^\x20\t\r\n\f]+/g;function O(e){return e}function j(e){throw e}function P(e,t,n,r){var i;try{e&&b(i=e.promise)?i.call(e).done(t).fail(n):e&&b(i=e.then)?i.call(e,t,n):t.apply(void 0,[e].slice(r))}catch(e){n.apply(void 0,[e])}}E.Callbacks=function(r){var n;r="string"==typeof r?(n={},E.each(r.match(M)||[],function(e,t){n[t]=!0}),n):E.extend({},r);function i(){for(a=a||r.once,t=o=!0;l.length;c=-1)for(e=l.shift();++c<s.length;)!1===s[c].apply(e[0],e[1])&&r.stopOnFalse&&(c=s.length,e=!1);r.memory||(e=!1),o=!1,a&&(s=e?[]:"")}var o,e,t,a,s=[],l=[],c=-1,u={add:function(){return s&&(e&&!o&&(c=s.length-1,l.push(e)),function n(e){E.each(e,function(e,t){b(t)?r.unique&&u.has(t)||s.push(t):t&&t.length&&"string"!==h(t)&&n(t)})}(arguments),e&&!o&&i()),this},remove:function(){return E.each(arguments,function(e,t){for(var n;-1<(n=E.inArray(t,s,n));)s.splice(n,1),n<=c&&c--}),this},has:function(e){return e?-1<E.inArray(e,s):0<s.length},empty:function(){return s=s&&[],this},disable:function(){return a=l=[],s=e="",this},disabled:function(){return!s},lock:function(){return a=l=[],e||o||(s=e=""),this},locked:function(){return!!a},fireWith:function(e,t){return a||(t=[e,(t=t||[]).slice?t.slice():t],l.push(t),o||i()),this},fire:function(){return u.fireWith(this,arguments),this},fired:function(){return!!t}};return u},E.extend({Deferred:function(e){var o=[["notify","progress",E.Callbacks("memory"),E.Callbacks("memory"),2],["resolve","done",E.Callbacks("once memory"),E.Callbacks("once memory"),0,"resolved"],["reject","fail",E.Callbacks("once memory"),E.Callbacks("once memory"),1,"rejected"]],i="pending",a={state:function(){return i},always:function(){return s.done(arguments).fail(arguments),this},catch:function(e){return a.then(null,e)},pipe:function(){var i=arguments;return E.Deferred(function(r){E.each(o,function(e,t){var n=b(i[t[4]])&&i[t[4]];s[t[1]](function(){var e=n&&n.apply(this,arguments);e&&b(e.promise)?e.promise().progress(r.notify).done(r.resolve).fail(r.reject):r[t[0]+"With"](this,n?[e]:arguments)})}),i=null}).promise()},then:function(t,n,r){var l=0;function c(i,o,a,s){return function(){function e(){var e,t;if(!(i<l)){if((e=a.apply(n,r))===o.promise())throw new TypeError("Thenable self-resolution");t=e&&("object"==_typeof(e)||"function"==typeof e)&&e.then,b(t)?s?t.call(e,c(l,o,O,s),c(l,o,j,s)):(l++,t.call(e,c(l,o,O,s),c(l,o,j,s),c(l,o,O,o.notifyWith))):(a!==O&&(n=void 0,r=[e]),(s||o.resolveWith)(n,r))}}var n=this,r=arguments,t=s?e:function(){try{e()}catch(e){E.Deferred.exceptionHook&&E.Deferred.exceptionHook(e,t.stackTrace),l<=i+1&&(a!==j&&(n=void 0,r=[e]),o.rejectWith(n,r))}};i?t():(E.Deferred.getStackHook&&(t.stackTrace=E.Deferred.getStackHook()),k.setTimeout(t))}}return E.Deferred(function(e){o[0][3].add(c(0,e,b(r)?r:O,e.notifyWith)),o[1][3].add(c(0,e,b(t)?t:O)),o[2][3].add(c(0,e,b(n)?n:j))}).promise()},promise:function(e){return null!=e?E.extend(e,a):a}},s={};return E.each(o,function(e,t){var n=t[2],r=t[5];a[t[1]]=n.add,r&&n.add(function(){i=r},o[3-e][2].disable,o[3-e][3].disable,o[0][2].lock,o[0][3].lock),n.add(t[3].fire),s[t[0]]=function(){return s[t[0]+"With"](this===s?void 0:this,arguments),this},s[t[0]+"With"]=n.fireWith}),a.promise(s),e&&e.call(s,s),s},when:function(e){function t(t){return function(e){i[t]=this,o[t]=1<arguments.length?s.call(arguments):e,--n||a.resolveWith(i,o)}}var n=arguments.length,r=n,i=Array(r),o=s.call(arguments),a=E.Deferred();if(n<=1&&(P(e,a.done(t(r)).resolve,a.reject,!n),"pending"===a.state()||b(o[r]&&o[r].then)))return a.then();for(;r--;)P(o[r],t(r),a.reject);return a.promise()}});var F=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;E.Deferred.exceptionHook=function(e,t){k.console&&k.console.warn&&e&&F.test(e.name)&&k.console.warn("jQuery.Deferred exception: "+e.message,e.stack,t)},E.readyException=function(e){k.setTimeout(function(){throw e})};var B=E.Deferred();function U(){T.removeEventListener("DOMContentLoaded",U),k.removeEventListener("load",U),E.ready()}E.fn.ready=function(e){return B.then(e).catch(function(e){E.readyException(e)}),this},E.extend({isReady:!1,readyWait:1,ready:function(e){(!0===e?--E.readyWait:E.isReady)||(E.isReady=!0)!==e&&0<--E.readyWait||B.resolveWith(T,[E])}}),E.ready.then=B.then,"complete"===T.readyState||"loading"!==T.readyState&&!T.documentElement.doScroll?k.setTimeout(E.ready):(T.addEventListener("DOMContentLoaded",U),k.addEventListener("load",U));function q(e,t,n,r,i,o,a){var s=0,l=e.length,c=null==n;if("object"===h(n))for(s in i=!0,n)q(e,t,s,n[s],!0,o,a);else if(void 0!==r&&(i=!0,b(r)||(a=!0),c&&(t=a?(t.call(e,r),null):(c=t,function(e,t,n){return c.call(E(e),n)})),t))for(;s<l;s++)t(e[s],n,a?r:r.call(e[s],s,t(e[s],n)));return i?e:c?t.call(e):l?t(e[0],n):o}var H=/^-ms-/,$=/-([a-z])/g;function G(e,t){return t.toUpperCase()}function V(e){return e.replace(H,"ms-").replace($,G)}function W(e){return 1===e.nodeType||9===e.nodeType||!+e.nodeType}function z(){this.expando=E.expando+z.uid++}z.uid=1,z.prototype={cache:function(e){var t=e[this.expando];return t||(t={},W(e)&&(e.nodeType?e[this.expando]=t:Object.defineProperty(e,this.expando,{value:t,configurable:!0}))),t},set:function(e,t,n){var r,i=this.cache(e);if("string"==typeof t)i[V(t)]=n;else for(r in t)i[V(r)]=t[r];return i},get:function(e,t){return void 0===t?this.cache(e):e[this.expando]&&e[this.expando][V(t)]},access:function(e,t,n){return void 0===t||t&&"string"==typeof t&&void 0===n?this.get(e,t):(this.set(e,t,n),void 0!==n?n:t)},remove:function(e,t){var n,r=e[this.expando];if(void 0!==r){if(void 0!==t){n=(t=Array.isArray(t)?t.map(V):(t=V(t))in r?[t]:t.match(M)||[]).length;for(;n--;)delete r[t[n]]}void 0!==t&&!E.isEmptyObject(r)||(e.nodeType?e[this.expando]=void 0:delete e[this.expando])}},hasData:function(e){e=e[this.expando];return void 0!==e&&!E.isEmptyObject(e)}};var Q=new z,X=new z,Y=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,K=/[A-Z]/g;function J(e,t,n){var r,i;if(void 0===n&&1===e.nodeType)if(r="data-"+t.replace(K,"-$&").toLowerCase(),"string"==typeof(n=e.getAttribute(r))){try{n="true"===(i=n)||"false"!==i&&("null"===i?null:i===+i+""?+i:Y.test(i)?JSON.parse(i):i)}catch(e){}X.set(e,t,n)}else n=void 0;return n}E.extend({hasData:function(e){return X.hasData(e)||Q.hasData(e)},data:function(e,t,n){return X.access(e,t,n)},removeData:function(e,t){X.remove(e,t)},_data:function(e,t,n){return Q.access(e,t,n)},_removeData:function(e,t){Q.remove(e,t)}}),E.fn.extend({data:function(n,e){var t,r,i,o=this[0],a=o&&o.attributes;if(void 0!==n)return"object"==_typeof(n)?this.each(function(){X.set(this,n)}):q(this,function(e){var t;return o&&void 0===e?void 0!==(t=X.get(o,n))||void 0!==(t=J(o,n))?t:void 0:void this.each(function(){X.set(this,n,e)})},null,e,1<arguments.length,null,!0);if(this.length&&(i=X.get(o),1===o.nodeType&&!Q.get(o,"hasDataAttrs"))){for(t=a.length;t--;)a[t]&&0===(r=a[t].name).indexOf("data-")&&(r=V(r.slice(5)),J(o,r,i[r]));Q.set(o,"hasDataAttrs",!0)}return i},removeData:function(e){return this.each(function(){X.remove(this,e)})}}),E.extend({queue:function(e,t,n){var r;if(e)return t=(t||"fx")+"queue",r=Q.get(e,t),n&&(!r||Array.isArray(n)?r=Q.access(e,t,E.makeArray(n)):r.push(n)),r||[]},dequeue:function(e,t){t=t||"fx";var n=E.queue(e,t),r=n.length,i=n.shift(),o=E._queueHooks(e,t);"inprogress"===i&&(i=n.shift(),r--),i&&("fx"===t&&n.unshift("inprogress"),delete o.stop,i.call(e,function(){E.dequeue(e,t)},o)),!r&&o&&o.empty.fire()},_queueHooks:function(e,t){var n=t+"queueHooks";return Q.get(e,n)||Q.access(e,n,{empty:E.Callbacks("once memory").add(function(){Q.remove(e,[t+"queue",n])})})}}),E.fn.extend({queue:function(t,n){var e=2;return"string"!=typeof t&&(n=t,t="fx",e--),arguments.length<e?E.queue(this[0],t):void 0===n?this:this.each(function(){var e=E.queue(this,t,n);E._queueHooks(this,t),"fx"===t&&"inprogress"!==e[0]&&E.dequeue(this,t)})},dequeue:function(e){return this.each(function(){E.dequeue(this,e)})},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){function n(){--i||o.resolveWith(a,[a])}var r,i=1,o=E.Deferred(),a=this,s=this.length;for("string"!=typeof e&&(t=e,e=void 0),e=e||"fx";s--;)(r=Q.get(a[s],e+"queueHooks"))&&r.empty&&(i++,r.empty.add(n));return n(),o.promise(t)}});var Z=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,ee=new RegExp("^(?:([+-])=|)("+Z+")([a-z%]*)$","i"),te=["Top","Right","Bottom","Left"],ne=T.documentElement,re=function(e){return E.contains(e.ownerDocument,e)},ie={composed:!0};ne.getRootNode&&(re=function(e){return E.contains(e.ownerDocument,e)||e.getRootNode(ie)===e.ownerDocument});function oe(e,t){return"none"===(e=t||e).style.display||""===e.style.display&&re(e)&&"none"===E.css(e,"display")}function ae(e,t,n,r){var i,o,a=20,s=r?function(){return r.cur()}:function(){return E.css(e,t,"")},l=s(),c=n&&n[3]||(E.cssNumber[t]?"":"px"),u=e.nodeType&&(E.cssNumber[t]||"px"!==c&&+l)&&ee.exec(E.css(e,t));if(u&&u[3]!==c){for(l/=2,c=c||u[3],u=+l||1;a--;)E.style(e,t,u+c),(1-o)*(1-(o=s()/l||.5))<=0&&(a=0),u/=o;u*=2,E.style(e,t,u+c),n=n||[]}return n&&(u=+u||+l||0,i=n[1]?u+(n[1]+1)*n[2]:+n[2],r&&(r.unit=c,r.start=u,r.end=i)),i}var se={};function le(e,t){for(var n,r,i,o,a,s,l=[],c=0,u=e.length;c<u;c++)(r=e[c]).style&&(n=r.style.display,t?("none"===n&&(l[c]=Q.get(r,"display")||null,l[c]||(r.style.display="")),""===r.style.display&&oe(r)&&(l[c]=(s=o=i=void 0,o=r.ownerDocument,a=r.nodeName,(s=se[a])||(i=o.body.appendChild(o.createElement(a)),s=E.css(i,"display"),i.parentNode.removeChild(i),"none"===s&&(s="block"),se[a]=s)))):"none"!==n&&(l[c]="none",Q.set(r,"display",n)));for(c=0;c<u;c++)null!=l[c]&&(e[c].style.display=l[c]);return e}E.fn.extend({show:function(){return le(this,!0)},hide:function(){return le(this)},toggle:function(e){return"boolean"==typeof e?e?this.show():this.hide():this.each(function(){oe(this)?E(this).show():E(this).hide()})}});var ce=/^(?:checkbox|radio)$/i,ue=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,de=/^$|^module$|\/(?:java|ecma)script/i,fe=T.createDocumentFragment().appendChild(T.createElement("div"));(f=T.createElement("input")).setAttribute("type","radio"),f.setAttribute("checked","checked"),f.setAttribute("name","t"),fe.appendChild(f),y.checkClone=fe.cloneNode(!0).cloneNode(!0).lastChild.checked,fe.innerHTML="<textarea>x</textarea>",y.noCloneChecked=!!fe.cloneNode(!0).lastChild.defaultValue,fe.innerHTML="<option></option>",y.option=!!fe.lastChild;var pe={thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};function he(e,t){var n=void 0!==e.getElementsByTagName?e.getElementsByTagName(t||"*"):void 0!==e.querySelectorAll?e.querySelectorAll(t||"*"):[];return void 0===t||t&&S(e,t)?E.merge([e],n):n}function ge(e,t){for(var n=0,r=e.length;n<r;n++)Q.set(e[n],"globalEval",!t||Q.get(t[n],"globalEval"))}pe.tbody=pe.tfoot=pe.colgroup=pe.caption=pe.thead,pe.th=pe.td,y.option||(pe.optgroup=pe.option=[1,"<select multiple='multiple'>","</select>"]);var me=/<|&#?\w+;/;function ve(e,t,n,r,i){for(var o,a,s,l,c,u=t.createDocumentFragment(),d=[],f=0,p=e.length;f<p;f++)if((o=e[f])||0===o)if("object"===h(o))E.merge(d,o.nodeType?[o]:o);else if(me.test(o)){for(a=a||u.appendChild(t.createElement("div")),s=(ue.exec(o)||["",""])[1].toLowerCase(),s=pe[s]||pe._default,a.innerHTML=s[1]+E.htmlPrefilter(o)+s[2],c=s[0];c--;)a=a.lastChild;E.merge(d,a.childNodes),(a=u.firstChild).textContent=""}else d.push(t.createTextNode(o));for(u.textContent="",f=0;o=d[f++];)if(r&&-1<E.inArray(o,r))i&&i.push(o);else if(l=re(o),a=he(u.appendChild(o),"script"),l&&ge(a),n)for(c=0;o=a[c++];)de.test(o.type||"")&&n.push(o);return u}var ye=/^key/,be=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,we=/^([^.]*)(?:\.(.+)|)/;function _e(){return!0}function xe(){return!1}function ke(e,t){return e===function(){try{return T.activeElement}catch(e){}}()==("focus"===t)}function Te(e,t,n,r,i,o){var a,s;if("object"==_typeof(t)){for(s in"string"!=typeof n&&(r=r||n,n=void 0),t)Te(e,s,n,r,t[s],o);return e}if(null==r&&null==i?(i=n,r=n=void 0):null==i&&("string"==typeof n?(i=r,r=void 0):(i=r,r=n,n=void 0)),!1===i)i=xe;else if(!i)return e;return 1===o&&(a=i,(i=function(e){return E().off(e),a.apply(this,arguments)}).guid=a.guid||(a.guid=E.guid++)),e.each(function(){E.event.add(this,t,i,r,n)})}function Ee(e,i,o){o?(Q.set(e,i,!1),E.event.add(e,i,{namespace:!1,handler:function(e){var t,n,r=Q.get(this,i);if(1&e.isTrigger&&this[i]){if(r.length)(E.event.special[i]||{}).delegateType&&e.stopPropagation();else if(r=s.call(arguments),Q.set(this,i,r),t=o(this,i),this[i](),r!==(n=Q.get(this,i))||t?Q.set(this,i,!1):n={},r!==n)return e.stopImmediatePropagation(),e.preventDefault(),n.value}else r.length&&(Q.set(this,i,{value:E.event.trigger(E.extend(r[0],E.Event.prototype),r.slice(1),this)}),e.stopImmediatePropagation())}})):void 0===Q.get(e,i)&&E.event.add(e,i,_e)}E.event={global:{},add:function(t,e,n,r,i){var o,a,s,l,c,u,d,f,p,h=Q.get(t);if(W(t))for(n.handler&&(n=(o=n).handler,i=o.selector),i&&E.find.matchesSelector(ne,i),n.guid||(n.guid=E.guid++),(s=h.events)||(s=h.events=Object.create(null)),(a=h.handle)||(a=h.handle=function(e){return void 0!==E&&E.event.triggered!==e.type?E.event.dispatch.apply(t,arguments):void 0}),l=(e=(e||"").match(M)||[""]).length;l--;)d=p=(c=we.exec(e[l])||[])[1],f=(c[2]||"").split(".").sort(),d&&(u=E.event.special[d]||{},d=(i?u.delegateType:u.bindType)||d,u=E.event.special[d]||{},c=E.extend({type:d,origType:p,data:r,handler:n,guid:n.guid,selector:i,needsContext:i&&E.expr.match.needsContext.test(i),namespace:f.join(".")},o),(p=s[d])||((p=s[d]=[]).delegateCount=0,u.setup&&!1!==u.setup.call(t,r,f,a)||t.addEventListener&&t.addEventListener(d,a)),u.add&&(u.add.call(t,c),c.handler.guid||(c.handler.guid=n.guid)),i?p.splice(p.delegateCount++,0,c):p.push(c),E.event.global[d]=!0)},remove:function(e,t,n,r,i){var o,a,s,l,c,u,d,f,p,h,g,m=Q.hasData(e)&&Q.get(e);if(m&&(l=m.events)){for(c=(t=(t||"").match(M)||[""]).length;c--;)if(p=g=(s=we.exec(t[c])||[])[1],h=(s[2]||"").split(".").sort(),p){for(d=E.event.special[p]||{},f=l[p=(r?d.delegateType:d.bindType)||p]||[],s=s[2]&&new RegExp("(^|\\.)"+h.join("\\.(?:.*\\.|)")+"(\\.|$)"),a=o=f.length;o--;)u=f[o],!i&&g!==u.origType||n&&n.guid!==u.guid||s&&!s.test(u.namespace)||r&&r!==u.selector&&("**"!==r||!u.selector)||(f.splice(o,1),u.selector&&f.delegateCount--,d.remove&&d.remove.call(e,u));a&&!f.length&&(d.teardown&&!1!==d.teardown.call(e,h,m.handle)||E.removeEvent(e,p,m.handle),delete l[p])}else for(p in l)E.event.remove(e,p+t[c],n,r,!0);E.isEmptyObject(l)&&Q.remove(e,"handle events")}},dispatch:function(e){var t,n,r,i,o,a=new Array(arguments.length),s=E.event.fix(e),l=(Q.get(this,"events")||Object.create(null))[s.type]||[],e=E.event.special[s.type]||{};for(a[0]=s,t=1;t<arguments.length;t++)a[t]=arguments[t];if(s.delegateTarget=this,!e.preDispatch||!1!==e.preDispatch.call(this,s)){for(o=E.event.handlers.call(this,s,l),t=0;(r=o[t++])&&!s.isPropagationStopped();)for(s.currentTarget=r.elem,n=0;(i=r.handlers[n++])&&!s.isImmediatePropagationStopped();)s.rnamespace&&!1!==i.namespace&&!s.rnamespace.test(i.namespace)||(s.handleObj=i,s.data=i.data,void 0!==(i=((E.event.special[i.origType]||{}).handle||i.handler).apply(r.elem,a))&&!1===(s.result=i)&&(s.preventDefault(),s.stopPropagation()));return e.postDispatch&&e.postDispatch.call(this,s),s.result}},handlers:function(e,t){var n,r,i,o,a,s=[],l=t.delegateCount,c=e.target;if(l&&c.nodeType&&!("click"===e.type&&1<=e.button))for(;c!==this;c=c.parentNode||this)if(1===c.nodeType&&("click"!==e.type||!0!==c.disabled)){for(o=[],a={},n=0;n<l;n++)void 0===a[i=(r=t[n]).selector+" "]&&(a[i]=r.needsContext?-1<E(i,this).index(c):E.find(i,this,null,[c]).length),a[i]&&o.push(r);o.length&&s.push({elem:c,handlers:o})}return c=this,l<t.length&&s.push({elem:c,handlers:t.slice(l)}),s},addProp:function(t,e){Object.defineProperty(E.Event.prototype,t,{enumerable:!0,configurable:!0,get:b(e)?function(){if(this.originalEvent)return e(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[t]},set:function(e){Object.defineProperty(this,t,{enumerable:!0,configurable:!0,writable:!0,value:e})}})},fix:function(e){return e[E.expando]?e:new E.Event(e)},special:{load:{noBubble:!0},click:{setup:function(e){e=this||e;return ce.test(e.type)&&e.click&&S(e,"input")&&Ee(e,"click",_e),!1},trigger:function(e){e=this||e;return ce.test(e.type)&&e.click&&S(e,"input")&&Ee(e,"click"),!0},_default:function(e){e=e.target;return ce.test(e.type)&&e.click&&S(e,"input")&&Q.get(e,"click")||S(e,"a")}},beforeunload:{postDispatch:function(e){void 0!==e.result&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}}},E.removeEvent=function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n)},E.Event=function(e,t){if(!(this instanceof E.Event))return new E.Event(e,t);e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||void 0===e.defaultPrevented&&!1===e.returnValue?_e:xe,this.target=e.target&&3===e.target.nodeType?e.target.parentNode:e.target,this.currentTarget=e.currentTarget,this.relatedTarget=e.relatedTarget):this.type=e,t&&E.extend(this,t),this.timeStamp=e&&e.timeStamp||Date.now(),this[E.expando]=!0},E.Event.prototype={constructor:E.Event,isDefaultPrevented:xe,isPropagationStopped:xe,isImmediatePropagationStopped:xe,isSimulated:!1,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=_e,e&&!this.isSimulated&&e.preventDefault()},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=_e,e&&!this.isSimulated&&e.stopPropagation()},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=_e,e&&!this.isSimulated&&e.stopImmediatePropagation(),this.stopPropagation()}},E.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,char:!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:function(e){var t=e.button;return null==e.which&&ye.test(e.type)?null!=e.charCode?e.charCode:e.keyCode:!e.which&&void 0!==t&&be.test(e.type)?1&t?1:2&t?3:4&t?2:0:e.which}},E.event.addProp),E.each({focus:"focusin",blur:"focusout"},function(e,t){E.event.special[e]={setup:function(){return Ee(this,e,ke),!1},trigger:function(){return Ee(this,e),!0},delegateType:t}}),E.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(e,i){E.event.special[e]={delegateType:i,bindType:i,handle:function(e){var t,n=e.relatedTarget,r=e.handleObj;return n&&(n===this||E.contains(this,n))||(e.type=r.origType,t=r.handler.apply(this,arguments),e.type=i),t}}}),E.fn.extend({on:function(e,t,n,r){return Te(this,e,t,n,r)},one:function(e,t,n,r){return Te(this,e,t,n,r,1)},off:function(e,t,n){var r,i;if(e&&e.preventDefault&&e.handleObj)return r=e.handleObj,E(e.delegateTarget).off(r.namespace?r.origType+"."+r.namespace:r.origType,r.selector,r.handler),this;if("object"!=_typeof(e))return!1!==t&&"function"!=typeof t||(n=t,t=void 0),!1===n&&(n=xe),this.each(function(){E.event.remove(this,e,n,t)});for(i in e)this.off(i,t,e[i]);return this}});var Se=/<script|<style|<link/i,Ce=/checked\s*(?:[^=]|=\s*.checked.)/i,Ae=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g;function Re(e,t){return S(e,"table")&&S(11!==t.nodeType?t:t.firstChild,"tr")&&E(e).children("tbody")[0]||e}function Le(e){return e.type=(null!==e.getAttribute("type"))+"/"+e.type,e}function De(e){return"true/"===(e.type||"").slice(0,5)?e.type=e.type.slice(5):e.removeAttribute("type"),e}function Ie(e,t){var n,r,i,o;if(1===t.nodeType){if(Q.hasData(e)&&(o=Q.get(e).events))for(i in Q.remove(t,"handle events"),o)for(n=0,r=o[i].length;n<r;n++)E.event.add(t,i,o[i][n]);X.hasData(e)&&(e=X.access(e),e=E.extend({},e),X.set(t,e))}}function Ne(n,r,i,o){r=m(r);var e,t,a,s,l,c,u=0,d=n.length,f=d-1,p=r[0],h=b(p);if(h||1<d&&"string"==typeof p&&!y.checkClone&&Ce.test(p))return n.each(function(e){var t=n.eq(e);h&&(r[0]=p.call(this,e,t.html())),Ne(t,r,i,o)});if(d&&(t=(e=ve(r,n[0].ownerDocument,!1,n,o)).firstChild,1===e.childNodes.length&&(e=t),t||o)){for(s=(a=E.map(he(e,"script"),Le)).length;u<d;u++)l=e,u!==f&&(l=E.clone(l,!0,!0),s&&E.merge(a,he(l,"script"))),i.call(n[u],l,u);if(s)for(c=a[a.length-1].ownerDocument,E.map(a,De),u=0;u<s;u++)l=a[u],de.test(l.type||"")&&!Q.access(l,"globalEval")&&E.contains(c,l)&&(l.src&&"module"!==(l.type||"").toLowerCase()?E._evalUrl&&!l.noModule&&E._evalUrl(l.src,{nonce:l.nonce||l.getAttribute("nonce")},c):w(l.textContent.replace(Ae,""),l,c))}return n}function Me(e,t,n){for(var r,i=t?E.filter(t,e):e,o=0;null!=(r=i[o]);o++)n||1!==r.nodeType||E.cleanData(he(r)),r.parentNode&&(n&&re(r)&&ge(he(r,"script")),r.parentNode.removeChild(r));return e}E.extend({htmlPrefilter:function(e){return e},clone:function(e,t,n){var r,i,o,a,s,l,c,u=e.cloneNode(!0),d=re(e);if(!(y.noCloneChecked||1!==e.nodeType&&11!==e.nodeType||E.isXMLDoc(e)))for(a=he(u),r=0,i=(o=he(e)).length;r<i;r++)s=o[r],"input"===(c=(l=a[r]).nodeName.toLowerCase())&&ce.test(s.type)?l.checked=s.checked:"input"!==c&&"textarea"!==c||(l.defaultValue=s.defaultValue);if(t)if(n)for(o=o||he(e),a=a||he(u),r=0,i=o.length;r<i;r++)Ie(o[r],a[r]);else Ie(e,u);return 0<(a=he(u,"script")).length&&ge(a,!d&&he(e,"script")),u},cleanData:function(e){for(var t,n,r,i=E.event.special,o=0;void 0!==(n=e[o]);o++)if(W(n)){if(t=n[Q.expando]){if(t.events)for(r in t.events)i[r]?E.event.remove(n,r):E.removeEvent(n,r,t.handle);n[Q.expando]=void 0}n[X.expando]&&(n[X.expando]=void 0)}}}),E.fn.extend({detach:function(e){return Me(this,e,!0)},remove:function(e){return Me(this,e)},text:function(e){return q(this,function(e){return void 0===e?E.text(this):this.empty().each(function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=e)})},null,e,arguments.length)},append:function(){return Ne(this,arguments,function(e){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||Re(this,e).appendChild(e)})},prepend:function(){return Ne(this,arguments,function(e){var t;1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(t=Re(this,e)).insertBefore(e,t.firstChild)})},before:function(){return Ne(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this)})},after:function(){return Ne(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)})},empty:function(){for(var e,t=0;null!=(e=this[t]);t++)1===e.nodeType&&(E.cleanData(he(e,!1)),e.textContent="");return this},clone:function(e,t){return e=null!=e&&e,t=null==t?e:t,this.map(function(){return E.clone(this,e,t)})},html:function(e){return q(this,function(e){var t=this[0]||{},n=0,r=this.length;if(void 0===e&&1===t.nodeType)return t.innerHTML;if("string"==typeof e&&!Se.test(e)&&!pe[(ue.exec(e)||["",""])[1].toLowerCase()]){e=E.htmlPrefilter(e);try{for(;n<r;n++)1===(t=this[n]||{}).nodeType&&(E.cleanData(he(t,!1)),t.innerHTML=e);t=0}catch(e){}}t&&this.empty().append(e)},null,e,arguments.length)},replaceWith:function(){var n=[];return Ne(this,arguments,function(e){var t=this.parentNode;E.inArray(this,n)<0&&(E.cleanData(he(this)),t&&t.replaceChild(e,this))},n)}}),E.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(e,a){E.fn[e]=function(e){for(var t,n=[],r=E(e),i=r.length-1,o=0;o<=i;o++)t=o===i?this:this.clone(!0),E(r[o])[a](t),l.apply(n,t.get());return this.pushStack(n)}});function Oe(e,t,n){var r,i={};for(r in t)i[r]=e.style[r],e.style[r]=t[r];for(r in n=n.call(e),t)e.style[r]=i[r];return n}var je,Pe,Fe,Be,Ue,qe,He,$e,Ge=new RegExp("^("+Z+")(?!px)[a-z%]+$","i"),Ve=function(e){var t=e.ownerDocument.defaultView;return t&&t.opener||(t=k),t.getComputedStyle(e)},We=new RegExp(te.join("|"),"i");function ze(e,t,n){var r,i,o=e.style;return(n=n||Ve(e))&&(""!==(i=n.getPropertyValue(t)||n[t])||re(e)||(i=E.style(e,t)),!y.pixelBoxStyles()&&Ge.test(i)&&We.test(t)&&(r=o.width,e=o.minWidth,t=o.maxWidth,o.minWidth=o.maxWidth=o.width=i,i=n.width,o.width=r,o.minWidth=e,o.maxWidth=t)),void 0!==i?i+"":i}function Qe(e,t){return{get:function(){if(!e())return(this.get=t).apply(this,arguments);delete this.get}}}function Xe(){var e;$e&&(He.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",$e.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",ne.appendChild(He).appendChild($e),e=k.getComputedStyle($e),je="1%"!==e.top,qe=12===Ye(e.marginLeft),$e.style.right="60%",Be=36===Ye(e.right),Pe=36===Ye(e.width),$e.style.position="absolute",Fe=12===Ye($e.offsetWidth/3),ne.removeChild(He),$e=null)}function Ye(e){return Math.round(parseFloat(e))}He=T.createElement("div"),($e=T.createElement("div")).style&&($e.style.backgroundClip="content-box",$e.cloneNode(!0).style.backgroundClip="",y.clearCloneStyle="content-box"===$e.style.backgroundClip,E.extend(y,{boxSizingReliable:function(){return Xe(),Pe},pixelBoxStyles:function(){return Xe(),Be},pixelPosition:function(){return Xe(),je},reliableMarginLeft:function(){return Xe(),qe},scrollboxSize:function(){return Xe(),Fe},reliableTrDimensions:function(){var e,t,n;return null==Ue&&(e=T.createElement("table"),n=T.createElement("tr"),t=T.createElement("div"),e.style.cssText="position:absolute;left:-11111px",n.style.height="1px",t.style.height="9px",ne.appendChild(e).appendChild(n).appendChild(t),n=k.getComputedStyle(n),Ue=3<parseInt(n.height),ne.removeChild(e)),Ue}}));var Ke=["Webkit","Moz","ms"],Je=T.createElement("div").style,Ze={};function et(e){return E.cssProps[e]||Ze[e]||(e in Je?e:Ze[e]=function(e){for(var t=e[0].toUpperCase()+e.slice(1),n=Ke.length;n--;)if((e=Ke[n]+t)in Je)return e}(e)||e)}var tt=/^(none|table(?!-c[ea]).+)/,nt=/^--/,rt={position:"absolute",visibility:"hidden",display:"block"},it={letterSpacing:"0",fontWeight:"400"};function ot(e,t,n){var r=ee.exec(t);return r?Math.max(0,r[2]-(n||0))+(r[3]||"px"):t}function at(e,t,n,r,i,o){var a="width"===t?1:0,s=0,l=0;if(n===(r?"border":"content"))return 0;for(;a<4;a+=2)"margin"===n&&(l+=E.css(e,n+te[a],!0,i)),r?("content"===n&&(l-=E.css(e,"padding"+te[a],!0,i)),"margin"!==n&&(l-=E.css(e,"border"+te[a]+"Width",!0,i))):(l+=E.css(e,"padding"+te[a],!0,i),"padding"!==n?l+=E.css(e,"border"+te[a]+"Width",!0,i):s+=E.css(e,"border"+te[a]+"Width",!0,i));return!r&&0<=o&&(l+=Math.max(0,Math.ceil(e["offset"+t[0].toUpperCase()+t.slice(1)]-o-l-s-.5))||0),l}function st(e,t,n){var r=Ve(e),i=(!y.boxSizingReliable()||n)&&"border-box"===E.css(e,"boxSizing",!1,r),o=i,a=ze(e,t,r),s="offset"+t[0].toUpperCase()+t.slice(1);if(Ge.test(a)){if(!n)return a;a="auto"}return(!y.boxSizingReliable()&&i||!y.reliableTrDimensions()&&S(e,"tr")||"auto"===a||!parseFloat(a)&&"inline"===E.css(e,"display",!1,r))&&e.getClientRects().length&&(i="border-box"===E.css(e,"boxSizing",!1,r),(o=s in e)&&(a=e[s])),(a=parseFloat(a)||0)+at(e,t,n||(i?"border":"content"),o,r,a)+"px"}function lt(e,t,n,r,i){return new lt.prototype.init(e,t,n,r,i)}E.extend({cssHooks:{opacity:{get:function(e,t){if(t){e=ze(e,"opacity");return""===e?"1":e}}}},cssNumber:{animationIterationCount:!0,columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{},style:function(e,t,n,r){if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style){var i,o,a,s=V(t),l=nt.test(t),c=e.style;if(l||(t=et(s)),a=E.cssHooks[t]||E.cssHooks[s],void 0===n)return a&&"get"in a&&void 0!==(i=a.get(e,!1,r))?i:c[t];"string"===(o=_typeof(n))&&(i=ee.exec(n))&&i[1]&&(n=ae(e,t,i),o="number"),null!=n&&n==n&&("number"!==o||l||(n+=i&&i[3]||(E.cssNumber[s]?"":"px")),y.clearCloneStyle||""!==n||0!==t.indexOf("background")||(c[t]="inherit"),a&&"set"in a&&void 0===(n=a.set(e,n,r))||(l?c.setProperty(t,n):c[t]=n))}},css:function(e,t,n,r){var i,o=V(t);return nt.test(t)||(t=et(o)),(o=E.cssHooks[t]||E.cssHooks[o])&&"get"in o&&(i=o.get(e,!0,n)),void 0===i&&(i=ze(e,t,r)),"normal"===i&&t in it&&(i=it[t]),""===n||n?(t=parseFloat(i),!0===n||isFinite(t)?t||0:i):i}}),E.each(["height","width"],function(e,s){E.cssHooks[s]={get:function(e,t,n){if(t)return!tt.test(E.css(e,"display"))||e.getClientRects().length&&e.getBoundingClientRect().width?st(e,s,n):Oe(e,rt,function(){return st(e,s,n)})},set:function(e,t,n){var r,i=Ve(e),o=!y.scrollboxSize()&&"absolute"===i.position,a=(o||n)&&"border-box"===E.css(e,"boxSizing",!1,i),n=n?at(e,s,n,a,i):0;return a&&o&&(n-=Math.ceil(e["offset"+s[0].toUpperCase()+s.slice(1)]-parseFloat(i[s])-at(e,s,"border",!1,i)-.5)),n&&(r=ee.exec(t))&&"px"!==(r[3]||"px")&&(e.style[s]=t,t=E.css(e,s)),ot(0,t,n)}}}),E.cssHooks.marginLeft=Qe(y.reliableMarginLeft,function(e,t){if(t)return(parseFloat(ze(e,"marginLeft"))||e.getBoundingClientRect().left-Oe(e,{marginLeft:0},function(){return e.getBoundingClientRect().left}))+"px"}),E.each({margin:"",padding:"",border:"Width"},function(i,o){E.cssHooks[i+o]={expand:function(e){for(var t=0,n={},r="string"==typeof e?e.split(" "):[e];t<4;t++)n[i+te[t]+o]=r[t]||r[t-2]||r[0];return n}},"margin"!==i&&(E.cssHooks[i+o].set=ot)}),E.fn.extend({css:function(e,t){return q(this,function(e,t,n){var r,i,o={},a=0;if(Array.isArray(t)){for(r=Ve(e),i=t.length;a<i;a++)o[t[a]]=E.css(e,t[a],!1,r);return o}return void 0!==n?E.style(e,t,n):E.css(e,t)},e,t,1<arguments.length)}}),((E.Tween=lt).prototype={constructor:lt,init:function(e,t,n,r,i,o){this.elem=e,this.prop=n,this.easing=i||E.easing._default,this.options=t,this.start=this.now=this.cur(),this.end=r,this.unit=o||(E.cssNumber[n]?"":"px")},cur:function(){var e=lt.propHooks[this.prop];return(e&&e.get?e:lt.propHooks._default).get(this)},run:function(e){var t,n=lt.propHooks[this.prop];return this.options.duration?this.pos=t=E.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):this.pos=t=e,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),(n&&n.set?n:lt.propHooks._default).set(this),this}}).init.prototype=lt.prototype,(lt.propHooks={_default:{get:function(e){return 1!==e.elem.nodeType||null!=e.elem[e.prop]&&null==e.elem.style[e.prop]?e.elem[e.prop]:(e=E.css(e.elem,e.prop,""))&&"auto"!==e?e:0},set:function(e){E.fx.step[e.prop]?E.fx.step[e.prop](e):1!==e.elem.nodeType||!E.cssHooks[e.prop]&&null==e.elem.style[et(e.prop)]?e.elem[e.prop]=e.now:E.style(e.elem,e.prop,e.now+e.unit)}}}).scrollTop=lt.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},E.easing={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2},_default:"swing"},E.fx=lt.prototype.init,E.fx.step={};var ct,ut,dt=/^(?:toggle|show|hide)$/,ft=/queueHooks$/;function pt(){ut&&(!1===T.hidden&&k.requestAnimationFrame?k.requestAnimationFrame(pt):k.setTimeout(pt,E.fx.interval),E.fx.tick())}function ht(){return k.setTimeout(function(){ct=void 0}),ct=Date.now()}function gt(e,t){var n,r=0,i={height:e};for(t=t?1:0;r<4;r+=2-t)i["margin"+(n=te[r])]=i["padding"+n]=e;return t&&(i.opacity=i.width=e),i}function mt(e,t,n){for(var r,i=(vt.tweeners[t]||[]).concat(vt.tweeners["*"]),o=0,a=i.length;o<a;o++)if(r=i[o].call(n,t,e))return r}function vt(i,e,t){var n,o,r=0,a=vt.prefilters.length,s=E.Deferred().always(function(){delete l.elem}),l=function(){if(o)return!1;for(var e=ct||ht(),e=Math.max(0,c.startTime+c.duration-e),t=1-(e/c.duration||0),n=0,r=c.tweens.length;n<r;n++)c.tweens[n].run(t);return s.notifyWith(i,[c,t,e]),t<1&&r?e:(r||s.notifyWith(i,[c,1,0]),s.resolveWith(i,[c]),!1)},c=s.promise({elem:i,props:E.extend({},e),opts:E.extend(!0,{specialEasing:{},easing:E.easing._default},t),originalProperties:e,originalOptions:t,startTime:ct||ht(),duration:t.duration,tweens:[],createTween:function(e,t){e=E.Tween(i,c.opts,e,t,c.opts.specialEasing[e]||c.opts.easing);return c.tweens.push(e),e},stop:function(e){var t=0,n=e?c.tweens.length:0;if(o)return this;for(o=!0;t<n;t++)c.tweens[t].run(1);return e?(s.notifyWith(i,[c,1,0]),s.resolveWith(i,[c,e])):s.rejectWith(i,[c,e]),this}}),u=c.props;for(function(e,t){var n,r,i,o,a;for(n in e)if(i=t[r=V(n)],o=e[n],Array.isArray(o)&&(i=o[1],o=e[n]=o[0]),n!==r&&(e[r]=o,delete e[n]),(a=E.cssHooks[r])&&"expand"in a)for(n in o=a.expand(o),delete e[r],o)n in e||(e[n]=o[n],t[n]=i);else t[r]=i}(u,c.opts.specialEasing);r<a;r++)if(n=vt.prefilters[r].call(c,i,u,c.opts))return b(n.stop)&&(E._queueHooks(c.elem,c.opts.queue).stop=n.stop.bind(n)),n;return E.map(u,mt,c),b(c.opts.start)&&c.opts.start.call(i,c),c.progress(c.opts.progress).done(c.opts.done,c.opts.complete).fail(c.opts.fail).always(c.opts.always),E.fx.timer(E.extend(l,{elem:i,anim:c,queue:c.opts.queue})),c}E.Animation=E.extend(vt,{tweeners:{"*":[function(e,t){var n=this.createTween(e,t);return ae(n.elem,e,ee.exec(t),n),n}]},tweener:function(e,t){for(var n,r=0,i=(e=b(e)?(t=e,["*"]):e.match(M)).length;r<i;r++)n=e[r],vt.tweeners[n]=vt.tweeners[n]||[],vt.tweeners[n].unshift(t)},prefilters:[function(e,t,n){var r,i,o,a,s,l,c,u="width"in t||"height"in t,d=this,f={},p=e.style,h=e.nodeType&&oe(e),g=Q.get(e,"fxshow");for(r in n.queue||(null==(a=E._queueHooks(e,"fx")).unqueued&&(a.unqueued=0,s=a.empty.fire,a.empty.fire=function(){a.unqueued||s()}),a.unqueued++,d.always(function(){d.always(function(){a.unqueued--,E.queue(e,"fx").length||a.empty.fire()})})),t)if(i=t[r],dt.test(i)){if(delete t[r],o=o||"toggle"===i,i===(h?"hide":"show")){if("show"!==i||!g||void 0===g[r])continue;h=!0}f[r]=g&&g[r]||E.style(e,r)}if((l=!E.isEmptyObject(t))||!E.isEmptyObject(f))for(r in u&&1===e.nodeType&&(n.overflow=[p.overflow,p.overflowX,p.overflowY],null==(c=g&&g.display)&&(c=Q.get(e,"display")),"none"===(u=E.css(e,"display"))&&(c?u=c:(le([e],!0),c=e.style.display||c,u=E.css(e,"display"),le([e]))),("inline"===u||"inline-block"===u&&null!=c)&&"none"===E.css(e,"float")&&(l||(d.done(function(){p.display=c}),null==c&&(u=p.display,c="none"===u?"":u)),p.display="inline-block")),n.overflow&&(p.overflow="hidden",d.always(function(){p.overflow=n.overflow[0],p.overflowX=n.overflow[1],p.overflowY=n.overflow[2]})),l=!1,f)l||(g?"hidden"in g&&(h=g.hidden):g=Q.access(e,"fxshow",{display:c}),o&&(g.hidden=!h),h&&le([e],!0),d.done(function(){for(r in h||le([e]),Q.remove(e,"fxshow"),f)E.style(e,r,f[r])})),l=mt(h?g[r]:0,r,d),r in g||(g[r]=l.start,h&&(l.end=l.start,l.start=0))}],prefilter:function(e,t){t?vt.prefilters.unshift(e):vt.prefilters.push(e)}}),E.speed=function(e,t,n){var r=e&&"object"==_typeof(e)?E.extend({},e):{complete:n||!n&&t||b(e)&&e,duration:e,easing:n&&t||t&&!b(t)&&t};return E.fx.off?r.duration=0:"number"!=typeof r.duration&&(r.duration in E.fx.speeds?r.duration=E.fx.speeds[r.duration]:r.duration=E.fx.speeds._default),null!=r.queue&&!0!==r.queue||(r.queue="fx"),r.old=r.complete,r.complete=function(){b(r.old)&&r.old.call(this),r.queue&&E.dequeue(this,r.queue)},r},E.fn.extend({fadeTo:function(e,t,n,r){return this.filter(oe).css("opacity",0).show().end().animate({opacity:t},e,n,r)},animate:function(t,e,n,r){var i=E.isEmptyObject(t),o=E.speed(e,n,r),r=function(){var e=vt(this,E.extend({},t),o);(i||Q.get(this,"finish"))&&e.stop(!0)};return r.finish=r,i||!1===o.queue?this.each(r):this.queue(o.queue,r)},stop:function(i,e,o){function a(e){var t=e.stop;delete e.stop,t(o)}return"string"!=typeof i&&(o=e,e=i,i=void 0),e&&this.queue(i||"fx",[]),this.each(function(){var e=!0,t=null!=i&&i+"queueHooks",n=E.timers,r=Q.get(this);if(t)r[t]&&r[t].stop&&a(r[t]);else for(t in r)r[t]&&r[t].stop&&ft.test(t)&&a(r[t]);for(t=n.length;t--;)n[t].elem!==this||null!=i&&n[t].queue!==i||(n[t].anim.stop(o),e=!1,n.splice(t,1));!e&&o||E.dequeue(this,i)})},finish:function(a){return!1!==a&&(a=a||"fx"),this.each(function(){var e,t=Q.get(this),n=t[a+"queue"],r=t[a+"queueHooks"],i=E.timers,o=n?n.length:0;for(t.finish=!0,E.queue(this,a,[]),r&&r.stop&&r.stop.call(this,!0),e=i.length;e--;)i[e].elem===this&&i[e].queue===a&&(i[e].anim.stop(!0),i.splice(e,1));for(e=0;e<o;e++)n[e]&&n[e].finish&&n[e].finish.call(this);delete t.finish})}}),E.each(["toggle","show","hide"],function(e,r){var i=E.fn[r];E.fn[r]=function(e,t,n){return null==e||"boolean"==typeof e?i.apply(this,arguments):this.animate(gt(r,!0),e,t,n)}}),E.each({slideDown:gt("show"),slideUp:gt("hide"),slideToggle:gt("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(e,r){E.fn[e]=function(e,t,n){return this.animate(r,e,t,n)}}),E.timers=[],E.fx.tick=function(){var e,t=0,n=E.timers;for(ct=Date.now();t<n.length;t++)(e=n[t])()||n[t]!==e||n.splice(t--,1);n.length||E.fx.stop(),ct=void 0},E.fx.timer=function(e){E.timers.push(e),E.fx.start()},E.fx.interval=13,E.fx.start=function(){ut||(ut=!0,pt())},E.fx.stop=function(){ut=null},E.fx.speeds={slow:600,fast:200,_default:400},E.fn.delay=function(r,e){return r=E.fx&&E.fx.speeds[r]||r,e=e||"fx",this.queue(e,function(e,t){var n=k.setTimeout(e,r);t.stop=function(){k.clearTimeout(n)}})},fe=T.createElement("input"),Z=T.createElement("select").appendChild(T.createElement("option")),fe.type="checkbox",y.checkOn=""!==fe.value,y.optSelected=Z.selected,(fe=T.createElement("input")).value="t",fe.type="radio",y.radioValue="t"===fe.value;var yt,bt=E.expr.attrHandle;E.fn.extend({attr:function(e,t){return q(this,E.attr,e,t,1<arguments.length)},removeAttr:function(e){return this.each(function(){E.removeAttr(this,e)})}}),E.extend({attr:function(e,t,n){var r,i,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return void 0===e.getAttribute?E.prop(e,t,n):(1===o&&E.isXMLDoc(e)||(i=E.attrHooks[t.toLowerCase()]||(E.expr.match.bool.test(t)?yt:void 0)),void 0!==n?null===n?void E.removeAttr(e,t):i&&"set"in i&&void 0!==(r=i.set(e,n,t))?r:(e.setAttribute(t,n+""),n):!(i&&"get"in i&&null!==(r=i.get(e,t)))&&null==(r=E.find.attr(e,t))?void 0:r)},attrHooks:{type:{set:function(e,t){if(!y.radioValue&&"radio"===t&&S(e,"input")){var n=e.value;return e.setAttribute("type",t),n&&(e.value=n),t}}}},removeAttr:function(e,t){var n,r=0,i=t&&t.match(M);if(i&&1===e.nodeType)for(;n=i[r++];)e.removeAttribute(n)}}),yt={set:function(e,t,n){return!1===t?E.removeAttr(e,n):e.setAttribute(n,n),n}},E.each(E.expr.match.bool.source.match(/\w+/g),function(e,t){var a=bt[t]||E.find.attr;bt[t]=function(e,t,n){var r,i,o=t.toLowerCase();return n||(i=bt[o],bt[o]=r,r=null!=a(e,t,n)?o:null,bt[o]=i),r}});var wt=/^(?:input|select|textarea|button)$/i,_t=/^(?:a|area)$/i;function xt(e){return(e.match(M)||[]).join(" ")}function kt(e){return e.getAttribute&&e.getAttribute("class")||""}function Tt(e){return Array.isArray(e)?e:"string"==typeof e&&e.match(M)||[]}E.fn.extend({prop:function(e,t){return q(this,E.prop,e,t,1<arguments.length)},removeProp:function(e){return this.each(function(){delete this[E.propFix[e]||e]})}}),E.extend({prop:function(e,t,n){var r,i,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return 1===o&&E.isXMLDoc(e)||(t=E.propFix[t]||t,i=E.propHooks[t]),void 0!==n?i&&"set"in i&&void 0!==(r=i.set(e,n,t))?r:e[t]=n:i&&"get"in i&&null!==(r=i.get(e,t))?r:e[t]},propHooks:{tabIndex:{get:function(e){var t=E.find.attr(e,"tabindex");return t?parseInt(t,10):wt.test(e.nodeName)||_t.test(e.nodeName)&&e.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),y.optSelected||(E.propHooks.selected={get:function(e){e=e.parentNode;return e&&e.parentNode&&e.parentNode.selectedIndex,null},set:function(e){e=e.parentNode;e&&(e.selectedIndex,e.parentNode&&e.parentNode.selectedIndex)}}),E.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){E.propFix[this.toLowerCase()]=this}),E.fn.extend({addClass:function(t){var e,n,r,i,o,a,s=0;if(b(t))return this.each(function(e){E(this).addClass(t.call(this,e,kt(this)))});if((e=Tt(t)).length)for(;n=this[s++];)if(a=kt(n),r=1===n.nodeType&&" "+xt(a)+" "){for(o=0;i=e[o++];)r.indexOf(" "+i+" ")<0&&(r+=i+" ");a!==(a=xt(r))&&n.setAttribute("class",a)}return this},removeClass:function(t){var e,n,r,i,o,a,s=0;if(b(t))return this.each(function(e){E(this).removeClass(t.call(this,e,kt(this)))});if(!arguments.length)return this.attr("class","");if((e=Tt(t)).length)for(;n=this[s++];)if(a=kt(n),r=1===n.nodeType&&" "+xt(a)+" "){for(o=0;i=e[o++];)for(;-1<r.indexOf(" "+i+" ");)r=r.replace(" "+i+" "," ");a!==(a=xt(r))&&n.setAttribute("class",a)}return this},toggleClass:function(i,t){var o=_typeof(i),a="string"===o||Array.isArray(i);return"boolean"==typeof t&&a?t?this.addClass(i):this.removeClass(i):b(i)?this.each(function(e){E(this).toggleClass(i.call(this,e,kt(this),t),t)}):this.each(function(){var e,t,n,r;if(a)for(t=0,n=E(this),r=Tt(i);e=r[t++];)n.hasClass(e)?n.removeClass(e):n.addClass(e);else void 0!==i&&"boolean"!==o||((e=kt(this))&&Q.set(this,"__className__",e),this.setAttribute&&this.setAttribute("class",!e&&!1!==i&&Q.get(this,"__className__")||""))})},hasClass:function(e){for(var t,n=0,r=" "+e+" ";t=this[n++];)if(1===t.nodeType&&-1<(" "+xt(kt(t))+" ").indexOf(r))return!0;return!1}});var Et=/\r/g;E.fn.extend({val:function(t){var n,e,r,i=this[0];return arguments.length?(r=b(t),this.each(function(e){1===this.nodeType&&(null==(e=r?t.call(this,e,E(this).val()):t)?e="":"number"==typeof e?e+="":Array.isArray(e)&&(e=E.map(e,function(e){return null==e?"":e+""})),(n=E.valHooks[this.type]||E.valHooks[this.nodeName.toLowerCase()])&&"set"in n&&void 0!==n.set(this,e,"value")||(this.value=e))})):i?(n=E.valHooks[i.type]||E.valHooks[i.nodeName.toLowerCase()])&&"get"in n&&void 0!==(e=n.get(i,"value"))?e:"string"==typeof(e=i.value)?e.replace(Et,""):null==e?"":e:void 0}}),E.extend({valHooks:{option:{get:function(e){var t=E.find.attr(e,"value");return null!=t?t:xt(E.text(e))}},select:{get:function(e){for(var t,n=e.options,r=e.selectedIndex,i="select-one"===e.type,o=i?null:[],a=i?r+1:n.length,s=r<0?a:i?r:0;s<a;s++)if(((t=n[s]).selected||s===r)&&!t.disabled&&(!t.parentNode.disabled||!S(t.parentNode,"optgroup"))){if(t=E(t).val(),i)return t;o.push(t)}return o},set:function(e,t){for(var n,r,i=e.options,o=E.makeArray(t),a=i.length;a--;)((r=i[a]).selected=-1<E.inArray(E.valHooks.option.get(r),o))&&(n=!0);return n||(e.selectedIndex=-1),o}}}}),E.each(["radio","checkbox"],function(){E.valHooks[this]={set:function(e,t){if(Array.isArray(t))return e.checked=-1<E.inArray(E(e).val(),t)}},y.checkOn||(E.valHooks[this].get=function(e){return null===e.getAttribute("value")?"on":e.value})}),y.focusin="onfocusin"in k;function St(e){e.stopPropagation()}var Ct=/^(?:focusinfocus|focusoutblur)$/;E.extend(E.event,{trigger:function(e,t,n,r){var i,o,a,s,l,c,u,d=[n||T],f=v.call(e,"type")?e.type:e,p=v.call(e,"namespace")?e.namespace.split("."):[],h=u=o=n=n||T;if(3!==n.nodeType&&8!==n.nodeType&&!Ct.test(f+E.event.triggered)&&(-1<f.indexOf(".")&&(f=(p=f.split(".")).shift(),p.sort()),s=f.indexOf(":")<0&&"on"+f,(e=e[E.expando]?e:new E.Event(f,"object"==_typeof(e)&&e)).isTrigger=r?2:3,e.namespace=p.join("."),e.rnamespace=e.namespace?new RegExp("(^|\\.)"+p.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,e.result=void 0,e.target||(e.target=n),t=null==t?[e]:E.makeArray(t,[e]),c=E.event.special[f]||{},r||!c.trigger||!1!==c.trigger.apply(n,t))){if(!r&&!c.noBubble&&!g(n)){for(a=c.delegateType||f,Ct.test(a+f)||(h=h.parentNode);h;h=h.parentNode)d.push(h),o=h;o===(n.ownerDocument||T)&&d.push(o.defaultView||o.parentWindow||k)}for(i=0;(h=d[i++])&&!e.isPropagationStopped();)u=h,e.type=1<i?a:c.bindType||f,(l=(Q.get(h,"events")||Object.create(null))[e.type]&&Q.get(h,"handle"))&&l.apply(h,t),(l=s&&h[s])&&l.apply&&W(h)&&(e.result=l.apply(h,t),!1===e.result&&e.preventDefault());return e.type=f,r||e.isDefaultPrevented()||c._default&&!1!==c._default.apply(d.pop(),t)||!W(n)||s&&b(n[f])&&!g(n)&&((o=n[s])&&(n[s]=null),E.event.triggered=f,e.isPropagationStopped()&&u.addEventListener(f,St),n[f](),e.isPropagationStopped()&&u.removeEventListener(f,St),E.event.triggered=void 0,o&&(n[s]=o)),e.result}},simulate:function(e,t,n){e=E.extend(new E.Event,n,{type:e,isSimulated:!0});E.event.trigger(e,null,t)}}),E.fn.extend({trigger:function(e,t){return this.each(function(){E.event.trigger(e,t,this)})},triggerHandler:function(e,t){var n=this[0];if(n)return E.event.trigger(e,t,n,!0)}}),y.focusin||E.each({focus:"focusin",blur:"focusout"},function(n,r){function i(e){E.event.simulate(r,e.target,E.event.fix(e))}E.event.special[r]={setup:function(){var e=this.ownerDocument||this.document||this,t=Q.access(e,r);t||e.addEventListener(n,i,!0),Q.access(e,r,(t||0)+1)},teardown:function(){var e=this.ownerDocument||this.document||this,t=Q.access(e,r)-1;t?Q.access(e,r,t):(e.removeEventListener(n,i,!0),Q.remove(e,r))}}});var At=k.location,Rt={guid:Date.now()},Lt=/\?/;E.parseXML=function(e){var t;if(!e||"string"!=typeof e)return null;try{t=(new k.DOMParser).parseFromString(e,"text/xml")}catch(e){t=void 0}return t&&!t.getElementsByTagName("parsererror").length||E.error("Invalid XML: "+e),t};var Dt=/\[\]$/,It=/\r?\n/g,Nt=/^(?:submit|button|image|reset|file)$/i,Mt=/^(?:input|select|textarea|keygen)/i;E.param=function(e,t){function n(e,t){t=b(t)?t():t,i[i.length]=encodeURIComponent(e)+"="+encodeURIComponent(null==t?"":t)}var r,i=[];if(null==e)return"";if(Array.isArray(e)||e.jquery&&!E.isPlainObject(e))E.each(e,function(){n(this.name,this.value)});else for(r in e)!function n(r,e,i,o){if(Array.isArray(e))E.each(e,function(e,t){i||Dt.test(r)?o(r,t):n(r+"["+("object"==_typeof(t)&&null!=t?e:"")+"]",t,i,o)});else if(i||"object"!==h(e))o(r,e);else for(var t in e)n(r+"["+t+"]",e[t],i,o)}(r,e[r],t,n);return i.join("&")},E.fn.extend({serialize:function(){return E.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var e=E.prop(this,"elements");return e?E.makeArray(e):this}).filter(function(){var e=this.type;return this.name&&!E(this).is(":disabled")&&Mt.test(this.nodeName)&&!Nt.test(e)&&(this.checked||!ce.test(e))}).map(function(e,t){var n=E(this).val();return null==n?null:Array.isArray(n)?E.map(n,function(e){return{name:t.name,value:e.replace(It,"\r\n")}}):{name:t.name,value:n.replace(It,"\r\n")}}).get()}});var Ot=/%20/g,jt=/#.*$/,Pt=/([?&])_=[^&]*/,Ft=/^(.*?):[ \t]*([^\r\n]*)$/gm,Bt=/^(?:GET|HEAD)$/,Ut=/^\/\//,qt={},Ht={},$t="*/".concat("*"),Gt=T.createElement("a");function Vt(o){return function(e,t){"string"!=typeof e&&(t=e,e="*");var n,r=0,i=e.toLowerCase().match(M)||[];if(b(t))for(;n=i[r++];)"+"===n[0]?(n=n.slice(1)||"*",(o[n]=o[n]||[]).unshift(t)):(o[n]=o[n]||[]).push(t)}}function Wt(t,r,i,o){var a={},s=t===Ht;function l(e){var n;return a[e]=!0,E.each(t[e]||[],function(e,t){t=t(r,i,o);return"string"!=typeof t||s||a[t]?s?!(n=t):void 0:(r.dataTypes.unshift(t),l(t),!1)}),n}return l(r.dataTypes[0])||!a["*"]&&l("*")}function zt(e,t){var n,r,i=E.ajaxSettings.flatOptions||{};for(n in t)void 0!==t[n]&&((i[n]?e:r=r||{})[n]=t[n]);return r&&E.extend(!0,e,r),e}Gt.href=At.href,E.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:At.href,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(At.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":$t,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":E.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(e,t){return t?zt(zt(e,E.ajaxSettings),t):zt(E.ajaxSettings,e)},ajaxPrefilter:Vt(qt),ajaxTransport:Vt(Ht),ajax:function(e,t){"object"==_typeof(e)&&(t=e,e=void 0),t=t||{};var l,c,u,n,d,r,f,p,i,o,h=E.ajaxSetup({},t),g=h.context||h,m=h.context&&(g.nodeType||g.jquery)?E(g):E.event,v=E.Deferred(),y=E.Callbacks("once memory"),b=h.statusCode||{},a={},s={},w="canceled",_={readyState:0,getResponseHeader:function(e){var t;if(f){if(!n)for(n={};t=Ft.exec(u);)n[t[1].toLowerCase()+" "]=(n[t[1].toLowerCase()+" "]||[]).concat(t[2]);t=n[e.toLowerCase()+" "]}return null==t?null:t.join(", ")},getAllResponseHeaders:function(){return f?u:null},setRequestHeader:function(e,t){return null==f&&(e=s[e.toLowerCase()]=s[e.toLowerCase()]||e,a[e]=t),this},overrideMimeType:function(e){return null==f&&(h.mimeType=e),this},statusCode:function(e){if(e)if(f)_.always(e[_.status]);else for(var t in e)b[t]=[b[t],e[t]];return this},abort:function(e){e=e||w;return l&&l.abort(e),x(0,e),this}};if(v.promise(_),h.url=((e||h.url||At.href)+"").replace(Ut,At.protocol+"//"),h.type=t.method||t.type||h.method||h.type,h.dataTypes=(h.dataType||"*").toLowerCase().match(M)||[""],null==h.crossDomain){r=T.createElement("a");try{r.href=h.url,r.href=r.href,h.crossDomain=Gt.protocol+"//"+Gt.host!=r.protocol+"//"+r.host}catch(e){h.crossDomain=!0}}if(h.data&&h.processData&&"string"!=typeof h.data&&(h.data=E.param(h.data,h.traditional)),Wt(qt,h,t,_),f)return _;for(i in(p=E.event&&h.global)&&0==E.active++&&E.event.trigger("ajaxStart"),h.type=h.type.toUpperCase(),h.hasContent=!Bt.test(h.type),c=h.url.replace(jt,""),h.hasContent?h.data&&h.processData&&0===(h.contentType||"").indexOf("application/x-www-form-urlencoded")&&(h.data=h.data.replace(Ot,"+")):(o=h.url.slice(c.length),h.data&&(h.processData||"string"==typeof h.data)&&(c+=(Lt.test(c)?"&":"?")+h.data,delete h.data),!1===h.cache&&(c=c.replace(Pt,"$1"),o=(Lt.test(c)?"&":"?")+"_="+Rt.guid+++o),h.url=c+o),h.ifModified&&(E.lastModified[c]&&_.setRequestHeader("If-Modified-Since",E.lastModified[c]),E.etag[c]&&_.setRequestHeader("If-None-Match",E.etag[c])),(h.data&&h.hasContent&&!1!==h.contentType||t.contentType)&&_.setRequestHeader("Content-Type",h.contentType),_.setRequestHeader("Accept",h.dataTypes[0]&&h.accepts[h.dataTypes[0]]?h.accepts[h.dataTypes[0]]+("*"!==h.dataTypes[0]?", "+$t+"; q=0.01":""):h.accepts["*"]),h.headers)_.setRequestHeader(i,h.headers[i]);if(h.beforeSend&&(!1===h.beforeSend.call(g,_,h)||f))return _.abort();if(w="abort",y.add(h.complete),_.done(h.success),_.fail(h.error),l=Wt(Ht,h,t,_)){if(_.readyState=1,p&&m.trigger("ajaxSend",[_,h]),f)return _;h.async&&0<h.timeout&&(d=k.setTimeout(function(){_.abort("timeout")},h.timeout));try{f=!1,l.send(a,x)}catch(e){if(f)throw e;x(-1,e)}}else x(-1,"No Transport");function x(e,t,n,r){var i,o,a,s=t;f||(f=!0,d&&k.clearTimeout(d),l=void 0,u=r||"",_.readyState=0<e?4:0,r=200<=e&&e<300||304===e,n&&(a=function(e,t,n){for(var r,i,o,a,s=e.contents,l=e.dataTypes;"*"===l[0];)l.shift(),void 0===r&&(r=e.mimeType||t.getResponseHeader("Content-Type"));if(r)for(i in s)if(s[i]&&s[i].test(r)){l.unshift(i);break}if(l[0]in n)o=l[0];else{for(i in n){if(!l[0]||e.converters[i+" "+l[0]]){o=i;break}a=a||i}o=o||a}if(o)return o!==l[0]&&l.unshift(o),n[o]}(h,_,n)),!r&&-1<E.inArray("script",h.dataTypes)&&(h.converters["text script"]=function(){}),a=function(e,t,n,r){var i,o,a,s,l,c={},u=e.dataTypes.slice();if(u[1])for(a in e.converters)c[a.toLowerCase()]=e.converters[a];for(o=u.shift();o;)if(e.responseFields[o]&&(n[e.responseFields[o]]=t),!l&&r&&e.dataFilter&&(t=e.dataFilter(t,e.dataType)),l=o,o=u.shift())if("*"===o)o=l;else if("*"!==l&&l!==o){if(!(a=c[l+" "+o]||c["* "+o]))for(i in c)if((s=i.split(" "))[1]===o&&(a=c[l+" "+s[0]]||c["* "+s[0]])){!0===a?a=c[i]:!0!==c[i]&&(o=s[0],u.unshift(s[1]));break}if(!0!==a)if(a&&e.throws)t=a(t);else try{t=a(t)}catch(e){return{state:"parsererror",error:a?e:"No conversion from "+l+" to "+o}}}return{state:"success",data:t}}(h,a,_,r),r?(h.ifModified&&((n=_.getResponseHeader("Last-Modified"))&&(E.lastModified[c]=n),(n=_.getResponseHeader("etag"))&&(E.etag[c]=n)),204===e||"HEAD"===h.type?s="nocontent":304===e?s="notmodified":(s=a.state,i=a.data,r=!(o=a.error))):(o=s,!e&&s||(s="error",e<0&&(e=0))),_.status=e,_.statusText=(t||s)+"",r?v.resolveWith(g,[i,s,_]):v.rejectWith(g,[_,s,o]),_.statusCode(b),b=void 0,p&&m.trigger(r?"ajaxSuccess":"ajaxError",[_,h,r?i:o]),y.fireWith(g,[_,s]),p&&(m.trigger("ajaxComplete",[_,h]),--E.active||E.event.trigger("ajaxStop")))}return _},getJSON:function(e,t,n){return E.get(e,t,n,"json")},getScript:function(e,t){return E.get(e,void 0,t,"script")}}),E.each(["get","post"],function(e,i){E[i]=function(e,t,n,r){return b(t)&&(r=r||n,n=t,t=void 0),E.ajax(E.extend({url:e,type:i,dataType:r,data:t,success:n},E.isPlainObject(e)&&e))}}),E.ajaxPrefilter(function(e){for(var t in e.headers)"content-type"===t.toLowerCase()&&(e.contentType=e.headers[t]||"")}),E._evalUrl=function(e,t,n){return E.ajax({url:e,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,converters:{"text script":function(){}},dataFilter:function(e){E.globalEval(e,t,n)}})},E.fn.extend({wrapAll:function(e){return this[0]&&(b(e)&&(e=e.call(this[0])),e=E(e,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&e.insertBefore(this[0]),e.map(function(){for(var e=this;e.firstElementChild;)e=e.firstElementChild;return e}).append(this)),this},wrapInner:function(n){return b(n)?this.each(function(e){E(this).wrapInner(n.call(this,e))}):this.each(function(){var e=E(this),t=e.contents();t.length?t.wrapAll(n):e.append(n)})},wrap:function(t){var n=b(t);return this.each(function(e){E(this).wrapAll(n?t.call(this,e):t)})},unwrap:function(e){return this.parent(e).not("body").each(function(){E(this).replaceWith(this.childNodes)}),this}}),E.expr.pseudos.hidden=function(e){return!E.expr.pseudos.visible(e)},E.expr.pseudos.visible=function(e){return!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)},E.ajaxSettings.xhr=function(){try{return new k.XMLHttpRequest}catch(e){}};var Qt={0:200,1223:204},Xt=E.ajaxSettings.xhr();y.cors=!!Xt&&"withCredentials"in Xt,y.ajax=Xt=!!Xt,E.ajaxTransport(function(i){var o,a;if(y.cors||Xt&&!i.crossDomain)return{send:function(e,t){var n,r=i.xhr();if(r.open(i.type,i.url,i.async,i.username,i.password),i.xhrFields)for(n in i.xhrFields)r[n]=i.xhrFields[n];for(n in i.mimeType&&r.overrideMimeType&&r.overrideMimeType(i.mimeType),i.crossDomain||e["X-Requested-With"]||(e["X-Requested-With"]="XMLHttpRequest"),e)r.setRequestHeader(n,e[n]);o=function(e){return function(){o&&(o=a=r.onload=r.onerror=r.onabort=r.ontimeout=r.onreadystatechange=null,"abort"===e?r.abort():"error"===e?"number"!=typeof r.status?t(0,"error"):t(r.status,r.statusText):t(Qt[r.status]||r.status,r.statusText,"text"!==(r.responseType||"text")||"string"!=typeof r.responseText?{binary:r.response}:{text:r.responseText},r.getAllResponseHeaders()))}},r.onload=o(),a=r.onerror=r.ontimeout=o("error"),void 0!==r.onabort?r.onabort=a:r.onreadystatechange=function(){4===r.readyState&&k.setTimeout(function(){o&&a()})},o=o("abort");try{r.send(i.hasContent&&i.data||null)}catch(e){if(o)throw e}},abort:function(){o&&o()}}}),E.ajaxPrefilter(function(e){e.crossDomain&&(e.contents.script=!1)}),E.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(e){return E.globalEval(e),e}}}),E.ajaxPrefilter("script",function(e){void 0===e.cache&&(e.cache=!1),e.crossDomain&&(e.type="GET")}),E.ajaxTransport("script",function(n){var r,i;if(n.crossDomain||n.scriptAttrs)return{send:function(e,t){r=E("<script>").attr(n.scriptAttrs||{}).prop({charset:n.scriptCharset,src:n.url}).on("load error",i=function(e){r.remove(),i=null,e&&t("error"===e.type?404:200,e.type)}),T.head.appendChild(r[0])},abort:function(){i&&i()}}});var Yt=[],Kt=/(=)\?(?=&|$)|\?\?/;E.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=Yt.pop()||E.expando+"_"+Rt.guid++;return this[e]=!0,e}}),E.ajaxPrefilter("json jsonp",function(e,t,n){var r,i,o,a=!1!==e.jsonp&&(Kt.test(e.url)?"url":"string"==typeof e.data&&0===(e.contentType||"").indexOf("application/x-www-form-urlencoded")&&Kt.test(e.data)&&"data");if(a||"jsonp"===e.dataTypes[0])return r=e.jsonpCallback=b(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback,a?e[a]=e[a].replace(Kt,"$1"+r):!1!==e.jsonp&&(e.url+=(Lt.test(e.url)?"&":"?")+e.jsonp+"="+r),e.converters["script json"]=function(){return o||E.error(r+" was not called"),o[0]},e.dataTypes[0]="json",i=k[r],k[r]=function(){o=arguments},n.always(function(){void 0===i?E(k).removeProp(r):k[r]=i,e[r]&&(e.jsonpCallback=t.jsonpCallback,Yt.push(r)),o&&b(i)&&i(o[0]),o=i=void 0}),"script"}),y.createHTMLDocument=((fe=T.implementation.createHTMLDocument("").body).innerHTML="<form></form><form></form>",2===fe.childNodes.length),E.parseHTML=function(e,t,n){return"string"!=typeof e?[]:("boolean"==typeof t&&(n=t,t=!1),t||(y.createHTMLDocument?((r=(t=T.implementation.createHTMLDocument("")).createElement("base")).href=T.location.href,t.head.appendChild(r)):t=T),r=!n&&[],(n=C.exec(e))?[t.createElement(n[1])]:(n=ve([e],t,r),r&&r.length&&E(r).remove(),E.merge([],n.childNodes)));var r},E.fn.load=function(e,t,n){var r,i,o,a=this,s=e.indexOf(" ");return-1<s&&(r=xt(e.slice(s)),e=e.slice(0,s)),b(t)?(n=t,t=void 0):t&&"object"==_typeof(t)&&(i="POST"),0<a.length&&E.ajax({url:e,type:i||"GET",dataType:"html",data:t}).done(function(e){o=arguments,a.html(r?E("<div>").append(E.parseHTML(e)).find(r):e)}).always(n&&function(e,t){a.each(function(){n.apply(this,o||[e.responseText,t,e])})}),this},E.expr.pseudos.animated=function(t){return E.grep(E.timers,function(e){return t===e.elem}).length},E.offset={setOffset:function(e,t,n){var r,i,o,a,s=E.css(e,"position"),l=E(e),c={};"static"===s&&(e.style.position="relative"),o=l.offset(),r=E.css(e,"top"),a=E.css(e,"left"),a=("absolute"===s||"fixed"===s)&&-1<(r+a).indexOf("auto")?(i=(s=l.position()).top,s.left):(i=parseFloat(r)||0,parseFloat(a)||0),b(t)&&(t=t.call(e,n,E.extend({},o))),null!=t.top&&(c.top=t.top-o.top+i),null!=t.left&&(c.left=t.left-o.left+a),"using"in t?t.using.call(e,c):("number"==typeof c.top&&(c.top+="px"),"number"==typeof c.left&&(c.left+="px"),l.css(c))}},E.fn.extend({offset:function(t){if(arguments.length)return void 0===t?this:this.each(function(e){E.offset.setOffset(this,t,e)});var e,n=this[0];return n?n.getClientRects().length?(e=n.getBoundingClientRect(),n=n.ownerDocument.defaultView,{top:e.top+n.pageYOffset,left:e.left+n.pageXOffset}):{top:0,left:0}:void 0},position:function(){if(this[0]){var e,t,n,r=this[0],i={top:0,left:0};if("fixed"===E.css(r,"position"))t=r.getBoundingClientRect();else{for(t=this.offset(),n=r.ownerDocument,e=r.offsetParent||n.documentElement;e&&(e===n.body||e===n.documentElement)&&"static"===E.css(e,"position");)e=e.parentNode;e&&e!==r&&1===e.nodeType&&((i=E(e).offset()).top+=E.css(e,"borderTopWidth",!0),i.left+=E.css(e,"borderLeftWidth",!0))}return{top:t.top-i.top-E.css(r,"marginTop",!0),left:t.left-i.left-E.css(r,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var e=this.offsetParent;e&&"static"===E.css(e,"position");)e=e.offsetParent;return e||ne})}}),E.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(t,i){var o="pageYOffset"===i;E.fn[t]=function(e){return q(this,function(e,t,n){var r;return g(e)?r=e:9===e.nodeType&&(r=e.defaultView),void 0===n?r?r[i]:e[t]:void(r?r.scrollTo(o?r.pageXOffset:n,o?n:r.pageYOffset):e[t]=n)},t,e,arguments.length)}}),E.each(["top","left"],function(e,n){E.cssHooks[n]=Qe(y.pixelPosition,function(e,t){if(t)return t=ze(e,n),Ge.test(t)?E(e).position()[n]+"px":t})}),E.each({Height:"height",Width:"width"},function(a,s){E.each({padding:"inner"+a,content:s,"":"outer"+a},function(r,o){E.fn[o]=function(e,t){var n=arguments.length&&(r||"boolean"!=typeof e),i=r||(!0===e||!0===t?"margin":"border");return q(this,function(e,t,n){var r;return g(e)?0===o.indexOf("outer")?e["inner"+a]:e.document.documentElement["client"+a]:9===e.nodeType?(r=e.documentElement,Math.max(e.body["scroll"+a],r["scroll"+a],e.body["offset"+a],r["offset"+a],r["client"+a])):void 0===n?E.css(e,t,i):E.style(e,t,n,i)},s,n?e:void 0,n)}})}),E.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(e,t){E.fn[t]=function(e){return this.on(t,e)}}),E.fn.extend({bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,n,r){return this.on(t,e,n,r)},undelegate:function(e,t,n){return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",n)},hover:function(e,t){return this.mouseenter(e).mouseleave(t||e)}}),E.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),function(e,n){E.fn[n]=function(e,t){return 0<arguments.length?this.on(n,null,e,t):this.trigger(n)}});var Jt=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g;E.proxy=function(e,t){var n,r;if("string"==typeof t&&(r=e[t],t=e,e=r),b(e))return n=s.call(arguments,2),(r=function(){return e.apply(t||this,n.concat(s.call(arguments)))}).guid=e.guid=e.guid||E.guid++,r},E.holdReady=function(e){e?E.readyWait++:E.ready(!0)},E.isArray=Array.isArray,E.parseJSON=JSON.parse,E.nodeName=S,E.isFunction=b,E.isWindow=g,E.camelCase=V,E.type=h,E.now=Date.now,E.isNumeric=function(e){var t=E.type(e);return("number"===t||"string"===t)&&!isNaN(e-parseFloat(e))},E.trim=function(e){return null==e?"":(e+"").replace(Jt,"")},"function"==typeof define&&define.amd&&define("jquery",[],function(){return E});var Zt=k.jQuery,en=k.$;return E.noConflict=function(e){return k.$===E&&(k.$=en),e&&k.jQuery===E&&(k.jQuery=Zt),E},void 0===e&&(k.jQuery=k.$=E),E}),function(e,t){"object"==("undefined"==typeof exports?"undefined":_typeof(exports))&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):e.Popper=t()}(this,function(){"use strict";function i(e){return e&&"[object Function]"==={}.toString.call(e)}function d(e,t){if(1!==e.nodeType)return[];e=e.ownerDocument.defaultView.getComputedStyle(e,null);return t?e[t]:e}function c(e){return"HTML"===e.nodeName?e:e.parentNode||e.host}function u(e){if(!e)return document.body;switch(e.nodeName){case"HTML":case"BODY":return e.ownerDocument.body;case"#document":return e.body}var t=d(e),n=t.overflow,r=t.overflowX,t=t.overflowY;return/(auto|scroll|overlay)/.test(n+t+r)?e:u(c(e))}function f(e){return e&&e.referenceNode?e.referenceNode:e}function p(e){return 11===e?$:10!==e&&$||G}function g(e){if(!e)return document.documentElement;for(var t=p(10)?document.body:null,n=e.offsetParent||null;n===t&&e.nextElementSibling;)n=(e=e.nextElementSibling).offsetParent;var r=n&&n.nodeName;return r&&"BODY"!==r&&"HTML"!==r?-1!==["TH","TD","TABLE"].indexOf(n.nodeName)&&"static"===d(n,"position")?g(n):n:(e?e.ownerDocument:document).documentElement}function o(e){return null===e.parentNode?e:o(e.parentNode)}function h(e,t){if(!(e&&e.nodeType&&t&&t.nodeType))return document.documentElement;var n=e.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_FOLLOWING,r=n?e:t,i=n?t:e,n=document.createRange();n.setStart(r,0),n.setEnd(i,0);n=n.commonAncestorContainer;if(e!==n&&t!==n||r.contains(i))return"BODY"===(i=(r=n).nodeName)||"HTML"!==i&&g(r.firstElementChild)!==r?g(n):n;n=o(e);return n.host?h(n.host,t):h(e,o(t).host)}function m(e,t){var n="top"===(1<arguments.length&&void 0!==t?t:"top")?"scrollTop":"scrollLeft",t=e.nodeName;if("BODY"!==t&&"HTML"!==t)return e[n];t=e.ownerDocument.documentElement;return(e.ownerDocument.scrollingElement||t)[n]}function l(e,t){var n="x"===t?"Left":"Top",t="Left"==n?"Right":"Bottom";return parseFloat(e["border"+n+"Width"])+parseFloat(e["border"+t+"Width"])}function r(e,t,n,r){return B(t["offset"+e],t["scroll"+e],n["client"+e],n["offset"+e],n["scroll"+e],p(10)?parseInt(n["offset"+e])+parseInt(r["margin"+("Height"===e?"Top":"Left")])+parseInt(r["margin"+("Height"===e?"Bottom":"Right")]):0)}function v(e){var t=e.body,n=e.documentElement,e=p(10)&&getComputedStyle(n);return{height:r("Height",t,n,e),width:r("Width",t,n,e)}}function y(e){return z({},e,{right:e.left+e.width,bottom:e.top+e.height})}function b(e){var t,n,r={};try{p(10)?(r=e.getBoundingClientRect(),t=m(e,"top"),n=m(e,"left"),r.top+=t,r.left+=n,r.bottom+=t,r.right+=n):r=e.getBoundingClientRect()}catch(e){}var i={left:r.left,top:r.top,width:r.right-r.left,height:r.bottom-r.top},o="HTML"===e.nodeName?v(e.ownerDocument):{},a=o.width||e.clientWidth||i.width,s=o.height||e.clientHeight||i.height,o=e.offsetWidth-a,a=e.offsetHeight-s;return(o||a)&&(o-=l(s=d(e),"x"),a-=l(s,"y"),i.width-=o,i.height-=a),y(i)}function w(e,t,n){var r=2<arguments.length&&void 0!==n&&n,i=p(10),o="HTML"===t.nodeName,a=b(e),s=b(t),l=u(e),c=d(t),n=parseFloat(c.borderTopWidth),e=parseFloat(c.borderLeftWidth);r&&o&&(s.top=B(s.top,0),s.left=B(s.left,0));a=y({top:a.top-s.top-n,left:a.left-s.left-e,width:a.width,height:a.height});return a.marginTop=0,a.marginLeft=0,!i&&o&&(o=parseFloat(c.marginTop),c=parseFloat(c.marginLeft),a.top-=n-o,a.bottom-=n-o,a.left-=e-c,a.right-=e-c,a.marginTop=o,a.marginLeft=c),(i&&!r?t.contains(l):t===l&&"BODY"!==l.nodeName)&&(a=function(e,t,n){var r=2<arguments.length&&void 0!==n&&n,n=m(t,"top"),t=m(t,"left"),r=r?-1:1;return e.top+=n*r,e.bottom+=n*r,e.left+=t*r,e.right+=t*r,e}(a,t)),a}function _(e){if(!e||!e.parentElement||p())return document.documentElement;for(var t=e.parentElement;t&&"none"===d(t,"transform");)t=t.parentElement;return t||document.documentElement}function x(e,t,n,r,i){var o,a=4<arguments.length&&void 0!==i&&i,s={top:0,left:0},i=a?_(e):h(e,f(t));"viewport"===r?s=function(e,t){var n=1<arguments.length&&void 0!==t&&t,r=e.ownerDocument.documentElement,i=w(e,r),o=B(r.clientWidth,window.innerWidth||0),t=B(r.clientHeight,window.innerHeight||0),e=n?0:m(r),r=n?0:m(r,"left");return y({top:e-i.top+i.marginTop,left:r-i.left+i.marginLeft,width:o,height:t})}(i,a):("scrollParent"===r?"BODY"===(o=u(c(t))).nodeName&&(o=e.ownerDocument.documentElement):o="window"===r?e.ownerDocument.documentElement:r,l=w(o,i,a),"HTML"!==o.nodeName||function e(t){var n=t.nodeName;if("BODY"===n||"HTML"===n)return!1;if("fixed"===d(t,"position"))return!0;t=c(t);return!!t&&e(t)}(i)?s=l:(e=(i=v(e.ownerDocument)).height,i=i.width,s.top+=l.top-l.marginTop,s.bottom=e+l.top,s.left+=l.left-l.marginLeft,s.right=i+l.left));var l="number"==typeof(n=n||0);return s.left+=l?n:n.left||0,s.top+=l?n:n.top||0,s.right-=l?n:n.right||0,s.bottom-=l?n:n.bottom||0,s}function a(e,t,n,r,i,o){o=5<arguments.length&&void 0!==o?o:0;if(-1===e.indexOf("auto"))return e;var i=x(n,r,o,i),a={top:{width:i.width,height:t.top-i.top},right:{width:i.right-t.right,height:i.height},bottom:{width:i.width,height:i.bottom-t.bottom},left:{width:t.left-i.left,height:i.height}},t=Object.keys(a).map(function(e){return z({key:e},a[e],{area:(e=a[e]).width*e.height})}).sort(function(e,t){return t.area-e.area}),i=t.filter(function(e){var t=e.width,e=e.height;return t>=n.clientWidth&&e>=n.clientHeight}),t=(0<i.length?i:t)[0].key,e=e.split("-")[1];return t+(e?"-"+e:"")}function s(e,t,n,r){r=3<arguments.length&&void 0!==r?r:null;return w(n,r?_(t):h(t,f(n)),r)}function k(e){var t=e.ownerDocument.defaultView.getComputedStyle(e),n=parseFloat(t.marginTop||0)+parseFloat(t.marginBottom||0),t=parseFloat(t.marginLeft||0)+parseFloat(t.marginRight||0);return{width:e.offsetWidth+t,height:e.offsetHeight+n}}function T(e){var t={left:"right",right:"left",bottom:"top",top:"bottom"};return e.replace(/left|right|bottom|top/g,function(e){return t[e]})}function E(e,t,n){n=n.split("-")[0];var r=k(e),i={width:r.width,height:r.height},o=-1!==["right","left"].indexOf(n),a=o?"top":"left",s=o?"left":"top",e=o?"height":"width",o=o?"width":"height";return i[a]=t[a]+t[e]/2-r[e]/2,i[s]=n===s?t[s]-r[o]:t[T(s)],i}function S(e,t){return Array.prototype.find?e.find(t):e.filter(t)[0]}function C(e,n,t){return(void 0===t?e:e.slice(0,function(e,t,n){if(Array.prototype.findIndex)return e.findIndex(function(e){return e[t]===n});var r=S(e,function(e){return e[t]===n});return e.indexOf(r)}(e,"name",t))).forEach(function(e){e.function;var t=e.function||e.fn;e.enabled&&i(t)&&(n.offsets.popper=y(n.offsets.popper),n.offsets.reference=y(n.offsets.reference),n=t(n,e))}),n}function e(e,n){return e.some(function(e){var t=e.name;return e.enabled&&t===n})}function A(e){for(var t=[!1,"ms","Webkit","Moz","O"],n=e.charAt(0).toUpperCase()+e.slice(1),r=0;r<t.length;r++){var i=t[r],i=i?""+i+n:e;if(void 0!==document.body.style[i])return i}return null}function R(e){e=e.ownerDocument;return e?e.defaultView:window}function t(e,t,n,r){n.updateBound=r,R(e).addEventListener("resize",n.updateBound,{passive:!0});e=u(e);return function e(t,n,r,i){var o="BODY"===t.nodeName,t=o?t.ownerDocument.defaultView:t;t.addEventListener(n,r,{passive:!0}),o||e(u(t.parentNode),n,r,i),i.push(t)}(e,"scroll",n.updateBound,n.scrollParents),n.scrollElement=e,n.eventsEnabled=!0,n}function n(){var e,t;this.state.eventsEnabled&&(cancelAnimationFrame(this.scheduleUpdate),this.state=(e=this.reference,t=this.state,R(e).removeEventListener("resize",t.updateBound),t.scrollParents.forEach(function(e){e.removeEventListener("scroll",t.updateBound)}),t.updateBound=null,t.scrollParents=[],t.scrollElement=null,t.eventsEnabled=!1,t))}function L(e){return""!==e&&!isNaN(parseFloat(e))&&isFinite(e)}function D(n,r){Object.keys(r).forEach(function(e){var t="";-1!==["width","height","top","right","bottom","left"].indexOf(e)&&L(r[e])&&(t="px"),n.style[e]=r[e]+t})}function I(e,t,n){var r=S(e,function(e){return e.name===t}),e=!!r&&e.some(function(e){return e.name===n&&e.enabled&&e.order<r.order});return e||0,e}function N(e,t){t=1<arguments.length&&void 0!==t&&t,e=Y.indexOf(e),e=Y.slice(e+1).concat(Y.slice(0,e));return t?e.reverse():e}function M(e,i,o,t){var a=[0,0],s=-1!==["right","left"].indexOf(t),n=e.split(/(\+|\-)/).map(function(e){return e.trim()}),t=n.indexOf(S(n,function(e){return-1!==e.search(/,|\s/)}));n[t]&&n[t].indexOf(",");e=/\s*,\s*|\s+/;return(-1===t?[n]:[n.slice(0,t).concat([n[t].split(e)[0]]),[n[t].split(e)[1]].concat(n.slice(t+1))]).map(function(e,t){var n=(1===t?!s:s)?"height":"width",r=!1;return e.reduce(function(e,t){return""===e[e.length-1]&&-1!==["+","-"].indexOf(t)?(e[e.length-1]=t,r=!0,e):r?(e[e.length-1]+=t,r=!1,e):e.concat(t)},[]).map(function(e){return function(e,t,n,r){var i,o=+(a=e.match(/((?:\-|\+)?\d*\.?\d*)(.*)/))[1],a=a[2];if(!o)return e;if(0!==a.indexOf("%"))return"vh"!==a&&"vw"!==a?o:("vh"===a?B(document.documentElement.clientHeight,window.innerHeight||0):B(document.documentElement.clientWidth,window.innerWidth||0))/100*o;switch(a){case"%p":i=n;break;case"%":case"%r":default:i=r}return y(i)[t]/100*o}(e,n,i,o)})}).forEach(function(n,r){n.forEach(function(e,t){L(e)&&(a[r]+=e*("-"===n[t-1]?-1:1))})}),a}function O(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var j=Math.min,P=Math.floor,F=Math.round,B=Math.max,U="undefined"!=typeof window&&"undefined"!=typeof document&&"undefined"!=typeof navigator,q=function(){for(var e=["Edge","Trident","Firefox"],t=0;t<e.length;t+=1)if(U&&0<=navigator.userAgent.indexOf(e[t]))return 1;return 0}(),H=U&&window.Promise?function(e){var t=!1;return function(){t||(t=!0,window.Promise.resolve().then(function(){t=!1,e()}))}}:function(e){var t=!1;return function(){t||(t=!0,setTimeout(function(){t=!1,e()},q))}},$=U&&!(!window.MSInputMethodContext||!document.documentMode),G=U&&/MSIE 10/.test(navigator.userAgent),V=function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},W=function(e,t,n){return t&&te(e.prototype,t),n&&te(e,n),e},z=Object.assign||function(e){for(var t,n=1;n<arguments.length;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},Q=U&&/Firefox/i.test(navigator.userAgent),X=["auto-start","auto","auto-end","top-start","top","top-end","right-start","right","right-end","bottom-end","bottom","bottom-start","left-end","left","left-start"],Y=X.slice(3),K="flip",J="clockwise",Z="counterclockwise",W=(W(ee,[{key:"update",value:function(){return function(){var e;this.state.isDestroyed||((e={instance:this,styles:{},arrowStyles:{},attributes:{},flipped:!1,offsets:{}}).offsets.reference=s(this.state,this.popper,this.reference,this.options.positionFixed),e.placement=a(this.options.placement,e.offsets.reference,this.popper,this.reference,this.options.modifiers.flip.boundariesElement,this.options.modifiers.flip.padding),e.originalPlacement=e.placement,e.positionFixed=this.options.positionFixed,e.offsets.popper=E(this.popper,e.offsets.reference,e.placement),e.offsets.popper.position=this.options.positionFixed?"fixed":"absolute",e=C(this.modifiers,e),this.state.isCreated?this.options.onUpdate(e):(this.state.isCreated=!0,this.options.onCreate(e)))}.call(this)}},{key:"destroy",value:function(){return function(){return this.state.isDestroyed=!0,e(this.modifiers,"applyStyle")&&(this.popper.removeAttribute("x-placement"),this.popper.style.position="",this.popper.style.top="",this.popper.style.left="",this.popper.style.right="",this.popper.style.bottom="",this.popper.style.willChange="",this.popper.style[A("transform")]=""),this.disableEventListeners(),this.options.removeOnDestroy&&this.popper.parentNode.removeChild(this.popper),this}.call(this)}},{key:"enableEventListeners",value:function(){return function(){this.state.eventsEnabled||(this.state=t(this.reference,this.options,this.state,this.scheduleUpdate))}.call(this)}},{key:"disableEventListeners",value:function(){return n.call(this)}}]),ee);function ee(e,t){var n=this,r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{};V(this,ee),this.scheduleUpdate=function(){return requestAnimationFrame(n.update)},this.update=H(this.update.bind(this)),this.options=z({},ee.Defaults,r),this.state={isDestroyed:!1,isCreated:!1,scrollParents:[]},this.reference=e&&e.jquery?e[0]:e,this.popper=t&&t.jquery?t[0]:t,this.options.modifiers={},Object.keys(z({},ee.Defaults.modifiers,r.modifiers)).forEach(function(e){n.options.modifiers[e]=z({},ee.Defaults.modifiers[e]||{},r.modifiers?r.modifiers[e]:{})}),this.modifiers=Object.keys(this.options.modifiers).map(function(e){return z({name:e},n.options.modifiers[e])}).sort(function(e,t){return e.order-t.order}),this.modifiers.forEach(function(e){e.enabled&&i(e.onLoad)&&e.onLoad(n.reference,n.popper,n.options,e,n.state)}),this.update();t=this.options.eventsEnabled;t&&this.enableEventListeners(),this.state.eventsEnabled=t}function te(e,t){for(var n,r=0;r<t.length;r++)(n=t[r]).enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}return W.Utils=("undefined"==typeof window?global:window).PopperUtils,W.placements=X,W.Defaults={placement:"bottom",positionFixed:!1,eventsEnabled:!0,removeOnDestroy:!1,onCreate:function(){},onUpdate:function(){},modifiers:{shift:{order:100,enabled:!0,fn:function(e){var t,n,r=e.placement,i=r.split("-")[0],o=r.split("-")[1];return o&&(t=(n=e.offsets).reference,r=n.popper,i=(n=-1!==["bottom","top"].indexOf(i))?"width":"height",i={start:O({},n=n?"left":"top",t[n]),end:O({},n,t[n]+t[i]-r[i])},e.offsets.popper=z({},r,i[o])),e}},offset:{order:200,enabled:!0,fn:function(e,t){var n=t.offset,r=e.placement,t=(i=e.offsets).popper,i=i.reference,r=r.split("-")[0],i=L(+n)?[+n,0]:M(n,t,i,r);return"left"===r?(t.top+=i[0],t.left-=i[1]):"right"===r?(t.top+=i[0],t.left+=i[1]):"top"===r?(t.left+=i[0],t.top-=i[1]):"bottom"===r&&(t.left+=i[0],t.top+=i[1]),e.popper=t,e},offset:0},preventOverflow:{order:300,enabled:!0,fn:function(e,r){var t=r.boundariesElement||g(e.instance.popper);e.instance.reference===t&&(t=g(t));var n=A("transform"),i=e.instance.popper.style,o=i.top,a=i.left,s=i[n];i.top="",i.left="",i[n]="";var l=x(e.instance.popper,e.instance.reference,r.padding,t,e.positionFixed);i.top=o,i.left=a,i[n]=s,r.boundaries=l;var s=r.priority,c=e.offsets.popper,u={primary:function(e){var t=c[e];return c[e]<l[e]&&!r.escapeWithReference&&(t=B(c[e],l[e])),O({},e,t)},secondary:function(e){var t="right"===e?"left":"top",n=c[t];return c[e]>l[e]&&!r.escapeWithReference&&(n=j(c[t],l[e]-("right"===e?c.width:c.height))),O({},t,n)}};return s.forEach(function(e){var t=-1===["left","top"].indexOf(e)?"secondary":"primary";c=z({},c,u[t](e))}),e.offsets.popper=c,e},priority:["left","right","top","bottom"],padding:5,boundariesElement:"scrollParent"},keepTogether:{order:400,enabled:!0,fn:function(e){var t=e.offsets,n=t.popper,r=t.reference,i=e.placement.split("-")[0],o=P,a=-1!==["top","bottom"].indexOf(i),t=a?"right":"bottom",i=a?"left":"top",a=a?"width":"height";return n[t]<o(r[i])&&(e.offsets.popper[i]=o(r[i])-n[a]),n[i]>o(r[t])&&(e.offsets.popper[i]=o(r[t])),e}},arrow:{order:500,enabled:!0,fn:function(e,t){if(!I(e.instance.modifiers,"arrow","keepTogether"))return e;var n=t.element;if("string"==typeof n){if(!(n=e.instance.popper.querySelector(n)))return e}else if(!e.instance.popper.contains(n))return e;var r=e.placement.split("-")[0],i=e.offsets,o=i.popper,a=i.reference,s=-1!==["left","right"].indexOf(r),l=s?"height":"width",c=s?"Top":"Left",u=c.toLowerCase(),t=s?"left":"top",i=s?"bottom":"right",r=k(n)[l];a[i]-r<o[u]&&(e.offsets.popper[u]-=o[u]-(a[i]-r)),a[u]+r>o[i]&&(e.offsets.popper[u]+=a[u]+r-o[i]),e.offsets.popper=y(e.offsets.popper);s=a[u]+a[l]/2-r/2,i=d(e.instance.popper),a=parseFloat(i["margin"+c]),c=parseFloat(i["border"+c+"Width"]),c=s-e.offsets.popper[u]-a-c,c=B(j(o[l]-r,c),0);return e.arrowElement=n,e.offsets.arrow=(O(n={},u,F(c)),O(n,t,""),n),e},element:"[x-arrow]"},flip:{order:600,enabled:!0,fn:function(c,u){if(e(c.instance.modifiers,"inner"))return c;if(c.flipped&&c.placement===c.originalPlacement)return c;var d=x(c.instance.popper,c.instance.reference,u.padding,u.boundariesElement,c.positionFixed),f=c.placement.split("-")[0],p=T(f),h=c.placement.split("-")[1]||"",g=[];switch(u.behavior){case K:g=[f,p];break;case J:g=N(f);break;case Z:g=N(f,!0);break;default:g=u.behavior}return g.forEach(function(e,t){if(f!==e||g.length===t+1)return c;f=c.placement.split("-")[0],p=T(f);var n=c.offsets.popper,r=c.offsets.reference,i=P,o="left"===f&&i(n.right)>i(r.left)||"right"===f&&i(n.left)<i(r.right)||"top"===f&&i(n.bottom)>i(r.top)||"bottom"===f&&i(n.top)<i(r.bottom),a=i(n.left)<i(d.left),s=i(n.right)>i(d.right),l=i(n.top)<i(d.top),e=i(n.bottom)>i(d.bottom),r="left"===f&&a||"right"===f&&s||"top"===f&&l||"bottom"===f&&e,n=-1!==["top","bottom"].indexOf(f),i=!!u.flipVariations&&(n&&"start"===h&&a||n&&"end"===h&&s||!n&&"start"===h&&l||!n&&"end"===h&&e),l=!!u.flipVariationsByContent&&(n&&"start"===h&&s||n&&"end"===h&&a||!n&&"start"===h&&e||!n&&"end"===h&&l),l=i||l;(o||r||l)&&(c.flipped=!0,(o||r)&&(f=g[t+1]),l&&(h="end"===(l=h)?"start":"start"===l?"end":l),c.placement=f+(h?"-"+h:""),c.offsets.popper=z({},c.offsets.popper,E(c.instance.popper,c.offsets.reference,c.placement)),c=C(c.instance.modifiers,c,"flip"))}),c},behavior:"flip",padding:5,boundariesElement:"viewport",flipVariations:!1,flipVariationsByContent:!1},inner:{order:700,enabled:!1,fn:function(e){var t=e.placement,n=t.split("-")[0],r=e.offsets,i=r.popper,o=r.reference,a=-1!==["left","right"].indexOf(n),r=-1===["top","left"].indexOf(n);return i[a?"left":"top"]=o[n]-(r?i[a?"width":"height"]:0),e.placement=T(t),e.offsets.popper=y(i),e}},hide:{order:800,enabled:!0,fn:function(e){if(!I(e.instance.modifiers,"hide","preventOverflow"))return e;var t=e.offsets.reference,n=S(e.instance.modifiers,function(e){return"preventOverflow"===e.name}).boundaries;if(t.bottom<n.top||t.left>n.right||t.top>n.bottom||t.right<n.left){if(!0===e.hide)return e;e.hide=!0,e.attributes["x-out-of-boundaries"]=""}else{if(!1===e.hide)return e;e.hide=!1,e.attributes["x-out-of-boundaries"]=!1}return e}},computeStyle:{order:850,enabled:!0,fn:function(e,t){var n,r,i,o,a=t.x,s=t.y,l=e.offsets.popper,c=S(e.instance.modifiers,function(e){return"applyStyle"===e.name}).gpuAcceleration,u=void 0===c?t.gpuAcceleration:c,d=g(e.instance.popper),f=b(d),p={position:l.position},t=(n=e,r=window.devicePixelRatio<2||!Q,h=(o=n.offsets).popper,i=o.reference,t=function(e){return e},l=(c=F)(i.width),o=c(h.width),i=-1!==["left","right"].indexOf(n.placement),n=-1!==n.placement.indexOf("-"),i=r?i||n||l%2==o%2?c:P:t,t=r?c:t,{left:i(1==l%2&&1==o%2&&!n&&r?h.left-1:h.left),top:t(h.top),bottom:t(h.bottom),right:i(h.right)}),i="bottom"===a?"top":"bottom",h="right"===s?"left":"right",a=A("transform"),s="bottom"==i?"HTML"===d.nodeName?-d.clientHeight+t.bottom:-f.height+t.bottom:t.top,t="right"==h?"HTML"===d.nodeName?-d.clientWidth+t.right:-f.width+t.right:t.left;u&&a?(p[a]="translate3d("+t+"px, "+s+"px, 0)",p[i]=0,p[h]=0,p.willChange="transform"):(u="bottom"==i?-1:1,a="right"==h?-1:1,p[i]=s*u,p[h]=t*a,p.willChange=i+", "+h);var h={"x-placement":e.placement};return e.attributes=z({},h,e.attributes),e.styles=z({},p,e.styles),e.arrowStyles=z({},e.offsets.arrow,e.arrowStyles),e},gpuAcceleration:!0,x:"bottom",y:"right"},applyStyle:{order:900,enabled:!0,fn:function(e){return D(e.instance.popper,e.styles),t=e.instance.popper,n=e.attributes,Object.keys(n).forEach(function(e){!1===n[e]?t.removeAttribute(e):t.setAttribute(e,n[e])}),e.arrowElement&&Object.keys(e.arrowStyles).length&&D(e.arrowElement,e.arrowStyles),e;var t,n},onLoad:function(e,t,n,r,i){i=s(i,t,e,n.positionFixed),e=a(n.placement,i,t,e,n.modifiers.flip.boundariesElement,n.modifiers.flip.padding);return t.setAttribute("x-placement",e),D(t,{position:n.positionFixed?"fixed":"absolute"}),n},gpuAcceleration:void 0}}},W}),function(e,t){"object"==("undefined"==typeof exports?"undefined":_typeof(exports))&&"undefined"!=typeof module?t(exports,require("jquery"),require("popper.js")):"function"==typeof define&&define.amd?define(["exports","jquery","popper.js"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).bootstrap={},e.jQuery,e.Popper)}(this,function(e,t,n){"use strict";function r(e){return e&&"object"==_typeof(e)&&"default"in e?e:{default:e}}var u=r(t),i=r(n);function o(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function a(e,t,n){return t&&o(e.prototype,t),n&&o(e,n),e}function s(){return(s=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n,r=arguments[t];for(n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var d={TRANSITION_END:"bsTransitionEnd",getUID:function(e){for(;e+=~~(1e6*Math.random()),document.getElementById(e););return e},getSelectorFromElement:function(e){var t,n=e.getAttribute("data-target");n&&"#"!==n||(n=(t=e.getAttribute("href"))&&"#"!==t?t.trim():"");try{return document.querySelector(n)?n:null}catch(e){return null}},getTransitionDurationFromElement:function(e){if(!e)return 0;var t=u.default(e).css("transition-duration"),n=u.default(e).css("transition-delay"),r=parseFloat(t),e=parseFloat(n);return r||e?(t=t.split(",")[0],n=n.split(",")[0],1e3*(parseFloat(t)+parseFloat(n))):0},reflow:function(e){return e.offsetHeight},triggerTransitionEnd:function(e){u.default(e).trigger("transitionend")},supportsTransitionEnd:function(){return Boolean("transitionend")},isElement:function(e){return(e[0]||e).nodeType},typeCheckConfig:function(e,t,n){for(var r in n)if(Object.prototype.hasOwnProperty.call(n,r)){var i=n[r],o=t[r],o=o&&d.isElement(o)?"element":null===o||void 0===o?""+o:{}.toString.call(o).match(/\s([a-z]+)/i)[1].toLowerCase();if(!new RegExp(i).test(o))throw new Error(e.toUpperCase()+': Option "'+r+'" provided type "'+o+'" but expected type "'+i+'".')}},findShadowRoot:function(e){if(!document.documentElement.attachShadow)return null;if("function"!=typeof e.getRootNode)return e instanceof ShadowRoot?e:e.parentNode?d.findShadowRoot(e.parentNode):null;e=e.getRootNode();return e instanceof ShadowRoot?e:null},jQueryDetection:function(){if(void 0===u.default)throw new TypeError("Bootstrap's JavaScript requires jQuery. jQuery must be included before Bootstrap's JavaScript.");var e=u.default.fn.jquery.split(" ")[0].split(".");if(e[0]<2&&e[1]<9||1===e[0]&&9===e[1]&&e[2]<1||4<=e[0])throw new Error("Bootstrap's JavaScript requires at least jQuery v1.9.1 but less than v4.0.0")}};d.jQueryDetection(),u.default.fn.emulateTransitionEnd=function(e){var t=this,n=!1;return u.default(this).one(d.TRANSITION_END,function(){n=!0}),setTimeout(function(){n||d.triggerTransitionEnd(t)},e),this},u.default.event.special[d.TRANSITION_END]={bindType:"transitionend",delegateType:"transitionend",handle:function(e){if(u.default(e.target).is(this))return e.handleObj.handler.apply(this,arguments)}};var l="alert",c=u.default.fn[l],f=((n=p.prototype).close=function(e){var t=this._element;e&&(t=this._getRootElement(e)),this._triggerCloseEvent(t).isDefaultPrevented()||this._removeElement(t)},n.dispose=function(){u.default.removeData(this._element,"bs.alert"),this._element=null},n._getRootElement=function(e){var t=d.getSelectorFromElement(e),n=!1;return t&&(n=document.querySelector(t)),n=n||u.default(e).closest(".alert")[0]},n._triggerCloseEvent=function(e){var t=u.default.Event("close.bs.alert");return u.default(e).trigger(t),t},n._removeElement=function(t){var e,n=this;u.default(t).removeClass("show"),u.default(t).hasClass("fade")?(e=d.getTransitionDurationFromElement(t),u.default(t).one(d.TRANSITION_END,function(e){return n._destroyElement(t,e)}).emulateTransitionEnd(e)):this._destroyElement(t)},n._destroyElement=function(e){u.default(e).detach().trigger("closed.bs.alert").remove()},p._jQueryInterface=function(n){return this.each(function(){var e=u.default(this),t=e.data("bs.alert");t||(t=new p(this),e.data("bs.alert",t)),"close"===n&&t[n](this)})},p._handleDismiss=function(t){return function(e){e&&e.preventDefault(),t.close(this)}},a(p,null,[{key:"VERSION",get:function(){return"4.5.3"}}]),p);function p(e){this._element=e}u.default(document).on("click.bs.alert.data-api",'[data-dismiss="alert"]',f._handleDismiss(new f)),u.default.fn[l]=f._jQueryInterface,u.default.fn[l].Constructor=f,u.default.fn[l].noConflict=function(){return u.default.fn[l]=c,f._jQueryInterface};var h=u.default.fn.button,g=((n=m.prototype).toggle=function(){var e,t=!0,n=!0,r=u.default(this._element).closest('[data-toggle="buttons"]')[0];!r||(e=this._element.querySelector('input:not([type="hidden"])'))&&("radio"===e.type&&(e.checked&&this._element.classList.contains("active")?t=!1:(r=r.querySelector(".active"))&&u.default(r).removeClass("active")),t&&("checkbox"!==e.type&&"radio"!==e.type||(e.checked=!this._element.classList.contains("active")),this.shouldAvoidTriggerChange||u.default(e).trigger("change")),e.focus(),n=!1),this._element.hasAttribute("disabled")||this._element.classList.contains("disabled")||(n&&this._element.setAttribute("aria-pressed",!this._element.classList.contains("active")),t&&u.default(this._element).toggleClass("active"))},n.dispose=function(){u.default.removeData(this._element,"bs.button"),this._element=null},m._jQueryInterface=function(n,r){return this.each(function(){var e=u.default(this),t=e.data("bs.button");t||(t=new m(this),e.data("bs.button",t)),t.shouldAvoidTriggerChange=r,"toggle"===n&&t[n]()})},a(m,null,[{key:"VERSION",get:function(){return"4.5.3"}}]),m);function m(e){this._element=e,this.shouldAvoidTriggerChange=!1}u.default(document).on("click.bs.button.data-api",'[data-toggle^="button"]',function(e){var t,n=e.target,r=n;u.default(n).hasClass("btn")||(n=u.default(n).closest(".btn")[0]),!n||n.hasAttribute("disabled")||n.classList.contains("disabled")||(t=n.querySelector('input:not([type="hidden"])'))&&(t.hasAttribute("disabled")||t.classList.contains("disabled"))?e.preventDefault():"INPUT"!==r.tagName&&"LABEL"===n.tagName||g._jQueryInterface.call(u.default(n),"toggle","INPUT"===r.tagName)}).on("focus.bs.button.data-api blur.bs.button.data-api",'[data-toggle^="button"]',function(e){var t=u.default(e.target).closest(".btn")[0];u.default(t).toggleClass("focus",/^focus(in)?$/.test(e.type))}),u.default(window).on("load.bs.button.data-api",function(){for(var e=[].slice.call(document.querySelectorAll('[data-toggle="buttons"] .btn')),t=0,n=e.length;t<n;t++){var r=e[t],i=r.querySelector('input:not([type="hidden"])');i.checked||i.hasAttribute("checked")?r.classList.add("active"):r.classList.remove("active")}for(var o=0,a=(e=[].slice.call(document.querySelectorAll('[data-toggle="button"]'))).length;o<a;o++){var s=e[o];"true"===s.getAttribute("aria-pressed")?s.classList.add("active"):s.classList.remove("active")}}),u.default.fn.button=g._jQueryInterface,u.default.fn.button.Constructor=g,u.default.fn.button.noConflict=function(){return u.default.fn.button=h,g._jQueryInterface};var v="carousel",y=u.default.fn[v],b={interval:5e3,keyboard:!0,slide:!1,pause:"hover",wrap:!0,touch:!0},w={interval:"(number|boolean)",keyboard:"boolean",slide:"(boolean|string)",pause:"(string|boolean)",wrap:"boolean",touch:"boolean"},_={TOUCH:"touch",PEN:"pen"},x=((n=k.prototype).next=function(){this._isSliding||this._slide("next")},n.nextWhenVisible=function(){var e=u.default(this._element);!document.hidden&&e.is(":visible")&&"hidden"!==e.css("visibility")&&this.next()},n.prev=function(){this._isSliding||this._slide("prev")},n.pause=function(e){e||(this._isPaused=!0),this._element.querySelector(".carousel-item-next, .carousel-item-prev")&&(d.triggerTransitionEnd(this._element),this.cycle(!0)),clearInterval(this._interval),this._interval=null},n.cycle=function(e){e||(this._isPaused=!1),this._interval&&(clearInterval(this._interval),this._interval=null),this._config.interval&&!this._isPaused&&(this._interval=setInterval((document.visibilityState?this.nextWhenVisible:this.next).bind(this),this._config.interval))},n.to=function(e){var t=this;this._activeElement=this._element.querySelector(".active.carousel-item");var n=this._getItemIndex(this._activeElement);if(!(e>this._items.length-1||e<0))if(this._isSliding)u.default(this._element).one("slid.bs.carousel",function(){return t.to(e)});else{if(n===e)return this.pause(),void this.cycle();n=n<e?"next":"prev";this._slide(n,this._items[e])}},n.dispose=function(){u.default(this._element).off(".bs.carousel"),u.default.removeData(this._element,"bs.carousel"),this._items=null,this._config=null,this._element=null,this._interval=null,this._isPaused=null,this._isSliding=null,this._activeElement=null,this._indicatorsElement=null},n._getConfig=function(e){return e=s({},b,e),d.typeCheckConfig(v,e,w),e},n._handleSwipe=function(){var e=Math.abs(this.touchDeltaX);e<=40||(e=e/this.touchDeltaX,(this.touchDeltaX=0)<e&&this.prev(),e<0&&this.next())},n._addEventListeners=function(){var t=this;this._config.keyboard&&u.default(this._element).on("keydown.bs.carousel",function(e){return t._keydown(e)}),"hover"===this._config.pause&&u.default(this._element).on("mouseenter.bs.carousel",function(e){return t.pause(e)}).on("mouseleave.bs.carousel",function(e){return t.cycle(e)}),this._config.touch&&this._addTouchEventListeners()},n._addTouchEventListeners=function(){var e,t,n=this;this._touchSupported&&(e=function(e){n._pointerEvent&&_[e.originalEvent.pointerType.toUpperCase()]?n.touchStartX=e.originalEvent.clientX:n._pointerEvent||(n.touchStartX=e.originalEvent.touches[0].clientX)},t=function(e){n._pointerEvent&&_[e.originalEvent.pointerType.toUpperCase()]&&(n.touchDeltaX=e.originalEvent.clientX-n.touchStartX),n._handleSwipe(),"hover"===n._config.pause&&(n.pause(),n.touchTimeout&&clearTimeout(n.touchTimeout),n.touchTimeout=setTimeout(function(e){return n.cycle(e)},500+n._config.interval))},u.default(this._element.querySelectorAll(".carousel-item img")).on("dragstart.bs.carousel",function(e){return e.preventDefault()}),this._pointerEvent?(u.default(this._element).on("pointerdown.bs.carousel",e),u.default(this._element).on("pointerup.bs.carousel",t),this._element.classList.add("pointer-event")):(u.default(this._element).on("touchstart.bs.carousel",e),u.default(this._element).on("touchmove.bs.carousel",function(e){(e=e).originalEvent.touches&&1<e.originalEvent.touches.length?n.touchDeltaX=0:n.touchDeltaX=e.originalEvent.touches[0].clientX-n.touchStartX}),u.default(this._element).on("touchend.bs.carousel",t)))},n._keydown=function(e){if(!/input|textarea/i.test(e.target.tagName))switch(e.which){case 37:e.preventDefault(),this.prev();break;case 39:e.preventDefault(),this.next()}},n._getItemIndex=function(e){return this._items=e&&e.parentNode?[].slice.call(e.parentNode.querySelectorAll(".carousel-item")):[],this._items.indexOf(e)},n._getItemByDirection=function(e,t){var n="next"===e,r="prev"===e,i=this._getItemIndex(t),o=this._items.length-1;if((r&&0===i||n&&i===o)&&!this._config.wrap)return t;e=(i+("prev"===e?-1:1))%this._items.length;return-1==e?this._items[this._items.length-1]:this._items[e]},n._triggerSlideEvent=function(e,t){var n=this._getItemIndex(e),r=this._getItemIndex(this._element.querySelector(".active.carousel-item")),n=u.default.Event("slide.bs.carousel",{relatedTarget:e,direction:t,from:r,to:n});return u.default(this._element).trigger(n),n},n._setActiveIndicatorElement=function(e){var t;this._indicatorsElement&&(t=[].slice.call(this._indicatorsElement.querySelectorAll(".active")),u.default(t).removeClass("active"),(e=this._indicatorsElement.children[this._getItemIndex(e)])&&u.default(e).addClass("active"))},n._slide=function(e,t){var n,r,i,o=this,a=this._element.querySelector(".active.carousel-item"),s=this._getItemIndex(a),l=t||a&&this._getItemByDirection(e,a),c=this._getItemIndex(l),t=Boolean(this._interval),e="next"===e?(n="carousel-item-left",r="carousel-item-next","left"):(n="carousel-item-right",r="carousel-item-prev","right");l&&u.default(l).hasClass("active")?this._isSliding=!1:!this._triggerSlideEvent(l,e).isDefaultPrevented()&&a&&l&&(this._isSliding=!0,t&&this.pause(),this._setActiveIndicatorElement(l),i=u.default.Event("slid.bs.carousel",{relatedTarget:l,direction:e,from:s,to:c}),u.default(this._element).hasClass("slide")?(u.default(l).addClass(r),d.reflow(l),u.default(a).addClass(n),u.default(l).addClass(n),(c=parseInt(l.getAttribute("data-interval"),10))?(this._config.defaultInterval=this._config.defaultInterval||this._config.interval,this._config.interval=c):this._config.interval=this._config.defaultInterval||this._config.interval,c=d.getTransitionDurationFromElement(a),u.default(a).one(d.TRANSITION_END,function(){u.default(l).removeClass(n+" "+r).addClass("active"),u.default(a).removeClass("active "+r+" "+n),o._isSliding=!1,setTimeout(function(){return u.default(o._element).trigger(i)},0)}).emulateTransitionEnd(c)):(u.default(a).removeClass("active"),u.default(l).addClass("active"),this._isSliding=!1,u.default(this._element).trigger(i)),t&&this.cycle())},k._jQueryInterface=function(r){return this.each(function(){var e=u.default(this).data("bs.carousel"),t=s({},b,u.default(this).data());"object"==_typeof(r)&&(t=s({},t,r));var n="string"==typeof r?r:t.slide;if(e||(e=new k(this,t),u.default(this).data("bs.carousel",e)),"number"==typeof r)e.to(r);else if("string"==typeof n){if(void 0===e[n])throw new TypeError('No method named "'+n+'"');e[n]()}else t.interval&&t.ride&&(e.pause(),e.cycle())})},k._dataApiClickHandler=function(e){var t,n,r=d.getSelectorFromElement(this);!r||(t=u.default(r)[0])&&u.default(t).hasClass("carousel")&&(n=s({},u.default(t).data(),u.default(this).data()),(r=this.getAttribute("data-slide-to"))&&(n.interval=!1),k._jQueryInterface.call(u.default(t),n),r&&u.default(t).data("bs.carousel").to(r),e.preventDefault())},a(k,null,[{key:"VERSION",get:function(){return"4.5.3"}},{key:"Default",get:function(){return b}}]),k);function k(e,t){this._items=null,this._interval=null,this._activeElement=null,this._isPaused=!1,this._isSliding=!1,this.touchTimeout=null,this.touchStartX=0,this.touchDeltaX=0,this._config=this._getConfig(t),this._element=e,this._indicatorsElement=this._element.querySelector(".carousel-indicators"),this._touchSupported="ontouchstart"in document.documentElement||0<navigator.maxTouchPoints,this._pointerEvent=Boolean(window.PointerEvent||window.MSPointerEvent),this._addEventListeners()}u.default(document).on("click.bs.carousel.data-api","[data-slide], [data-slide-to]",x._dataApiClickHandler),u.default(window).on("load.bs.carousel.data-api",function(){for(var e=[].slice.call(document.querySelectorAll('[data-ride="carousel"]')),t=0,n=e.length;t<n;t++){var r=u.default(e[t]);x._jQueryInterface.call(r,r.data())}}),u.default.fn[v]=x._jQueryInterface,u.default.fn[v].Constructor=x,u.default.fn[v].noConflict=function(){return u.default.fn[v]=y,x._jQueryInterface};var T="collapse",E=u.default.fn[T],S={toggle:!0,parent:""},C={toggle:"boolean",parent:"(string|element)"},A=((n=R.prototype).toggle=function(){u.default(this._element).hasClass("show")?this.hide():this.show()},n.show=function(){var e,t,n,r,i=this;this._isTransitioning||u.default(this._element).hasClass("show")||(this._parent&&0===(r=[].slice.call(this._parent.querySelectorAll(".show, .collapsing")).filter(function(e){return"string"==typeof i._config.parent?e.getAttribute("data-parent")===i._config.parent:e.classList.contains("collapse")})).length&&(r=null),r&&(n=u.default(r).not(this._selector).data("bs.collapse"))&&n._isTransitioning)||(e=u.default.Event("show.bs.collapse"),u.default(this._element).trigger(e),e.isDefaultPrevented()||(r&&(R._jQueryInterface.call(u.default(r).not(this._selector),"hide"),n||u.default(r).data("bs.collapse",null)),t=this._getDimension(),u.default(this._element).removeClass("collapse").addClass("collapsing"),this._element.style[t]=0,this._triggerArray.length&&u.default(this._triggerArray).removeClass("collapsed").attr("aria-expanded",!0),this.setTransitioning(!0),n="scroll"+(t[0].toUpperCase()+t.slice(1)),r=d.getTransitionDurationFromElement(this._element),u.default(this._element).one(d.TRANSITION_END,function(){u.default(i._element).removeClass("collapsing").addClass("collapse show"),i._element.style[t]="",i.setTransitioning(!1),u.default(i._element).trigger("shown.bs.collapse")}).emulateTransitionEnd(r),this._element.style[t]=this._element[n]+"px"))},n.hide=function(){var e=this;if(!this._isTransitioning&&u.default(this._element).hasClass("show")){var t=u.default.Event("hide.bs.collapse");if(u.default(this._element).trigger(t),!t.isDefaultPrevented()){t=this._getDimension();this._element.style[t]=this._element.getBoundingClientRect()[t]+"px",d.reflow(this._element),u.default(this._element).addClass("collapsing").removeClass("collapse show");var n=this._triggerArray.length;if(0<n)for(var r=0;r<n;r++){var i=this._triggerArray[r],o=d.getSelectorFromElement(i);null!==o&&(u.default([].slice.call(document.querySelectorAll(o))).hasClass("show")||u.default(i).addClass("collapsed").attr("aria-expanded",!1))}this.setTransitioning(!0),this._element.style[t]="";t=d.getTransitionDurationFromElement(this._element);u.default(this._element).one(d.TRANSITION_END,function(){e.setTransitioning(!1),u.default(e._element).removeClass("collapsing").addClass("collapse").trigger("hidden.bs.collapse")}).emulateTransitionEnd(t)}}},n.setTransitioning=function(e){this._isTransitioning=e},n.dispose=function(){u.default.removeData(this._element,"bs.collapse"),this._config=null,this._parent=null,this._element=null,this._triggerArray=null,this._isTransitioning=null},n._getConfig=function(e){return(e=s({},S,e)).toggle=Boolean(e.toggle),d.typeCheckConfig(T,e,C),e},n._getDimension=function(){return u.default(this._element).hasClass("width")?"width":"height"},n._getParent=function(){var e,n=this;d.isElement(this._config.parent)?(e=this._config.parent,void 0!==this._config.parent.jquery&&(e=this._config.parent[0])):e=document.querySelector(this._config.parent);var t='[data-toggle="collapse"][data-parent="'+this._config.parent+'"]',t=[].slice.call(e.querySelectorAll(t));return u.default(t).each(function(e,t){n._addAriaAndCollapsedClass(R._getTargetFromElement(t),[t])}),e},n._addAriaAndCollapsedClass=function(e,t){e=u.default(e).hasClass("show");t.length&&u.default(t).toggleClass("collapsed",!e).attr("aria-expanded",e)},R._getTargetFromElement=function(e){e=d.getSelectorFromElement(e);return e?document.querySelector(e):null},R._jQueryInterface=function(r){return this.each(function(){var e=u.default(this),t=e.data("bs.collapse"),n=s({},S,e.data(),"object"==_typeof(r)&&r?r:{});if(!t&&n.toggle&&"string"==typeof r&&/show|hide/.test(r)&&(n.toggle=!1),t||(t=new R(this,n),e.data("bs.collapse",t)),"string"==typeof r){if(void 0===t[r])throw new TypeError('No method named "'+r+'"');t[r]()}})},a(R,null,[{key:"VERSION",get:function(){return"4.5.3"}},{key:"Default",get:function(){return S}}]),R);function R(t,e){this._isTransitioning=!1,this._element=t,this._config=this._getConfig(e),this._triggerArray=[].slice.call(document.querySelectorAll('[data-toggle="collapse"][href="#'+t.id+'"],[data-toggle="collapse"][data-target="#'+t.id+'"]'));for(var n=[].slice.call(document.querySelectorAll('[data-toggle="collapse"]')),r=0,i=n.length;r<i;r++){var o=n[r],a=d.getSelectorFromElement(o),s=[].slice.call(document.querySelectorAll(a)).filter(function(e){return e===t});null!==a&&0<s.length&&(this._selector=a,this._triggerArray.push(o))}this._parent=this._config.parent?this._getParent():null,this._config.parent||this._addAriaAndCollapsedClass(this._element,this._triggerArray),this._config.toggle&&this.toggle()}u.default(document).on("click.bs.collapse.data-api",'[data-toggle="collapse"]',function(e){"A"===e.currentTarget.tagName&&e.preventDefault();var n=u.default(this),e=d.getSelectorFromElement(this),e=[].slice.call(document.querySelectorAll(e));u.default(e).each(function(){var e=u.default(this),t=e.data("bs.collapse")?"toggle":n.data();A._jQueryInterface.call(e,t)})}),u.default.fn[T]=A._jQueryInterface,u.default.fn[T].Constructor=A,u.default.fn[T].noConflict=function(){return u.default.fn[T]=E,A._jQueryInterface};var L="dropdown",D=u.default.fn[L],I=new RegExp("38|40|27"),N={offset:0,flip:!0,boundary:"scrollParent",reference:"toggle",display:"dynamic",popperConfig:null},M={offset:"(number|string|function)",flip:"boolean",boundary:"(string|element)",reference:"(string|element)",display:"string",popperConfig:"(null|object)"},O=((n=j.prototype).toggle=function(){var e;this._element.disabled||u.default(this._element).hasClass("disabled")||(e=u.default(this._menu).hasClass("show"),j._clearMenus(),e||this.show(!0))},n.show=function(e){if(void 0===e&&(e=!1),!(this._element.disabled||u.default(this._element).hasClass("disabled")||u.default(this._menu).hasClass("show"))){var t={relatedTarget:this._element},n=u.default.Event("show.bs.dropdown",t),r=j._getParentFromElement(this._element);if(u.default(r).trigger(n),!n.isDefaultPrevented()){if(!this._inNavbar&&e){if(void 0===i.default)throw new TypeError("Bootstrap's dropdowns require Popper.js (https://popper.js.org/)");e=this._element;"parent"===this._config.reference?e=r:d.isElement(this._config.reference)&&(e=this._config.reference,void 0!==this._config.reference.jquery&&(e=this._config.reference[0])),"scrollParent"!==this._config.boundary&&u.default(r).addClass("position-static"),this._popper=new i.default(e,this._menu,this._getPopperConfig())}"ontouchstart"in document.documentElement&&0===u.default(r).closest(".navbar-nav").length&&u.default(document.body).children().on("mouseover",null,u.default.noop),this._element.focus(),this._element.setAttribute("aria-expanded",!0),u.default(this._menu).toggleClass("show"),u.default(r).toggleClass("show").trigger(u.default.Event("shown.bs.dropdown",t))}}},n.hide=function(){var e,t,n;this._element.disabled||u.default(this._element).hasClass("disabled")||!u.default(this._menu).hasClass("show")||(e={relatedTarget:this._element},t=u.default.Event("hide.bs.dropdown",e),n=j._getParentFromElement(this._element),u.default(n).trigger(t),t.isDefaultPrevented()||(this._popper&&this._popper.destroy(),u.default(this._menu).toggleClass("show"),u.default(n).toggleClass("show").trigger(u.default.Event("hidden.bs.dropdown",e))))},n.dispose=function(){u.default.removeData(this._element,"bs.dropdown"),u.default(this._element).off(".bs.dropdown"),this._element=null,(this._menu=null)!==this._popper&&(this._popper.destroy(),this._popper=null)},n.update=function(){this._inNavbar=this._detectNavbar(),null!==this._popper&&this._popper.scheduleUpdate()},n._addEventListeners=function(){var t=this;u.default(this._element).on("click.bs.dropdown",function(e){e.preventDefault(),e.stopPropagation(),t.toggle()})},n._getConfig=function(e){return e=s({},this.constructor.Default,u.default(this._element).data(),e),d.typeCheckConfig(L,e,this.constructor.DefaultType),e},n._getMenuElement=function(){var e;return this._menu||(e=j._getParentFromElement(this._element))&&(this._menu=e.querySelector(".dropdown-menu")),this._menu},n._getPlacement=function(){var e=u.default(this._element.parentNode),t="bottom-start";return e.hasClass("dropup")?t=u.default(this._menu).hasClass("dropdown-menu-right")?"top-end":"top-start":e.hasClass("dropright")?t="right-start":e.hasClass("dropleft")?t="left-start":u.default(this._menu).hasClass("dropdown-menu-right")&&(t="bottom-end"),t},n._detectNavbar=function(){return 0<u.default(this._element).closest(".navbar").length},n._getOffset=function(){var t=this,e={};return"function"==typeof this._config.offset?e.fn=function(e){return e.offsets=s({},e.offsets,t._config.offset(e.offsets,t._element)||{}),e}:e.offset=this._config.offset,e},n._getPopperConfig=function(){var e={placement:this._getPlacement(),modifiers:{offset:this._getOffset(),flip:{enabled:this._config.flip},preventOverflow:{boundariesElement:this._config.boundary}}};return"static"===this._config.display&&(e.modifiers.applyStyle={enabled:!1}),s({},e,this._config.popperConfig)},j._jQueryInterface=function(t){return this.each(function(){var e=u.default(this).data("bs.dropdown");if(e||(e=new j(this,"object"==_typeof(t)?t:null),u.default(this).data("bs.dropdown",e)),"string"==typeof t){if(void 0===e[t])throw new TypeError('No method named "'+t+'"');e[t]()}})},j._clearMenus=function(e){if(!e||3!==e.which&&("keyup"!==e.type||9===e.which))for(var t=[].slice.call(document.querySelectorAll('[data-toggle="dropdown"]')),n=0,r=t.length;n<r;n++){var i,o,a=j._getParentFromElement(t[n]),s=u.default(t[n]).data("bs.dropdown"),l={relatedTarget:t[n]};e&&"click"===e.type&&(l.clickEvent=e),s&&(i=s._menu,!u.default(a).hasClass("show")||e&&("click"===e.type&&/input|textarea/i.test(e.target.tagName)||"keyup"===e.type&&9===e.which)&&u.default.contains(a,e.target)||(o=u.default.Event("hide.bs.dropdown",l),u.default(a).trigger(o),o.isDefaultPrevented()||("ontouchstart"in document.documentElement&&u.default(document.body).children().off("mouseover",null,u.default.noop),t[n].setAttribute("aria-expanded","false"),s._popper&&s._popper.destroy(),u.default(i).removeClass("show"),u.default(a).removeClass("show").trigger(u.default.Event("hidden.bs.dropdown",l)))))}},j._getParentFromElement=function(e){var t,n=d.getSelectorFromElement(e);return n&&(t=document.querySelector(n)),t||e.parentNode},j._dataApiKeydownHandler=function(e){if(!(/input|textarea/i.test(e.target.tagName)?32===e.which||27!==e.which&&(40!==e.which&&38!==e.which||u.default(e.target).closest(".dropdown-menu").length):!I.test(e.which))&&!this.disabled&&!u.default(this).hasClass("disabled")){var t=j._getParentFromElement(this),n=u.default(t).hasClass("show");if(n||27!==e.which){if(e.preventDefault(),e.stopPropagation(),!n||27===e.which||32===e.which)return 27===e.which&&u.default(t.querySelector('[data-toggle="dropdown"]')).trigger("focus"),void u.default(this).trigger("click");n=[].slice.call(t.querySelectorAll(".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)")).filter(function(e){return u.default(e).is(":visible")});0!==n.length&&(t=n.indexOf(e.target),38===e.which&&0<t&&t--,40===e.which&&t<n.length-1&&t++,t<0&&(t=0),n[t].focus())}}},a(j,null,[{key:"VERSION",get:function(){return"4.5.3"}},{key:"Default",get:function(){return N}},{key:"DefaultType",get:function(){return M}}]),j);function j(e,t){this._element=e,this._popper=null,this._config=this._getConfig(t),this._menu=this._getMenuElement(),this._inNavbar=this._detectNavbar(),this._addEventListeners()}u.default(document).on("keydown.bs.dropdown.data-api",'[data-toggle="dropdown"]',O._dataApiKeydownHandler).on("keydown.bs.dropdown.data-api",".dropdown-menu",O._dataApiKeydownHandler).on("click.bs.dropdown.data-api keyup.bs.dropdown.data-api",O._clearMenus).on("click.bs.dropdown.data-api",'[data-toggle="dropdown"]',function(e){e.preventDefault(),e.stopPropagation(),O._jQueryInterface.call(u.default(this),"toggle")}).on("click.bs.dropdown.data-api",".dropdown form",function(e){e.stopPropagation()}),u.default.fn[L]=O._jQueryInterface,u.default.fn[L].Constructor=O,u.default.fn[L].noConflict=function(){return u.default.fn[L]=D,O._jQueryInterface};var P=u.default.fn.modal,F={backdrop:!0,keyboard:!0,focus:!0,show:!0},B={backdrop:"(boolean|string)",keyboard:"boolean",focus:"boolean",show:"boolean"},U=((n=q.prototype).toggle=function(e){return this._isShown?this.hide():this.show(e)},n.show=function(e){var t,n=this;this._isShown||this._isTransitioning||(u.default(this._element).hasClass("fade")&&(this._isTransitioning=!0),t=u.default.Event("show.bs.modal",{relatedTarget:e}),u.default(this._element).trigger(t),this._isShown||t.isDefaultPrevented()||(this._isShown=!0,this._checkScrollbar(),this._setScrollbar(),this._adjustDialog(),this._setEscapeEvent(),this._setResizeEvent(),u.default(this._element).on("click.dismiss.bs.modal",'[data-dismiss="modal"]',function(e){return n.hide(e)}),u.default(this._dialog).on("mousedown.dismiss.bs.modal",function(){u.default(n._element).one("mouseup.dismiss.bs.modal",function(e){u.default(e.target).is(n._element)&&(n._ignoreBackdropClick=!0)})}),this._showBackdrop(function(){return n._showElement(e)})))},n.hide=function(e){var t=this;e&&e.preventDefault(),this._isShown&&!this._isTransitioning&&(e=u.default.Event("hide.bs.modal"),u.default(this._element).trigger(e),this._isShown&&!e.isDefaultPrevented()&&(this._isShown=!1,(e=u.default(this._element).hasClass("fade"))&&(this._isTransitioning=!0),this._setEscapeEvent(),this._setResizeEvent(),u.default(document).off("focusin.bs.modal"),u.default(this._element).removeClass("show"),u.default(this._element).off("click.dismiss.bs.modal"),u.default(this._dialog).off("mousedown.dismiss.bs.modal"),e?(e=d.getTransitionDurationFromElement(this._element),u.default(this._element).one(d.TRANSITION_END,function(e){return t._hideModal(e)}).emulateTransitionEnd(e)):this._hideModal()))},n.dispose=function(){[window,this._element,this._dialog].forEach(function(e){return u.default(e).off(".bs.modal")}),u.default(document).off("focusin.bs.modal"),u.default.removeData(this._element,"bs.modal"),this._config=null,this._element=null,this._dialog=null,this._backdrop=null,this._isShown=null,this._isBodyOverflowing=null,this._ignoreBackdropClick=null,this._isTransitioning=null,this._scrollbarWidth=null},n.handleUpdate=function(){this._adjustDialog()},n._getConfig=function(e){return e=s({},F,e),d.typeCheckConfig("modal",e,B),e},n._triggerBackdropTransition=function(){var e,t,n,r=this;"static"===this._config.backdrop?(e=u.default.Event("hidePrevented.bs.modal"),u.default(this._element).trigger(e),e.isDefaultPrevented()||((t=this._element.scrollHeight>document.documentElement.clientHeight)||(this._element.style.overflowY="hidden"),this._element.classList.add("modal-static"),n=d.getTransitionDurationFromElement(this._dialog),u.default(this._element).off(d.TRANSITION_END),u.default(this._element).one(d.TRANSITION_END,function(){r._element.classList.remove("modal-static"),t||u.default(r._element).one(d.TRANSITION_END,function(){r._element.style.overflowY=""}).emulateTransitionEnd(r._element,n)}).emulateTransitionEnd(n),this._element.focus())):this.hide()},n._showElement=function(e){var t=this,n=u.default(this._element).hasClass("fade"),r=this._dialog?this._dialog.querySelector(".modal-body"):null;this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE||document.body.appendChild(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),u.default(this._dialog).hasClass("modal-dialog-scrollable")&&r?r.scrollTop=0:this._element.scrollTop=0,n&&d.reflow(this._element),u.default(this._element).addClass("show"),this._config.focus&&this._enforceFocus();var i=u.default.Event("shown.bs.modal",{relatedTarget:e}),e=function(){t._config.focus&&t._element.focus(),t._isTransitioning=!1,u.default(t._element).trigger(i)};n?(n=d.getTransitionDurationFromElement(this._dialog),u.default(this._dialog).one(d.TRANSITION_END,e).emulateTransitionEnd(n)):e()},n._enforceFocus=function(){var t=this;u.default(document).off("focusin.bs.modal").on("focusin.bs.modal",function(e){document!==e.target&&t._element!==e.target&&0===u.default(t._element).has(e.target).length&&t._element.focus()})},n._setEscapeEvent=function(){var t=this;this._isShown?u.default(this._element).on("keydown.dismiss.bs.modal",function(e){t._config.keyboard&&27===e.which?(e.preventDefault(),t.hide()):t._config.keyboard||27!==e.which||t._triggerBackdropTransition()}):this._isShown||u.default(this._element).off("keydown.dismiss.bs.modal")},n._setResizeEvent=function(){var t=this;this._isShown?u.default(window).on("resize.bs.modal",function(e){return t.handleUpdate(e)}):u.default(window).off("resize.bs.modal")},n._hideModal=function(){var e=this;this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._showBackdrop(function(){u.default(document.body).removeClass("modal-open"),e._resetAdjustments(),e._resetScrollbar(),u.default(e._element).trigger("hidden.bs.modal")})},n._removeBackdrop=function(){this._backdrop&&(u.default(this._backdrop).remove(),this._backdrop=null)},n._showBackdrop=function(e){var t,n=this,r=u.default(this._element).hasClass("fade")?"fade":"";this._isShown&&this._config.backdrop?(this._backdrop=document.createElement("div"),this._backdrop.className="modal-backdrop",r&&this._backdrop.classList.add(r),u.default(this._backdrop).appendTo(document.body),u.default(this._element).on("click.dismiss.bs.modal",function(e){n._ignoreBackdropClick?n._ignoreBackdropClick=!1:e.target===e.currentTarget&&n._triggerBackdropTransition()}),r&&d.reflow(this._backdrop),u.default(this._backdrop).addClass("show"),e&&(r?(t=d.getTransitionDurationFromElement(this._backdrop),u.default(this._backdrop).one(d.TRANSITION_END,e).emulateTransitionEnd(t)):e())):!this._isShown&&this._backdrop?(u.default(this._backdrop).removeClass("show"),r=function(){n._removeBackdrop(),e&&e()},u.default(this._element).hasClass("fade")?(t=d.getTransitionDurationFromElement(this._backdrop),u.default(this._backdrop).one(d.TRANSITION_END,r).emulateTransitionEnd(t)):r()):e&&e()},n._adjustDialog=function(){var e=this._element.scrollHeight>document.documentElement.clientHeight;!this._isBodyOverflowing&&e&&(this._element.style.paddingLeft=this._scrollbarWidth+"px"),this._isBodyOverflowing&&!e&&(this._element.style.paddingRight=this._scrollbarWidth+"px")},n._resetAdjustments=function(){this._element.style.paddingLeft="",this._element.style.paddingRight=""},n._checkScrollbar=function(){var e=document.body.getBoundingClientRect();this._isBodyOverflowing=Math.round(e.left+e.right)<window.innerWidth,this._scrollbarWidth=this._getScrollbarWidth()},n._setScrollbar=function(){var e,t,i=this;this._isBodyOverflowing&&(e=[].slice.call(document.querySelectorAll(".fixed-top, .fixed-bottom, .is-fixed, .sticky-top")),t=[].slice.call(document.querySelectorAll(".sticky-top")),u.default(e).each(function(e,t){var n=t.style.paddingRight,r=u.default(t).css("padding-right");u.default(t).data("padding-right",n).css("padding-right",parseFloat(r)+i._scrollbarWidth+"px")}),u.default(t).each(function(e,t){var n=t.style.marginRight,r=u.default(t).css("margin-right");u.default(t).data("margin-right",n).css("margin-right",parseFloat(r)-i._scrollbarWidth+"px")}),e=document.body.style.paddingRight,t=u.default(document.body).css("padding-right"),u.default(document.body).data("padding-right",e).css("padding-right",parseFloat(t)+this._scrollbarWidth+"px")),u.default(document.body).addClass("modal-open")},n._resetScrollbar=function(){var e=[].slice.call(document.querySelectorAll(".fixed-top, .fixed-bottom, .is-fixed, .sticky-top"));u.default(e).each(function(e,t){var n=u.default(t).data("padding-right");u.default(t).removeData("padding-right"),t.style.paddingRight=n||""});e=[].slice.call(document.querySelectorAll(".sticky-top"));u.default(e).each(function(e,t){var n=u.default(t).data("margin-right");void 0!==n&&u.default(t).css("margin-right",n).removeData("margin-right")});e=u.default(document.body).data("padding-right");u.default(document.body).removeData("padding-right"),document.body.style.paddingRight=e||""},n._getScrollbarWidth=function(){var e=document.createElement("div");e.className="modal-scrollbar-measure",document.body.appendChild(e);var t=e.getBoundingClientRect().width-e.clientWidth;return document.body.removeChild(e),t},q._jQueryInterface=function(n,r){return this.each(function(){var e=u.default(this).data("bs.modal"),t=s({},F,u.default(this).data(),"object"==_typeof(n)&&n?n:{});if(e||(e=new q(this,t),u.default(this).data("bs.modal",e)),"string"==typeof n){if(void 0===e[n])throw new TypeError('No method named "'+n+'"');e[n](r)}else t.show&&e.show(r)})},a(q,null,[{key:"VERSION",get:function(){return"4.5.3"}},{key:"Default",get:function(){return F}}]),q);function q(e,t){this._config=this._getConfig(t),this._element=e,this._dialog=e.querySelector(".modal-dialog"),this._backdrop=null,this._isShown=!1,this._isBodyOverflowing=!1,this._ignoreBackdropClick=!1,this._isTransitioning=!1,this._scrollbarWidth=0}u.default(document).on("click.bs.modal.data-api",'[data-toggle="modal"]',function(e){var t,n=this,r=d.getSelectorFromElement(this);r&&(t=document.querySelector(r));r=u.default(t).data("bs.modal")?"toggle":s({},u.default(t).data(),u.default(this).data());"A"!==this.tagName&&"AREA"!==this.tagName||e.preventDefault();var i=u.default(t).one("show.bs.modal",function(e){e.isDefaultPrevented()||i.one("hidden.bs.modal",function(){u.default(n).is(":visible")&&n.focus()})});U._jQueryInterface.call(u.default(t),r,this)}),u.default.fn.modal=U._jQueryInterface,u.default.fn.modal.Constructor=U,u.default.fn.modal.noConflict=function(){return u.default.fn.modal=P,U._jQueryInterface};var H=["background","cite","href","itemtype","longdesc","poster","src","xlink:href"],$=/^(?:(?:https?|mailto|ftp|tel|file):|[^#&/:?]*(?:[#/?]|$))/gi,G=/^data:(?:image\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\/(?:mpeg|mp4|ogg|webm)|audio\/(?:mp3|oga|ogg|opus));base64,[\d+/a-z]+=*$/i;function V(e,i,t){if(0===e.length)return e;if(t&&"function"==typeof t)return t(e);for(var e=(new window.DOMParser).parseFromString(e,"text/html"),o=Object.keys(i),a=[].slice.call(e.body.querySelectorAll("*")),n=0,r=a.length;n<r;n++)!function(e){var t=a[e],n=t.nodeName.toLowerCase();if(-1===o.indexOf(t.nodeName.toLowerCase()))return t.parentNode.removeChild(t);var e=[].slice.call(t.attributes),r=[].concat(i["*"]||[],i[n]||[]);e.forEach(function(e){!function(e,t){var n=e.nodeName.toLowerCase();if(-1!==t.indexOf(n))return-1===H.indexOf(n)||Boolean(e.nodeValue.match($)||e.nodeValue.match(G));for(var r=t.filter(function(e){return e instanceof RegExp}),i=0,o=r.length;i<o;i++)if(n.match(r[i]))return 1}(e,r)&&t.removeAttribute(e.nodeName)})}(n);return e.body.innerHTML}var W="tooltip",z=u.default.fn[W],Q=new RegExp("(^|\\s)bs-tooltip\\S+","g"),X=["sanitize","whiteList","sanitizeFn"],Y={animation:"boolean",template:"string",title:"(string|element|function)",trigger:"string",delay:"(number|object)",html:"boolean",selector:"(string|boolean)",placement:"(string|function)",offset:"(number|string|function)",container:"(string|element|boolean)",fallbackPlacement:"(string|array)",boundary:"(string|element)",sanitize:"boolean",sanitizeFn:"(null|function)",whiteList:"object",popperConfig:"(null|object)"},K={AUTO:"auto",TOP:"top",RIGHT:"right",BOTTOM:"bottom",LEFT:"left"},J={animation:!0,template:'<div class="tooltip" role="tooltip"><div class="arrow"></div><div class="tooltip-inner"></div></div>',trigger:"hover focus",title:"",delay:0,html:!1,selector:!1,placement:"top",offset:0,container:!1,fallbackPlacement:"flip",boundary:"scrollParent",sanitize:!0,sanitizeFn:null,whiteList:{"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],div:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},popperConfig:null},Z={HIDE:"hide.bs.tooltip",HIDDEN:"hidden.bs.tooltip",SHOW:"show.bs.tooltip",SHOWN:"shown.bs.tooltip",INSERTED:"inserted.bs.tooltip",CLICK:"click.bs.tooltip",FOCUSIN:"focusin.bs.tooltip",FOCUSOUT:"focusout.bs.tooltip",MOUSEENTER:"mouseenter.bs.tooltip",MOUSELEAVE:"mouseleave.bs.tooltip"},ee=((n=te.prototype).enable=function(){this._isEnabled=!0},n.disable=function(){this._isEnabled=!1},n.toggleEnabled=function(){this._isEnabled=!this._isEnabled},n.toggle=function(e){var t,n;this._isEnabled&&(e?(t=this.constructor.DATA_KEY,(n=u.default(e.currentTarget).data(t))||(n=new this.constructor(e.currentTarget,this._getDelegateConfig()),u.default(e.currentTarget).data(t,n)),n._activeTrigger.click=!n._activeTrigger.click,n._isWithActiveTrigger()?n._enter(null,n):n._leave(null,n)):u.default(this.getTipElement()).hasClass("show")?this._leave(null,this):this._enter(null,this))},n.dispose=function(){clearTimeout(this._timeout),u.default.removeData(this.element,this.constructor.DATA_KEY),u.default(this.element).off(this.constructor.EVENT_KEY),u.default(this.element).closest(".modal").off("hide.bs.modal",this._hideModalHandler),this.tip&&u.default(this.tip).remove(),this._isEnabled=null,this._timeout=null,this._hoverState=null,this._activeTrigger=null,this._popper&&this._popper.destroy(),this._popper=null,this.element=null,this.config=null,this.tip=null},n.show=function(){var t=this;if("none"===u.default(this.element).css("display"))throw new Error("Please use show on visible elements");var e,n,r=u.default.Event(this.constructor.Event.SHOW);this.isWithContent()&&this._isEnabled&&(u.default(this.element).trigger(r),n=d.findShadowRoot(this.element),e=u.default.contains(null!==n?n:this.element.ownerDocument.documentElement,this.element),!r.isDefaultPrevented()&&e&&(n=this.getTipElement(),r=d.getUID(this.constructor.NAME),n.setAttribute("id",r),this.element.setAttribute("aria-describedby",r),this.setContent(),this.config.animation&&u.default(n).addClass("fade"),e="function"==typeof this.config.placement?this.config.placement.call(this,n,this.element):this.config.placement,r=this._getAttachment(e),this.addAttachmentClass(r),e=this._getContainer(),u.default(n).data(this.constructor.DATA_KEY,this),u.default.contains(this.element.ownerDocument.documentElement,this.tip)||u.default(n).appendTo(e),u.default(this.element).trigger(this.constructor.Event.INSERTED),this._popper=new i.default(this.element,n,this._getPopperConfig(r)),u.default(n).addClass("show"),"ontouchstart"in document.documentElement&&u.default(document.body).children().on("mouseover",null,u.default.noop),r=function(){t.config.animation&&t._fixTransition();var e=t._hoverState;t._hoverState=null,u.default(t.element).trigger(t.constructor.Event.SHOWN),"out"===e&&t._leave(null,t)},u.default(this.tip).hasClass("fade")?(n=d.getTransitionDurationFromElement(this.tip),u.default(this.tip).one(d.TRANSITION_END,r).emulateTransitionEnd(n)):r()))},n.hide=function(e){function t(){"show"!==n._hoverState&&r.parentNode&&r.parentNode.removeChild(r),n._cleanTipClass(),n.element.removeAttribute("aria-describedby"),u.default(n.element).trigger(n.constructor.Event.HIDDEN),null!==n._popper&&n._popper.destroy(),e&&e()}var n=this,r=this.getTipElement(),i=u.default.Event(this.constructor.Event.HIDE);u.default(this.element).trigger(i),i.isDefaultPrevented()||(u.default(r).removeClass("show"),"ontouchstart"in document.documentElement&&u.default(document.body).children().off("mouseover",null,u.default.noop),this._activeTrigger.click=!1,this._activeTrigger.focus=!1,this._activeTrigger.hover=!1,u.default(this.tip).hasClass("fade")?(i=d.getTransitionDurationFromElement(r),u.default(r).one(d.TRANSITION_END,t).emulateTransitionEnd(i)):t(),this._hoverState="")},n.update=function(){null!==this._popper&&this._popper.scheduleUpdate()},n.isWithContent=function(){return Boolean(this.getTitle())},n.addAttachmentClass=function(e){u.default(this.getTipElement()).addClass("bs-tooltip-"+e)},n.getTipElement=function(){return this.tip=this.tip||u.default(this.config.template)[0],this.tip},n.setContent=function(){var e=this.getTipElement();this.setElementContent(u.default(e.querySelectorAll(".tooltip-inner")),this.getTitle()),u.default(e).removeClass("fade show")},n.setElementContent=function(e,t){"object"!=_typeof(t)||!t.nodeType&&!t.jquery?this.config.html?(this.config.sanitize&&(t=V(t,this.config.whiteList,this.config.sanitizeFn)),e.html(t)):e.text(t):this.config.html?u.default(t).parent().is(e)||e.empty().append(t):e.text(u.default(t).text())},n.getTitle=function(){return this.element.getAttribute("data-original-title")||("function"==typeof this.config.title?this.config.title.call(this.element):this.config.title)},n._getPopperConfig=function(e){var t=this;return s({},{placement:e,modifiers:{offset:this._getOffset(),flip:{behavior:this.config.fallbackPlacement},arrow:{element:".arrow"},preventOverflow:{boundariesElement:this.config.boundary}},onCreate:function(e){e.originalPlacement!==e.placement&&t._handlePopperPlacementChange(e)},onUpdate:function(e){return t._handlePopperPlacementChange(e)}},this.config.popperConfig)},n._getOffset=function(){var t=this,e={};return"function"==typeof this.config.offset?e.fn=function(e){return e.offsets=s({},e.offsets,t.config.offset(e.offsets,t.element)||{}),e}:e.offset=this.config.offset,e},n._getContainer=function(){return!1===this.config.container?document.body:d.isElement(this.config.container)?u.default(this.config.container):u.default(document).find(this.config.container)},n._getAttachment=function(e){return K[e.toUpperCase()]},n._setListeners=function(){var n=this;this.config.trigger.split(" ").forEach(function(e){var t;"click"===e?u.default(n.element).on(n.constructor.Event.CLICK,n.config.selector,function(e){return n.toggle(e)}):"manual"!==e&&(t="hover"===e?n.constructor.Event.MOUSEENTER:n.constructor.Event.FOCUSIN,e="hover"===e?n.constructor.Event.MOUSELEAVE:n.constructor.Event.FOCUSOUT,u.default(n.element).on(t,n.config.selector,function(e){return n._enter(e)}).on(e,n.config.selector,function(e){return n._leave(e)}))}),this._hideModalHandler=function(){n.element&&n.hide()},u.default(this.element).closest(".modal").on("hide.bs.modal",this._hideModalHandler),this.config.selector?this.config=s({},this.config,{trigger:"manual",selector:""}):this._fixTitle()},n._fixTitle=function(){var e=_typeof(this.element.getAttribute("data-original-title"));!this.element.getAttribute("title")&&"string"===e||(this.element.setAttribute("data-original-title",this.element.getAttribute("title")||""),this.element.setAttribute("title",""))},n._enter=function(e,t){var n=this.constructor.DATA_KEY;(t=t||u.default(e.currentTarget).data(n))||(t=new this.constructor(e.currentTarget,this._getDelegateConfig()),u.default(e.currentTarget).data(n,t)),e&&(t._activeTrigger["focusin"===e.type?"focus":"hover"]=!0),u.default(t.getTipElement()).hasClass("show")||"show"===t._hoverState?t._hoverState="show":(clearTimeout(t._timeout),t._hoverState="show",t.config.delay&&t.config.delay.show?t._timeout=setTimeout(function(){"show"===t._hoverState&&t.show()},t.config.delay.show):t.show())},n._leave=function(e,t){var n=this.constructor.DATA_KEY;(t=t||u.default(e.currentTarget).data(n))||(t=new this.constructor(e.currentTarget,this._getDelegateConfig()),u.default(e.currentTarget).data(n,t)),e&&(t._activeTrigger["focusout"===e.type?"focus":"hover"]=!1),t._isWithActiveTrigger()||(clearTimeout(t._timeout),t._hoverState="out",t.config.delay&&t.config.delay.hide?t._timeout=setTimeout(function(){"out"===t._hoverState&&t.hide()},t.config.delay.hide):t.hide())},n._isWithActiveTrigger=function(){for(var e in this._activeTrigger)if(this._activeTrigger[e])return!0;return!1},n._getConfig=function(e){var t=u.default(this.element).data();return Object.keys(t).forEach(function(e){-1!==X.indexOf(e)&&delete t[e]}),"number"==typeof(e=s({},this.constructor.Default,t,"object"==_typeof(e)&&e?e:{})).delay&&(e.delay={show:e.delay,hide:e.delay}),"number"==typeof e.title&&(e.title=e.title.toString()),"number"==typeof e.content&&(e.content=e.content.toString()),d.typeCheckConfig(W,e,this.constructor.DefaultType),e.sanitize&&(e.template=V(e.template,e.whiteList,e.sanitizeFn)),e},n._getDelegateConfig=function(){var e={};if(this.config)for(var t in this.config)this.constructor.Default[t]!==this.config[t]&&(e[t]=this.config[t]);return e},n._cleanTipClass=function(){var e=u.default(this.getTipElement()),t=e.attr("class").match(Q);null!==t&&t.length&&e.removeClass(t.join(""))},n._handlePopperPlacementChange=function(e){this.tip=e.instance.popper,this._cleanTipClass(),this.addAttachmentClass(this._getAttachment(e.placement))},n._fixTransition=function(){var e=this.getTipElement(),t=this.config.animation;null===e.getAttribute("x-placement")&&(u.default(e).removeClass("fade"),this.config.animation=!1,this.hide(),this.show(),this.config.animation=t)},te._jQueryInterface=function(r){return this.each(function(){var e=u.default(this),t=e.data("bs.tooltip"),n="object"==_typeof(r)&&r;if((t||!/dispose|hide/.test(r))&&(t||(t=new te(this,n),e.data("bs.tooltip",t)),"string"==typeof r)){if(void 0===t[r])throw new TypeError('No method named "'+r+'"');t[r]()}})},a(te,null,[{key:"VERSION",get:function(){return"4.5.3"}},{key:"Default",get:function(){return J}},{key:"NAME",get:function(){return W}},{key:"DATA_KEY",get:function(){return"bs.tooltip"}},{key:"Event",get:function(){return Z}},{key:"EVENT_KEY",get:function(){return".bs.tooltip"}},{key:"DefaultType",get:function(){return Y}}]),te);function te(e,t){if(void 0===i.default)throw new TypeError("Bootstrap's tooltips require Popper.js (https://popper.js.org/)");this._isEnabled=!0,this._timeout=0,this._hoverState="",this._activeTrigger={},this._popper=null,this.element=e,this.config=this._getConfig(t),this.tip=null,this._setListeners()}u.default.fn[W]=ee._jQueryInterface,u.default.fn[W].Constructor=ee,u.default.fn[W].noConflict=function(){return u.default.fn[W]=z,ee._jQueryInterface};var ne="popover",re=u.default.fn[ne],ie=new RegExp("(^|\\s)bs-popover\\S+","g"),oe=s({},ee.Default,{placement:"right",trigger:"click",content:"",template:'<div class="popover" role="tooltip"><div class="arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>'}),ae=s({},ee.DefaultType,{content:"(string|element|function)"}),se={HIDE:"hide.bs.popover",HIDDEN:"hidden.bs.popover",SHOW:"show.bs.popover",SHOWN:"shown.bs.popover",INSERTED:"inserted.bs.popover",CLICK:"click.bs.popover",FOCUSIN:"focusin.bs.popover",FOCUSOUT:"focusout.bs.popover",MOUSEENTER:"mouseenter.bs.popover",MOUSELEAVE:"mouseleave.bs.popover"},le=function(e){var t;function r(){return e.apply(this,arguments)||this}n=e,(t=r).prototype=Object.create(n.prototype),(t.prototype.constructor=t).__proto__=n;var n=r.prototype;return n.isWithContent=function(){return this.getTitle()||this._getContent()},n.addAttachmentClass=function(e){u.default(this.getTipElement()).addClass("bs-popover-"+e)},n.getTipElement=function(){return this.tip=this.tip||u.default(this.config.template)[0],this.tip},n.setContent=function(){var e=u.default(this.getTipElement());this.setElementContent(e.find(".popover-header"),this.getTitle());var t=this._getContent();"function"==typeof t&&(t=t.call(this.element)),this.setElementContent(e.find(".popover-body"),t),e.removeClass("fade show")},n._getContent=function(){return this.element.getAttribute("data-content")||this.config.content},n._cleanTipClass=function(){var e=u.default(this.getTipElement()),t=e.attr("class").match(ie);null!==t&&0<t.length&&e.removeClass(t.join(""))},r._jQueryInterface=function(n){return this.each(function(){var e=u.default(this).data("bs.popover"),t="object"==_typeof(n)?n:null;if((e||!/dispose|hide/.test(n))&&(e||(e=new r(this,t),u.default(this).data("bs.popover",e)),"string"==typeof n)){if(void 0===e[n])throw new TypeError('No method named "'+n+'"');e[n]()}})},a(r,null,[{key:"VERSION",get:function(){return"4.5.3"}},{key:"Default",get:function(){return oe}},{key:"NAME",get:function(){return ne}},{key:"DATA_KEY",get:function(){return"bs.popover"}},{key:"Event",get:function(){return se}},{key:"EVENT_KEY",get:function(){return".bs.popover"}},{key:"DefaultType",get:function(){return ae}}]),r}(ee);u.default.fn[ne]=le._jQueryInterface,u.default.fn[ne].Constructor=le,u.default.fn[ne].noConflict=function(){return u.default.fn[ne]=re,le._jQueryInterface};var ce="scrollspy",ue=u.default.fn[ce],de={offset:10,method:"auto",target:""},fe={offset:"number",method:"string",target:"(string|element)"},pe=((n=he.prototype).refresh=function(){var t=this,e=this._scrollElement===this._scrollElement.window?"offset":"position",r="auto"===this._config.method?e:this._config.method,i="position"===r?this._getScrollTop():0;this._offsets=[],this._targets=[],this._scrollHeight=this._getScrollHeight(),[].slice.call(document.querySelectorAll(this._selector)).map(function(e){var t,n=d.getSelectorFromElement(e);if(n&&(t=document.querySelector(n)),t){e=t.getBoundingClientRect();if(e.width||e.height)return[u.default(t)[r]().top+i,n]}return null}).filter(function(e){return e}).sort(function(e,t){return e[0]-t[0]}).forEach(function(e){t._offsets.push(e[0]),t._targets.push(e[1])})},n.dispose=function(){u.default.removeData(this._element,"bs.scrollspy"),u.default(this._scrollElement).off(".bs.scrollspy"),this._element=null,this._scrollElement=null,this._config=null,this._selector=null,this._offsets=null,this._targets=null,this._activeTarget=null,this._scrollHeight=null},n._getConfig=function(e){var t;return"string"!=typeof(e=s({},de,"object"==_typeof(e)&&e?e:{})).target&&d.isElement(e.target)&&((t=u.default(e.target).attr("id"))||(t=d.getUID(ce),u.default(e.target).attr("id",t)),e.target="#"+t),d.typeCheckConfig(ce,e,fe),e},n._getScrollTop=function(){return this._scrollElement===window?this._scrollElement.pageYOffset:this._scrollElement.scrollTop},n._getScrollHeight=function(){return this._scrollElement.scrollHeight||Math.max(document.body.scrollHeight,document.documentElement.scrollHeight)},n._getOffsetHeight=function(){return this._scrollElement===window?window.innerHeight:this._scrollElement.getBoundingClientRect().height},n._process=function(){var e=this._getScrollTop()+this._config.offset,t=this._getScrollHeight(),n=this._config.offset+t-this._getOffsetHeight();if(this._scrollHeight!==t&&this.refresh(),n<=e){n=this._targets[this._targets.length-1];this._activeTarget!==n&&this._activate(n)}else{if(this._activeTarget&&e<this._offsets[0]&&0<this._offsets[0])return this._activeTarget=null,void this._clear();for(var r=this._offsets.length;r--;)this._activeTarget!==this._targets[r]&&e>=this._offsets[r]&&(void 0===this._offsets[r+1]||e<this._offsets[r+1])&&this._activate(this._targets[r])}},n._activate=function(t){this._activeTarget=t,this._clear();var e=this._selector.split(",").map(function(e){return e+'[data-target="'+t+'"],'+e+'[href="'+t+'"]'}),e=u.default([].slice.call(document.querySelectorAll(e.join(","))));e.hasClass("dropdown-item")?(e.closest(".dropdown").find(".dropdown-toggle").addClass("active"),e.addClass("active")):(e.addClass("active"),e.parents(".nav, .list-group").prev(".nav-link, .list-group-item").addClass("active"),e.parents(".nav, .list-group").prev(".nav-item").children(".nav-link").addClass("active")),u.default(this._scrollElement).trigger("activate.bs.scrollspy",{relatedTarget:t})},n._clear=function(){[].slice.call(document.querySelectorAll(this._selector)).filter(function(e){return e.classList.contains("active")}).forEach(function(e){return e.classList.remove("active")})},he._jQueryInterface=function(t){return this.each(function(){var e=u.default(this).data("bs.scrollspy");if(e||(e=new he(this,"object"==_typeof(t)&&t),u.default(this).data("bs.scrollspy",e)),"string"==typeof t){if(void 0===e[t])throw new TypeError('No method named "'+t+'"');e[t]()}})},a(he,null,[{key:"VERSION",get:function(){return"4.5.3"}},{key:"Default",get:function(){return de}}]),he);function he(e,t){var n=this;this._element=e,this._scrollElement="BODY"===e.tagName?window:e,this._config=this._getConfig(t),this._selector=this._config.target+" .nav-link,"+this._config.target+" .list-group-item,"+this._config.target+" .dropdown-item",this._offsets=[],this._targets=[],this._activeTarget=null,this._scrollHeight=0,u.default(this._scrollElement).on("scroll.bs.scrollspy",function(e){return n._process(e)}),this.refresh(),this._process()}u.default(window).on("load.bs.scrollspy.data-api",function(){for(var e=[].slice.call(document.querySelectorAll('[data-spy="scroll"]')),t=e.length;t--;){var n=u.default(e[t]);pe._jQueryInterface.call(n,n.data())}}),u.default.fn[ce]=pe._jQueryInterface,u.default.fn[ce].Constructor=pe,u.default.fn[ce].noConflict=function(){return u.default.fn[ce]=ue,pe._jQueryInterface};var ge=u.default.fn.tab,me=((n=ve.prototype).show=function(){var e,t,n,r,i,o,a=this;this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE&&u.default(this._element).hasClass("active")||u.default(this._element).hasClass("disabled")||(o=u.default(this._element).closest(".nav, .list-group")[0],t=d.getSelectorFromElement(this._element),o&&(i="UL"===o.nodeName||"OL"===o.nodeName?"> li > .active":".active",n=(n=u.default.makeArray(u.default(o).find(i)))[n.length-1]),r=u.default.Event("hide.bs.tab",{relatedTarget:this._element}),i=u.default.Event("show.bs.tab",{relatedTarget:n}),n&&u.default(n).trigger(r),u.default(this._element).trigger(i),i.isDefaultPrevented()||r.isDefaultPrevented()||(t&&(e=document.querySelector(t)),this._activate(this._element,o),o=function(){var e=u.default.Event("hidden.bs.tab",{relatedTarget:a._element}),t=u.default.Event("shown.bs.tab",{relatedTarget:n});u.default(n).trigger(e),u.default(a._element).trigger(t)},e?this._activate(e,e.parentNode,o):o()))},n.dispose=function(){u.default.removeData(this._element,"bs.tab"),this._element=null},n._activate=function(e,t,n){var r=this,i=(!t||"UL"!==t.nodeName&&"OL"!==t.nodeName?u.default(t).children(".active"):u.default(t).find("> li > .active"))[0],o=n&&i&&u.default(i).hasClass("fade"),t=function(){return r._transitionComplete(e,i,n)};i&&o?(o=d.getTransitionDurationFromElement(i),u.default(i).removeClass("show").one(d.TRANSITION_END,t).emulateTransitionEnd(o)):t()},n._transitionComplete=function(e,t,n){var r;t&&(u.default(t).removeClass("active"),(r=u.default(t.parentNode).find("> .dropdown-menu .active")[0])&&u.default(r).removeClass("active"),"tab"===t.getAttribute("role")&&t.setAttribute("aria-selected",!1)),u.default(e).addClass("active"),"tab"===e.getAttribute("role")&&e.setAttribute("aria-selected",!0),d.reflow(e),e.classList.contains("fade")&&e.classList.add("show"),e.parentNode&&u.default(e.parentNode).hasClass("dropdown-menu")&&((t=u.default(e).closest(".dropdown")[0])&&(t=[].slice.call(t.querySelectorAll(".dropdown-toggle")),u.default(t).addClass("active")),e.setAttribute("aria-expanded",!0)),n&&n()},ve._jQueryInterface=function(n){return this.each(function(){var e=u.default(this),t=e.data("bs.tab");if(t||(t=new ve(this),e.data("bs.tab",t)),"string"==typeof n){if(void 0===t[n])throw new TypeError('No method named "'+n+'"');t[n]()}})},a(ve,null,[{key:"VERSION",get:function(){return"4.5.3"}}]),ve);function ve(e){this._element=e}u.default(document).on("click.bs.tab.data-api",'[data-toggle="tab"], [data-toggle="pill"], [data-toggle="list"]',function(e){e.preventDefault(),me._jQueryInterface.call(u.default(this),"show")}),u.default.fn.tab=me._jQueryInterface,u.default.fn.tab.Constructor=me,u.default.fn.tab.noConflict=function(){return u.default.fn.tab=ge,me._jQueryInterface};var ye=u.default.fn.toast,be={animation:"boolean",autohide:"boolean",delay:"number"},we={animation:!0,autohide:!0,delay:500},_e=((n=xe.prototype).show=function(){var e,t=this,n=u.default.Event("show.bs.toast");u.default(this._element).trigger(n),n.isDefaultPrevented()||(this._clearTimeout(),this._config.animation&&this._element.classList.add("fade"),e=function(){t._element.classList.remove("showing"),t._element.classList.add("show"),u.default(t._element).trigger("shown.bs.toast"),t._config.autohide&&(t._timeout=setTimeout(function(){t.hide()},t._config.delay))},this._element.classList.remove("hide"),d.reflow(this._element),this._element.classList.add("showing"),this._config.animation?(n=d.getTransitionDurationFromElement(this._element),u.default(this._element).one(d.TRANSITION_END,e).emulateTransitionEnd(n)):e())},n.hide=function(){var e;this._element.classList.contains("show")&&(e=u.default.Event("hide.bs.toast"),u.default(this._element).trigger(e),e.isDefaultPrevented()||this._close())},n.dispose=function(){this._clearTimeout(),this._element.classList.contains("show")&&this._element.classList.remove("show"),u.default(this._element).off("click.dismiss.bs.toast"),u.default.removeData(this._element,"bs.toast"),this._element=null,this._config=null},n._getConfig=function(e){return e=s({},we,u.default(this._element).data(),"object"==_typeof(e)&&e?e:{}),d.typeCheckConfig("toast",e,this.constructor.DefaultType),e},n._setListeners=function(){var e=this;u.default(this._element).on("click.dismiss.bs.toast",'[data-dismiss="toast"]',function(){return e.hide()})},n._close=function(){function e(){n._element.classList.add("hide"),u.default(n._element).trigger("hidden.bs.toast")}var t,n=this;this._element.classList.remove("show"),this._config.animation?(t=d.getTransitionDurationFromElement(this._element),u.default(this._element).one(d.TRANSITION_END,e).emulateTransitionEnd(t)):e()},n._clearTimeout=function(){clearTimeout(this._timeout),this._timeout=null},xe._jQueryInterface=function(n){return this.each(function(){var e=u.default(this),t=e.data("bs.toast");if(t||(t=new xe(this,"object"==_typeof(n)&&n),e.data("bs.toast",t)),"string"==typeof n){if(void 0===t[n])throw new TypeError('No method named "'+n+'"');t[n](this)}})},a(xe,null,[{key:"VERSION",get:function(){return"4.5.3"}},{key:"DefaultType",get:function(){return be}},{key:"Default",get:function(){return we}}]),xe);function xe(e,t){this._element=e,this._config=this._getConfig(t),this._timeout=null,this._setListeners()}u.default.fn.toast=_e._jQueryInterface,u.default.fn.toast.Constructor=_e,u.default.fn.toast.noConflict=function(){return u.default.fn.toast=ye,_e._jQueryInterface},e.Alert=f,e.Button=g,e.Carousel=x,e.Collapse=A,e.Dropdown=O,e.Modal=U,e.Popover=le,e.Scrollspy=pe,e.Tab=me,e.Toast=_e,e.Tooltip=ee,e.Util=d,Object.defineProperty(e,"__esModule",{value:!0})});var browser,isChrome=!navigator.userAgent.includes("Edg")&&!navigator.userAgent.includes("OPR")&&(navigator.userAgent.includes("Chrome")||navigator.userAgent.includes("Crios")),chromeVersion=isChrome&&Number((navigator.userAgent.match(/(?:Chrome|Crios)\/(\d+)/)||[])[1]||0)||0;navigator.userAgent.includes("Chrome")&&("function"==typeof Window?window.browser=chrome:browser=chrome);var extension_source="chrome",enable_yt=!1,enable_check_update=!1,freeLimit={hqDownload:{maxCount:1,hours:1},streamDownload:{maxCount:1,hours:1},videoRecord:{maxCount:1,hours:1},audioRecord:{maxCount:1,hours:1},tabRecord:{maxCount:1,hours:1},screenRecord:{maxCount:1,hours:1}},configuration={BRAND:"VidHelper",DOMAIN:"https://vidhelper.app",FAQ:"https://vidhelper.app#faqs",MEASUREMENT_ID:"G-LNBWCVYXVX",API_SECRET:"vzBAqqNRSb6ugOR-mqf6CA",LICENSE_BASEURL:"https://vidhelper.app/api",CHECK_FOR_UPDATE:enable_check_update,UPDATE_INTERVAL:36e5,CHECK_UPDATE_URL:"https://app.vidhelper.app/latest",HOWTO_INSTAL_URL:"https://vidhelper.app/how-to-manually-install-vidhelper-extension",STORE_ID:19749,PRODUCT_IDS:[170497,170523,170502,170524,170494,170522],PRODUCT:{name:browser.i18n.getMessage("Pro"),features:[{name:browser.i18n.getMessage("UnlimitMediaDL"),free:browser.i18n.getMessage("Yes"),paid:browser.i18n.getMessage("Yes")},{name:browser.i18n.getMessage("sitesSupported"),free:browser.i18n.getMessage("Yes"),paid:browser.i18n.getMessage("Yes")},{name:browser.i18n.getMessage("1080pSupport"),free:browser.i18n.getMessage("NumEveryHours").replace("{{1}}",freeLimit.hqDownload.maxCount).replace("{{2}}",freeLimit.hqDownload.hours),paid:browser.i18n.getMessage("Unlimited")},{name:browser.i18n.getMessage("NumStreamLimit"),free:browser.i18n.getMessage("NumEveryHours").replace("{{1}}",freeLimit.streamDownload.maxCount).replace("{{2}}",freeLimit.streamDownload.hours),paid:browser.i18n.getMessage("Unlimited")},{name:browser.i18n.getMessage("EachRecordLimit"),free:browser.i18n.getMessage("NumEveryHours").replace("{{1}}",freeLimit.videoRecord.maxCount).replace("{{2}}",freeLimit.videoRecord.hours),paid:browser.i18n.getMessage("Unlimited")},{name:browser.i18n.getMessage("hlsDashMerge"),free:browser.i18n.getMessage("No"),paid:browser.i18n.getMessage("Yes")}],discount:browser.i18n.getMessage("discountInfo"),plans:[{name:browser.i18n.getMessage("Monthly"),checkoutUrl:"https://vidhelper.app/checkout?plan=monthly",price:"$5.9",priceMonthly:"$5.9",origPriceMonthly:"$7.9",description:browser.i18n.getMessage("MonthlyDes")},{name:browser.i18n.getMessage("Yearly"),checkoutUrl:"https://vidhelper.app/checkout?plan=yearly",price:"$29",priceMonthly:"$2.4",origPriceMonthly:"$3.2",description:browser.i18n.getMessage("YearlyDes").replace("{{1}}","$29").replace("{{2}}","$30"),mostPopular:!0},{name:browser.i18n.getMessage("Lifetime"),checkoutUrl:"https://vidhelper.app/checkout?plan=lifetime",price:"$55",origPrice:"$75",description:browser.i18n.getMessage("LifetimeDes")}]},sentryDSN:"https://<EMAIL>/4508324004036608",FREE_LIMIT:freeLimit,GENERAL:{showMergeInfo:!1,showVideoElRecordTip:!0,showTabRecordTip:!1,showDownloadStreamTip:!0,recordCountDown:3,useDedicatedDownloader:!1,dedicatedDownloader:"downloadingPage",defaultDownloader:"downloadingFrame",enableRegionSelector:!0,enableScreenRecord:!0,tabRecordType:"recordingPage"},MEDIA_FILTER:{VIDEO:{enable:!0,size:1048576,range:[0,104857600],step:1048576,candisable:!1},AUDIO:{enable:!0,size:307200,range:[0,20971520],step:1024,candisable:!1}},CSP_LIST:["www.facebook.com","www.instagram.com","twitter.com","x.com","youtube.com","vimeo.com","www.tokopedia.com"],SHAKA_PLAYER_LIST:["vk.com","ok.ru"],SKIP_BG_DOWNLOAD:["www.tiktok.com","streamplay.pw"],ENABLE_POPUP_QUERY:!1,INFOBOX_UP:!1,CONTACT_US:"mailto:<EMAIL>",RATE_US:"https://chrome.google.com/webstore/detail/".concat(browser.runtime.id,"/reviews"),DROPBOX_KEY:"6awh8g09fftibys",ENABLE_CONTEXTMENU:!1,CONTEXTMENU_TITLE:"Search Videos for",ENABLE_INSTALL_URL:!1,INSTAL_SUFFIX:"/install",ENABLE_UNINSTALL_URL:!1,UNINSTAL_SUFFIX:"/uninstall",ENABLE_UPDATE_URL:!1,UPDATE_URL:"/uninstall",DISALLOW_YOUTUBE:!enable_yt,DISALLOW_DOMAINS_REGEXP:enable_yt?null:/^(?:(?:https?:)?\/\/)?(?:[^/]*.)?youtube.com/,DISABLE_HEADRECEIVE_DOMAINS_REGEXP:/^(?:(?:https?:)?\/\/)?(?:[^/]*.)?(?:www.tiktok.com|www.instagram.com|www.facebook.com|vimeo.com|youtube.com|www.bilibili.com|www.bilibili.tv)/,DISABLE_IMAGE_HEADRECEIVE_DOMAINS_REGEXP:/^(?:(?:https?:)?\/\/)?(?:[^/]*.)?(?:vidhelper.app)/,INSTALL_DAYS:30,USE_COUNT:10,MAX_USE_STATISTICS:60,ENABLE_FINDERS:_objectSpread(_objectSpread({vimeo:"VMFinder",dailymotion:"DMFinder",facebook:"FBFinder",tiktok:"TTFinder",instagram:"IGFinder",soundcloud:"SCFinder"},enable_yt?{youtube:"YTFinder"}:{}),{},{bilibili:"BILIFinder",xx:"XXFinder"}),FINDER_MATCHES:["<all_urls>"],FINDER_EXCLUDE_MATCHES:["*://https://vidhelper.app/*"]},contentMap={m3u8:"video/mp4",m3u:"video/mp4",mp4:"video/mp4","3gp":"video/3gpp",flv:"video/x-flv",mov:"video/quicktime",avi:"video/x-msvideo",wmv:"video/x-ms-wmv",webm:"video/webm",ogg:"video/ogg",ogv:"video/ogg",f4v:"video/x-f4v",acc:"application/vnd.americandynamics.acc",mkv:"video/x-matroska",rmvb:"application/vnd.rn-realmedia-vbr",m4s:"video/iso.segment"},streamExts=["m3u8","m3u","mpd"],audioExts=["aac","mid","mp3","oga","wav","wma","flac","acc"],videoExts=["webm","3gp","3g2","rmvb","mp4","3gp2","flv","mov","avi","wmv","ogg","ogv","f4v","mkv"],imageExts=["bmp","gif","avif","ico","png","apng","svg","tif","tiff","webp","jpeg","jpg"],imageFormats={"image/bmp":["bmp"],"image/gif":["gif"],"image/avif":["avif"],"image/x-icon":["ico"],"image/vnd.microsoft.icon":["ico"],"image/png":["png"],"image/apng":["apng"],"image/svg+xml":["svg"],"image/tiff":["tif","tiff"],"image/webp":["webp"],"image/jpeg":["jpeg","jpg"],"image/jpg":["jpeg","jpg"]},imageMimeTypes=Object.keys(imageFormats),audioFormats={"audio/mp3":["mp3"],"audio/mp4":["m4a"],"audio/aac":["aac"],"audio/midi":["mid"],"audio/x-midi":["mid"],"audio/mpeg":["mp3"],"audio/ogg":["oga"],"audio/wav":["wav"],"audio/x-wav":["wav"],"audio/vnd.wave":["wav"],"audio/wave":["wav"],"audio/x-pn-wav":["wav"],"audio/webm":["webm"],"audio/3gpp":["3gp"],"audio/3gpp2":["3g2"],"audio/x-ms-wma":["wma"],"audio/flac":["flac"]},audioMimeTypes=Object.keys(audioFormats),videoFormats={"application/vnd.americandynamics.acc":["acc"],"application/vnd.rn-realmedia-vbr":["rmvb"],"video/mp4":["mp4","m4s","m4v"],"video/3gpp":["3gp"],"video/3gpp2":["3gp2"],"video/x-flv":["flv"],"video/x-m4v":["m4v"],"video/quicktime":["mov"],"video/x-msvideo":["avi"],"video/x-ms-wmv":["wmv"],"video/webm":["webm"],"video/ogg":["ogg","ogv"],"application/ogg":["ogg"],"video/x-f4v":["f4v"],"video/x-matroska":["mkv"],"video/iso.segment":["m4s"],"application/mp4":["mp4"]},videoMimeTypes=Object.keys(videoFormats),streamFormats={"audio/mpegurl":["m3u8","m3u"],"audio/x-mpegurl":["m3u8","m3u"],"application/vnd.apple.mpegurl":["m3u8","m3u"],"application/x-mpegurl":["m3u8","m3u"],"application/dash+xml":["mpd"],"application/octet-stream":["m3u8","m3u","mpd","mp4","webm","avi","ogg","flv","mkv","3gp","ts","m4v","mp3","wav"],"binary/octet-stream":["m3u8","m3u","mpd","mp4","webm","avi","ogg","flv","mkv","3gp","ts","m4v","mp3","wav"]},streamMimeTypes=Object.keys(streamFormats),mimeTypeFormats={audio:audioFormats,video:videoFormats,stream:streamFormats},allowedExtensions=[audioExts,videoExts,streamExts].flat(),typeExts=Object.entries(mimeTypeFormats).flatMap(function(e){var t=_slicedToArray(e,2),e=t[0],t=t[1];if("stream"!==e)return _defineProperty({},e,Object.values(t).flatMap(function(e){return e}))}).reduce(function(e,t){return Object.assign(e,t)},{}),mimeTypes=Object.entries(mimeTypeFormats).flatMap(function(e){e=_slicedToArray(e,2),e[0],e=e[1];return Object.keys(e)});function getTypeFromExt(e){if("string"!=typeof e)return null;for(var t=0,n=Object.keys(mimeTypeFormats);t<n.length;t++){var r=n[t],i=Object.values(mimeTypeFormats[r]).flat();if("stream"!=r&&-1!=i.indexOf(e))return r}return null}function getFileNameFromURL(e){var t=e.match(/\/?([^/?#]+)(?:$|\?|#)/);return(t=t||e.match(/\/([^/]+)\/?$/))?t[1]:"unknown"}function getFileNameFromURLPathName(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:64,n=2<arguments.length&&void 0!==arguments[2]&&arguments[2];try{var r,i=new URL(t).pathname;n&&(r=i.match(_wrapRegExp(/(.+?)(?:\.(\w{2,4}))?$/,{name:1,ext:2})),r&&(i=r.groups.name),r.groups.ext);var i=i.split("").reverse().join(""),o=_toConsumableArray(i.matchAll(/\//g)).map(function(e){return e.index}),a=e;if(0<o.length)for(var s=o.length-1;0<=s;s--)if(e>=o[s]){a=o[s];break}return i.slice(0,a).split("").map(function(e){return"/"===e?"-":e}).join("").split("").reverse().join("")}catch(e){return getFileNameFromURL(t)}}function getTitleExtFromFileName(e){var t=_slicedToArray(e.split(/\.(\w{2,4})$/),2),e=t[0],t=t[1],t=void 0===t?null:t;return{title:e,ext:(null==t?void 0:t.toLowerCase())||null}}function getTitleExtFromUrl(e){var t=1<arguments.length&&void 0!==arguments[1]&&arguments[1],n={title:"unknow",ext:null};return"string"!=typeof e||(n=getTitleExtFromFileName(getFileNameFromURL(e))).ext&&t&&allowedExtensions.indexOf(n.ext)<0&&(n.ext=null),n}function getTypeFromExt(e){return videoExts.includes(e)?"video":audioExts.includes(e)?"audio":streamExts.includes(e)?"stream":null}function getTypeFormatFromExtMime(e,t){var n={type:null,ext:null};if("string"!=typeof(t=t&&t.toLowerCase().split(";")[0]))return n;for(var r=0,i=Object.keys(mimeTypeFormats);r<i.length;r++){var o=i[r],a=mimeTypeFormats[o][t];if("stream"!=o&&a){var s=a.indexOf(e);return n.type=o,n.ext=s<0?a[0]:a[s],n}}if(mimeTypeFormats.stream[t]){var l=mimeTypeFormats.stream[t].indexOf(e);if(n.type="stream",0<=t.indexOf("octet-stream")){n.ext=l<0?null:mimeTypeFormats.stream[t][l];for(var c=0,u=Object.entries(typeExts);c<u.length;c++){var d=_slicedToArray(u[c],2),f=d[0];if(d[1].includes(n.ext)){n.type=f;break}}}else n.ext=l<0?mimeTypeFormats.stream[t][0]:mimeTypeFormats.stream[t][l];return n}return n}function areObjectsEqual(e,t){var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(var i=0,o=n;i<o.length;i++){var a=o[i];if(null==t||!t.key||e[a]!==t[a])return!1}return!0}function p(e){return _p.apply(this,arguments)}function _p(){return(_p=_asyncToGenerator(_regeneratorRuntime().mark(function e(r){var t,i,n,o=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:for(t=o.length,i=new Array(1<t?t-1:0),n=1;n<t;n++)i[n-1]=o[n];return e.abrupt("return",new Promise(function(t,n){r.apply(void 0,i.concat([function(e){browser.runtime.lastError?n(browser.runtime.lastError):t(e)}]))}));case 2:case"end":return e.stop()}},e)}))).apply(this,arguments)}function isDevelop(){return browser.runtime.getManifest().name.startsWith("Develop")}function parse_match_pattern(e){if("string"!=typeof e)return null;function t(e){return e.replace(/[[^$.|?*+(){}\\]/g,"\\$&")}var n="(?:^",r=/^(\*|https?|file|ftp|chrome-extension):\/\//.exec(e);if(!r)return null;if(e=e.substr(r[0].length),n+="*"===r[1]?"https?://":r[1]+"://","file"!==r[1]){if(!(r=/^(?:\*|(\*\.)?([^\/*]+))(?=\/)/.exec(e)))return null;e=e.substr(r[0].length),"*"===r[0]?n+="[^/]+":(r[1]&&(n+="(?:[^/]+\\.)?"),n+=t(r[2]))}return n+=e.split("*").map(t).join(".*"),n+="$)"}function formatBytes(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:2,n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:1024;if("string"==typeof e&&(e=parseInt(e)),0===e)return"0 Bytes";var r=t<0?0:t,t=Math.floor(Math.log(e)/Math.log(n));return parseFloat((e/Math.pow(n,t)).toFixed(r))+" "+["Bytes","KB","MB","GB","TB","PB","EB","ZB","YB"][t]}function is1080pOrHigher(e){if(null!=e&&e.width&&null!=e&&e.height){if(1920<=e.width&&1080<=e.height||1080<=e.width&&1920<=e.height)return!0}else if(e.resolution)try{var t=e.resolution.split("x"),n=parseInt(t[0],10),t=parseInt(t[1],10);if(1920<=n&&1080<=t||1080<=n&&1920<=t)return!0}catch(e){}else if(e.quality)try{if(1080<=parseInt(e.quality.split("p")[0]))return!0}catch(e){}return!1}function getFileNameFromcontentDisposition(e){e=e.match(/filename=["']?([^'"\s;]+)["']?/i);return e?e[1]:null}function getRangeInfo(e){var t=e.split(" ");if(2===t.length){e=t[1].split("/");if(2===e.length){t=parseInt(e[1]);if(t)return{chunk:e[0],total:t}}}return null}function getSizeFromReceivedHeader(e){var t=e.get("content-length",null),e=e.get("content-range",null);if(t)return parseInt(t);if(e){e=getRangeInfo(e);if(e)return e.total}return null}function jsonHeadersToResponseHeaders(e){var n=_objectSpread({},e);return{get:function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:null,t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:null;if(n){if(e){e=e.toLowerCase();return n[e]||t}return n}return n}}}function responseHeadersToJson(e){var t={},n=_createForOfIteratorHelper(e.entries());try{for(n.s();!(i=n.n()).done;){var r=_slicedToArray(i.value,2),i=r[0],r=r[1];t[i]=r}}catch(e){n.e(e)}finally{n.f()}return t}function needPicker(e){if(1610612736<(null==e?void 0:e.size))return!0;if(null!=e&&e.duration){var t,n,r=0;if(null!=e&&e.bandwidth?r=e.bandwidth:e.width&&e.height?(n=(null==e?void 0:e.frameRate)||30,r=e.width*e.height*n*.1):e.resolution&&(t=e.resolution.split("x"),n=(null==e?void 0:e.frameRate)||30,r=t[0]*t[1]*n*.1),1610612736<e.duration*r/8)return!0}return!1}function hms(e){var t,n=e<3600?(t=14,5):(t=11,8);return new Date(1e3*e).toISOString().substr(t,n)}function getQualityFromVideoLink(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"";return e.width&&e.height&&(e.resolution="".concat(e.width,"x").concat(e.height)),e.bandwidth&&e.resolution?t="".concat(Math.floor(e.bandwidth/1e3)," kbps (").concat(e.resolution,")"):e.resolution?t=e.resolution:e.bandwidth&&e.quality?t="".concat(Math.floor(e.bandwidth/1e3)," kbps (").concat(e.quality,")"):e.quality?t=e.quality:e.bandwidth?t="".concat(Math.floor(e.bandwidth/1e3)," kbps"):e.size?t=formatBytes(e.size):e.duration?t=e.duration:e.name&&(t=e.name),t}function formatResolution(e,t){for(var n=0,r=Object.entries({"144p":[256,144],"240p":[426,240],"360p":[640,360],"480p":[854,480],"720p":[1280,720],"1080p":[1920,1080],"2K":[2048,1080],QHD:[2560,1440],"4K":[3840,2160],"DCI 4K":[4096,2160],"8K":[7680,4320]});n<r.length;n++){var i=_slicedToArray(r[n],2),o=i[0],a=_slicedToArray(i[1],2),i=a[0],a=a[1];if(e===i&&t===a)return o}return"".concat(e,"x").concat(t)}function getQualityFromVideoLink_new(e){var t=[];e.width&&e.height&&t.push("".concat(formatResolution(e.width,e.height))),e.frameRate&&t.push("".concat(e.frameRate," fps")),e.bandwidth?t.push("".concat(Math.floor(e.bandwidth/1e3)," kbps")):e.bitrate&&t.push("".concat(e.bitrate," kbps"));try{e.duration&&t.push(hms(e.duration))}catch(e){}return e.size&&t.push(formatBytes(e.size)),"audio"==e.type&&e.name&&t.push(e.name),t.join(" | ")}function checkAttr(e,t,n){for(var r=0,i=e.length;r<i;r++)if(e[r][t]==n)return!0;return!1}function checkScript(e,t){return checkAttr(document.scripts,e,t)}function inIframe(){try{return window.self!==window.top}catch(e){return!0}}function delay(t){return new Promise(function(e){return setTimeout(e,t)})}function waitHeaderLoaded(){return _waitHeaderLoaded.apply(this,arguments)}function _waitHeaderLoaded(){return(_waitHeaderLoaded=_asyncToGenerator(_regeneratorRuntime().mark(function e(){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",new Promise(function(e){function t(){return document.head||document.documentElement}var n;t()?e():(n=new MutationObserver(function(){t()&&(n.disconnect(),e())})).observe(document,{childList:!0,subtree:!0})}));case 1:case"end":return e.stop()}},e)}))).apply(this,arguments)}function waitBodyLoaded(){return _waitBodyLoaded.apply(this,arguments)}function _waitBodyLoaded(){return(_waitBodyLoaded=_asyncToGenerator(_regeneratorRuntime().mark(function e(){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",new Promise(function(e){function t(){return document.body}var n;t()?e():(n=new MutationObserver(function(){t()&&(n.disconnect(),e())})).observe(document,{childList:!0,subtree:!0})}));case 1:case"end":return e.stop()}},e)}))).apply(this,arguments)}function waitLoaded(){return _waitLoaded.apply(this,arguments)}function _waitLoaded(){return(_waitLoaded=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var n,r,t=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(n=0<t.length&&void 0!==t[0]?t[0]:null,"loading"!==document.readyState)return e.abrupt("return");e.next=5;break;case 5:return r=null,e.abrupt("return",new Promise(function(e,t){"number"==typeof n&&(r=setTimeout(function(){t(new Error("Timeout exceeded"))},n)),document.addEventListener("DOMContentLoaded",function(){null!=r&&(clearTimeout(r),r=null),e()})}));case 7:case"end":return e.stop()}},e)}))).apply(this,arguments)}function loadScript(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:null,n=!(2<arguments.length&&void 0!==arguments[2])||arguments[2]?document.head||document.documentElement:document.body,r=document.createElement("script"),i=setTimeout(function(){t(r)},1e3);if(e.src?r.src=e.src:e.textContent?r.textContent=e.textContent:e.innerHTML&&(r.innerHTML=e.innerHTML),e.attrs)for(var o=0,a=Object.entries(e.attrs);o<a.length;o++){var s=_slicedToArray(a[o],2),l=s[0],s=s[1],s=void 0===s||s;r.setAttribute(l,s)}t&&(r.onload=function(){null!=i&&(clearTimeout(i),i=null),t(r)}),n.appendChild(r)}function loadScriptPromise(n){var r=1<arguments.length&&void 0!==arguments[1]&&arguments[1];return new Promise(function(t,e){loadScript(n,function(e){t(e)},r)})}function checkLoadScript(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"src",n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;"innerHTML"==t?checkScript(t,e[t])||loadScript(e,n):e.attrs&&e.attrs[t]&&!checkScript(t,e.attrs[t])&&loadScript(e,n)}function loadLink(e){var t=document.head,n=document.createElement("link");if(n.rel="stylesheet",n.href=e.href,e.attrs)for(var r=0,i=Object.entries(e.attrs);r<i.length;r++){var o=_slicedToArray(i[r],2),a=o[0],o=o[1],o=void 0===o||o;n.setAttribute(a,o)}t.appendChild(n)}function checkLink(e,t){return checkAttr(document.styleSheets,e,t)}function checkLoadLink(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"href";e.attrs&&e.attrs[t]&&!checkLink(t,e.attrs[t])&&loadLink(e)}function sendMessage(e){return _sendMessage.apply(this,arguments)}function _sendMessage(){return(_sendMessage=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",p(browser.runtime.sendMessage,t).catch(function(e){return null}));case 1:case"end":return e.stop()}},e)}))).apply(this,arguments)}function XMLRequest(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:null;return null==t&&(t={method:"GET"}),t.cache=t.cache||"default",fetch(e,t).then(function(e){return e})}function syncStorageGet(e){return _syncStorageGet.apply(this,arguments)}function _syncStorageGet(){return(_syncStorageGet=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,p(browser.storage.sync.get.bind(browser.storage.sync),t).catch(function(e){});case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e)}))).apply(this,arguments)}function syncStorageSet(e){return _syncStorageSet.apply(this,arguments)}function _syncStorageSet(){return(_syncStorageSet=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,p(browser.storage.sync.set.bind(browser.storage.sync),t).catch(function(e){});case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e)}))).apply(this,arguments)}function syncStorageRemove(e){return _syncStorageRemove.apply(this,arguments)}function _syncStorageRemove(){return(_syncStorageRemove=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,p(browser.storage.sync.remove.bind(browser.storage.sync),t).catch(function(e){});case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e)}))).apply(this,arguments)}function localStorageGet(e){return _localStorageGet.apply(this,arguments)}function _localStorageGet(){return(_localStorageGet=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,p(browser.storage.local.get.bind(browser.storage.local),t).catch(function(e){});case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e)}))).apply(this,arguments)}function localStorageSet(e){return _localStorageSet.apply(this,arguments)}function _localStorageSet(){return(_localStorageSet=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,p(browser.storage.local.set.bind(browser.storage.local),t).catch(function(e){});case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e)}))).apply(this,arguments)}function localStorageRemove(e){return _localStorageRemove.apply(this,arguments)}function _localStorageRemove(){return(_localStorageRemove=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,p(browser.storage.local.remove.bind(browser.storage.local),t).catch(function(e){});case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e)}))).apply(this,arguments)}function isDocumentReady(){return!(!document.head&&!document.documentElement)}function toBlobURL(e,t){return _toBlobURL.apply(this,arguments)}function _toBlobURL(){return(_toBlobURL=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,n){var r,i=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return r=2<i.length&&void 0!==i[2]?i[2]:void 0,e.next=3,fetch(t,r);case 3:return e.next=5,e.sent.arrayBuffer();case 5:return r=e.sent,r=new Blob([r],{type:n}),e.abrupt("return",URL.createObjectURL(r));case 8:case"end":return e.stop()}},e)}))).apply(this,arguments)}function mediaIsMP4(e){return 0===e[0]&&0===e[1]&&0===e[2]&&(24===e[3]||32===e[3])&&102===e[4]&&116===e[5]&&121===e[6]&&112===e[7]}function mediaIsTs(e){if(71!==e[0])return!1;for(var t=188;t<Math.min(e.length,940);t+=188)if(71!==e[t])return!1;return!0}function determineStreamType(e){if(!e||"string"!=typeof e)return null;var t=Math.min(e.length,1e3),t=e.slice(0,t);return t.includes("#EXTM3U")?"hls":/\s*<MPD\s/.test(t)?"dash":null}function getResponseInfoSafe(e){return _getResponseInfoSafe.apply(this,arguments)}function _getResponseInfoSafe(){return(_getResponseInfoSafe=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var n,r=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return n=1<r.length&&void 0!==r[1]?r[1]:{},e.abrupt("return",getResponseInfo(t,n).catch(function(e){return null}));case 2:case"end":return e.stop()}},e)}))).apply(this,arguments)}function getResponseInfo(e){return _getResponseInfo.apply(this,arguments)}function _getResponseInfo(){return(_getResponseInfo=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var n,r,i,o,a,s=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return r=(a=1<s.length&&void 0!==s[1]?s[1]:{}).init,n=void 0===r?null:r,r=a.redirect,r=void 0===r?"follow":r,a=a.abortData,i=void 0===a||a,o=new AbortController,a=o.signal,n=null!=n?n:{headers:{accept:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9"},credentials:"include",redirect:r,method:"GET"},e.abrupt("return",XMLRequest(t,_objectSpread(_objectSpread({},n),{},{signal:a})).then(function(e){return i&&o.abort(),e}));case 6:case"end":return e.stop()}},e)}))).apply(this,arguments)}function setHeaders(e,t){return _setHeaders.apply(this,arguments)}function _setHeaders(){return(_setHeaders=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,n){var r,i,o,a,s;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:for(r=[],i=[],o=Date.now()%Math.pow(2,31),a=0;a<t.length;a++)s=a+o,t[a]&&t[a].startsWith("http")&&(r.push({id:s,priority:1,action:{type:"modifyHeaders",requestHeaders:n.map(function(e){return{operation:e.operation,header:e.header,value:e.value}})},condition:{urlFilter:"||".concat(new URL(t[a]).hostname)}}),i.push(s));return e.next=6,browser.declarativeNetRequest.updateSessionRules({addRules:r,removeRuleIds:i});case 6:return e.abrupt("return",i);case 7:case"end":return e.stop()}},e)}))).apply(this,arguments)}function removeRulesForTabs(e){return _removeRulesForTabs.apply(this,arguments)}function _removeRulesForTabs(){return(_removeRulesForTabs=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var n;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,chrome.declarativeNetRequest.getSessionRules();case 2:if(n=e.sent,0<(n=n.filter(function(e){return null===(e=e.condition)||void 0===e||null===(e=e.tabIds)||void 0===e?void 0:e.some(function(e){return t.includes(e)})}).map(function(e){return e.id})).length)return e.next=7,chrome.declarativeNetRequest.updateSessionRules({removeRuleIds:n});e.next=7;break;case 7:case"end":return e.stop()}},e)}))).apply(this,arguments)}function updateSessionRules(e,t){return _updateSessionRules.apply(this,arguments)}function _updateSessionRules(){return(_updateSessionRules=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,n){var r,i,o,a,s,l,c,u,d,f=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:for(r=(o=2<f.length&&void 0!==f[2]?f[2]:{}).tabIds,i=void 0===r?null:r,o=o.attrs,a=void 0===o?[]:o,n&&a.push({header:"Referer",value:n}),s=[],l=[],c=Date.now()%Math.pow(2,31),u=0;u<t.length;u++)d=u+c,t[u]&&t[u].startsWith("http")&&(s.push({id:d,priority:1,action:{type:"modifyHeaders",requestHeaders:a.map(function(e){return{operation:(null==e?void 0:e.operation)||"set",header:e.header,value:e.value}})},condition:{urlFilter:"||".concat(new URL(t[u]).hostname),tabIds:i||[browser.tabs.TAB_ID_NONE]}}),l.push(d));return e.prev=7,e.next=10,browser.declarativeNetRequest.updateSessionRules({addRules:s,removeRuleIds:l});case 10:e.next=19;break;case 12:if(e.prev=12,e.t0=e.catch(7),i)return e.next=17,removeRulesForTabs(i);e.next=17;break;case 17:return e.next=19,browser.declarativeNetRequest.updateSessionRules({addRules:s,removeRuleIds:l});case 19:return e.abrupt("return",l);case 20:case"end":return e.stop()}},e,null,[[7,12]])}))).apply(this,arguments)}function filterHeaders(){var e,t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},n=["x-client-data","referer","user-agent","origin","cache-control","pragma","accept-encoding","accept-language","sec-ch-ua","sec-ch-ua-mobile","sec-ch-ua-platform","sec-fetch-dest","sec-fetch-mode","sec-fetch-site"],r={};for(e in t)n.includes(e)||(r[e]=t[e]);return r}function playVideoLink(p,e){var o=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null,t=3<arguments.length&&void 0!==arguments[3]?arguments[3]:["Html5","Shaka"],n=(null==p?void 0:p.download_url)||(null==p?void 0:p.url),r="video/mp4",r=n&&"video"!==p.type||null==p||!p.manifestUrl?(0<n.indexOf(".webm")?r="video/webm":0<n.indexOf(".flv")?r="video/x-flv":0<n.indexOf(".mpd")?r="application/dash+xml":0<n.indexOf(".m3u8")&&(r="application/x-mpegURL"),{type:r,src:updatePandavideoToken(n)}):("hls"===p.streamType?r="application/x-mpegURL":"dash"===p.streamType&&(r="application/dash+xml"),{type:r,src:updatePandavideoToken(p.manifestUrl)});videojs.log.level="debug";var h=videojs(e,{controlBar:{fullscreenToggle:!1},techOrder:t,playbackRates:[.5,1,1.25,1.5,2,4]});return h.ready(function(){h.controlBar.subsCapsButton.hide()}),h.src(r),h.on("loadedmetadata",function(){if("audio"==p.type&&h.audioOnlyMode(!0),"Shaka"==h.techName_){var e,t=h.tech(!0).shaka_;t.configure({abr:{enabled:!1}});var n,r="audio"==p.type?p.language:(null==p||null===(e=p.audioLink)||void 0===e?void 0:e.language)||null;!r||(n=t.getAudioLanguagesAndRoles().filter(function(e){return e.language==r})).length&&t.selectAudioLanguage(n[0].language,n[0].role),"video"!=p.type||(n=t.getVariantTracks().filter(function(e){return!("variant"!==e.type||(null==p?void 0:p.width)!=(null==e?void 0:e.width)||(null==p?void 0:p.height)!=(null==e?void 0:e.height)||null!=p&&p.bandwidth&&(null==p?void 0:p.bandwidth)!=((null==e?void 0:e.videoBandwidth)||(null==e?void 0:e.bandwidth)))})).length&&t.selectVariantTrack(n[0],!0)}else{var i,o="audio"==p.type?p.name:(null==p||null===(i=p.audioLink)||void 0===i?void 0:i.name)||null;if(!o||(i=Array.from(h.audioTracks()).find(function(e){return e.label===o}))&&(i.enabled=!0),"video"==p.type){var a=h.qualityLevels().levels_;if(1<a.length&&(p.width&&p.height||p.bandwidth)&&1<a.length&&p){for(var s=p.width&&p.height?p.width*p.height:null,l=p.bandwidth||null,c=!1,u=0;u<a.length;u++){var d=a[u],f=s&&d.width*d.height===s||l&&d.bitrate===l;d.enabled=f,c=c||f}c||(a[0].enabled=!0)}}}}),h.on("xhr-hooks-ready",function(){isPrivacyCombrUrl(h.currentSrc())&&h.tech(!0).vhs.xhr.onRequest(function(e){return e.beforeSend=function(t){var e,n=updatePrivacyCombrHeaders(t.url,filterHeaders((null==p||null===(e=p.init)||void 0===e?void 0:e.headers)||{}));Object.keys(n).forEach(function(e){t.setRequestHeader(e,n[e])})},e});var i=!1;h.tech(!0).vhs.xhr.onResponse(function(e,t,n){var r;403==n.statusCode&&0==i&&isPandavideoTokenUrl(h.currentSrc())&&(i=!0,r=(null==p||null===(r=p.init)||void 0===r||null===(r=r.headers)||void 0===r?void 0:r.referer)||o||playUrl,updateSessionRules([e.url],r))})}),h.on(["loadstart","play","playing","firstplay","pause","ended","adplay","adplaying","adfirstplay","adpause","adended","contentplay","contentplaying","contentfirstplay","contentpause","contentended","contentupdate","loadeddata","loadedmetadata"],function(e){}),h}function getDataFromBg(e){return _getDataFromBg.apply(this,arguments)}function _getDataFromBg(){return(_getDataFromBg=_asyncToGenerator(_regeneratorRuntime().mark(function e(l){var c,u,t=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return c=1<t.length&&void 0!==t[1]?t[1]:{},u=2<t.length&&void 0!==t[2]?t[2]:location.href,e.abrupt("return",new Promise(function(n,r){function i(){e&&(clearTimeout(e),e=null)}function o(e){if(e.message==="".concat(l,"-chunk")&&e.uniqueEventName===a&&(i(),s+=e.data,e.done))try{chrome.runtime.onMessage.removeListener(o);var t=JSON.parse(s);n(t)}catch(e){r(new Error("Error parsing tab data: ".concat(e.message,", ").concat(u,", ").concat(l,", ").concat(JSON.stringify(c))))}}var a="".concat(l,"-").concat(Date.now(),"-").concat(Math.random()),s="",e=null;chrome.runtime.onMessage.addListener(o),sendMessage(_objectSpread({message:l,uniqueEventName:a},c)).then(function(e){i(),e.data&&(chrome.runtime.onMessage.removeListener(o),n(e.data))}).catch(function(e){r(e)}),e=setTimeout(function(){chrome.runtime.onMessage.removeListener(o),r(new Error("".concat(l," timed out")))},1e4)}));case 3:case"end":return e.stop()}},e)}))).apply(this,arguments)}function _linkClickDownload(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"",n=document.createElement("a");n.setAttribute("download",configuration.BRAND+"/"+t),n.setAttribute("target","_blank"),n.href=e,document.body.appendChild(n),n.click(),n.remove()}function _backgroundDownload2(e,t){return _backgroundDownload.apply(this,arguments)}function _backgroundDownload(){return(_backgroundDownload=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,n){var r,i,o,a=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(i={message:"download-video-link",url:t,fileName:n},"object"==_typeof(r=2<a.length&&void 0!==a[2]?a[2]:{}))for(o in r)i[o]=r[o];return e.next=5,sendMessage(i);case 5:if(e.t0=e.sent,e.t0){e.next=8;break}e.t0={};case 8:return e.abrupt("return",e.t0);case 9:case"end":return e.stop()}},e)}))).apply(this,arguments)}var AbstractDownloader=(_defineProperty(_class=function(){"use strict";function n(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:null,t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:null;_classCallCheck(this,n),this.stateListener=e,this.progressListener=t}var r,i,t,o;return _createClass(n,[{key:"handleProgress",value:function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{};this.progressListener&&e&&this.progressListener(e,t,n)}},{key:"handleStateChange",value:function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:"";this.stateListener&&e&&this.stateListener(e,t,n)}},{key:"xmlDownload",value:(o=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,n){var r,i,o,a,s,l,c,u,d,f,p,h,g=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return o=2<g.length&&void 0!==g[2]?g[2]:{},r=new AbortController,i=null,o=o.timeoutMillis,i=setTimeout(function(){r.abort()},void 0===o?3e4:o),this.handleStateChange(n,LinkDownloader.STATE.START),e.prev=6,e.next=9,XMLRequest(t.download_url,{signal:r.signal});case 9:if(a=e.sent,clearTimeout(i),a.ok){e.next=13;break}throw new Error("Network response was not ok");case 13:s=null,l=t.title+t.ext,null!==(f=a.headers.get("content-length"))&&(s=parseInt(f,10)),c=a.body.getReader(),u=0,d=[];case 20:return e.next=23,c.read();case 23:if(p=e.sent,f=p.done,p=p.value,f)return h=URL.createObjectURL(new Blob(d)),this.linkClickDownload(h,l),URL.revokeObjectURL(h),this.handleProgress(n,1),this.handleStateChange(n,LinkDownloader.STATE.COMPLETE),e.abrupt("break",39);e.next=33;break;case 33:u+=p.length,d.push(p),null!==s&&(h=Math.floor(u/s*100),this.handleProgress(n,h)),e.next=20;break;case 39:e.next=47;break;case 41:e.prev=41,e.t0=e.catch(6),clearTimeout(i),r.abort(),this.handleStateChange(n,LinkDownloader.STATE.FAILED);case 47:case"end":return e.stop()}},e,this,[[6,41]])})),function(e,t){return o.apply(this,arguments)})},{key:"linkClickDownload",value:function(e){return _linkClickDownload(e,1<arguments.length&&void 0!==arguments[1]?arguments[1]:"")}},{key:"windowOpen",value:(t=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:window.open(t);case 1:case"end":return e.stop()}},e)})),function(e){return t.apply(this,arguments)})},{key:"backgroundDownload",value:(i=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,n){var r,i=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return r=2<i.length&&void 0!==i[2]?i[2]:{},e.abrupt("return",_backgroundDownload2(t,n,r));case 2:case"end":return e.stop()}},e)})),function(e,t){return i.apply(this,arguments)})},{key:"download",value:(r=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,n){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:throw new Error("You have to implement the method doSomething!");case 2:case"end":return e.stop()}},e)})),function(e,t){return r.apply(this,arguments)})}]),n}(),"STATE",{START:"start",DOWNLOADING:"downloading",PAUSED:"paused",INTERRUPTED:"interrupted",COMPLETE:"complete",FAILED:"failed"}),_class),LinkDownloader=function(){"use strict";_inherits(o,AbstractDownloader);var n,r=_createSuper(o);function o(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:null,t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:null;return _classCallCheck(this,o),(t=r.call(this,e,t)).listener=t.initListener(),t}return _createClass(o,[{key:"initListener",value:function(){function e(e,t,n){switch(e.message){case"download-changed":r.handleStateChange(e.key,e.state,(null==e?void 0:e.errmsg)||""),n();break;case"progress-changed":r.handleProgress(e.key,e.progress),n();break;case"complete":n()}}var r=this;return browser.runtime.onMessage.addListener(e),e}},{key:"dispose",value:function(){this.listener&&(browser.runtime.onMessage.removeListener(this.listener),this.listener=null),this.stateListener=null,this.progressListener=null}},{key:"download",value:(n=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,n){var r,i;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return i=t.title,new RegExp("."+t.ext+"$").test(t.title)||(i=t.title+"."+t.ext),this.handleStateChange(n,o.STATE.START),r=t.download_url,e.prev=5,e.next=8,this.backgroundDownload(r,i);case 8:"result"in(i=e.sent)&&0<i.result?this.handleStateChange(n,o.STATE.DOWNLOADING):this.handleStateChange(n,o.STATE.FAILED),e.next=15;break;case 12:e.prev=12,e.t0=e.catch(5),this.xmlDownload(t);case 15:case"end":return e.stop()}},e,this,[[5,12]])})),function(e,t){return n.apply(this,arguments)})}]),o}();function isPandavideoUrl(e){return/https?:\/\/(?:[^.]+\.)*(?:[^/]+)\.pandavideo\.com(?:\.[^/]+)?\/.*\.m3u8/.test(e)}function isPandavideoNoTokenUrl(e){return/https?:\/\/(?:[^.]+\.)*(?:[^/]+)\.pandavideo\.com(?:\.[^/]+)?\/.*\.m3u8(?:(?!.token=[a-zA-Z0-9]{64})[?&][^&]*)?$/.test(e)}function isPandavideoTokenUrl(e){return _wrapRegExp(/https?:\/\/(?:[^.]+\.)*(?:[^/]+)\.pandavideo\.com(?:\.[^/]+)?\/.*\.m3u8\?(?:[^&]+&)?token=([a-zA-Z0-9]{64})/,{token:1}).test(e)}function updatePandavideoToken(e){var t=_wrapRegExp(/https?:\/\/(?:[^.]+\.)*(?:[^/]+)\.pandavideo\.com(?:\.[^/]+)?\/.*\.m3u8\?(?:[^&]+&)?token=([a-zA-Z0-9]{64})/,{token:1}),t=e.match(t);return t&&(e=e.replace(t.groups.token,function(){for(var e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",t="",n=0;n<64;n++){var r=Math.floor(Math.random()*e.length);t+=e.charAt(r)}return t}())),e}function isPolyvNetUrl(e){return!(null==e||!e.match(/^https?:\/\/hls\.videocc\.net/))}function getPolyvNetManifestId(e){e=null==e?void 0:e.match(/^https?:\/\/hls\.videocc\.net\/.*?\/([^\/_]+)(_\d+\.key|(_\d+)?.m3u8)/);return e?e[1]:null}function getPolyvNetManifestToken(e){e=null==e?void 0:e.match(/^https?:\/\/hls\.videocc\.net\/.*?\/([^\/]+)_\d+\.key\?token=([^&]+)/);return e?{manifestId:e[1],token:e[2]}:null}function updatePolyvNetKeyToken(e,t){var n;try{if(null!=e&&null!==(n=e[0])&&void 0!==n&&null!==(n=n.segments)&&void 0!==n&&null!==(n=n[0])&&void 0!==n&&null!==(n=n.key)&&void 0!==n&&null!==(n=n.url)&&void 0!==n&&n.match(/^https?:\/\/hls\.videocc\.net\/.*?\/([^\/]+)_\d+\.key/)){var r=_createForOfIteratorHelper(e);try{for(r.s();!(i=r.n()).done;){var i=i.value,o=_createForOfIteratorHelper(null==i?void 0:i.segments);try{for(o.s();!(s=o.n()).done;){var a=s.value,s=new URL(a.key.url);s.searchParams.set("token",t),a.key.url=s.toString()}}catch(e){o.e(e)}finally{o.f()}}}catch(e){r.e(e)}finally{r.f()}}}catch(e){}return e}function isBoomstreamUrl(e){return!(null==e||!e.match(/^https?:\/\/(?:[^\.]+\.)*?boomstream\.com/))}function updateLoomstreamMediaPlaylist(e,t,n){var r=/(?:#EXT-X-MEDIA-READY: *(\w+))\r?\n?/.exec(e);if(!r||!t)return e;var i="bv17b7v24iedrvzoaihwvugef89ewy7834f35",o="001a5068005b176d560504";function a(e,t){for(var n=[];t.length<e.length;)t+=t;for(var r=0;r<e.length;r+=2){var i=e.substring(r,r+2),i=parseInt(i,16)^t.charCodeAt(r/2);n.push(String.fromCharCode(i))}return n.join("")}var s=a(r=r[1],a(o,i)),r=(r=s,new Uint8Array([].map.call(r,function(e){return e.charCodeAt(0)})).buffer),r="0x"+(r=r.slice(20,36),Array.prototype.map.call(new Uint8Array(r),function(e){return("00"+e.toString(16)).slice(-2)}).join("")),i=n+"/process/"+function(e,t){for(var n=[];t.length<e.length;)t+=t;for(var r=0;r<e.length;r++){var i=(e.charCodeAt(r)^t.charCodeAt(r)).toString(16);i.length<2&&(i="0"+i),n.push(i)}return n.join("")}(s.slice(0,20)+t,a(o,i));return(e=e.replace("[KEY]",i)).replace("[IV]",r)}function isPrivacyCombrUrl(e){return!(null==e||!e.match(/^https?:\/\/(?:[^\.]+\.)*?privacy.com\.br/))}function updatePrivacyCombrHeaders(e,t){if(t["x-content-uri"]&&isPrivacyCombrUrl(e))try{var n=new URL(e).pathname.split("/").filter(function(e){return e});0<n.length&&(t["x-content-uri"]=n[n.length-1])}catch(e){}return t}function middleTruncate_old(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:64;if(e.length<=t)return e;var n=Math.ceil((t-3)/2),t=Math.floor((t-3)/2);return e.slice(0,n)+"..."+e.slice(e.length-t)}function middleTruncate(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:64,n=new TextEncoder;if(n.encode(e).length<=t)return e;for(var r=Math.ceil((t-3)/2),i=Math.floor((t-3)/2),o="",a="",s=0;s<e.length;s++){var l=e[s];if(n.encode(o+l).length>r)break;o+=l}for(var c=e.length-1;0<=c;c--){var u=e[c];if(n.encode(u+a).length>i)break;a=u+a}return o+"..."+a}function truncate_utf8_bak(e,t){if("string"!=typeof e)throw new Error("Input must be string");for(var n,r,i,o,a=e.length,s=0,l=0;l<a;l+=1){if(n=e.charCodeAt(l),r=e[l],55296<=(o=n)&&o<=56319&&(56320<=(i=e.charCodeAt(l+1))&&i<=57343)&&(r+=e[l+=1]),(s+=unescape(encodeURIComponent(r)).length)===t)return e.slice(0,l+1);if(t<s)return e.slice(0,l-r.length+1)}return e}function truncate_utf8(e,t){var n=!(2<arguments.length&&void 0!==arguments[2])||arguments[2];if("string"!=typeof e)throw new Error("Input must be a string");var r=_toConsumableArray(e),i=0;if(n)for(var o=0;o<r.length;o++){var a=r[o];if(t<(i+=unescape(encodeURIComponent(a)).length))return r.slice(0,o).join("")}else for(var s=r.length-1;0<=s;s--){var l=r[s];if(t<(i+=unescape(encodeURIComponent(l)).length))return r.slice(s+1).join("")}return e}function sanitizeFilename(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:100;try{return truncate_utf8(e=e.replace(/^\./,"_").replace(/[\u0000-\u001f\u007f-\u009f\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200b-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,"").replace(/&quot;/g,"").replace(/&amp;/g,"&").replace(/[\\/:*?<>|~↵"\t]/g,"_"),t)}catch(e){}}function reportMsg(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"error";chrome.runtime.sendMessage({message:"sentry-report-msg",data:{message:e,level:t}},function(){})}function sendGaPreview(e,t){return sendMessage({message:"ga-msg",type:"pageview",title:e,location:t,params:2<arguments.length&&void 0!==arguments[2]?arguments[2]:{}},function(){})}function _sendGaEvent(e){return sendMessage({message:"ga-msg",type:"event",name:e,params:1<arguments.length&&void 0!==arguments[1]?arguments[1]:{}},function(){})}function sendGaException(e){return sendMessage({message:"ga-msg",type:"exception",error:e,params:1<arguments.length&&void 0!==arguments[1]?arguments[1]:{}},function(){})}function onError(r){"object"==_typeof(window.onerror)&&(window.onerror=function(e,t,n){sendGaException("page: ".concat(r,": url:").concat(t," err: ").concat(e))})}function localizeHtmlPage(){for(var e=document.getElementsByTagName("html"),t=0;t<e.length;t++){var n=e[t],r=n.innerHTML.toString(),i=r.replace(/__MSG_(\w+)__/g,function(e,t){return t?browser.i18n.getMessage(t):""});i!=r&&(n.innerHTML=i)}}function url_host_split_bak(e){try{var t=e.match(/^(?:(?:https?:)?\/\/)?((?:[\w-]+\.)?(([\w-]+)\.[^?\/]+))/);return t.shift(),t.reverse(),t}catch(e){return null}}function url_host_split(e){try{for(var t=[],n=e.match(/^(?:(?:https?:)?\/\/)?((?:[\w-]+\.)?[^?\/]+)/)[1].split("."),r=n.length,i=0;i<r-1;i++)t.unshift(n.join(".")),2<n.length&&n.shift();return t.unshift(n[0]),t}catch(e){return null}}function storeGeneral(e){return _storeGeneral.apply(this,arguments)}function _storeGeneral(){return(_storeGeneral=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,syncStorageSet(_defineProperty({},"".concat(browser.runtime.id,"_general"),t));case 2:case"end":return e.stop()}},e)}))).apply(this,arguments)}function getStoredGeneral(){return _getStoredGeneral.apply(this,arguments)}function _getStoredGeneral(){return(_getStoredGeneral=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t,n;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t="".concat(browser.runtime.id,"_general"),e.next=3,syncStorageGet(t);case 3:return n=e.sent,e.abrupt("return",(null==n?void 0:n[t])||{});case 5:case"end":return e.stop()}},e)}))).apply(this,arguments)}function storeFilter(e){return _storeFilter.apply(this,arguments)}function _storeFilter(){return(_storeFilter=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,syncStorageSet(_defineProperty({},"".concat(browser.runtime.id,"_filter"),t));case 2:case"end":return e.stop()}},e)}))).apply(this,arguments)}function getStoredFilter(){return _getStoredFilter.apply(this,arguments)}function _getStoredFilter(){return(_getStoredFilter=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t,n;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t="".concat(browser.runtime.id,"_filter"),e.next=3,syncStorageGet(t);case 3:return n=e.sent,e.abrupt("return",(null==n?void 0:n[t])||{});case 5:case"end":return e.stop()}},e)}))).apply(this,arguments)}function updateFilter(e,t){return _updateFilter.apply(this,arguments)}function _updateFilter(){return(_updateFilter=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,n){var r,i,o,a,s,l,c;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:for(o in i=function(e,t,n){return void 0!==e&&e>=t[0]&&e<=t[1]?e:n},r={},n)l=t[o]||{},a=void 0===(c=l.enable)?n[o].enable:c,s=l.size,c=n[o],l=c.range,c=c.size,r[o]={enable:a,size:i(s,l,c)};return e.abrupt("return",r);case 4:case"end":return e.stop()}},e)}))).apply(this,arguments)}function needFilter(e,t){return!t||(e<t.size||0==t.enable)}function getStoredCspList(){return _getStoredCspList.apply(this,arguments)}function _getStoredCspList(){return(_getStoredCspList=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t,n;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t="".concat(browser.runtime.id,"_csplist"),e.next=3,syncStorageGet(t);case 3:return n=e.sent,e.abrupt("return",(null==n?void 0:n[t])||null);case 5:case"end":return e.stop()}},e)}))).apply(this,arguments)}function storeCspList(e){return _storeCspList.apply(this,arguments)}function _storeCspList(){return(_storeCspList=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,syncStorageSet(_defineProperty({},"".concat(browser.runtime.id,"_csplist"),t));case 2:case"end":return e.stop()}},e)}))).apply(this,arguments)}function addElement(e,t){switch(2<arguments.length&&void 0!==arguments[2]?arguments[2]:"prepend"){case"insertBefore":t.insertAdjacentElement("beforebegin",e);break;case"insertAfter":t.insertAdjacentElement("afterend",e);break;case"prepend":t.prepend(e);break;case"append":t.append(e);break;case"appendChild":t.appendChild(e)}}var Base64={_keyStr:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",encode:function(e){var t,n,r,i,o,a,s="",l=0;for(e=Base64._utf8_encode(e);l<e.length;)r=(a=e.charCodeAt(l++))>>2,i=(3&a)<<4|(t=e.charCodeAt(l++))>>4,o=(15&t)<<2|(n=e.charCodeAt(l++))>>6,a=63&n,isNaN(t)?o=a=64:isNaN(n)&&(a=64),s=s+this._keyStr.charAt(r)+this._keyStr.charAt(i)+this._keyStr.charAt(o)+this._keyStr.charAt(a);return s},decode:function(e){var t,n,r,i,o,a="",s=0;for(e=e.replace(/[^A-Za-z0-9+/=]/g,"");s<e.length;)t=this._keyStr.indexOf(e.charAt(s++))<<2|(r=this._keyStr.indexOf(e.charAt(s++)))>>4,n=(15&r)<<4|(i=this._keyStr.indexOf(e.charAt(s++)))>>2,r=(3&i)<<6|(o=this._keyStr.indexOf(e.charAt(s++))),a+=String.fromCharCode(t),64!=i&&(a+=String.fromCharCode(n)),64!=o&&(a+=String.fromCharCode(r));return a=Base64._utf8_decode(a)},_utf8_encode:function(e){var t="";e=e.replace(/\r\n/g,"\n");for(var n=0;n<e.length;n++){var r=e.charCodeAt(n);r<128?t+=String.fromCharCode(r):(127<r&&r<2048?t+=String.fromCharCode(r>>6|192):(t+=String.fromCharCode(r>>12|224),t+=String.fromCharCode(r>>6&63|128)),t+=String.fromCharCode(63&r|128))}return t},_utf8_decode:function(e){for(var t,n,r="",i=0,o=t=0;i<e.length;)(o=e.charCodeAt(i))<128?(r+=String.fromCharCode(o),i++):191<o&&o<224?(t=e.charCodeAt(i+1),r+=String.fromCharCode((31&o)<<6|63&t),i+=2):(t=e.charCodeAt(i+1),n=e.charCodeAt(i+2),r+=String.fromCharCode((15&o)<<12|(63&t)<<6|63&n),i+=3);return r}};function buf2hex(e){return _toConsumableArray(new Uint8Array(e)).map(function(e){return e.toString(16).padStart(2,"0")}).join("")}function hex2buf(e){return new Uint8Array(e.match(/[\da-f]{2}/gi).map(function(e){return parseInt(e,16)}))}function genUniqueId(){for(var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:8,t="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",n="",r=0;r<e;r++)n+=t[Math.floor(Math.random()*t.length)];return n}var TimePicker=function(){"use strict";function a(e){if(_classCallCheck(this,a),"timePicker"in e)return e.timePicker;Object.defineProperty(e,"timePicker",{value:this,configurable:!0}),this.input=e,this.format=e.getAttribute("format")||"hh:mm:ss",this.separators=this.format.replace(/[hms]/g,"").split(""),this.time={hh:"00",mm:"00",ss:"00"},this.initializeValue(),this.setupEventListeners(),this.renderInitialValue()}return _createClass(a,[{key:"initializeValue",value:function(){var e=this.input.value;e&&(/^\d+$/.test(e)?this.parseSeconds(parseInt(e)):(e=this.parseInput(e))&&(this.time=e))}},{key:"parseInput",value:function(e){e=e.split(this.separators[0]);return 3!==e.length?null:{hh:e[0],mm:e[1],ss:e[2]}}},{key:"setupEventListeners",value:function(){var n=this,e={keydown:this.handleKeyDown.bind(this),beforeinput:this.handleBeforeInput.bind(this),mouseup:this.handleMouseUp.bind(this)};Object.entries(e).forEach(function(e){var t=_slicedToArray(e,2),e=t[0],t=t[1];n.input.addEventListener(e,t)})}},{key:"getPartDetails",value:function(t){var e=(null===(e=Object.entries(a.RANGES).find(function(e){e=_slicedToArray(e,2),e[0],e=e[1];return t>=e.start&&t<=e.end}))||void 0===e?void 0:e[0])||"hh";return _objectSpread({part:e},a.RANGES[e])}},{key:"handleBeforeInput",value:function(e){var t,n,r,i,o;/[0-9]/.test(e.data)&&(e.preventDefault(),t=this.input.selectionStart,this.separators.includes(this.input.value[t])&&t++,n=(i=this.getPartDetails(t)).part,o=i.start,r=i.end,i=this.input.value.split(""),o=t===o,this.updateDigit(i,t,e.data,n,o),this.updateTimeFromInput(i.join("")),t+1===r||this.separators.includes(i[t+1])?this.selectNextPart(t+1):this.input.setSelectionRange(t+1,t+1),this.dispatchInputEvent())}},{key:"updateDigit",value:function(e,t,n,r,i){var o="hh"===r,r=parseInt(e[a.RANGES[r].start]);i?o&&2<parseInt(n)||!o&&5<parseInt(n)?e[t]="0":e[t]=n:o&&2===r&&3<parseInt(n)?e[t]="0":e[t]=n}},{key:"updateTimeFromInput",value:function(e){var t=e.split(this.separators[0]);this.time={hh:t[0],mm:t[1],ss:t[2]},this.input.value=e}},{key:"handleKeyDown",value:function(e){"ArrowUp"!==e.key&&"ArrowDown"!==e.key||(e.preventDefault(),this.adjustTime("ArrowUp"===e.key?1:-1),this.dispatchInputEvent())}},{key:"handleMouseUp",value:function(){var e=this.getPartDetails(this.input.selectionStart).part;this.selectPart(this.getPartIndex(e))}},{key:"adjustTime",value:function(e){var t=this.getPartDetails(this.input.selectionStart),n=t.part,t=t.max,e=parseInt(this.time[n],10)+e;t<e&&(e=0),e<0&&(e=t),this.time[n]=this.pad(e),this.input.value=this.formatTime(),this.selectPart(this.getPartIndex(n))}},{key:"selectPart",value:function(e){e=Object.values(a.RANGES)[e];this.input.setSelectionRange(e.start,e.end)}},{key:"selectNextPart",value:function(e){e=this.getPartDetails(e).nextIndex;this.selectPart(e)}},{key:"formatTime",value:function(){return this.format.replace("hh",this.pad(this.time.hh)).replace("mm",this.pad(this.time.mm)).replace("ss",this.pad(this.time.ss))}},{key:"renderInitialValue",value:function(){this.input.value=this.formatTime()}},{key:"pad",value:function(e){return e.toString().padStart(2,"0")}},{key:"parseSeconds",value:function(e){var t=Math.floor(e/3600),n=Math.floor(e%3600/60),e=e%60;this.time={hh:this.pad(t),mm:this.pad(n),ss:this.pad(e)}}},{key:"getTotalSeconds",value:function(){return 3600*parseInt(this.time.hh)+60*parseInt(this.time.mm)+parseInt(this.time.ss)}},{key:"dispatchInputEvent",value:function(){var e=new Event("input",{bubbles:!0,cancelable:!0});this.input.dispatchEvent(e)}},{key:"getPartIndex",value:function(e){return Object.keys(a.RANGES).indexOf(e)}}],[{key:"from",value:function(e){return e.timePicker}},{key:"destroy",value:function(e){"timePicker"in e&&delete e.timePicker}}]),a}();function sproutVideoSig(e,t){var n={jpg:"j",key:"k",m3u8:"m",ts:"t"},r=new URL(e);if(r.search.length)return e;var i,r=r.pathname.match(new RegExp("\\.(?<ext>".concat(Object.keys(n).join("|"),")")));if(r){r=n[r.groups.ext];return e+(i=r,i=t.signatures[i],"?Policy=".concat(encodeURIComponent(i["CloudFront-Policy"]),"&Signature=").concat(encodeURIComponent(i["CloudFront-Signature"]),"&Key-Pair-Id=").concat(encodeURIComponent(i["CloudFront-Key-Pair-Id"]),"&sessionID=").concat(encodeURIComponent(t.sessionID)))}return e}function applySig(t,e){try{return window[e.func](t,e.config)}catch(e){return t}}_defineProperty(TimePicker,"RANGES",{hh:{start:0,end:2,max:23,nextIndex:1},mm:{start:3,end:5,max:59,nextIndex:2},ss:{start:6,end:8,max:59,nextIndex:0}}),function(e,t){"object"==("undefined"==typeof exports?"undefined":_typeof(exports))&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):e.QRious=t()}(this,function(){"use strict";function a(e,t,n){for(var r,i,o=0,a=(n=c.call(arguments,2)).length;o<a;o++)for(r in i=n[o])e&&!l.call(i,r)||(t[r]=i[r])}function e(){}var s=function(){},l=Object.prototype.hasOwnProperty,c=Array.prototype.slice,t=function(e,t,n,r){var i,o=this;return"string"!=typeof e&&(r=n,n=t,t=e,e=null),"function"!=typeof t&&(r=n,n=t,t=function(){return o.apply(this,arguments)}),a(!1,t,o,r),t.prototype=(r=o.prototype,n=n,"function"==typeof Object.create?i=Object.create(r):(s.prototype=r,i=new s,s.prototype=null),n&&a(!0,i,n),i),(t.prototype.constructor=t).class_=e||o.class_,t.super_=o,t};e.class_="Nevis",e.super_=Object,e.extend=t;var n=e,r=n.extend(function(e,t,n){this.qrious=e,this.element=t,this.element.qrious=e,this.enabled=Boolean(n)},{draw:function(e){},getElement:function(){return this.enabled||(this.enabled=!0,this.render()),this.element},getModuleSize:function(e){var t=this.qrious,n=t.padding||0,e=Math.floor((t.size-2*n)/e.width);return Math.max(1,e)},getOffset:function(e){var t=this.qrious,n=t.padding;if(null!=n)return n;n=this.getModuleSize(e),e=Math.floor((t.size-n*e.width)/2);return Math.max(0,e)},render:function(e){this.enabled&&(this.resize(),this.reset(),this.draw(e))},reset:function(){},resize:function(){}}),i=r.extend({draw:function(e){var t,n,r=this.qrious,i=this.getModuleSize(e),o=this.getOffset(e),a=this.element.getContext("2d");for(a.fillStyle=r.foreground,a.globalAlpha=r.foregroundAlpha,t=0;t<e.width;t++)for(n=0;n<e.width;n++)e.buffer[n*e.width+t]&&a.fillRect(i*t+o,i*n+o,i,i)},reset:function(){var e=this.qrious,t=this.element.getContext("2d"),n=e.size;t.lineWidth=1,t.clearRect(0,0,n,n),t.fillStyle=e.background,t.globalAlpha=e.backgroundAlpha,t.fillRect(0,0,n,n)},resize:function(){var e=this.element;e.width=e.height=this.qrious.size}}),o=n.extend(null,{BLOCK:[0,11,15,19,23,27,31,16,18,20,22,24,26,28,20,22,24,24,26,28,28,22,24,24,26,26,28,28,24,24,26,26,26,28,28,24,26,26,26,28,28]}),u=n.extend(null,{BLOCKS:[1,0,19,7,1,0,16,10,1,0,13,13,1,0,9,17,1,0,34,10,1,0,28,16,1,0,22,22,1,0,16,28,1,0,55,15,1,0,44,26,2,0,17,18,2,0,13,22,1,0,80,20,2,0,32,18,2,0,24,26,4,0,9,16,1,0,108,26,2,0,43,24,2,2,15,18,2,2,11,22,2,0,68,18,4,0,27,16,4,0,19,24,4,0,15,28,2,0,78,20,4,0,31,18,2,4,14,18,4,1,13,26,2,0,97,24,2,2,38,22,4,2,18,22,4,2,14,26,2,0,116,30,3,2,36,22,4,4,16,20,4,4,12,24,2,2,68,18,4,1,43,26,6,2,19,24,6,2,15,28,4,0,81,20,1,4,50,30,4,4,22,28,3,8,12,24,2,2,92,24,6,2,36,22,4,6,20,26,7,4,14,28,4,0,107,26,8,1,37,22,8,4,20,24,12,4,11,22,3,1,115,30,4,5,40,24,11,5,16,20,11,5,12,24,5,1,87,22,5,5,41,24,5,7,24,30,11,7,12,24,5,1,98,24,7,3,45,28,15,2,19,24,3,13,15,30,1,5,107,28,10,1,46,28,1,15,22,28,2,17,14,28,5,1,120,30,9,4,43,26,17,1,22,28,2,19,14,28,3,4,113,28,3,11,44,26,17,4,21,26,9,16,13,26,3,5,107,28,3,13,41,26,15,5,24,30,15,10,15,28,4,4,116,28,17,0,42,26,17,6,22,28,19,6,16,30,2,7,111,28,17,0,46,28,7,16,24,30,34,0,13,24,4,5,121,30,4,14,47,28,11,14,24,30,16,14,15,30,6,4,117,30,6,14,45,28,11,16,24,30,30,2,16,30,8,4,106,26,8,13,47,28,7,22,24,30,22,13,15,30,10,2,114,28,19,4,46,28,28,6,22,28,33,4,16,30,8,4,122,30,22,3,45,28,8,26,23,30,12,28,15,30,3,10,117,30,3,23,45,28,4,31,24,30,11,31,15,30,7,7,116,30,21,7,45,28,1,37,23,30,19,26,15,30,5,10,115,30,19,10,47,28,15,25,24,30,23,25,15,30,13,3,115,30,2,29,46,28,42,1,24,30,23,28,15,30,17,0,115,30,10,23,46,28,10,35,24,30,19,35,15,30,17,1,115,30,14,21,46,28,29,19,24,30,11,46,15,30,13,6,115,30,14,23,46,28,44,7,24,30,59,1,16,30,12,7,121,30,12,26,47,28,39,14,24,30,22,41,15,30,6,14,121,30,6,34,47,28,46,10,24,30,2,64,15,30,17,4,122,30,29,14,46,28,49,10,24,30,24,46,15,30,4,18,122,30,13,32,46,28,48,14,24,30,42,32,15,30,20,4,117,30,40,7,47,28,43,22,24,30,10,67,15,30,19,6,118,30,18,31,47,28,34,34,24,30,20,61,15,30],FINAL_FORMAT:[30660,29427,32170,30877,26159,25368,27713,26998,21522,20773,24188,23371,17913,16590,20375,19104,13663,12392,16177,14854,9396,8579,11994,11245,5769,5054,7399,6608,1890,597,3340,2107],LEVELS:{L:1,M:2,Q:3,H:4}}),d=n.extend(null,{EXPONENT:[1,2,4,8,16,32,64,128,29,58,116,232,205,135,19,38,76,152,45,90,180,117,234,201,143,3,6,12,24,48,96,192,157,39,78,156,37,74,148,53,106,212,181,119,238,193,159,35,70,140,5,10,20,40,80,160,93,186,105,210,185,111,222,161,95,190,97,194,153,47,94,188,101,202,137,15,30,60,120,240,253,231,211,187,107,214,177,127,254,225,223,163,91,182,113,226,217,175,67,134,17,34,68,136,13,26,52,104,208,189,103,206,129,31,62,124,248,237,199,147,59,118,236,197,151,51,102,204,133,23,46,92,184,109,218,169,79,158,33,66,132,21,42,84,168,77,154,41,82,164,85,170,73,146,57,114,228,213,183,115,230,209,191,99,198,145,63,126,252,229,215,179,123,246,241,255,227,219,171,75,150,49,98,196,149,55,110,220,165,87,174,65,130,25,50,100,200,141,7,14,28,56,112,224,221,167,83,166,81,162,89,178,121,242,249,239,195,155,43,86,172,69,138,9,18,36,72,144,61,122,244,245,247,243,251,235,203,139,11,22,44,88,176,125,250,233,207,131,27,54,108,216,173,71,142,0],LOG:[255,0,1,25,2,50,26,198,3,223,51,238,27,104,199,75,4,100,224,14,52,141,239,129,28,193,105,248,200,8,76,113,5,138,101,47,225,36,15,33,53,147,142,218,240,18,130,69,29,181,194,125,106,39,249,185,201,154,9,120,77,228,114,166,6,191,139,98,102,221,48,253,226,152,37,179,16,145,34,136,54,208,148,206,143,150,219,189,241,210,19,92,131,56,70,64,30,66,182,163,195,72,126,110,107,58,40,84,250,133,186,61,202,94,155,159,10,21,121,43,78,212,229,172,115,243,167,87,7,112,192,247,140,128,99,13,103,74,222,237,49,197,254,24,227,165,153,119,38,184,180,124,17,68,146,217,35,32,137,46,55,63,209,91,149,188,207,205,144,135,151,178,220,252,190,97,242,86,211,171,20,42,93,158,132,60,57,83,71,109,65,162,31,45,67,216,183,123,164,118,196,23,73,236,127,12,111,246,108,161,59,82,41,157,85,170,251,96,134,177,187,204,62,90,203,89,95,176,156,169,160,81,11,245,22,235,122,117,44,215,79,174,213,233,230,231,173,232,116,214,244,234,168,80,88,175]}),f=n.extend(null,{BLOCK:[3220,1468,2713,1235,3062,1890,2119,1549,2344,2936,1117,2583,1330,2470,1667,2249,2028,3780,481,4011,142,3098,831,3445,592,2517,1776,2234,1951,2827,1070,2660,1345,3177]}),p=n.extend(function(e){var t,n,r,i,o,a=e.value.length;for(this._badness=[],this._level=u.LEVELS[e.level],this._polynomial=[],this._value=e.value,this._version=0,this._stringBuffer=[];this._version<40&&(this._version++,r=4*(this._level-1)+16*(this._version-1),i=u.BLOCKS[r++],o=u.BLOCKS[r++],t=u.BLOCKS[r++],n=u.BLOCKS[r],!(a<=t*(i+o)+o-3+(this._version<=9))););this._dataBlock=t,this._eccBlock=n,this._neccBlock1=i,this._neccBlock2=o;e=this.width=17+4*this._version;this.buffer=p._createArray(e*e),this._ecc=p._createArray(t+(t+n)*(i+o)+o),this._mask=p._createArray((e*(1+e)+1)/2),this._insertFinders(),this._insertAlignments(),this.buffer[8+e*(e-8)]=1,this._insertTimingGap(),this._reverseMask(),this._insertTimingRowAndColumn(),this._insertVersion(),this._syncMask(),this._convertBitStream(a),this._calculatePolynomial(),this._appendEccToData(),this._interleaveBlocks(),this._pack(),this._finish()},{_addAlignment:function(e,t){var n,r=this.buffer,i=this.width;for(r[e+i*t]=1,n=-2;n<2;n++)r[e+n+i*(t-2)]=1,r[e-2+i*(t+n+1)]=1,r[e+2+i*(t+n)]=1,r[e+n+1+i*(t+2)]=1;for(n=0;n<2;n++)this._setMask(e-1,t+n),this._setMask(e+1,t-n),this._setMask(e-n,t-1),this._setMask(e+n,t+1)},_appendData:function(e,t,n,r){for(var i,o,a=this._polynomial,s=this._stringBuffer,l=0;l<r;l++)s[n+l]=0;for(l=0;l<t;l++){if(255!==(i=d.LOG[s[e+l]^s[n]]))for(o=1;o<r;o++)s[n+o-1]=s[n+o]^d.EXPONENT[p._modN(i+a[r-o])];else for(o=n;o<n+r;o++)s[o]=s[o+1];s[n+r-1]=255===i?0:d.EXPONENT[p._modN(i+a[0])]}},_appendEccToData:function(){for(var e=0,t=this._dataBlock,n=this._calculateMaxLength(),r=this._eccBlock,i=0;i<this._neccBlock1;i++)this._appendData(e,t,n,r),e+=t,n+=r;for(i=0;i<this._neccBlock2;i++)this._appendData(e,t+1,n,r),e+=t+1,n+=r},_applyMask:function(e){var t,n,r,i,o=this.buffer,a=this.width;switch(e){case 0:for(i=0;i<a;i++)for(r=0;r<a;r++)r+i&1||this._isMasked(r,i)||(o[r+i*a]^=1);break;case 1:for(i=0;i<a;i++)for(r=0;r<a;r++)1&i||this._isMasked(r,i)||(o[r+i*a]^=1);break;case 2:for(i=0;i<a;i++)for(r=t=0;r<a;r++,t++)3===t&&(t=0),t||this._isMasked(r,i)||(o[r+i*a]^=1);break;case 3:for(i=n=0;i<a;i++,n++)for(3===n&&(n=0),t=n,r=0;r<a;r++,t++)3===t&&(t=0),t||this._isMasked(r,i)||(o[r+i*a]^=1);break;case 4:for(i=0;i<a;i++)for(n=i>>1&1,r=t=0;r<a;r++,t++)3===t&&(t=0,n=!n),n||this._isMasked(r,i)||(o[r+i*a]^=1);break;case 5:for(i=n=0;i<a;i++,n++)for(3===n&&(n=0),r=t=0;r<a;r++,t++)3===t&&(t=0),(r&i&1)+!(!t|!n)||this._isMasked(r,i)||(o[r+i*a]^=1);break;case 6:for(i=n=0;i<a;i++,n++)for(3===n&&(n=0),r=t=0;r<a;r++,t++)3===t&&(t=0),(r&i&1)+(t&&t===n)&1||this._isMasked(r,i)||(o[r+i*a]^=1);break;case 7:for(i=n=0;i<a;i++,n++)for(3===n&&(n=0),r=t=0;r<a;r++,t++)3===t&&(t=0),(t&&t===n)+(r+i&1)&1||this._isMasked(r,i)||(o[r+i*a]^=1)}},_calculateMaxLength:function(){return this._dataBlock*(this._neccBlock1+this._neccBlock2)+this._neccBlock2},_calculatePolynomial:function(){var e,t,n=this._eccBlock,r=this._polynomial;for(r[0]=1,e=0;e<n;e++){for(r[e+1]=1,t=e;0<t;t--)r[t]=r[t]?r[t-1]^d.EXPONENT[p._modN(d.LOG[r[t]]+e)]:r[t-1];r[0]=d.EXPONENT[p._modN(d.LOG[r[0]]+e)]}for(e=0;e<=n;e++)r[e]=d.LOG[r[e]]},_checkBadness:function(){for(var e,t,n,r,i=0,o=this._badness,a=this.buffer,s=this.width,l=0;l<s-1;l++)for(r=0;r<s-1;r++)(a[r+s*l]&&a[r+1+s*l]&&a[r+s*(l+1)]&&a[r+1+s*(l+1)]||!(a[r+s*l]||a[r+1+s*l]||a[r+s*(l+1)]||a[r+1+s*(l+1)]))&&(i+=p.N2);var c=0;for(l=0;l<s;l++){for(r=e=o[n=0]=0;r<s;r++)e===(t=a[r+s*l])?o[n]++:o[++n]=1,c+=(e=t)?1:-1;i+=this._getBadness(n)}c<0&&(c=-c);var u=0,d=c;for(d+=d<<2,d<<=1;s*s<d;)d-=s*s,u++;for(i+=u*p.N4,r=0;r<s;r++){for(l=e=o[n=0]=0;l<s;l++)e===(t=a[r+s*l])?o[n]++:o[++n]=1,e=t;i+=this._getBadness(n)}return i},_convertBitStream:function(e){for(var t,n=this._ecc,r=this._version,i=0;i<e;i++)n[i]=this._value.charCodeAt(i);var o=this._stringBuffer=n.slice(),a=this._calculateMaxLength();a-2<=e&&(e=a-2,9<r&&e--);var s=e;if(9<r){for(o[s+2]=0,o[s+3]=0;s--;)t=o[s],o[s+3]|=255&t<<4,o[s+2]=t>>4;o[2]|=255&e<<4,o[1]=e>>4,o[0]=64|e>>12}else{for(o[s+1]=0,o[s+2]=0;s--;)t=o[s],o[s+2]|=255&t<<4,o[s+1]=t>>4;o[1]|=255&e<<4,o[0]=64|e>>4}for(s=e+3-(r<10);s<a;)o[s++]=236,o[s++]=17},_getBadness:function(e){for(var t=0,n=this._badness,r=0;r<=e;r++)5<=n[r]&&(t+=p.N1+n[r]-5);for(r=3;r<e-1;r+=2)n[r-2]===n[r+2]&&n[r+2]===n[r-1]&&n[r-1]===n[r+1]&&3*n[r-1]===n[r]&&(0===n[r-3]||e<r+3||3*n[r-3]>=4*n[r]||3*n[r+3]>=4*n[r])&&(t+=p.N3);return t},_finish:function(){this._stringBuffer=this.buffer.slice();for(var e,t=0,n=3e4,r=0;r<8&&(this._applyMask(r),(e=this._checkBadness())<n&&(n=e,t=r),7!==t);r++)this.buffer=this._stringBuffer.slice();t!==r&&this._applyMask(t),n=u.FINAL_FORMAT[t+(this._level-1<<3)];var i=this.buffer,o=this.width;for(r=0;r<8;r++,n>>=1)1&n&&(i[o-1-r+8*o]=1,r<6?i[8+o*r]=1:i[8+o*(r+1)]=1);for(r=0;r<7;r++,n>>=1)1&n&&(i[8+o*(o-7+r)]=1,r?i[6-r+8*o]=1:i[7+8*o]=1)},_interleaveBlocks:function(){for(var e,t=this._dataBlock,n=this._ecc,r=this._eccBlock,i=0,o=this._calculateMaxLength(),a=this._neccBlock1,s=this._neccBlock2,l=this._stringBuffer,c=0;c<t;c++){for(e=0;e<a;e++)n[i++]=l[c+e*t];for(e=0;e<s;e++)n[i++]=l[a*t+c+e*(t+1)]}for(e=0;e<s;e++)n[i++]=l[a*t+c+e*(t+1)];for(c=0;c<r;c++)for(e=0;e<a+s;e++)n[i++]=l[o+c+e*r];this._stringBuffer=n},_insertAlignments:function(){var e,t,n,r=this._version,i=this.width;if(1<r)for(e=o.BLOCK[r],n=i-7;;){for(t=i-7;e-3<t&&(this._addAlignment(t,n),!(t<e));)t-=e;if(n<=e+9)break;n-=e,this._addAlignment(6,n),this._addAlignment(n,6)}},_insertFinders:function(){for(var e,t,n,r=this.buffer,i=this.width,o=0;o<3;o++){for(n=e=0,1===o&&(e=i-7),2===o&&(n=i-7),r[n+3+i*(e+3)]=1,t=0;t<6;t++)r[n+t+i*e]=1,r[n+i*(e+t+1)]=1,r[n+6+i*(e+t)]=1,r[n+t+1+i*(e+6)]=1;for(t=1;t<5;t++)this._setMask(n+t,e+1),this._setMask(n+1,e+t+1),this._setMask(n+5,e+t),this._setMask(n+t+1,e+5);for(t=2;t<4;t++)r[n+t+i*(e+2)]=1,r[n+2+i*(e+t+1)]=1,r[n+4+i*(e+t)]=1,r[n+t+1+i*(e+4)]=1}},_insertTimingGap:function(){for(var e,t=this.width,n=0;n<7;n++)this._setMask(7,n),this._setMask(t-8,n),this._setMask(7,n+t-7);for(e=0;e<8;e++)this._setMask(e,7),this._setMask(e+t-8,7),this._setMask(e,t-8)},_insertTimingRowAndColumn:function(){for(var e=this.buffer,t=this.width,n=0;n<t-14;n++)1&n?(this._setMask(8+n,6),this._setMask(6,8+n)):(e[8+n+6*t]=1,e[6+t*(8+n)]=1)},_insertVersion:function(){var e,t,n,r,i=this.buffer,o=this._version,a=this.width;if(6<o)for(e=f.BLOCK[o-7],t=17,n=0;n<6;n++)for(r=0;r<3;r++,t--)1&(11<t?o>>t-12:e>>t)?(i[5-n+a*(2-r+a-11)]=1,i[2-r+a-11+a*(5-n)]=1):(this._setMask(5-n,2-r+a-11),this._setMask(2-r+a-11,5-n))},_isMasked:function(e,t){t=p._getMaskBit(e,t);return 1===this._mask[t]},_pack:function(){for(var e,t,n=1,r=1,i=this.width,o=i-1,a=i-1,s=(this._dataBlock+this._eccBlock)*(this._neccBlock1+this._neccBlock2)+this._neccBlock2,l=0;l<s;l++)for(e=this._stringBuffer[l],t=0;t<8;t++,e<<=1)for(128&e&&(this.buffer[o+i*a]=1);r?o--:(o++,n?0!==a?a--:(n=!n,6==(o-=2)&&(o--,a=9)):a!==i-1?a++:(n=!n,6==(o-=2)&&(o--,a-=8))),r=!r,this._isMasked(o,a););},_reverseMask:function(){for(var e,t=this.width,n=0;n<9;n++)this._setMask(n,8);for(n=0;n<8;n++)this._setMask(n+t-8,8),this._setMask(8,n);for(e=0;e<7;e++)this._setMask(8,e+t-7)},_setMask:function(e,t){t=p._getMaskBit(e,t);this._mask[t]=1},_syncMask:function(){for(var e,t=this.width,n=0;n<t;n++)for(e=0;e<=n;e++)this.buffer[e+t*n]&&this._setMask(e,n)}},{_createArray:function(e){for(var t=[],n=0;n<e;n++)t[n]=0;return t},_getMaskBit:function(e,t){var n;return t<e&&(n=e,e=t,t=n),n=t,n+=t*t,(n>>=1)+e},_modN:function(e){for(;255<=e;)e=((e-=255)>>8)+(255&e);return e},N1:3,N2:3,N3:40,N4:10}),h=p,g=r.extend({draw:function(){this.element.src=this.qrious.toDataURL()},reset:function(){this.element.src=""},resize:function(){var e=this.element;e.width=e.height=this.qrious.size}}),m=n.extend(function(e,t,n,r){this.name=e,this.modifiable=Boolean(t),this.defaultValue=n,this._valueTransformer=r},{transform:function(e){var t=this._valueTransformer;return"function"==typeof t?t(e,this):e}}),v=n.extend(null,{abs:function(e){return null!=e?Math.abs(e):null},hasOwn:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},noop:function(){},toUpperCase:function(e){return null!=e?e.toUpperCase():null}}),y=n.extend(function(e){this.options={},e.forEach(function(e){this.options[e.name]=e},this)},{exists:function(e){return null!=this.options[e]},get:function(e,t){return y._get(this.options[e],t)},getAll:function(e){var t,n=this.options,r={};for(t in n)v.hasOwn(n,t)&&(r[t]=y._get(n[t],e));return r},init:function(e,t,n){var r,i;for(r in"function"!=typeof n&&(n=v.noop),this.options)v.hasOwn(this.options,r)&&(i=this.options[r],y._set(i,i.defaultValue,t),y._createAccessor(i,t,n));this._setAll(e,t,!0)},set:function(e,t,n){return this._set(e,t,n)},setAll:function(e,t){return this._setAll(e,t)},_set:function(e,t,n,r){var i=this.options[e];if(!i)throw new Error("Invalid option: "+e);if(!i.modifiable&&!r)throw new Error("Option cannot be modified: "+e);return y._set(i,t,n)},_setAll:function(e,t,n){if(!e)return!1;var r,i=!1;for(r in e)v.hasOwn(e,r)&&this._set(r,e[r],t,n)&&(i=!0);return i}},{_createAccessor:function(t,n,r){var e={get:function(){return y._get(t,n)}};t.modifiable&&(e.set=function(e){y._set(t,e,n)&&r(e,t)}),Object.defineProperty(n,t.name,e)},_get:function(e,t){return t["_"+e.name]},_set:function(e,t,n){var r="_"+e.name,i=n[r],e=e.transform(null!=t?t:e.defaultValue);return(n[r]=e)!==i}}),t=y,r=n.extend(function(){this._services={}},{getService:function(e){var t=this._services[e];if(!t)throw new Error("Service is not being managed with name: "+e);return t},setService:function(e,t){if(this._services[e])throw new Error("Service is already managed with name: "+e);t&&(this._services[e]=t)}}),b=new t([new m("background",!0,"white"),new m("backgroundAlpha",!0,1,v.abs),new m("element"),new m("foreground",!0,"black"),new m("foregroundAlpha",!0,1,v.abs),new m("level",!0,"L",v.toUpperCase),new m("mime",!0,"image/png"),new m("padding",!0,null,v.abs),new m("size",!0,100,v.abs),new m("value",!0,"")]),w=new r,r=n.extend(function(e){b.init(e,this,this.update.bind(this));var t=b.get("element",this),n=w.getService("element"),e=t&&n.isCanvas(t)?t:n.createCanvas(),n=t&&n.isImage(t)?t:n.createImage();this._canvasRenderer=new i(this,e,!0),this._imageRenderer=new g(this,n,n===t),this.update()},{get:function(){return b.getAll(this)},set:function(e){b.setAll(e,this)&&this.update()},toDataURL:function(e){return this.canvas.toDataURL(e||this.mime)},update:function(){var e=new h({level:this.level,value:this.value});this._canvasRenderer.render(e),this._imageRenderer.render(e)}},{use:function(e){w.setService(e.getName(),e)}});Object.defineProperties(r.prototype,{canvas:{get:function(){return this._canvasRenderer.getElement()}},image:{get:function(){return this._imageRenderer.getElement()}}});n=n.extend({getName:function(){}}).extend({createCanvas:function(){},createImage:function(){},getName:function(){return"element"},isCanvas:function(e){},isImage:function(e){}}).extend({createCanvas:function(){return document.createElement("canvas")},createImage:function(){return document.createElement("img")},isCanvas:function(e){return e instanceof HTMLCanvasElement},isImage:function(e){return e instanceof HTMLImageElement}});return r.use(new n),r});var PopDownloader=function(){"use strict";_inherits(c,LinkDownloader);var r,i,o,e,a,t=_createSuper(c);function c(){return _classCallCheck(this,c),t.apply(this,arguments)}return _createClass(c,[{key:"download",value:(a=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,n,r,i){var o;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(o=r.skipBgDl,"isBrave"==(null===(o=navigator)||void 0===o||null===(o=o.brave)||void 0===o||null===(o=o.isBrave)||void 0===o?void 0:o.name)&&t.streamType,o=null,!i.useDedicatedDownloader){e.next=15;break}if("downloadingPage"==configuration.GENERAL.dedicatedDownloader)return e.next=7,this.downloadingPageDownload(t,n,r);e.next=10;break;case 7:o=e.sent,e.next=13;break;case 10:return e.next=12,this.downloadingPageDownload(t,n,r,!0);case 12:o=e.sent;case 13:e.next=24;break;case 15:if("downloadingFrame"==configuration.GENERAL.defaultDownloader)return e.next=18,this.downloadingFramedownload(t,n,r);e.next=21;break;case 18:o=e.sent,e.next=24;break;case 21:return e.next=23,this.contentDownload(t,n,r);case 23:o=e.sent;case 24:"result"in o&&0<o.result?this.handleStateChange(n,c.STATE.DOWNLOADING):this.handleStateChange(n,c.STATE.FAILED);case 25:case"end":return e.stop()}},e,this)})),function(e,t,n,r){return a.apply(this,arguments)})},{key:"createDownloadingTab",value:(e=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t,n,r,i,o,a=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(n="html/downloading.html"+(null!=(t=0<a.length&&void 0!==a[0]?a[0]:null)&&t.tabId?"?t=".concat(t.tabId):""),null!=this&&this.downloadingTab){e.next=18;break}return e.next=5,p(browser.tabs.query,{url:chrome.runtime.getURL(n)});case 5:if(0==(r=e.sent).length)return i={url:chrome.runtime.getURL(n),active:!1},null!=(null==t?void 0:t.tabIndex)&&(i.index=t.tabIndex+1),e.next=11,browser.tabs.create(i);e.next=17;break;case 11:return o=e.sent,e.next=14,new Promise(function(){var t=_asyncToGenerator(_regeneratorRuntime().mark(function e(i){var t;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:t=function e(t,n,r){t===o.id&&"complete"===n.status&&(chrome.tabs.onUpdated.removeListener(e),i())},chrome.tabs.onUpdated.addListener(t);case 2:case"end":return e.stop()}},e)}));return function(e){return t.apply(this,arguments)}}());case 14:this.downloadingTab=o,e.next=18;break;case 17:this.downloadingTab=r[0];case 18:case"end":return e.stop()}},e,this)})),function(){return e.apply(this,arguments)})},{key:"downloadingPageDownload",value:(o=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,n,r){var i,o=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return i=3<o.length&&void 0!==o[3]&&o[3]?r:null,e.next=4,this.createDownloadingTab(i);case 4:return e.next=6,p(browser.tabs.sendMessage,this.downloadingTab.id,{message:"downloading-download-video-stream",videoLink:t,key:n,options:r},{frameId:0});case 6:return e.abrupt("return",e.sent);case 7:case"end":return e.stop()}},e,this)})),function(e,t,n){return o.apply(this,arguments)})},{key:"downloadingFramedownload",value:(i=_asyncToGenerator(_regeneratorRuntime().mark(function e(n,r,i){var o,a,t,s;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return s=function(){return(s=_asyncToGenerator(_regeneratorRuntime().mark(function e(){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,sendMessage({message:"install-script",tabId:o,frameId:0});case 2:return e.sent,e.next=6,new Promise(function(t){setTimeout(_asyncToGenerator(_regeneratorRuntime().mark(function e(){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,p(browser.tabs.sendMessage,o,{message:"downloading-download-video-stream",videoLink:n,key:r,options:i});case 2:a=e.sent,t(a);case 4:case"end":return e.stop()}},e)})),1e3)});case 6:case"end":return e.stop()}},e)}))).apply(this,arguments)},t=function(){return s.apply(this,arguments)},o=i.tabId,i.skipBgDl,a=null,e.next=6,p(browser.tabs.sendMessage,o,{message:"downloading-download-video-stream",videoLink:n,key:r,options:i}).catch(function(e){return t()});case 6:return a=e.sent,e.abrupt("return",a);case 8:case"end":return e.stop()}},e)})),function(e,t,n){return i.apply(this,arguments)})},{key:"contentDownload",value:(r=_asyncToGenerator(_regeneratorRuntime().mark(function e(n,r,i){var o,a,t,s,l;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return s=function(){return(s=_asyncToGenerator(_regeneratorRuntime().mark(function e(){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,sendMessage({message:"install-script",tabId:o,frameId:0});case 2:return e.sent,e.next=6,new Promise(function(t){setTimeout(_asyncToGenerator(_regeneratorRuntime().mark(function e(){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,p(browser.tabs.sendMessage,o,{message:"download-video-stream",videoLink:n,key:r,options:i},{frameId:0});case 2:a=e.sent,t(a);case 4:case"end":return e.stop()}},e)})),100)});case 6:return e.abrupt("return",e.sent);case 7:case"end":return e.stop()}},e)}))).apply(this,arguments)},t=function(){return s.apply(this,arguments)},o=i.tabId,i.skipBgDl,a=null,this.handleStateChange(r,c.STATE.START),l={frameId:(null==n?void 0:n.frameId)||0},e.prev=6,e.next=9,p(browser.tabs.sendMessage,o,{message:"download-video-stream",videoLink:n,key:r,options:i},l);case 9:a=e.sent,e.next=31;break;case 12:if(e.prev=12,e.t0=e.catch(6),0<(null==n?void 0:n.frameId))return e.prev=15,e.next=18,p(browser.tabs.sendMessage,o,{message:"download-video-stream",videoLink:n,key:r,options:i},{frameId:0});e.next=28;break;case 18:a=e.sent,e.next=26;break;case 21:return e.prev=21,e.t1=e.catch(15),e.next=25,t();case 25:a=e.sent;case 26:e.next=31;break;case 28:return e.next=30,t();case 30:a=e.sent;case 31:return e.abrupt("return",a);case 32:case"end":return e.stop()}},e,this,[[6,12],[15,21]])})),function(e,t,n){return r.apply(this,arguments)})}]),c}(),Popup=function(){"use strict";function e(){_classCallCheck(this,e),this.tab=null,this.videoLinks=[],this.sig={},this.license=null,this.options={},this.record={mode:"Video",options:{},status:{}},this.videoEls=[],this.downloader=new PopDownloader(this.stateListener.bind(this),this.progressListener.bind(this))}var t,n,r,i,o,a,s,l,c,u,d,f,h,g,m,v,y,b,w,_,x,k,T,E,S,C,A,R;return _createClass(e,[{key:"isIE",value:function(){var e=window.navigator.userAgent;return 0<e.indexOf("MSIE ")||0<e.indexOf("Trident/")||0<e.indexOf("Edge/")}},{key:"getCurrActiveTab",value:(R=_asyncToGenerator(_regeneratorRuntime().mark(function e(){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,p(browser.tabs.query,{active:!0,currentWindow:!0}).then(function(e){return e[0]}).catch(function(e){});case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e)})),function(){return R.apply(this,arguments)})},{key:"getMaxResolution",value:function(){var e=navigator.deviceMemory||4,t=Math.round(window.screen.width*window.devicePixelRatio),n=Math.round(window.screen.height*window.devicePixelRatio);return e<=2||t<1280||n<720?"480p":e<=4&&t<1920&&n<1080?"720p":e<=6&&t<2560&&n<1440?"1080p":e<=8&&t<3840&&n<2160?"1440p":3840<=t||2160<=n?"2160p":"720p"}},{key:"injectDownload",value:(A=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t,n,r,i,o,a,s=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(t=0<s.length&&void 0!==s[0]?s[0]:"list",(n=1<s.length&&void 0!==s[1]?s[1]:null)&&n>=this.videoLinks.length)return e.abrupt("return");e.next=5;break;case 5:if("detail"!=t||null===n||!this.videoLinks[n].download_url){e.next=11;break}r={a:"link",s:this.videoLinks[n].size,t:this.videoLinks[n].type,u:encodeURIComponent(Base64.encode(this.videoLinks[n].download_url)),n:encodeURIComponent(Base64.encode(this.videoLinks[n].title))},r=this.config.INJECT_PATH+"?"+new URLSearchParams(r),p(browser.tabs.create,{url:r,active:!0}),e.next=23;break;case 11:return i="".concat(this.tab.id,"-").concat(Date.now(),"-").concat(Math.random()),o=n&&0<=n?[this.videoLinks[n]]:this.videoLinks,e.next=15,localStorageSet(_defineProperty({},i,o));case 15:return o={a:t,t:this.tab.id,i:n,k:encodeURIComponent(Base64.encode(i))},o=this.config.INJECT_PATH+"?"+new URLSearchParams(o),e.next=19,p(browser.tabs.create,{url:o,active:!1});case 19:return a=e.sent,e.next=22,sendMessage({message:"store-injected-linkskey",tabId:a.id,linkskey:i});case 22:p(browser.tabs.update,a.id,{active:!0});case 23:case"end":return e.stop()}},e,this)})),function(){return A.apply(this,arguments)})},{key:"findLinkByAttrs",value:function(e){var t,r=Object.entries(e),i=null,n=_createForOfIteratorHelper(this.videoLinks);try{var o;for(n.s();!(t=n.n()).done;)if(o=function(){var n=t.value;return r.every(function(e){var t=_slicedToArray(e,2),e=t[0],t=t[1];return(null==n?void 0:n[e])===t})?null!=n&&n.default?{v:n}:void(i=i||n):0}(),0!==o&&o)return o.v}catch(e){n.e(e)}finally{n.f()}return i}},{key:"directDownload",value:(C=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t,n,r,i,o,a,s,l,c,u=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(t=null==(i=0<u.length&&void 0!==u[0]?u[0]:null)?this.videoLinks:[this.videoLinks[i]],n=this.config.SKIP_BG_DOWNLOAD.includes(new URL(this.tab.url).hostname),r=!1,null!=this&&this.general){e.next=9;break}return e.next=7,getStoredGeneral();case 7:i=e.sent,this.general=_objectSpread(_objectSpread({},this.config.GENERAL),i);case 9:o=_createForOfIteratorHelper(t);try{for(o.s();!(l=o.n()).done;)c=l.value,s={tabId:this.tab.id,tabUrl:this.tab.url,tabIndex:this.tab.index,skipBgDl:n},c.audioLink&&(s.skipBgDl=!0),c.streamType&&"video"===c.type&&c.audio&&(c.audioLink||(c.audioLink=this.findLinkByAttrs({streamType:c.streamType,type:"audio",groupName:c.audio,manifestUrl:null==c?void 0:c.manifestUrl}))),(c.streamType||s.skipBgDl||c.audioLink)&&1==(null===(l=this.general)||void 0===l?void 0:l.showDownloadStreamTip)&&0==r&&(this.showDownloadStreamModal(),r=!0),null!=(c=_objectSpread({},c))&&c.sid&&c.sid in this.sig&&(c.sig=this.sig[c.sid]),null!=c&&null!==(a=c.audioLink)&&void 0!==a&&a.sid&&c.audioLink.sid in this.sig&&(c.audioLink.sig=this.sig[c.audioLink.sid]),this.downloader.download(c,c.id,s,this.general)}catch(e){o.e(e)}finally{o.f()}case 11:case"end":return e.stop()}},e,this)})),function(){return C.apply(this,arguments)})},{key:"downloadVideos",value:(S=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t,n,r,i=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(r=null!=(n=1<i.length&&void 0!==i[1]?i[1]:null)?!(null===(t=this.videoLinks[n])||void 0===t||!t.streamType)||!(null===(r=this.videoLinks[n])||void 0===r||!r.audioLink):-1!=this.videoLinks.findIndex(function(e){return e.streamType||e.audioLink}),null!=this.license){e.next=15;break}if(!r){e.next=9;break}if(this.checkIncUsedCount("streamDownload"))return this.showlimitModal(),e.abrupt("return");e.next=9;break;case 9:if(!(null!=n?is1080pOrHigher(this.videoLinks[n]):-1!=this.videoLinks.findIndex(function(e){return!(null!=e&&e.streamType)&&is1080pOrHigher(e)}))){e.next=15;break}if(this.checkIncUsedCount("hqDownload"))return this.showlimitModal(),e.abrupt("return");e.next=15;break;case 15:if(r)return e.next=18,this.sendGaEvent("stream-download",{tabUrl:this.tab.url});e.next=20;break;case 18:e.next=22;break;case 20:return e.next=22,this.sendGaEvent("download",{tabUrl:this.tab.url});case 22:this.directDownload(n);case 23:case"end":return e.stop()}},e,this)})),function(){return S.apply(this,arguments)})},{key:"stateListener",value:function(t,e,n){var r=this.videoLinks.findIndex(function(e){return e.id==t||(null==e?void 0:e.download_url)==t});-1!=r&&this.updateState(r,e,n)}},{key:"progressListener",value:function(t,e){var n=this.videoLinks.findIndex(function(e){return e.id==t||(null==e?void 0:e.download_url)==t});-1!=n&&this.updateProgress(n,e)}},{key:"updateState",value:function(e,t,n){this.videoLinks[e].state=browser.i18n.getMessage(t),t!=PopDownloader.STATE.START&&t!=PopDownloader.STATE.FAILED&&t!=PopDownloader.STATE.INTERRUPTED&&t!=PopDownloader.STATE.COMPLETE||(e=this.videoLinks[e].state,t!=PopDownloader.STATE.FAILED&&t!=PopDownloader.STATE.INTERRUPTED||!n.length||(e="".concat(e,":").concat(n)),this.showToast(e))}},{key:"updateProgress",value:function(e,t){this.videoLinks[e].progress=t;t=$("#progress-"+e);t.length&&(t.attr("value",this.videoLinks[e].progress),t.html(this.videoLinks[e].progress))}},{key:"checkIncUsedCount",value:function(e){var t;this.usage||(null==(r=localStorage.getItem("".concat(browser.runtime.id,"_usage")))?this.usage=Object.fromEntries(Object.keys(this.config.FREE_LIMIT).map(function(e){return[e,[]]})):(t=JSON.parse(r),this.usage=this.usage=Object.fromEntries(Object.keys(this.config.FREE_LIMIT).map(function(e){return[e,Array.isArray(null==t?void 0:t[e])?t[e]:[]]}))));var n=this.config.FREE_LIMIT[e];this.usage[e]=this.usage[e].filter(function(e){return e<Date.now()&&Date.now()<e+60*n.hours*60*1e3});var r=this.usage[e].length>=n.maxCount;return r||(this.usage[e].push(Date.now()),localStorage.setItem("".concat(browser.runtime.id,"_usage"),JSON.stringify(this.usage))),r}},{key:"createDownloadSection",value:function(t,n){var r,e=getQualityFromVideoLink(n),i={video:"#B71C1C",audio:"#3F51B5",image:"#673AB7"},o=n.streamType?"#F44336":(null===i?void 0:i[n.type])||"#009688",a=!!(null!=n&&n.audio||null!=n&&n.audioLink),s=null!=n&&n.streamType?browser.i18n.getMessage("Stream"):n.type.toUpperCase(),l='<span style="color: '.concat(o,';"\n        title="').concat(s,'" >').concat(((null==n?void 0:n.ext)||n.streamType||n.type).toUpperCase(),"</span>"),i=n.streamType?'<div class="title" title="'.concat(n.title,'">').concat(l," <b>").concat(e,"</b> ").concat(n.title,"</div>"):'<div class="title" title="'.concat(n.title,'">').concat(l," <b>").concat(e,"</b> ").concat(n.title,".").concat(n.ext,"</div>"),o=!(null==n||!n.contentProtection),s="";n.streamType&&n.audio&&(0<(l=this.videoLinks.filter(function(e){return e.streamType===n.streamType&&"audio"===e.type&&e.groupName===n.audio&&e.manifestUrl===(null==n?void 0:n.manifestUrl)})).length&&(r=function(e){var t,n=null==e||null===(n=e.language)||void 0===n?void 0:n.toUpperCase();return"N/A"===n&&(n=null==e||null===(t=e.name)||void 0===t?void 0:t.toUpperCase()),n||browser.i18n.getMessage("Audio")},e=l.find(function(e){return e.default})||l[0],s='\n                <div class="dropdown text-primary mr-1">\n                    <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" id="audio-dropdown-'.concat(t,'" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">\n                        ').concat(r(e)||browser.i18n.getMessage("Audio"),'\n                    </button>\n                    <div class="dropdown-menu" aria-labelledby="audio-dropdown-').concat(t,'">\n                        ').concat(l.map(function(e){return'\n                            <a class="dropdown-item audio-select" href="#" \n                                data-video-index="'.concat(t,'"\n                                data-audio-id="').concat(e.id,'"\n                                ').concat(e.default?'data-default="true"':"",">\n                                ").concat(r(e),"\n                            </a>\n                        ")}).join(""),"\n                    </div>\n                </div>")));a=o?'<a id="'.concat(t,'" class="drm-button text-primary" title="').concat(browser.i18n.getMessage("videoProtected"),'" section-id="record"><i class="icon-lock">&nbspprotected</i></a>'):"\n            ".concat(s,'\n            <a id="').concat(t,'" class="link-button text-primary" ').concat(null!=n&&n.streamType?"disabled":"",'></a>\n            <a id="').concat(t,'" class="qr-button text-primary" ').concat(null!=n&&n.streamType?"disabled":"",'></a>\n            <a id="').concat(t,'" class="download-button text-primary ').concat(a?"":"bg-success",'" title="').concat(a?browser.i18n.getMessage("mergeDownload"):browser.i18n.getMessage("download"),'"></a>');return'<li class="video">\n        <div class="action">\n            <a id="'.concat(t,'" class="play-button text-primary" title="').concat(browser.i18n.getMessage("play"),'"></a>\n            ').concat(i,"\n            ").concat(a,'\n        </div>\n        <progress id="progress-').concat(t,'" value="0" max="100"></progress>\n        </li>')}},{key:"drowInfoBox",value:function(e,t,n){var r=this;this.enablehightText(t),this.player&&(this.player.dispose(),this.player=null),$("#info-box").remove(),$('<div id="info-box">'.concat(e,"</div>")).insertAfter(n),$("#info-close").click(function(e){e.preventDefault(),r.player&&(r.player.dispose(),r.player=null),$("#info-box").remove(),r.cancelHightLight()})}},{key:"cancelHightLight",value:function(){this.config.INFOBOX_UP&&($("#video-list li:eq(".concat(this.id,") .title")).removeAttr("style"),this.id=null)}},{key:"enablehightText",value:function(e){this.config.INFOBOX_UP&&(this.id&&this.id!=this.videoLinks[e]&&this.cancelHightLight(),this.id=e,$("#video-list li:eq(".concat(e,") .title")).attr("style","color:red"))}},{key:"playVideo",value:function(_,x,k){var T=this;this.sendGaEvent("play");var E='<div id="info-close">&times;</div>\n        <div>\n            <video id="video-player" class="video-js" controls autoplay data-setup=\'{}\'>\n            </video>\n        </div>';this.drowInfoBox(E,x,k),_.streamType&&"video"===_.type&&_.audio&&(_.audioLink||(_.audioLink=this.findLinkByAttrs({streamType:_.streamType,type:"audio",groupName:_.audio,manifestUrl:null==_?void 0:_.manifestUrl})));var e=this.config.SHAKA_PLAYER_LIST.includes(new URL(this.tab.url).hostname)?["Shaka","Html5","flvjs"]:["Html5","Shaka","flvjs"];this.player=playVideoLink(_,"video-player",this.tab.url,e);var s=[],l=!1;this.player.on("xhr-hooks-ready",function(){function a(e,t,n){var r,i,o=T.player.currentSource();e.url==o.src&&["application/x-mpegURL","application/dash+xml"].includes(o.type)&&0==s.length?n.body.includes("#EXTM3U")||n.body.includes("<MPD")||(r=(null==_||null===(r=_.init)||void 0===r||null===(r=r.headers)||void 0===r?void 0:r.referer)||(null==_?void 0:_.initiator)||(null==T?void 0:T.tab.url),updateSessionRules([e.url],r).then(function(e){s=e,T.player.tech(!0).vhs.xhr.offResponse(a),T.player.src(T.player.currentSource())})):0==s.length&&"Error from cloudfront"!==(null===(r=n.headers)||void 0===r?void 0:r["x-cache"])&&(400==n.statusCode||403==n.statusCode?(i=(null==_||null===(i=_.init)||void 0===i||null===(i=i.headers)||void 0===i?void 0:i.referer)||(null==_?void 0:_.initiator)||(null==T?void 0:T.tab.url),updateSessionRules([e.url],i).then(function(e){s=e,T.player.tech(!0).vhs.xhr.offResponse(a),T.player.src(T.player.currentSource())})):[200,206,520].includes(n.statusCode)||l||(l=!0,reportMsg("playVideo error: ".concat(T.tab.url," ").concat(n.statusCode))))}var t;T.player.tech(!0).vhs.xhr.onResponse(a),null!=_&&_.sid&&(t=T.sig[null==_?void 0:_.sid],T.player.tech(!0).vhs.xhr.onRequest(function(e){return e.uri=applySig(e.uri,t),e}))});var S=0;this.player.on("error",function(){var t=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var n,r,i,o,a,s,l,c,u,d,f,p,h,g,m,v,y,b,w;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!(S<=1)){e.next=152;break}if(1==++S&&3==(null===(n=T.player.error())||void 0===n?void 0:n.code)&&null!=_&&_.streamType&&"Html5"==T.player.techName_)return T.player.dispose(),T.player=null,T.drowInfoBox(E,x,k),T.player=playVideoLink(_,"video-player",T.tab.url,["Shaka","Html5"]),e.abrupt("return");e.next=6;break;case 6:if(r=T.player.currentSource(),4!=T.player.error().code&&3!=T.player.error().code){e.next=152;break}if(o=function(e){e=e.headers.get("Content-Type");return!e||e.match("text|html")?null:e},a=function(){var t=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var n;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!(n=t.headers.get("Content-Length"))){e.next=12;break}if(n<103628800)return e.next=5,toBlobURL(t.url);e.next=8;break;case 5:return e.abrupt("return",e.sent);case 8:return e.abrupt("return");case 10:e.next=14;break;case 12:return e.abrupt("return");case 14:case"end":return e.stop()}},e)}));return function(e){return t.apply(this,arguments)}}(),s=T.player.currentSrc(),c=((l=null)==_||null===(i=_.init)||void 0===i||null===(i=i.headers)||void 0===i?void 0:i.referer)||(null==_?void 0:_.initiator)||(null==T?void 0:T.tab.url),!_.streamType){e.next=117;break}if(d=(null==_?void 0:_.url)||(null==_||null===(u=_.segments)||void 0===u||null===(u=u[0])||void 0===u?void 0:u.url),(null==_?void 0:_.manifestUrl)==s)return e.next=18,getResponseInfoSafe(s,{abortData:!!d});e.next=89;break;case 18:if(null!=(f=e.sent)&&f.ok){e.next=55;break}return p=d?[s,d]:[s],e.next=23,updateSessionRules(p,c);case 23:return l=e.sent,e.next=26,getResponseInfoSafe(s,{abortData:!!d});case 26:if(!(f=e.sent)){e.next=51;break}if(d)return e.next=31,getResponseInfoSafe(d);e.next=49;break;case 31:if(null!=(f=e.sent)&&f.ok){e.next=47;break}return e.next=35,getResponseInfoSafe(d,{redirect:"manual"});case 35:if(f=e.sent,null!==(p=f)&&void 0!==p&&p.url&&f.url!=d)return d=f.url,e.t0=l,e.next=41,updateSessionRules([d],c);e.next=45;break;case 41:e.t1=e.sent,e.t0.concat.call(e.t0,e.t1),e.next=47;break;case 45:return e.abrupt("return");case 47:e.next=49;break;case 49:e.next=53;break;case 51:return e.abrupt("return");case 53:e.next=87;break;case 55:if(null==o(f))return e.abrupt("return");e.next=58;break;case 58:if(d)return e.next=61,getResponseInfoSafe(d);e.next=87;break;case 61:if(null==(f=e.sent))return e.next=65,updateSessionRules(urls,c);e.next=68;break;case 65:l=e.sent,e.next=85;break;case 68:if(!f.ok){e.next=83;break}if(null==_||!_.url){e.next=79;break}if(null==(h=o(f)))return e.abrupt("return");e.next=73;break;case 73:return e.next=75,a(f);case 75:(v=e.sent)&&(s=v,r.type=h),e.next=81;break;case 79:return e.abrupt("return");case 81:e.next=85;break;case 83:return e.abrupt("return");case 85:e.next=87;break;case 87:e.next=115;break;case 89:return e.next=91,getResponseInfoSafe(d);case 91:if(null==(g=e.sent))return e.next=95,updateSessionRules([d],c);e.next=98;break;case 95:l=e.sent,e.next=115;break;case 98:if(!g.ok){e.next=113;break}if(null==_||!_.url){e.next=109;break}if(null==(m=o(g)))return e.abrupt("return");e.next=103;break;case 103:return e.next=105,a(g);case 105:(v=e.sent)&&(s=v,r.type=m),e.next=111;break;case 109:return e.abrupt("return");case 111:e.next=115;break;case 113:return e.abrupt("return");case 115:e.next=151;break;case 117:return e.next=119,getResponseInfoSafe(s);case 119:if(null!=(y=e.sent)&&y.ok){e.next=144;break}return e.next=123,updateSessionRules([s],c);case 123:return l=e.sent,e.next=126,getResponseInfoSafe(s);case 126:if(null==(y=e.sent))return e.next=130,getResponseInfoSafe(s,{redirect:"manual"});e.next=142;break;case 130:if(y=e.sent,null!==(w=y)&&void 0!==w&&w.url&&y.url!=s)return s=y.url,e.t2=l,e.next=136,updateSessionRules([s],c);e.next=140;break;case 136:e.t3=e.sent,e.t2.concat.call(e.t2,e.t3),e.next=142;break;case 140:return e.abrupt("return");case 142:e.next=151;break;case 144:if(null==(b=o(y)))return e.abrupt("return");e.next=147;break;case 147:return e.next=149,a(y);case 149:(w=e.sent)&&(s=w,r.type=b);case 151:T.player.src(_objectSpread(_objectSpread({},r),{},{src:s}));case 152:case"end":return e.stop()}},e)}));return function(e){return t.apply(this,arguments)}}())}},{key:"showImage",value:function(e,t,n){e='<div class="bg-dark" ><img class="figure-img img-fluid rounded" src="'+e+'"/></div>';this.drowInfoBox(e,t,n)}},{key:"showYoutubeNotAllowed",value:function(e,t){var n='\n        <div id="info-close">&times;</div>\n        <div class="mt-2">\n            <div class="alert alert-danger">'.concat(browser.i18n.getMessage("notPermitted"),"\n        <p>").concat(browser.i18n.getMessage("disableyoutube"),"</p></div>\n            </video>\n        </div>");this.drowInfoBox(n,e,t)}},{key:"qrCode",value:function(e,t,n){this.sendGaEvent("qrcode");e='<div><img id="qr_image" src="'+new QRious({value:e,size:280,foreground:"#3b6c96"}).toDataURL()+'"/></div>';this.drowInfoBox(e,t,n)}},{key:"showBtnBox",value:function(){var e=$("#btn-box"),t=(browser.i18n.getMessage("downloadbest"),'<button id="downloadall" class="btn btn-sm btn-primary border-primary">'+browser.i18n.getMessage("downloadall")+"</button>");e.html(t),e.show()}},{key:"closeBtnBox",value:function(){$("#btn-box").hide().html("")}},{key:"updateVideoLinks",value:(E=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var n,r,i,o;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:n=this.videoLinks.map(function(e){return e.id}),r=[],i=_createForOfIteratorHelper(t);try{for(i.s();!(o=i.n()).done;)o=o.value,n.includes(o.id)||r.push(o)}catch(e){i.e(e)}finally{i.f()}if(r.length<=0)return e.abrupt("return");e.next=6;break;case 6:this.addVideoLinkstoList(r);case 7:case"end":return e.stop()}},e,this)})),function(e){return E.apply(this,arguments)})},{key:"getTabDataFromBg",value:(T=_asyncToGenerator(_regeneratorRuntime().mark(function e(){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,getDataFromBg("get-video-links",{tabId:this.tab.id},this.tab.url);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e,this)})),function(){return T.apply(this,arguments)})},{key:"getTabDataFromStorage",value:(k=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t,n;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.tab.id,e.next=3,localStorageGet(String(t));case 3:if(null!=(n=e.sent)&&n.hasOwnProperty(String(t)))return e.abrupt("return",n[String(t)]);e.next=6;break;case 6:return e.abrupt("return",null);case 7:case"end":return e.stop()}},e,this)})),function(){return k.apply(this,arguments)})},{key:"getTabData",value:(x=_asyncToGenerator(_regeneratorRuntime().mark(function e(){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.getTabDataFromStorage());case 1:case"end":return e.stop()}},e,this)})),function(){return x.apply(this,arguments)})},{key:"getAddVideoList",value:(_=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t,n;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(this.testYoutube(this.tab.url))return $("section[id=popup] #go-spinner").addClass("d-none"),this.youtubeAlert(),this.hideVideoInfo(),this.closeBtnBox(),e.abrupt("return");e.next=6;break;case 6:return e.next=8,this.getTabData();case 8:if(null!=(n=e.sent)&&n.sig&&(this.sig=n.sig),$("section[id=popup] #go-spinner").addClass("d-none"),null!=n&&n.url&&0<(null==n||null===(t=n.videoLinks)||void 0===t?void 0:t.length)){e.next=17;break}return this.noVideoFound(),this.hideVideoInfo(),this.closeBtnBox(),e.abrupt("return");case 17:this.addVideoLinkstoList(n.videoLinks);case 18:case"end":return e.stop()}},e,this)})),function(){return _.apply(this,arguments)})},{key:"addVideoLinkstoList",value:(w=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var n,r,i,o,a,s=this;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(r=!1,i=$("#video-list"),t.length<=0)return e.abrupt("return");e.next=5;break;case 5:this.noVideoIsShow()&&(this.showRateUs(),this.hideNoVideoFound(),this.showVideoInfo()),o=this.videoLinks.length,(n=this.videoLinks).push.apply(n,_toConsumableArray(t)),t.forEach(function(e,t){i.append(s.createDownloadSection(o+t,e))}),1<this.videoLinks.length&&(a=0,this.best_index=0,r=this.videoLinks.every(function(e,t){return e.size>a&&(a=e.size,s.best_index=t),e.title==s.videoLinks[0].title})),this.showBtnBox(!1,r,!0);case 11:case"end":return e.stop()}},e,this)})),function(e){return w.apply(this,arguments)})},{key:"noVideoIsShow",value:function(){return"block"==$("#no-video-found").css("display")}},{key:"showVideoInfo",value:function(){$("#video-info").show()}},{key:"hideVideoInfo",value:function(){$("#video-info").hide()}},{key:"hideNoVideoFound",value:function(){$("#no-video-found").hide()}},{key:"noVideoFound",value:function(){var e="\n        <div>\n            <h1>".concat(browser.i18n.getMessage("notfound"),"</h1>\n            <p>").concat(browser.i18n.getMessage("novidehelp"),"</p>\n        </div>\n        <p>").concat(browser.i18n.getMessage("popularSites"),'\n        <a href="https://www.facebook.com" target="_blank">Facebook</a>,\n        <a href="https://www.instagram.com" target="_blank">Instagram</a>,\n        <a href="https://www.pinterest.com" target="_blank">Pinterest</a>,\n        <a href="https://www.tiktok.com" target="_blank">TikTok</a>,\n        <a href="https://www.twitter.com" target="_blank">Twitter</a>,\n        <a href="https://www.dailymotion.com" target="_blank">Dailymotion</a>,\n        <a href="https://www.vimeo.com" target="_blank">Vimeo</a>,\n        <a href="https://www.twitch.tv/" target="_blank">Twitch</a></p>\n        <div class="row">\n            <div class="col-6"><button section-id="record" class="btn btn-block btn-primary border-primary">').concat(browser.i18n.getMessage("Record"),'</button></div>\n            <div class="col-6"><button id="refresh" class="btn btn-block btn-primary border-primary">').concat(browser.i18n.getMessage("refresh"),"</button></div>\n        </div>");$("#no-video-found").show().html(e)}},{key:"youtubeAlert",value:function(){var e='\n        <div class="alert alert-danger">\n            <h1>'.concat(browser.i18n.getMessage("notAllowed"),"</h1>\n            <p>").concat(browser.i18n.getMessage("disableyoutube"),"</p>\n        </div>\n        <p>").concat(browser.i18n.getMessage("popularSites"),'\n        <a href="https://www.facebook.com" target="_blank">Facebook</a>,\n        <a href="https://www.instagram.com" target="_blank">Instagram</a>,\n        <a href="https://www.pinterest.com" target="_blank">Pinterest</a>,\n        <a href="https://www.tiktok.com" target="_blank">TikTok</a>,\n        <a href="https://www.twitter.com" target="_blank">Twitter</a>,\n        <a href="https://www.dailymotion.com" target="_blank">Dailymotion</a>,\n        <a href="https://www.vimeo.com" target="_blank">Vimeo</a>,\n        <a href="https://www.twitch.tv/" target="_blank">Twitch</a></p>\n        <div class="row">\n            <div class="col-6"><button section-id="record" class="btn btn-block btn-primary border-primary">').concat(browser.i18n.getMessage("Record"),'</button></div>\n            <div class="col-6"><button id="refresh" class="btn btn-block btn-primary border-primary">').concat(browser.i18n.getMessage("refresh"),"</button></div>\n        </div>");$("#no-video-found").show().html(e)}},{key:"testYoutube",value:function(){var e=(e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:null)||this.tab.url;return!!this.config.DISALLOW_YOUTUBE&&/^(?:(?:https?:)?\/\/)?(?:[^\/]*\.)?youtube.com/.test(e)}},{key:"openWindow",value:function(e){this.testYoutube(e)?this.youtubeAlert():(e=this.config.QUERY_PATH+e,browser.tabs.create({url:e,active:!0}))}},{key:"serverSideQuery",value:(b=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t=$("#text-input").val(),$("#text-input").val(""),e.next=4,this.sendGaEvent("query");case 4:""!=t?this.openWindow(encodeURI(t)):this.openWindow(this.tab.url);case 5:case"end":return e.stop()}},e,this)})),function(){return b.apply(this,arguments)})},{key:"showToast",value:function(e){$("#toast").html(e).addClass("show"),setTimeout(function(){$("#toast").removeClass("show").html("")},3e3)}},{key:"copyUrl",value:function(t){this.sendGaEvent("copyUrl"),document.addEventListener("copy",function(e){e.clipboardData.setData("text/plain",t),e.preventDefault()},!0),document.execCommand("copy"),this.showToast(browser.i18n.getMessage("copyurl"))}},{key:"button_event",value:(y=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,n,r){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(this.testYoutube())return this.info={id:n.currentTarget.id,target:"nowAllowed"},this.showYoutubeNotAllowed(n.currentTarget.id,this.config.INFOBOX_UP?"#search-box":n.currentTarget.parentElement),e.abrupt("return");e.next=4;break;case 4:t.download_url||t.streamType?r(t):this.showToast(browser.i18n.getMessage("notfound"));case 5:case"end":return e.stop()}},e,this)})),function(e,t,n){return y.apply(this,arguments)})},{key:"refresh",value:function(){p(browser.tabs.reload,this.tab.id)}},{key:"contactUs",value:function(){var e=this.config.CONTACT_US;p(browser.tabs.create,{url:e,active:!0})}},{key:"updateHeaderBar",value:function(){var e;this.license?(e=this.license.customer_name||browser.i18n.getMessage("Deactivate"),$("#header-bar > #license").html('<div class="btn btn-sm btn-outline-primary border-0 px-2" title="'.concat(e,'" section-id="deactivate"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" class="bi bi-key-fill" viewBox="0 0 16 16">\n            <path d="M3.5 11.5a3.5 3.5 0 1 1 3.163-5H14L15.5 8 14 9.5l-1-1-1 1-1-1-1 1-1-1-1 1H6.663a3.5 3.5 0 0 1-3.163 2M2.5 9a1 1 0 1 0 0-2 1 1 0 0 0 0 2"></path>\n        </svg></div>'))):$("#header-bar > #license").html('<div class="btn btn-sm btn-outline-primary border-0 px-2" title="'.concat(browser.i18n.getMessage("LicenseKey"),'" section-id="activate"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" class="bi bi-key" viewBox="0 0 16 16">\n            <path d="M0 8a4 4 0 0 1 7.465-2H14a.5.5 0 0 1 .354.146l1.5 1.5a.5.5 0 0 1 0 .708l-1.5 1.5a.5.5 0 0 1-.708 0L13 9.207l-.646.647a.5.5 0 0 1-.708 0L11 9.207l-.646.647a.5.5 0 0 1-.708 0L9 9.207l-.646.647A.5.5 0 0 1 8 10h-.535A4 4 0 0 1 0 8m4-3a3 3 0 1 0 2.712 4.285A.5.5 0 0 1 7.163 9h.63l.853-.854a.5.5 0 0 1 .708 0l.646.647.646-.647a.5.5 0 0 1 .708 0l.646.647.646-.647a.5.5 0 0 1 .708 0l.646.647.793-.793-1-1h-6.63a.5.5 0 0 1-.451-.285A3 3 0 0 0 4 5"/>\n            <path d="M4 8a1 1 0 1 1-2 0 1 1 0 0 1 2 0"/>\n        </svg></div>'))}},{key:"selectSection",value:function(n){$("section[id").each(function(e,t){$(t).attr("id")===n?$(t).removeClass("d-none"):$(t).hasClass("d-none")||$(t).addClass("d-none")})}},{key:"updateSettingsUi",value:(v=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t,n;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(null!=this&&this.general){e.next=5;break}return e.next=3,getStoredGeneral();case 3:t=e.sent,this.general=_objectSpread(_objectSpread({},this.config.GENERAL),t);case 5:if(this.initGeneralUi(),null!=this&&this.filter){e.next=13;break}return e.next=9,getStoredFilter();case 9:return n=e.sent,e.next=12,updateFilter(n,this.config.MEDIA_FILTER);case 12:this.filter=e.sent;case 13:if(this.initFilterUi(),null!=this&&this.csplist){e.next=19;break}return e.next=17,getStoredCspList();case 17:n=e.sent,this.csplist=null==n?this.config.CSP_LIST.map(function(e){return{domain:e,enable:!0}}):n;case 19:this.initAudioFixerUi(this.csplist);case 20:case"end":return e.stop()}},e,this)})),function(){return v.apply(this,arguments)})},{key:"initGeneralUi",value:(m=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t,n=this;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:t='\n        <div class="container">\n            <div class="row pt-3">\n                <div class="col-md-12">\n                    <div class="text-center pb-3">\n                        <h1>'.concat(browser.i18n.getMessage("GeneralSettings"),'</h1>\n                    </div>\n                </div>\n            </div>\n            <div class="font-weight-bold border-bottom">').concat(browser.i18n.getMessage("ShowTips"),'</div>\n            <div class="form-check-inline">\n                <input id="showVideoElRecordTip" type="checkbox" class="form-check-input" ').concat(this.general.showVideoElRecordTip?"checked":"",'>\n                <label class="form-check-label">').concat(browser.i18n.getMessage("ShowVideoElRecordTip"),'</label>\n            </div>\n            <div class="form-check-inline">\n                <input id="showDownloadStreamTip" type="checkbox" class="form-check-input" ').concat(this.general.showDownloadStreamTip?"checked":"",'>\n                <label class="form-check-label">').concat(browser.i18n.getMessage("showDownloadStreamTip"),'</label>\n            </div>\n            <div class="form-group">\n                <label class="form-check-label">').concat(browser.i18n.getMessage("SetRecordCountDown"),'</label>\n                <input id="recordCountDown" type="number" class="form-control" value="').concat(this.general.recordCountDown,'" min="0" max="99">\n            </div>\n            <div class="form-check-inline">\n                <input id="useDedicatedDownloader" type="checkbox" class="form-check-input" ').concat(this.general.useDedicatedDownloader?"checked":"",'>\n                <label class="form-check-label">').concat(browser.i18n.getMessage("useDedicatedDownloader"),"</label>\n            </div>\n        </div>\n        "),$("section[id=settings] [id=myTabContent] [id=general]").html(t),$("body").on("change","section[id=settings] [id=general] .form-check-input",function(e){var t=$(e.currentTarget).prop("checked");n.general[e.target.id]=!!t,storeGeneral(n.general)}),$("body").on("change","section[id=settings] [id=general] #recordCountDown",function(e){var t=$(e.currentTarget).val();n.general[e.target.id]=t,storeGeneral(n.general)});case 4:case"end":return e.stop()}},e,this)})),function(){return m.apply(this,arguments)})},{key:"initFilterUi",value:function(){var e,i=this;$("section[id=settings] [id=myTabContent] [id=filter]:has(div)").length||(e='\n            <div class="container">\n                <div class="row pt-3">\n                    <div class="col-md-12">\n                        <div class="text-center pb-3">\n                            <h1>'.concat(browser.i18n.getMessage("fileSizeFilter"),'</h1>\n                        </div>\n                    </div>\n                </div>\n                <div class="row">\n                    <div class="col">\n                        <div class="card border-primary rounded-lg shadow">\n                            <div class="card-body bg-light rounded">\n                                <form>\n                                <div class="font-weight-bold">').concat(browser.i18n.getMessage("smallSizeFilter"),"</div>\n                                ").concat(Object.entries(this.filter).map(function(e){var t=_slicedToArray(e,2),n=t[0],e=t[1];return'<div class="border-bottom">'.concat(n," ").concat(browser.i18n.getMessage("Options"),'</div>\n                                    <div class="form-inline my-2">\n                                        <div class="form-check-inline">\n                                            <input type="checkbox" class="form-check-input" ').concat(0==(null===(t=i.config.MEDIA_FILTER[n])||void 0===t?void 0:t.candisable)?"disabled":"",' data-filtertype="').concat(n,'"  ').concat(e.enable?"checked":"",'>\n                                            <label class="form-check-label">').concat(browser.i18n.getMessage("Enable")," ").concat(n,'</label>\n                                        </div>\n                                        <div class="d-flex align-items-center ml-auto">\n                                            <span id="').concat(n,'-size" class="mr-1">').concat(formatBytes(e.size),'</span>\n                                        </div>\n                                    </div>\n                                    <div class="form-group">\n                                        <input type="range" class="form-control-range" data-filtertype="').concat(n,'" step="').concat(i.config.MEDIA_FILTER[n].step,'" min="').concat(i.config.MEDIA_FILTER[n].range[0],'" max="').concat(i.config.MEDIA_FILTER[n].range[1],'" value=').concat(e.size,">\n                                    </div>\n                                    ")}).join(""),'\n                                    <button id="reset" class="btn btn-block btn-primary border-primary text-uppercase rounded-lg">').concat(browser.i18n.getMessage("Reset"),'</button>\n                                </form>\n                                <div class="mt-1"><i class="icon-info text-primary mr-1"></i>').concat(browser.i18n.getMessage("applySettings"),"</div>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        "),$("section[id=settings] [id=myTabContent] [id=filter]").html(e),$("body").on("click","section[id=settings] [id=filter] #reset",function(e){for(var t in e.preventDefault(),i.config.MEDIA_FILTER){var n=i.config.MEDIA_FILTER[t],r=n.enable,n=n.size;i.filter[t]={enable:r,size:n},$("section[id=settings] [id=filter] .form-check-input[data-filtertype=".concat(t)).val(r),$("section[id=settings] [id=filter] .form-control-range[data-filtertype=".concat(t)).val(n),$("section[id=settings] [id=filter] #".concat(t,"-size")).text(formatBytes(n))}storeFilter(i.filter)}),$("body").on("change","section[id=settings] [id=filter] .form-check-input",function(e){var t=$(e.currentTarget).data("filtertype"),e=$(e.currentTarget).prop("checked");i.filter[t].enable=e,storeFilter(i.filter)}),$("body").on("input","section[id=settings] [id=filter] .form-control-range",function(e){var t=$(e.currentTarget).data("filtertype"),e=$(e.currentTarget).val();i.filter[t].size=parseInt(e),$("section[id=settings] [id=filter] #".concat(t,"-size")).text(formatBytes(e)),storeFilter(i.filter)}))}},{key:"isDomainInCsplist",value:function(t){return this.csplist.some(function(e){return e.domain===t})}},{key:"initAudioFixerUi",value:(g=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var n,r=this;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if($("section[id=settings] [id=myTabContent] [id=audioFixer]:has(div)").length)return e.abrupt("return");e.next=2;break;case 2:n='\n        <div class="container">\n            <div class="row pt-3">\n                    <div class="col-md-12">\n                        <div class="text-center pb-3">\n                            <h1>'.concat(browser.i18n.getMessage("hlsDashAudioFixer"),'</h1>\n                        </div>\n                    </div>\n                </div>\n            <p class="text-left">').concat(browser.i18n.getMessage("AudioFixerDesc"),'</p>\n            <table class="table table-striped">\n                <thead class="thread-dark">\n                    <tr>\n                        <th scope="col" style="width: 35%">').concat(browser.i18n.getMessage("Domain"),'</th>\n                        <th scope="col" style="width: 25%">').concat(browser.i18n.getMessage("Status"),'</th>\n                        <th scope="col" style="width: 40%">').concat(browser.i18n.getMessage("Actions"),'</th>\n                    </tr>\n                </thead>\n                <tbody id="csp-list">\n                ').concat(t.map(function(e,t){return r.createCspItem(t,e)}).join(""),'\n                </tbody>\n            </table>\n            <div class="row">\n                <div class="col-7">\n                    <input type="text" class="form-control" id="new-domain-input" placeholder="').concat(browser.i18n.getMessage("inputDomainLike"),' \'xxx.com\'" />\n                </div>\n                <div class="col-5">\n                    <button class="btn btn-primary  add-domain-btn">').concat(browser.i18n.getMessage("addDomain"),"</button>\n                </div>\n                ").concat(this.tab.url.startsWith("http")?'\n                <div class="col-12 mt-3">\n                    <button class="btn btn-primary btn-block add-domain-from-tab-btn">'.concat(browser.i18n.getMessage("addDomainFromCurrTab"),"</button>\n                </div>"):"",'\n                <div class="col-12 mt-3">\n                    <button id="reset" class="btn btn-block btn-outline-primary border-primary text-uppercase rounded-lg">').concat(browser.i18n.getMessage("Reset"),'</button>\n                </div>\n                <div class="col-12 mt-1">\n                    <i class="icon-info text-danger mr-1"></i>').concat(browser.i18n.getMessage("addDomainNote"),"\n                </div>\n            </div>\n        </div>\n        "),$("section[id=settings] [id=myTabContent] [id=audioFixer]").html(n),$("body").on("click","section[id=settings] [id=myTabContent] [id=audioFixer] .delete-btn",function(e){e=$(e.target).closest("tr").data("index");0<=e&&e<r.csplist.length&&(r.csplist.splice(e,1),storeCspList(r.csplist),r.updateCspUi(r.csplist))}),$("body").on("click","section[id=settings] [id=myTabContent] [id=audioFixer] .enable-btn",function(e){e=$(e.target).closest("tr").data("index");r.csplist[e].enable=!r.csplist[e].enable,storeCspList(r.csplist),r.updateCspUi(r.csplist)}),$("body").on("click","section[id=settings] [id=myTabContent] [id=audioFixer] .add-domain-btn",function(e){e.preventDefault();var t=$("section[id=settings] [id=myTabContent] [id=audioFixer] #new-domain-input").val();t?r.isDomainInCsplist(t)?alert(browser.i18n.getMessage("domainAlreadyAdded")):(r.csplist.push({domain:t,enable:!0}),p(browser.tabs.query,{url:"*://*/*",status:"complete"}).then(function(e){e.forEach(function(e){new URL(e.url).hostname.includes(t)&&browser.tabs.reload(e.id,{bypassCache:!0})})}),storeCspList(r.csplist),r.updateCspUi(r.csplist)):r.showToast(browser.i18n.getMessage("invalidDomain"))}),$("body").on("click","section[id=settings] [id=myTabContent] [id=audioFixer] .add-domain-from-tab-btn",function(e){var t=new URL(r.tab.url).hostname;r.isDomainInCsplist(t)?r.showToast(browser.i18n.getMessage("domainAlreadyAdded")):(r.csplist.push({domain:t,enable:!0}),browser.tabs.reload(r.tab.id,{bypassCache:!0}),storeCspList(r.csplist),r.updateCspUi(r.csplist))}),$("body").on("click","section[id=settings] [id=myTabContent] [id=audioFixer] #reset",function(e){r.csplist=r.config.CSP_LIST.map(function(e){return{domain:e,enable:!0}}),storeCspList(r.csplist),r.updateCspUi(r.csplist)});case 9:case"end":return e.stop()}},e,this)})),function(e){return g.apply(this,arguments)})},{key:"createCspItem",value:function(e,t){return'\n        <tr data-index="'.concat(e,'">\n            <td class="align-middle">').concat(t.domain,'</td>\n            <td class="align-middle">').concat(browser.i18n.getMessage(t.enable?"Enable":"Disable"),'</td>\n            <td>\n                <button class="btn btn-sm btn-outline-danger delete-btn">').concat(browser.i18n.getMessage("Delete"),'</button>\n                <button class="ml-2 btn btn-sm btn-outline-primary enable-btn">').concat(browser.i18n.getMessage(t.enable?"Disable":"Enable"),"</button>\n            </td>\n        </tr>")}},{key:"updateCspUi",value:(h=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var n,r=this;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:n=t.map(function(e,t){return r.createCspItem(t,e)}).join(""),$("section[id=settings] [id=myTabContent] [id=audioFixer] [id=csp-list]").html(n);case 2:case"end":return e.stop()}},e)})),function(e){return h.apply(this,arguments)})},{key:"initListener",value:function(){var o=this;browser.runtime.onMessage.addListener(function(e,t,n){var r,i;"update-video-links"===e.message&&(e.tabId==o.tab.id&&(null!=e&&null!==(r=e.videoLinks)&&void 0!==r&&r.length&&o.updateVideoLinks(e.videoLinks),null==e||null===(i=e.sig)||void 0===i||!i.sid||e.sig in o.sig||(o.sig[e.sig.sid]=e.sig)),n())}),$("body").on("click","[section-id]",function(){var t=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var n,r;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:n=t.currentTarget.getAttribute("section-id"),o.selectSection(n),$("nav #navbarCollapse").hasClass("show")&&$('nav [id="nav-btn"]').dropdown("toggle"),"activate"==n||"deactivate"==n?$("section[id=".concat(n,"] form div[id=result]")).html("").addClass("d-none"):"settings"==n?o.updateSettingsUi():"feedback"==n&&0==$("section[id=".concat(n,"]:has(div)")).length&&(r='<a href="'.concat(o.config.CONTACT_US,'">').concat(o.config.CONTACT_US.replace("mailto:",""),"</a>"),r='\n                    <div style="height: 400px; overflow-y: scroll;">\n                        <div class="container">\n                        <div class="row justify-content-center">\n                            <div class="col-12 col-lg-8 text-center">\n                                <h1 class="pb-3">'.concat(browser.i18n.getMessage("contact"),'</h1>\n                            </div>\n                        </div>\n                            <div class="mx-2">').concat(browser.i18n.getMessage("contactFeedback").replace("{{1}}",r),'</div>\n                        </div>\n                        <iframe scrolling="no" src="https://docs.google.com/forms/d/e/1FAIpQLSfWYNojNP8CJE9jBeUFP6wEunV1dvAUqYIRSH1O329TIuPCuQ/viewform?embedded=true" width="100%" height="950px" frameborder="0" marginheight="0" marginwidth="0">Loading…</iframe>\n                    </div>'),$("section[id=".concat(n,"]")).html(r));case 4:case"end":return e.stop()}},e)}));return function(e){return t.apply(this,arguments)}}()),$("body").on("change","section[id=activate] form input",function(){var t=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:$("section[id=activate] form div[id=result]").html("").addClass("d-none");case 1:case"end":return e.stop()}},e)}));return function(e){return t.apply(this,arguments)}}()),$("body").on("click","section[id=activate] button[id=activate]",function(){var t=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var n;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t.preventDefault(),$("section[id=activate] #go-spinner").removeClass("d-none"),n=$("section[id=activate] form input[id=license]").val(),$("section[id=activate] form input, section[id=activate] form button").prop("disabled",!0),$("section[id=activate] form div[id=result]").html("").addClass("d-none"),e.next=7,o.activate(n);case 7:1==(n=e.sent).activated?(o.license=n.licenseInfo,setTimeout(function(){return o.selectSection("popup")},3e3),$("section[id=activate] form div[id=result]").html('<div class="alert alert-success">'.concat(browser.i18n.getMessage("licenseActivated"),"</div>")).removeClass("d-none")):$("section[id=activate] form div[id=result]").html('<div class="alert alert-danger">'.concat(browser.i18n.getMessage("licenseFailedWith"),": ").concat(n.error,"</div>")).removeClass("d-none"),$("section[id=activate] #go-spinner").addClass("d-none"),$("section[id=activate] form input, section[id=activate] form button").prop("disabled",!1).val(""),o.updateHeaderBar();case 13:case"end":return e.stop()}},e)}));return function(e){return t.apply(this,arguments)}}()),$("body").on("click","section[id=deactivate] button[id=deactivate]",function(){var t=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var n;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t.preventDefault(),$("section[id=deactivate] form button").prop("disabled",!0),$("section[id=deactivate] #go-spinner").removeClass("d-none"),e.next=5,o.deactivate();case 5:1==(n=e.sent).deactivated?(o.license=n.licenseInfo,setTimeout(function(){return o.selectSection("activate")},3e3),$("section[id=deactivate] form div[id=result]").html('<div class="alert alert-success">'.concat(browser.i18n.getMessage("DeactivateSuccess"),"</div>")).removeClass("d-none")):$("section[id=deactivate] form div[id=result]").html('<div class="alert alert-danger">'.concat(n.error,"</div>")).removeClass("d-none"),$("section[id=deactivate] #go-spinner").addClass("d-none"),$("section[id=deactivate] form button").prop("disabled",!1).val(""),o.updateHeaderBar();case 10:case"end":return e.stop()}},e)}));return function(e){return t.apply(this,arguments)}}()),$("body").on("click","section[id=pricing] a",function(){var t=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t.preventDefault(),e.next=3,o.sendGaEvent("checkout",{tabUrl:o.tab.url});case 3:p(browser.tabs.create,{url:t.target.href,active:!0});case 4:case"end":return e.stop()}},e)}));return function(e){return t.apply(this,arguments)}}()),$("body").on("click",".play-button",function(t){t.preventDefault();var e=o.videoLinks[t.currentTarget.id];if(o.info&&o.info.id==t.currentTarget.id&&("video"==o.info.target||"image"==o.info.target))return o.player&&(o.player.dispose(),o.player=null),$("#info-box").remove(),o.cancelHightLight(),void(o.info=null);"image"==e.type?(o.info={id:t.currentTarget.id,target:"image"},o.button_event(e,t,function(e){o.showImage(e.download_url,t.currentTarget.id,o.config.INFOBOX_UP?"#search-box":t.currentTarget.parentElement)})):(o.info={id:t.currentTarget.id,target:"video"},o.button_event(e,t,function(e){o.playVideo(e,t.currentTarget.id,o.config.INFOBOX_UP?"#search-box":t.currentTarget.parentElement)}))}),$("body").on("click",".download-button",function(t){t.preventDefault();var e=o.videoLinks[t.currentTarget.id];o.button_event(e,t,function(e){o.downloadVideos("detail",t.currentTarget.id)})}),$("body").on("click","#downloadbest",function(e){var t=o.best_index||0;o.downloadVideos("detail",t)}),$("body").on("click","#downloadall",function(e){o.downloadVideos()}),$("body").on("click","#refresh",function(e){o.refresh()}),$("body").on("click","#contactus",function(e){o.contactUs()}),$("body").on("click",".link-button",function(e){e.preventDefault();var t=o.videoLinks[e.currentTarget.id],n=t.streamType?t.url||t.manifestUrl:t.download_url;o.button_event(t,e,function(e){o.copyUrl(n)})}),$("body").on("click",".qr-button",function(t){t.preventDefault();var n,e=o.videoLinks[t.currentTarget.id];o.info&&o.info.id==t.currentTarget.id&&"qrcode"==o.info.target?(o.player&&(o.player.dispose(),o.player=null),$("#info-box").remove(),o.cancelHightLight(),o.info=null):(o.info={id:t.currentTarget.id,target:"qrcode"},n=e.streamType?e.url||e.manifestUrl:e.download_url,o.button_event(e,t,function(e){o.qrCode(n,t.currentTarget.id,o.config.INFOBOX_UP?"#search-box":t.currentTarget.parentElement)}))}),$("body").on("click","#query-button",function(e){o.serverSideQuery()}),$("body").on("keypress","#text-input",function(e){13==e.which&&o.serverSideQuery()}),$("body").on("paste","#text-input",function(e){var t="",t=o.isIE()?window.clipboardData.getData("text"):e.originalEvent.clipboardData.getData("text/plain");$("#text-input").val(t),o.serverSideQuery()}),$("body").on("click","#modal a",function(e){o.toggleModal()}),$("body").on("click","#modal",function(e){e.target==e.currentTarget&&o.toggleModal()}),$("body").on("click",".audio-select",function(e){e.preventDefault();var t=$(e.currentTarget).data("video-index"),n=$(e.currentTarget).data("audio-id"),r=o.videoLinks[t];r.audioLink=o.findLinkByAttrs({streamType:r.streamType,type:"audio",id:n,groupName:r.audio,manifestUrl:null==r?void 0:r.manifestUrl});e=$(e.currentTarget).text();$("#audio-dropdown-".concat(t)).text(e)})}},{key:"showModal",value:function(e){$("#modal .modal_content").html(e),$("#modal").addClass("show-modal")}},{key:"toggleModal",value:function(){$("#modal").toggleClass("show-modal")}},{key:"showQuery",value:function(){var e;this.config.ENABLE_POPUP_QUERY&&(e='<div class="has-search">\n                <i class="icon-magnifier text-primary"></i>\n                <input id="text-input" type="text" class="form-control" placeholder="'.concat(browser.i18n.getMessage("placeholder"),'">\n                <button class="btn btn-sm btn-primary border-primary" id="query-button">').concat(browser.i18n.getMessage("search"),"</button>\n            </div>"),$("#search-box").html(e))}},{key:"showRateUs",value:function(){var e;this.config.RATE_US&&(e='<div class="mb-2" >\n                '.concat(browser.i18n.getMessage("rateus"),' <a target="_blank" class="text-primary" href="').concat(this.config.RATE_US,'">\n                    <img src="/images/stars.png"/>\n                </a>\n            </div>'),$("#rate-us").append(e))}},{key:"updateMenuInfo",value:function(){var e,t=[],n=_defineProperty({},browser.i18n.getMessage("FAQ"),this.config.FAQ);for(e in n)t.push('\n                <li class="nav-item">\n                    <a class="nav-link text-dark" target="blank" href="'.concat(n[e],'">').concat(e,"</a>\n                </li>\n            "));$("nav.navbar ul.navbar-nav").append(t.join(""))}},{key:"drowFootInfo",value:function(){var e,t,n=(_defineProperty(o={},browser.i18n.getMessage("Home"),"popup"),_defineProperty(o,browser.i18n.getMessage("Pricing"),"pricing"),_defineProperty(o,browser.i18n.getMessage("Contact"),"feedback"),o),r=_defineProperty({},browser.i18n.getMessage("FAQ"),this.config.FAQ),i=[];for(e in n)i.push('<a class="text-primary" section-id="'.concat(n[e],'">').concat(e,"</a>"));for(t in r)i.push(' <a target="_blank" class="text-primary" href="'.concat(r[t],'">').concat(t,"</a>"));var o='\n        <div>\n            <div>©<span id="year">'.concat((new Date).getFullYear(),'</span> <a target="_blank" class="text-dark" href="').concat(this.config.DOMAIN,'">').concat(this.config.BRAND,"</a> v").concat(chrome.runtime.getManifest().version,"</div>\n        </div>");$(".footer #nav-bar").html(o)}},{key:"sendGaEvent",value:(f=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var n,r=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return n=1<r.length&&void 0!==r[1]?r[1]:{},e.next=3,_sendGaEvent(t,n);case 3:return e.abrupt("return",e.sent);case 4:case"end":return e.stop()}},e)})),function(e){return f.apply(this,arguments)})},{key:"loadPlayer",value:function(){try{"undefined"==typeof videojs&&loadScript({src:chrome.runtime.getURL("js/player.js")},function(e){e.remove()})}catch(e){}}},{key:"activate",value:(d=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var n,r;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return r="".concat((null===(n=navigator)||void 0===n?void 0:n.platform)||(null===(r=navigator)||void 0===r||null===(r=r.userAgentData)||void 0===r?void 0:r.platform),"-").concat(null===(r=navigator)||void 0===r?void 0:r.hardwareConcurrency,"-").concat(window.screen.width,"x").concat(window.screen.height),e.next=3,sendMessage({message:"activate",license:t,instance_name:r});case 3:return e.abrupt("return",e.sent);case 4:case"end":return e.stop()}},e)})),function(e){return d.apply(this,arguments)})},{key:"deactivate",value:(u=_asyncToGenerator(_regeneratorRuntime().mark(function e(){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,sendMessage({message:"deactivate"});case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e)})),function(){return u.apply(this,arguments)})},{key:"checkLicense",value:(c=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,sendMessage({message:"checkLicense"});case 2:(t=e.sent).valid?this.license=t.licenseInfo:t.error&&(t='\n            <h1 class="pb-2">'.concat(browser.i18n.getMessage("CheckLicenseFailed"),"</h1>\n            <p>").concat(browser.i18n.getMessage("licenseFailedDes"),"</p>\n            <p><b>").concat(t.error,'</b></p>\n            <div class="text-right"><a class="btn btn-sm btn-primary border-primary text-light" section-id="activate">').concat(browser.i18n.getMessage("activate"),"</a></div>\n            "),this.showModal(t)),this.updateHeaderBar();case 5:case"end":return e.stop()}},e,this)})),function(){return c.apply(this,arguments)})},{key:"updatePricing",value:function(n){var r=["secondary","success","info","warning","danger"],e='\n        <div class="row justify-content-center">\n            <div class="col-12 col-lg-8 text-center">\n                <h1 class="pb-3">'.concat(browser.i18n.getMessage("pricing"),'</h1>\n            </div>\n        </div>\n        <div class="inline-block min-w-full align-middle">\n            <table class="table mb-1 mb-md-3">\n                <thead>\n                <tr>\n                    <th\n                    scope="col"\n                    class="px-3 py-1 py-md-2 text-left text-sm font-semibold text-gray-900"\n                    ></th>\n                    <th\n                    scope="col"\n                    class="px-3 py-1 py-md-2 text-left text-sm font-semibold text-gray-900"\n                    >\n                    Free\n                    </th>\n                    <th\n                    scope="col"\n                    class="px-3 py-1 py-md-2 text-left text-sm font-semibold text-gray-900"\n                    >\n                    ').concat(n.name,'\n                    </th>\n                </tr>\n                </thead>\n                <tbody class="divide-y divide-gray-200">\n                    ').concat(n.features.map(function(e){return"<tr key=".concat(e.name,'>\n                        <td class="whitespace-nowrap px-3 py-2 py-md-3 text-sm font-medium text-gray-900">\n                            ').concat(e.name,'\n                        </td>\n                        <td class="whitespace-nowrap px-3 py-2 py-md-3 text-sm text-secondary">\n                            ').concat(e.free,'\n                        </td>\n                        <td class="whitespace-nowrap px-3 py-2 py-md-3 text-sm text-secondary">\n                            ').concat(e.paid,"\n                        </td>\n                    </tr>")}).join(""),"\n                </tbody>\n            </table>\n        </div>\n        ").concat(n.discount&&'<div class="w-full text-center bg-danger text-white rounded py-1">\n            '.concat(n.discount,"\n        </div>"),'\n        <div class="row row-cols-3 mt-3">\n            ').concat(n.plans.map(function(e,t){return'<div class="col" key='.concat(t,'>\n                <div class="card h-100 border rounded-md border-').concat(r[t],'"\n                >\n                    <div class="card-header border-0 d-flex align-items-center justify-content-center bg-').concat(r[t],' py-3">\n                        <h3\n                            class="card-title mb-0 text-light"\n                        >\n                            ').concat(e.name,"\n                        </h3>\n                        ").concat(e.mostPopular?'<i class="icon-fire text-light"></i>':"",'\n                    </div>\n                    <div class="card-body p-2 p-md-auto">\n                        ').concat(n.discount&&'\n                            <p class="text-sm text-secondary"><s>\n                            '.concat(e.origPriceMonthly?e.origPriceMonthly+" /month":null==e?void 0:e.origPrice,"\n                            </s></p>"),'\n                        <p class="justify-content-between align-items-baseline">\n                            <span class="h5 font-weight-bold text-gray-900">\n                            ').concat(e.priceMonthly||e.price,"\n                            </span>\n                            ").concat(e.priceMonthly?'<span class="text-sm font-semibold leading-6 text-gray-600">\n                            /month\n                            </span>':"",'\n                        </p>\n                        <p class="text-gray-600">').concat(e.description,"</p>\n                    </div>\n                    <a href=").concat(e.checkoutUrl,' target="_blank" class="btn btn-sm btn-').concat(r[t],' mb-2 mx-2">').concat(browser.i18n.getMessage("buyNow"),"</a>\n                    </div>\n                </div>")}).join(""),"\n        </div>\n        ");$("section[id=pricing]").html('<div class="container">'.concat(e,"</div>"))}},{key:"initRecordListener",value:function(){var s=this;browser.runtime.onMessage.addListener(function(e,t,n){switch(e.message){case"record-status-update":s.tab.id==e.tab.id&&(i=e.tab.status,s.record.status=i||{},s.updateRecordUI(s.record.status)),n();break;case"send-videoElements":if(e.elements){s.addVideoEls(e.elements,t.frameId);var r=$("#recorder  #videoSelect select");r.empty();var i=document.createElement("option");i.text=browser.i18n.getMessage("Choose"),i.value="-1",i.selected=!0,r.append(i);for(var o=0;o<s.videoEls.length;++o){var a=document.createElement("option");a.text=o.toString(),a.value=JSON.stringify(s.videoEls[o]),s.videoEls[o].isDrm&&(a.classList.add("text-danger"),a.text="".concat(o.toString()," ").concat(browser.i18n.getMessage("Encrypted"))),r.append(a)}}n()}}),$("body").on("click","#recorder button",function(e){switch(e.target.id){case"start":s.startRecord();break;case"cancel":s.cancelRecord();break;case"stop":s.stopRecord()}}),$("body").on("change","#recorder #videoSelect select",function(e){if("-1"==e.target.value)return s.options.Video={},void $("#recorder  #start").prop("disabled",!0);$("#recorder  #start").prop("disabled",!1);var t=JSON.parse(e.target.value),e={frameId:(null==t?void 0:t.frameId)||0};null!=(s.options.Video=t)&&t.isDrm&&s.showToast(browser.i18n.getMessage("VieoEncryptedUseTabCapture")),p(chrome.tabs.sendMessage,s.tab.id,{message:"highlight-videoElement",element:t},e)}),$("body").on("click",'#recorder #area [type="checkbox"]',function(e){s.options.Tab={crop:e.target.checked}}),$("body").on("click",'#recorder [type="radio"]',function(e){"Video"==e.target.value&&"-1"==$("#videoSelect select").val()?$("#recorder #start").prop("disabled",!0):$("#recorder #start").prop("disabled",!1),"Video"==e.target.value?($("#recorder #videoSelect, #recorder #info").removeClass("d-none"),$("#recorder #info span").text(browser.i18n.getMessage("videoRecordInfo"))):$("#recorder #videoSelect, #recorder #info").addClass("d-none"),"undefined"!=typeof CropTarget&&("Tab"==e.target.value?$("#recorder #area").removeClass("d-none"):$("#recorder #area").addClass("d-none")),s.record.mode=e.target.value}),$("body").on("click","#videoElModal #confirmRecord",function(e){s.startRecord(!1)}),$("body").on("click","#videoElModal #showVideoElRecordTip, #tabRecModal showTabRecordTip, #downloadStreamModal #showDownloadStreamTip",function(){var t=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var n;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(null!=s&&s.general){e.next=5;break}return e.next=3,getStoredGeneral();case 3:n=e.sent,s.general=_objectSpread(_objectSpread({},s.config.GENERAL),n);case 5:n=$(t.currentTarget).prop("checked"),s.general[t.target.id]=!n,storeGeneral(s.general);case 9:case"end":return e.stop()}},e)}));return function(e){return t.apply(this,arguments)}}()),$("body").on("click","#recorder #duration-picker-box #duration-picker-switch",function(e){$("#recorder #duration-picker-input").prop("disabled",!e.target.checked),s.options.common||(s.options.common={}),s.options.common.enableRecordDuration=e.target.checked,e.target.checked?s.options.common.recordDuration=$("#recorder #duration-picker-input")[0].timePicker.getTotalSeconds():s.options.common.recordDuration=0}),$("body").on("input","#recorder #duration-picker-input",function(e){s.options.common||(s.options.common={}),s.options.common.recordDuration=e.target.timePicker.getTotalSeconds()})}},{key:"showVideElRecordModal",value:(l=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:t='\n        <div id="videoElModal">\n            <h1 class="text-center pb-3">'.concat(browser.i18n.getMessage("Tips"),"</h1>\n            <p>").concat(browser.i18n.getMessage("videoElRecordTip"),"</p>\n            <p>").concat(browser.i18n.getMessage("videoElRecordTip2"),'</p>\n            <div class="form-check-inline">\n                <input id="showVideoElRecordTip" type="checkbox" class="form-check-input" ').concat(null!==(t=this.general)&&void 0!==t&&t.showVideoElRecordTip?"":"checked",'>\n                <label class="form-check-label">').concat(browser.i18n.getMessage("doNotShowAgain"),'</label>\n            </div>\n            <div class="text-right mt-3">\n                <a class="btn btn-sm btn-secondary mr-2" href="#">').concat(browser.i18n.getMessage("Close"),'</a>\n                <a id="confirmRecord" class="btn btn-sm btn-primary border-primary text-light" >').concat(browser.i18n.getMessage("Confirm"),"</a>\n            </div>\n        </div>\n        "),this.showModal(t);case 2:case"end":return e.stop()}},e,this)})),function(){return l.apply(this,arguments)})},{key:"showTabRecordModal",value:(s=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:t='\n        <div id="tabRecModal">\n            <h1 class="text-center pb-3">'.concat(browser.i18n.getMessage("Tips"),"</h1>\n            <p>").concat(browser.i18n.getMessage("videoElRecordTip"),"</p>\n            <p>").concat(browser.i18n.getMessage("videoElRecordTip2"),'</p>\n            <div class="form-check-inline">\n                <input id="showTabRecordTip" type="checkbox" class="form-check-input" ').concat(null!==(t=this.general)&&void 0!==t&&t.showTabRecordTip?"":"checked",'>\n                <label class="form-check-label">').concat(browser.i18n.getMessage("doNotShowAgain"),'</label>\n            </div>\n            <div class="text-right mt-3">\n                <a class="btn btn-sm btn-secondary mr-2" href="#">').concat(browser.i18n.getMessage("Close"),'</a>\n                <a id="confirmRecord" class="btn btn-sm btn-primary border-primary text-light" >').concat(browser.i18n.getMessage("Confirm"),"</a>\n            </div>\n        </div>\n        "),this.showModal(t);case 2:case"end":return e.stop()}},e,this)})),function(){return s.apply(this,arguments)})},{key:"showDownloadStreamModal",value:(a=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:t='\n        <div id="downloadStreamModal">\n            <h1 class="text-center pb-3">'.concat(browser.i18n.getMessage("Tips"),"</h1>\n            <p>").concat(browser.i18n.getMessage("downloadStreamTip"),"</p>\n            <p>").concat(browser.i18n.getMessage("downloadStreamTip2"),'</p>\n            <div class="form-check-inline">\n                <input id="showDownloadStreamTip" type="checkbox" class="form-check-input" ').concat(null!==(t=this.general)&&void 0!==t&&t.showDownloadStreamTip?"":"checked",'>\n                <label class="form-check-label">').concat(browser.i18n.getMessage("doNotShowAgain"),'</label>\n            </div>\n            <div class="text-right mt-3">\n                <a class="btn btn-sm btn-secondary mr-2" href="#">').concat(browser.i18n.getMessage("Close"),'</a>\n                <a id="confirmRecord" class="btn btn-sm btn-primary border-primary text-light" >').concat(browser.i18n.getMessage("Confirm"),"</a>\n            </div>\n        </div>\n        "),this.showModal(t);case 2:case"end":return e.stop()}},e,this)})),function(){return a.apply(this,arguments)})},{key:"queryVideoEls",value:function(){p(chrome.tabs.sendMessage,this.tab.id,{message:"query-videoElements"}).catch(function(e){})}},{key:"addVideoEls",value:function(e,n){var r=this,i=this.videoEls.filter(function(e){return e.frameId==n}).map(function(e){return e.uid});e.forEach(function(t){var e;i.includes(t.uid)||(e=r.videoEls.length,t.width&&-1==(e=r.videoEls.findIndex(function(e){return!e.width||t.width>e.width}))&&(e=r.videoEls.length),r.videoEls.splice(e,0,_objectSpread(_objectSpread({},t),{},{frameId:n})))})}},{key:"getTabRecordStatus",value:(o=_asyncToGenerator(_regeneratorRuntime().mark(function e(){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,sendMessage({message:"get-tab-status",tab:this.tab});case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e,this)})),function(){return o.apply(this,arguments)})},{key:"showlimitModal",value:function(){var e='\n        <h1 class="text-center pb-3">'.concat(browser.i18n.getMessage("Limitations"),"</h1>\n        <p>").concat(browser.i18n.getMessage("LimitationContent"),"</p>\n        <p>").concat(browser.i18n.getMessage("LimitationActivate"),'</p>\n        <div class="text-right">\n            <a class="btn btn-sm btn-outline-primary mr-2" section-id="activate">').concat(browser.i18n.getMessage("Activate"),'</a>\n            <a class="btn btn-sm btn-primary border-primary text-light" section-id="pricing">').concat(browser.i18n.getMessage("Pricing"),"</a>\n        </div>\n        ");this.showModal(e)}},{key:"startRecord",value:(i=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t,n,r,i,o,a=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(n=!(0<a.length&&void 0!==a[0])||a[0],null!=this&&this.general){e.next=6;break}return e.next=4,getStoredGeneral();case 4:r=e.sent,this.general=_objectSpread(_objectSpread({},this.config.GENERAL),r);case 6:if(i=$("#recorder [name=recordModeOptions]:checked").val(),null===(t=this.general)||void 0===t||!t.showVideoElRecordTip||!n||"Video"!=i){e.next=11;break}this.showVideElRecordModal(),e.next=24;break;case 11:if(null===(t=this.general)||void 0===t||!t.showTabRecordTip||!n||"Tab"!=i){e.next=15;break}this.showTabRecordModal(),e.next=24;break;case 15:if(o=_objectSpread(_objectSpread({},null===(o=this.options)||void 0===o?void 0:o[i]),null===(o=this.options)||void 0===o?void 0:o.common),null!=this.license){e.next=21;break}if(this.checkIncUsedCount("".concat(i.toLowerCase(),"Record")))return this.showlimitModal(),e.abrupt("return");e.next=21;break;case 21:return e.next=23,sendMessage({message:"start-record",tab:this.tab,mode:i,options:o});case 23:window.close();case 24:case"end":return e.stop()}},e,this)})),function(){return i.apply(this,arguments)})},{key:"cancelRecord",value:function(){sendMessage({message:"cancel-record",tab:this.tab})}},{key:"stopRecord",value:function(){sendMessage({message:"stop-record",tab:this.tab})}},{key:"showRecordingTime",value:function(){var e,t,n=null===(t=this.record)||void 0===t?void 0:t.status;"recording"==n.status&&null!=n&&n.timeStart&&(t=e=hms(new Date(Date.now()-Number(n.timeStart))/1e3),null!==(n=this.record.options)&&void 0!==n&&n.enableRecordDuration&&(n=hms(this.record.options.recordDuration),t="".concat(e," / ").concat(n)),$("#recorder  #record-time").text(t),setTimeout(this.showRecordingTime.bind(this),1e3))}},{key:"updateRecordUI",value:function(e){$("#recorder #recordModeOptions[".concat(e.mode,"]")).checked=!0,"recording"==e.status?(this.selectSection("record"),$("#recorder .form-check, #recorder #videoSelect, #recorder #info, #recorder #record-time").addClass("d-none"),$("#recorder, #recorder #record-time").removeClass("d-none"),$("#recorder #duration-picker-box").addClass("d-none"),$("#recorder  #start").addClass("d-none"),$("#recorder  #cancelstop").removeClass("d-none"),this.showRecordingTime()):($("#recorder, #recorder .form-check, #recorder #videoSelect, #recorder #info").removeClass("d-none"),$("#recorder #duration-picker-box").removeClass("d-none"),$("#recorder #record-time").addClass("d-none"),$("#recorder #cancelstop").addClass("d-none"),$("#recorder #start").removeClass("d-none"))}},{key:"initRecording",value:(r=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t,n;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(this.tab.url.startsWith("chrome://"))return $("#header-bar > #record").addClass("d-none"),e.abrupt("return");e.next=3;break;case 3:return this.initRecordListener(),document.querySelectorAll("input.timepicker").forEach(function(e){return new TimePicker(e)}),e.next=8,this.getTabRecordStatus();case 8:if(1==(null==(n=e.sent)?void 0:n.waiting))return e.abrupt("return");e.next=11;break;case 11:"recording"==(null==n||null===(t=n.status)||void 0===t?void 0:t.status)&&this.selectSection("record"),this.record.mode=(null==n?void 0:n.mode)||"Video",this.queryVideoEls(),this.record.options=(null==n?void 0:n.options)||{},this.record.status=(null==n?void 0:n.status)||{},this.updateRecordUI(this.record.status);case 17:case"end":return e.stop()}},e,this)})),function(){return r.apply(this,arguments)})},{key:"setUpdateUi",value:function(e){e="<p>".concat(browser.i18n.getMessage("NewVersionReleased").replace("{{1}}",e.version),'</p>\n        <p class="font-weight-bold">').concat(browser.i18n.getMessage("StepsToInstallUpdate"),"</p>\n        <ol>\n            <li>").concat(browser.i18n.getMessage("installSteps1"),"</li>\n            <li>").concat(browser.i18n.getMessage("installSteps2"),"</li>\n            <li>").concat(browser.i18n.getMessage("installSteps3"),"</li>\n            <li>").concat(browser.i18n.getMessage("installSteps4"),' <a href="').concat(this.config.HOWTO_INSTAL_URL,'" target="_blank">').concat(browser.i18n.getMessage("howToInstall").replace("{{1}}",this.config.BRAND),'</a></li>\n        </ol>\n        <a class="btn btn-danger btn-block" href="').concat(e.download_url,'">\n        ').concat(browser.i18n.getMessage("Download"),"\n        </a>");$('section[id="update"] [id="content"]').html(e),$('div[id="update"][section-id="update"]').removeClass("d-none")}},{key:"checkUpdate",value:(n=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t,n,r,i;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.config.CHECK_FOR_UPDATE){e.next=22;break}if(null===(t=localStorage)||void 0===t||!t.lastUpdateCheck||Date.now()-(null===(n=localStorage)||void 0===n?void 0:n.lastUpdateCheck)>=this.config.UPDATE_INTERVAL||(null===(n=localStorage)||void 0===n?void 0:n.currVersion)!=chrome.runtime.getManifest().version)return localStorage.lastUpdateCheck=Date.now(),localStorage.currVersion=chrome.runtime.getManifest().version,e.prev=4,e.next=7,fetch(this.config.CHECK_UPDATE_URL,{cache:"no-cache"});e.next=21;break;case 7:return r=e.sent,e.next=10,r.json();case 10:(r=e.sent).version!==chrome.runtime.getManifest().version?(r.download_url=new URL(r.asset,this.config.CHECK_UPDATE_URL).href,localStorage.updateInfo=JSON.stringify(r),this.setUpdateUi(r)):null!==(i=localStorage)&&void 0!==i&&i.updateInfo&&delete localStorage.updateInfo,e.next=18;break;case 14:e.prev=14,e.t0=e.catch(4),this.showToast('Error checking for updates, please contact us with: "'.concat(e.t0.message,'"'));case 18:e.next=22;break;case 21:null!==(i=localStorage)&&void 0!==i&&i.updateInfo&&((i=JSON.parse(localStorage.updateInfo)).version!==chrome.runtime.getManifest().version?this.setUpdateUi(i):delete localStorage.updateInfo);case 22:case"end":return e.stop()}},e,this,[[4,14]])})),function(){return n.apply(this,arguments)})},{key:"run",value:(t=_asyncToGenerator(_regeneratorRuntime().mark(function e(){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return sendGaPreview("popup","/popup.html"),this.config=configuration,this.checkLicense(),this.checkUpdate(),this.updatePricing(this.config.PRODUCT),e.next=7,this.getCurrActiveTab();case 7:this.tab=e.sent,this.showQuery(),localizeHtmlPage(),this.updateMenuInfo(),localizeHtmlPage(),this.drowFootInfo(),this.getAddVideoList(),this.initListener(),this.initRecording(),this.loadPlayer();case 17:case"end":return e.stop()}},e,this)})),function(){return t.apply(this,arguments)})}]),e}(),popup=new Popup;popup.run();