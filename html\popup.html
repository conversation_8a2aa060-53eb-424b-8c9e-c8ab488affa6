<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><title>__MSG_title__</title><link rel="stylesheet" type="text/css" href="../css/bootstrap-4.5.3.min.css"><link rel="stylesheet" type="text/css" href="../css/video-js.min.css"><link rel="stylesheet" type="text/css" href="../css/videojs-shaka.css"><link rel="stylesheet" type="text/css" href="../css/popup.css"><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/simple-line-icons/2.4.1/css/simple-line-icons.min.css"><link href="https://fonts.googleapis.com/css2?family=Nunito:wght@300;600&display=swap" rel="stylesheet"></head><body><nav class="navbar navbar-expand-md navbar-dark bg-white border-primary border-bottom fixed-top p-3"><div class="container"><div class="navbar-brand" section-id="popup"><h1 class="my-auto"><span class="text-primary text-center">__MSG_brand__</span></h1></div><div id="header-bar" class="ml-auto d-flex text-primary mr-1"><div id="update" section-id="update" class="d-none"><div id="${index}" class="btn btn-sm btn-outline-danger border-0 mr-1" title="__MSG_Updateversion__"><svg stroke="currentColor" width="24" height="24" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" height="200px" width="200px" xmlns="http://www.w3.org/2000/svg"><path d="M8 256C8 119 119 8 256 8s248 111 248 248-111 248-248 248S8 393 8 256zm292 116V256h70.9c10.7 0 16.1-13 8.5-20.5L264.5 121.2c-4.7-4.7-12.2-4.7-16.9 0l-115 114.3c-7.6 7.6-2.2 20.5 8.5 20.5H212v116c0 6.6 5.4 12 12 12h64c6.6 0 12-5.4 12-12z"></path></svg></div></div><div id="record" section-id="record"><div id="${index}" class="btn btn-sm btn-outline-primary border-0 mr-1" title="__MSG_RecordingMode__"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" class="bi bi-record-circle" viewBox="0 0 16 16"><path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14m0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16"/><path d="M11 8a3 3 0 1 1-6 0 3 3 0 0 1 6 0"/></svg></div></div><div id="license"></div></div><div id="nav-btn" class="btn btn-sm btn-outline-primary border-0 collapsed" type="button" data-toggle="collapse" data-target="#navbarCollapse" aria-controls="navbarCollapse" aria-expanded="false" aria-label="Toggle navigation" title="__MSG_Settings__"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" class="bi bi-list" viewBox="0 0 16 16"><path fill-rule="evenodd" d="M2.5 12a.5.5 0 0 1 .5-.5h10a.5.5 0 0 1 0 1H3a.5.5 0 0 1-.5-.5m0-4a.5.5 0 0 1 .5-.5h10a.5.5 0 0 1 0 1H3a.5.5 0 0 1-.5-.5m0-4a.5.5 0 0 1 .5-.5h10a.5.5 0 0 1 0 1H3a.5.5 0 0 1-.5-.5"/></svg></div><div class="navbar-collapse justify-content-end collapse" id="navbarCollapse"><ul class="navbar-nav mr-auto"><li class="nav-item"><a class="nav-link text-dark" section-id="popup">__MSG_Home__</a></li><li class="nav-item"><a class="nav-link text-dark" section-id="settings">__MSG_Settings__</a></li><li class="nav-item"><a class="nav-link text-dark" section-id="pricing">__MSG_Pricing__</a></li><li class="nav-item"><a class="nav-link text-dark" section-id="feedback">__MSG_Contact__</a></li></ul></div></div></nav><!-- <div class="d-flex p-3 border-primary border-bottom">
        <div class="navbar-brand" section-id="popup">
            <h1 class="my-auto"><span class="text-primary text-center">__MSG_brand__</span>
            </h1>
        </div>
        <div id="header-bar" class="ml-auto d-flex text-primary">
            <div id="update" section-id="update" class="d-none">
                <button id="${index}" class="btn btn-sm btn-outline-danger border-danger mr-2" title="__MSG_Updateversion__">
                    <svg stroke="currentColor" width="16" height="16" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" height="200px" width="200px" xmlns="http://www.w3.org/2000/svg"><path d="M8 256C8 119 119 8 256 8s248 111 248 248-111 248-248 248S8 393 8 256zm292 116V256h70.9c10.7 0 16.1-13 8.5-20.5L264.5 121.2c-4.7-4.7-12.2-4.7-16.9 0l-115 114.3c-7.6 7.6-2.2 20.5 8.5 20.5H212v116c0 6.6 5.4 12 12 12h64c6.6 0 12-5.4 12-12z"></path></svg>
                </button>
            </div>
            <div id="record" section-id="record">
                <button id="${index}" class="btn btn-sm btn-outline-primary border-primary mr-2" title="__MSG_RecordingMode__">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-record-circle" viewBox="0 0 16 16">
                        <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14m0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16"/>
                        <path d="M11 8a3 3 0 1 1-6 0 3 3 0 0 1 6 0"/>
                    </svg>
                </button>
            </div>
            <div id="license">
            </div>
            <div id="settings" section-id="settings">
                <button id="${index}" class="btn btn-sm btn-primary border-primary ml-2" title="__MSG_Settings__"><i class="icon-settings align-middle"></i></button>
            </div>
        </div>
    </div> --><section id="popup" class="py-3"><div class="container"><div id="go-spinner" class="text-center mx-auto"><img src="../images/loading.gif"></div><div id="search-box"></div><!-- <div id="info-box"></div> --><div id="no-video-found"></div><div id="video-info"><div id="btn-box" class="pb-2" style="display:none;"></div><ul id="video-list"></ul><span class="text-muted"><i class="icon-info mr-1"></i>__MSG_KeepCurrDlTabOpen__</span></div></div></section><section id="record" class="py-3 d-none"><div class="container"><div id="recorder" class="mt-2 d-none"><h5>Record video</h5><div class="mb-2"><div class="form-check form-check-inline"><input class="form-check-input" type="radio" name="recordModeOptions" id="Video" value="Video" checked="checked"> <label class="form-check-label" for="inlineRadio1">__MSG_Video__</label></div><div class="form-check form-check-inline"><input class="form-check-input" type="radio" name="recordModeOptions" id="Audio" value="Audio"> <label class="form-check-label" for="inlineRadio2">__MSG_Audio__</label></div><div class="form-check form-check-inline"><input class="form-check-input" type="radio" name="recordModeOptions" id="Tab" value="Tab"> <label class="form-check-label" for="inlineRadio3">__MSG_TabCapture__</label></div><div class="form-check form-check-inline"><input class="form-check-input" type="radio" name="recordModeOptions" id="Screen" value="Screen"> <label class="form-check-label" for="inlineRadio3">__MSG_Screen__</label></div></div><div id="videoSelect" class="d-none"><div class="d-flex flex-row"><label class="my-1 mr-auto" for="inlineFormCustomSelectPref">__MSG_SelectVideoOnPage__</label> <select class="custom-select w-50" aria-label="Select video Element"><option selected="selected" value="-1">__MSG_Choose__</option></select></div></div><div id="area" class="d-none mb-2"><div class="d-flex flex-row"><div class="custom-control custom-switch"><input type="checkbox" class="custom-control-input" id="customSwitch1"> <label class="custom-control-label" for="customSwitch1">__MSG_recordTabArea__</label></div></div></div><div id="duration-picker-box" class="mb-2"><div class="d-flex flex-row mb-2"><div class="custom-control custom-switch"><input type="checkbox" class="custom-control-input" id="duration-picker-switch"> <label class="custom-control-label" for="duration-picker-switch">__MSG_setRecordDuration__</label></div></div><input type="text" id="duration-picker-input" class="form-control timepicker" value="00:05:00" integer disabled="disabled"></div><p class="mb-0">__MSG_Hotkeys__</p><ul><li>__MSG_cancelCmd__</li><li>__MSG_stopCmd__</li></ul><p id="info" class="text-muted d-none"><small><i class="icon-info mr-1"></i> <span>__MSG_videoRecordInfo__</span></small></p><div id="record-time" class="d-none"></div><!-- <button id="pip-button" class="pure-button pure-input-1-1">Picture In Picture</button> --> <button id="start" class="btn btn-block btn-primary border-primary mt-2" disabled="disabled">__MSG_startRecord__</button><div id="cancelstop" class="row d-none"><div class="col-6"><button id="cancel" class="btn btn-block btn-primary border-primary mt-2">__MSG_cancelRecord__</button></div><div class="col-6"><button id="stop" class="btn btn-block btn-primary border-primary mt-2">__MSG_stopRecord__</button></div></div></div></div></section><section id="activate" class="py-3 d-none"><div class="container"><div class="row"><div class="col-md-12"><div class="text-center pb-4"><h1>__MSG_ActivateYourLicenseKey__</h1></div></div></div><div class="row"><div class="col"><div class="card border-primary rounded-lg shadow"><div class="card-body bg-light rounded"><form><div class="form-group"><label for="license">__MSG_LicenseKey__</label> <input class="form-control" id="license" placeholder="__MSG_EnterLicenseKey__" required></div><div id="go-spinner" class="text-center mx-auto d-none"><img src="../images/loading.gif"></div><div id="result" class="text-center text-light mx-auto d-none"></div><a class="text-primary" section-id="pricing">__MSG_PurchaseLicenseKey__ </a><!-- <button type="submit" class="btn btn-primary">Submit</button> --> <button id="activate" class="btn btn-block btn-primary border-primary text-uppercase rounded-lg">__MSG_Activate__</button></form></div></div></div></div><div class="row pt-3"><div class="col-md-12"><div class="price-heading clearfix"><span class="text-muted"><i class="icon-info mr-1"></i>__MSG_ActivateNotices__</span></div></div></div></div></section><section id="deactivate" class="py-3 d-none"><div class="container"><div class="row"><div class="col-md-12"><div class="text-center pb-4"><h1>__MSG_DeactivateLicenseKey__</h1></div></div></div><div class="row"><div class="col"><div class="card border-primary rounded-lg shadow"><div class="card-body bg-light rounded"><form><div class="form-group"><label for="license">__MSG_LicenseKey__</label><p>__MSG_DeactivateDes__</p></div><div id="go-spinner" class="text-center mx-auto d-none"><img src="../images/loading.gif"></div><div id="result" class="text-center mx-auto d-none"></div><button id="deactivate" class="btn btn-block btn-primary border-primary text-uppercase rounded-lg">__MSG_Deactivate__</button></form></div></div></div></div></div></section><section id="pricing" class="py-3 d-none"></section><section id="settings" class="py-3 d-none"><div class="row justify-content-center"><div class="col-12 col-lg-8 text-center"><h1 class="pb-3">__MSG_Settings__</h1></div></div><ul class="nav nav-tabs" id="myTab" role="tablist"><li class="nav-item" role="presentation"><a class="nav-link active" id="home-tab" data-toggle="tab" href="#general" role="tab" aria-controls="home" aria-selected="true"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" class="bi bi-gear" viewBox="0 0 16 16"><path d="M8 4.754a3.246 3.246 0 1 0 0 6.492 3.246 3.246 0 0 0 0-6.492M5.754 8a2.246 2.246 0 1 1 4.492 0 2.246 2.246 0 0 1-4.492 0"/><path d="M9.796 1.343c-.527-1.79-3.065-1.79-3.592 0l-.094.319a.873.873 0 0 1-1.255.52l-.292-.16c-1.64-.892-3.433.902-2.54 2.541l.159.292a.873.873 0 0 1-.52 1.255l-.319.094c-1.79.527-1.79 3.065 0 3.592l.319.094a.873.873 0 0 1 .52 1.255l-.16.292c-.892 1.64.901 3.434 2.541 2.54l.292-.159a.873.873 0 0 1 1.255.52l.094.319c.527 1.79 3.065 1.79 3.592 0l.094-.319a.873.873 0 0 1 1.255-.52l.292.16c1.64.893 3.434-.902 2.54-2.541l-.159-.292a.873.873 0 0 1 .52-1.255l.319-.094c1.79-.527 1.79-3.065 0-3.592l-.319-.094a.873.873 0 0 1-.52-1.255l.16-.292c.893-1.64-.902-3.433-2.541-2.54l-.292.159a.873.873 0 0 1-1.255-.52zm-2.633.283c.246-.835 1.428-.835 1.674 0l.094.319a1.873 1.873 0 0 0 2.693 1.115l.291-.16c.764-.415 1.6.42 1.184 1.185l-.159.292a1.873 1.873 0 0 0 1.116 2.692l.318.094c.835.246.835 1.428 0 1.674l-.319.094a1.873 1.873 0 0 0-1.115 2.693l.16.291c.415.764-.42 1.6-1.185 1.184l-.291-.159a1.873 1.873 0 0 0-2.693 1.116l-.094.318c-.246.835-1.428.835-1.674 0l-.094-.319a1.873 1.873 0 0 0-2.692-1.115l-.292.16c-.764.415-1.6-.42-1.184-1.185l.159-.291A1.873 1.873 0 0 0 1.945 8.93l-.319-.094c-.835-.246-.835-1.428 0-1.674l.319-.094A1.873 1.873 0 0 0 3.06 4.377l-.16-.292c-.415-.764.42-1.6 1.185-1.184l.292.159a1.873 1.873 0 0 0 2.692-1.115z"/></svg> __MSG_General__</a></li><li class="nav-item" role="presentation"><a class="nav-link" id="home-tab" data-toggle="tab" href="#filter" role="tab" aria-controls="home" aria-selected="true"><svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 -960 960 960" width="24" fill="currentColor"><path d="M440-120v-240h80v80h320v80H520v80h-80Zm-320-80v-80h240v80H120Zm160-160v-80H120v-80h160v-80h80v240h-80Zm160-80v-80h400v80H440Zm160-160v-240h80v80h160v80H680v80h-80Zm-480-80v-80h400v80H120Z"/></svg> __MSG_Filter__</a></li><li class="nav-item" role="presentation"><a class="nav-link" id="contact-tab" data-toggle="tab" href="#audioFixer" role="tab" aria-controls="contact" aria-selected="false"><svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 -960 960 960" width="24" fill="currentColor"><path d="M500-360q42 0 71-29t29-71v-220h120v-80H560v220q-13-10-28-15t-32-5q-42 0-71 29t-29 71q0 42 29 71t71 29ZM320-240q-33 0-56.5-23.5T240-320v-480q0-33 23.5-56.5T320-880h480q33 0 56.5 23.5T880-800v480q0 33-23.5 56.5T800-240H320Zm0-80h480v-480H320v480ZM160-80q-33 0-56.5-23.5T80-160v-560h80v560h560v80H160Zm160-720v480-480Z"/></svg> __MSG_AudioFixer__</a></li></ul><div class="tab-content" id="myTabContent"><div class="tab-pane fade show active" id="general" role="tabpanel" aria-labelledby="home-tab"></div><div class="tab-pane fade show" id="filter" role="tabpanel" aria-labelledby="home-tab"></div><div class="tab-pane fade" id="audioFixer" role="tabpanel" aria-labelledby="home-tab"></div></div></section><section id="feedback" class="py-3 d-none"></section><section id="faq" class="py-3 d-none"><div class="container"><div class="row justify-content-center"><div class="col-12 col-lg-8 text-center"><h1 class="pb-3">__MSG_FAQ__</h1></div></div><div class="row justify-content-center"><div class="col-lg-10 display-6"><p class="font-weight-bold">__MSG_HowtoDownload__</p><ol><li>__MSG_HowtoDownloadStep1__</li><li>__MSG_HowtoDownloadStep2__</li></ol><p class="font-weight-bold">__MSG_supportHLSDASH__</p><p>__MSG_supportHLSDASHResult__</p><p class="font-weight-bold">__MSG_canCopyLink__</p><p>__MSG_canCopyLinkResult__</p><p class="font-weight-bold">__MSG_needInstall__</p><p>__MSG_needInstallResult__</p><p class="font-weight-bold">__MSG_supportConvert__</p><p>__MSG_supportConvertResult__</p><p class="font-weight-bold">__MSG_howtoActivate__</p><p>__MSG_howtoActivateResult__</p><img class="img-thumbnail" src="../images/activate.png"><p class="font-weight-bold">__MSG_howtoUpgrade__</p><p>__MSG_howtoUpgradeResult__</p><p class="font-weight-bold">__MSG_howtoCancelSubscription__</p><p>__MSG_howtoCancelSubscriptionResult__</p></div></div></div></section><section id="update" class="py-3 d-none"><div class="container"><div class="row"><div class="col-md-12"><div class="text-center pb-4"><h1>__MSG_Update__</h1></div></div></div><div class="row"><div class="col" id="content"><p>__MSG_alreadyLatestVersion__</p></div></div></div></section><div id="toast">__MSG_copyurl__</div><div class="footer"><div id="rate-us" class="text-center"></div><div id="nav-bar" class="link"></div></div><div id="modal" class="modal"><div class="modal_body"><a href="#" class="modal_close">&times;</a><div class="modal_content"></div></div></div><script type="text/javascript" src="../js/common.js"></script><script type="text/javascript" src="../js/popup.js"></script></body></html>