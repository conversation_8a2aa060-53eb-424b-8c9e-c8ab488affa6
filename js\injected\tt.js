(()=>{function e(e,t){const a=Object.keys(e);let r;for(let e of a)if(e.indexOf(t)>-1){r=e;break}return r}function t(){document.body.addEventListener("tt-query",(async t=>{t.detail.videoId;const a=t.detail.componentId;let r=document.querySelectorAll(`[data-btn-id="${a}"]`)[0],i={};{let a,d,n;switch(t.detail.page){case"video-detail-br-mode":a=r.parentElement.parentElement.parentElement.parentElement.parentElement.parentElement,d=e(a,"reactFiber"),n=a[d].return.return.return.pendingProps.item;break;case"video-detail":a=r.parentElement.parentElement.parentElement,d=e(a,"reactFiber"),n=a[d].return.return.return.pendingProps.item;break;case"search":a=r.parentElement.querySelector('[data-e2e="search-card-desc"]'),d=e(a,"reactFiber");const t=a[d].return.return.pendingProps.videoData;n={desc:t.desc,id:t.id,diversificationLabels:(t.challenges||[]).map((e=>e.title)),video:t.video,music:t?.music||null,author:t.author,stats:t.stats,authorId:t.authorId,authorStats:t.authorStats,avatarThumb:t.avatarThumb};break;case"music":case"tag":a=r.parentElement.querySelector('[class*="-DivDesContainer"]'),d=e(a,"reactFiber");const i=a[d].return.return.pendingProps.videoData;n={desc:i.desc,id:i.id,diversificationLabels:(i.challenges||[]).map((e=>e.title)),video:i.video,music:i?.music||null,author:i.author,stats:i.stats,authorId:i.authorId,authorStats:i.authorStats,avatarThumb:i.avatarThumb};break;case"explore":a=r.parentElement.querySelector('[data-e2e="explore-card-desc"]'),d=e(a,"reactFiber");const o=a[d].return.return.pendingProps.videoData;n={desc:o.desc,id:o.id,diversificationLabels:(o.challenges||[]).map((e=>e.title)),video:o.video,music:o?.music||null,author:o.author,authorId:o.authorId,authorStats:o.authorStats,stats:o.stats,avatarThumb:o.avatarThumb};break;case"userHome":a=r.parentElement.querySelector('div[class*="-DivCardFooter"]'),d=e(a,"reactFiber");const s=a[d].return.return.pendingProps.videoData;n={desc:s.desc,id:s.id,diversificationLabels:(s.challenges||[]).map((e=>e.title)),video:s.video,music:s?.music||null,author:s.author,authorId:s.authorId,authorStats:s.authorStats,stats:s.stats,avatarThumb:s.avatarThumb};break;default:a=r.parentElement.parentElement,d=e(a,"reactFiber"),n=a[d].return.return.pendingProps.item}const o=[];if(n.video?.bitrateInfo)for(let e of n.video.bitrateInfo)o.push({url_list:e.PlayAddr.UrlList,bitrate:e.Bitrate,height:n.video.height,width:n.video.width,size:e.PlayAddr.DataSize});const s={url_list:[n.video.playAddr],bitrate:n.video.bitrate,duration:n.video.duration,height:n.video.height,width:n.video.width,size:n.video.size};i={id:n.id,title:n.desc.length?n.desc:n.author,author:n.author,duration:s.duration,video:s,videos:o};try{n.music.playUrl&&(i.music={id:n.music.id,title:n.music.title,url_list:[n.music.playUrl],duration:n.music.duration})}catch(e){}}const d=new CustomEvent("tt-result",{detail:{metaData:i,detailInfo:{}}});(r||window).dispatchEvent(d)}))}document.body?t():document.addEventListener("DOMContentLoaded",t)})();