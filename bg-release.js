// function reddenPage() {
//   document.body.style.backgroundColor = 'red';
// }

// chrome.action.onClicked.addListener((tab) => {
//   chrome.scripting.executeScript({
//     target: { tabId: tab.id },
//     function: reddenPage
//   });
// });
var is_release = true;
// sentry need window
if (typeof window == "undefined") {
  globalThis.window = globalThis;
}
try {
  importScripts("js/common.js", "js/background.js");
} catch (e) {
  console.error(e);
}