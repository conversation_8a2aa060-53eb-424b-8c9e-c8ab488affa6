(()=>{function e(n,t){Array.isArray(t)||(t=[t]);for(let r in n)if(n.hasOwnProperty(r)){if(t.includes(r))return n[r];if("object"==typeof n[r]&&null!==n[r]){var i=e(n[r],t);if(void 0!==i)return i}}}function n(){document.body.addEventListener("ig-query",(async n=>{const t=n.detail.componentId;let i=document.querySelectorAll(`video[data-unique-id="${t}"]`)[0];const r=(function(n){let t=[{name:"url",keys:["sdSrc","videoUrl"]},{name:"mediaId",keys:["videoFBID","mediaId"]},{name:"videoId",keys:["shortcode","code"]},{name:"caption",keys:"caption"},{name:"username",keys:"username"},{name:"duration",keys:"videoDuration"},{name:"video_dash_manifest",keys:"video_dash_manifest"}],i={},r=[];return(function(e,n){const t=(function(e){const n=Object.keys(e);let t;for(let e of n)if(e.indexOf("reactProps")>-1){t=e;break}return t})(e);for(;e;){if(e.querySelectorAll("video").length>1)return;try{if(n(JSON.parse(JSON.stringify(e[t]))))return}catch(e){}e=e.parentElement}})(n,(n=>{let o=JSON.stringify(n);for(let d=0;d<t.length;d++){if(r.includes(d))continue;let a,s=t[d];if(a=Array.isArray(s.keys)?o.match(`(?:"(?:${s.keys.join("|")})":\\s*)`):o.match(`(?:"${s.keys}":\\s*)`),a){let t=e(n,s.keys);void 0!==t&&(i[s.name]=t,r.push(d))}}if(r.length===t.length)return!0})),i})(i),o=new CustomEvent("ig-result",{detail:{...r}});(i||window).dispatchEvent(o)}))}document.body?n():document.addEventListener("DOMContentLoaded",n)})();