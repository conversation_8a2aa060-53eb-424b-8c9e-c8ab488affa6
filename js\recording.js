var _class,_excluded=["classWorkerURL","workerType"];function _objectWithoutProperties(e,t){if(null==e)return{};var n,r=_objectWithoutPropertiesLoose(e,t);if(Object.getOwnPropertySymbols)for(var i=Object.getOwnPropertySymbols(e),o=0;o<i.length;o++)n=i[o],0<=t.indexOf(n)||Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n]);return r}function _objectWithoutPropertiesLoose(e,t){if(null==e)return{};for(var n,r={},i=Object.keys(e),o=0;o<i.length;o++)n=i[o],0<=t.indexOf(n)||(r[n]=e[n]);return r}function _classPrivateFieldInitSpec(e,t,n){_checkPrivateRedeclaration(e,t),t.set(e,n)}function _checkPrivateRedeclaration(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")}function _classPrivateFieldSet(e,t,n){return _classApplyDescriptorSet(e,_classExtractFieldDescriptor(e,t,"set"),n),n}function _classApplyDescriptorSet(e,t,n){if(t.set)t.set.call(e,n);else{if(!t.writable)throw new TypeError("attempted to set read only private field");t.value=n}}function _classPrivateFieldGet(e,t){return _classApplyDescriptorGet(e,_classExtractFieldDescriptor(e,t,"get"))}function _classExtractFieldDescriptor(e,t,n){if(!t.has(e))throw new TypeError("attempted to "+n+" private field on non-instance");return t.get(e)}function _classApplyDescriptorGet(e,t){return t.get?t.get.call(e):t.value}function _createSuper(n){var r=_isNativeReflectConstruct();return function(){var e,t=_getPrototypeOf(n);return _possibleConstructorReturn(this,r?(e=_getPrototypeOf(this).constructor,Reflect.construct(t,arguments,e)):t.apply(this,arguments))}}function _possibleConstructorReturn(e,t){if(t&&("object"===_typeof(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return _assertThisInitialized(e)}function _assertThisInitialized(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}function _getPrototypeOf(e){return(_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function _regeneratorRuntime(){"use strict";_regeneratorRuntime=function(){return a};var l,a={},e=Object.prototype,u=e.hasOwnProperty,c=Object.defineProperty||function(e,t,n){e[t]=n.value},t="function"==typeof Symbol?Symbol:{},r=t.iterator||"@@iterator",n=t.asyncIterator||"@@asyncIterator",i=t.toStringTag||"@@toStringTag";function o(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{o({},"")}catch(l){o=function(e,t,n){return e[t]=n}}function s(e,t,n,r){var i,o,a,s,t=t&&t.prototype instanceof v?t:v,t=Object.create(t.prototype),r=new k(r||[]);return c(t,"_invoke",{value:(i=e,o=n,a=r,s=f,function(e,t){if(s===h)throw new Error("Generator is already running");if(s===m){if("throw"===e)throw t;return{value:l,done:!0}}for(a.method=e,a.arg=t;;){var n=a.delegate;if(n){var r=function e(t,n){var r=n.method,i=t.iterator[r];if(i===l)return n.delegate=null,"throw"===r&&t.iterator.return&&(n.method="return",n.arg=l,e(t,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),g;i=d(i,t.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,g;var i=i.arg;return i?i.done?(n[t.resultName]=i.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=l),n.delegate=null,g):i:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,g)}(n,a);if(r){if(r===g)continue;return r}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if(s===f)throw s=m,a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);s=h;r=d(i,o,a);if("normal"===r.type){if(s=a.done?m:p,r.arg===g)continue;return{value:r.arg,done:a.done}}"throw"===r.type&&(s=m,a.method="throw",a.arg=r.arg)}})}),t}function d(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}a.wrap=s;var f="suspendedStart",p="suspendedYield",h="executing",m="completed",g={};function v(){}function y(){}function b(){}var _={};o(_,r,function(){return this});t=Object.getPrototypeOf,t=t&&t(t(C([])));t&&t!==e&&u.call(t,r)&&(_=t);var w=b.prototype=v.prototype=Object.create(_);function x(e){["next","throw","return"].forEach(function(t){o(e,t,function(e){return this._invoke(t,e)})})}function E(a,s){var t;c(this,"_invoke",{value:function(n,r){function e(){return new s(function(e,t){!function t(e,n,r,i){e=d(a[e],a,n);if("throw"!==e.type){var o=e.arg,n=o.value;return n&&"object"==_typeof(n)&&u.call(n,"__await")?s.resolve(n.__await).then(function(e){t("next",e,r,i)},function(e){t("throw",e,r,i)}):s.resolve(n).then(function(e){o.value=e,r(o)},function(e){return t("throw",e,r,i)})}i(e.arg)}(n,r,e,t)})}return t=t?t.then(e,e):e()}})}function T(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function S(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function k(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(T,this),this.reset(!0)}function C(t){if(t||""===t){var e=t[r];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,e=function e(){for(;++n<t.length;)if(u.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=l,e.done=!0,e};return e.next=e}}throw new TypeError(_typeof(t)+" is not iterable")}return c(w,"constructor",{value:y.prototype=b,configurable:!0}),c(b,"constructor",{value:y,configurable:!0}),y.displayName=o(b,i,"GeneratorFunction"),a.isGeneratorFunction=function(e){e="function"==typeof e&&e.constructor;return!!e&&(e===y||"GeneratorFunction"===(e.displayName||e.name))},a.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,b):(e.__proto__=b,o(e,i,"GeneratorFunction")),e.prototype=Object.create(w),e},a.awrap=function(e){return{__await:e}},x(E.prototype),o(E.prototype,n,function(){return this}),a.AsyncIterator=E,a.async=function(e,t,n,r,i){void 0===i&&(i=Promise);var o=new E(s(e,t,n,r),i);return a.isGeneratorFunction(t)?o:o.next().then(function(e){return e.done?e.value:o.next()})},x(w),o(w,i,"Generator"),o(w,r,function(){return this}),o(w,"toString",function(){return"[object Generator]"}),a.keys=function(e){var t,n=Object(e),r=[];for(t in n)r.push(t);return r.reverse(),function e(){for(;r.length;){var t=r.pop();if(t in n)return e.value=t,e.done=!1,e}return e.done=!0,e}},a.values=C,k.prototype={constructor:k,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=l,this.done=!1,this.delegate=null,this.method="next",this.arg=l,this.tryEntries.forEach(S),!e)for(var t in this)"t"===t.charAt(0)&&u.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=l)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(n){if(this.done)throw n;var r=this;function e(e,t){return o.type="throw",o.arg=n,r.next=e,t&&(r.method="next",r.arg=l),!!t}for(var t=this.tryEntries.length-1;0<=t;--t){var i=this.tryEntries[t],o=i.completion;if("root"===i.tryLoc)return e("end");if(i.tryLoc<=this.prev){var a=u.call(i,"catchLoc"),s=u.call(i,"finallyLoc");if(a&&s){if(this.prev<i.catchLoc)return e(i.catchLoc,!0);if(this.prev<i.finallyLoc)return e(i.finallyLoc)}else if(a){if(this.prev<i.catchLoc)return e(i.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return e(i.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;0<=n;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&u.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var i=r;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=e,o.arg=t,i?(this.method="next",this.next=i.finallyLoc,g):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),g},finish:function(e){for(var t=this.tryEntries.length-1;0<=t;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),S(n),g}},catch:function(e){for(var t=this.tryEntries.length-1;0<=t;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r,i=n.completion;return"throw"===i.type&&(r=i.arg,S(n)),r}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:C(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=l),g}},a}function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function _defineProperties(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,_toPropertyKey(r.key),r)}}function _createClass(e,t,n){return t&&_defineProperties(e.prototype,t),n&&_defineProperties(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function _createForOfIteratorHelper(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=_unsupportedIterableToArray(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,t=function(){};return{s:t,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:t}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,o=!0,a=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){a=!0,i=e},f:function(){try{o||null==n.return||n.return()}finally{if(a)throw i}}}}function asyncGeneratorStep(e,t,n,r,i,o,a){try{var s=e[o](a),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,i)}function _asyncToGenerator(s){return function(){var e=this,a=arguments;return new Promise(function(t,n){var r=s.apply(e,a);function i(e){asyncGeneratorStep(r,t,n,i,o,"next",e)}function o(e){asyncGeneratorStep(r,t,n,i,o,"throw",e)}i(void 0)})}}function _toConsumableArray(e){return _arrayWithoutHoles(e)||_iterableToArray(e)||_unsupportedIterableToArray(e)||_nonIterableSpread()}function _nonIterableSpread(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _iterableToArray(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function _arrayWithoutHoles(e){if(Array.isArray(e))return _arrayLikeToArray(e)}function _wrapRegExp(){_wrapRegExp=function(e,t){return new r(e,void 0,t)};var i=RegExp.prototype,a=new WeakMap;function r(e,t,n){t=new RegExp(e,t);return a.set(t,n||a.get(e)),_setPrototypeOf(t,r.prototype)}function o(i,e){var o=a.get(e);return Object.keys(o).reduce(function(e,t){var n=o[t];if("number"==typeof n)e[t]=i[n];else{for(var r=0;void 0===i[n[r]]&&r+1<n.length;)r++;e[t]=i[n[r]]}return e},Object.create(null))}return _inherits(r,RegExp),r.prototype.exec=function(e){var t=i.exec.call(this,e);return t&&(t.groups=o(t,this),(e=t.indices)&&(e.groups=o(e,this))),t},r.prototype[Symbol.replace]=function(e,t){if("string"==typeof t){var n=a.get(this);return i[Symbol.replace].call(this,e,t.replace(/\$<([^>]+)>/g,function(e,t){t=n[t];return"$"+(Array.isArray(t)?t.join("$"):t)}))}if("function"!=typeof t)return i[Symbol.replace].call(this,e,t);var r=this;return i[Symbol.replace].call(this,e,function(){var e=arguments;return"object"!=_typeof(e[e.length-1])&&(e=[].slice.call(e)).push(o(e,r)),t.apply(this,e)})},_wrapRegExp.apply(this,arguments)}function _inherits(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&_setPrototypeOf(e,t)}function _setPrototypeOf(e,t){return(_setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function _slicedToArray(e,t){return _arrayWithHoles(e)||_iterableToArrayLimit(e,t)||_unsupportedIterableToArray(e,t)||_nonIterableRest()}function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return _arrayLikeToArray(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?_arrayLikeToArray(e,t):void 0}}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function _iterableToArrayLimit(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,o,a,s=[],l=!0,u=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=o.call(n)).done)&&(s.push(r.value),s.length!==t);l=!0);}catch(e){u=!0,i=e}finally{try{if(!l&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw i}}return s}}function _arrayWithHoles(e){if(Array.isArray(e))return e}function ownKeys(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}function _objectSpread(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?ownKeys(Object(n),!0).forEach(function(e){_defineProperty(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function _defineProperty(e,t,n){return(t=_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function _toPropertyKey(e){e=_toPrimitive(e,"string");return"symbol"===_typeof(e)?e:String(e)}function _toPrimitive(e,t){if("object"!==_typeof(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0===n)return("string"===t?String:Number)(e);t=n.call(e,t||"default");if("object"!==_typeof(t))return t;throw new TypeError("@@toPrimitive must return a primitive value.")}function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}!function(e,t){"use strict";"object"==("undefined"==typeof module?"undefined":_typeof(module))&&"object"==_typeof(module.exports)?module.exports=e.document?t(e,!0):function(e){if(!e.document)throw new Error("jQuery requires a window with a document");return t(e)}:t(e)}("undefined"!=typeof window?window:this,function(E,e){"use strict";function m(e){return null!=e&&e===e.window}var t=[],n=Object.getPrototypeOf,s=t.slice,g=t.flat?function(e){return t.flat.call(e)}:function(e){return t.concat.apply([],e)},l=t.push,i=t.indexOf,r={},o=r.toString,v=r.hasOwnProperty,a=v.toString,u=a.call(Object),y={},b=function(e){return"function"==typeof e&&"number"!=typeof e.nodeType},T=E.document,c={type:!0,src:!0,nonce:!0,noModule:!0};function _(e,t,n){var r,i,o=(n=n||T).createElement("script");if(o.text=e,t)for(r in c)(i=t[r]||t.getAttribute&&t.getAttribute(r))&&o.setAttribute(r,i);n.head.appendChild(o).parentNode.removeChild(o)}function h(e){return null==e?e+"":"object"==_typeof(e)||"function"==typeof e?r[o.call(e)]||"object":_typeof(e)}var S=function e(t,n){return new e.fn.init(t,n)};function d(e){var t=!!e&&"length"in e&&e.length,n=h(e);return!b(e)&&!m(e)&&("array"===n||0===t||"number"==typeof t&&0<t&&t-1 in e)}S.fn=S.prototype={jquery:"3.5.1",constructor:S,length:0,toArray:function(){return s.call(this)},get:function(e){return null==e?s.call(this):e<0?this[e+this.length]:this[e]},pushStack:function(e){e=S.merge(this.constructor(),e);return e.prevObject=this,e},each:function(e){return S.each(this,e)},map:function(n){return this.pushStack(S.map(this,function(e,t){return n.call(e,t,e)}))},slice:function(){return this.pushStack(s.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack(S.grep(this,function(e,t){return(t+1)%2}))},odd:function(){return this.pushStack(S.grep(this,function(e,t){return t%2}))},eq:function(e){var t=this.length,e=+e+(e<0?t:0);return this.pushStack(0<=e&&e<t?[this[e]]:[])},end:function(){return this.prevObject||this.constructor()},push:l,sort:t.sort,splice:t.splice},S.extend=S.fn.extend=function(){var e,t,n,r,i,o=arguments[0]||{},a=1,s=arguments.length,l=!1;for("boolean"==typeof o&&(l=o,o=arguments[a]||{},a++),"object"==_typeof(o)||b(o)||(o={}),a===s&&(o=this,a--);a<s;a++)if(null!=(e=arguments[a]))for(t in e)n=e[t],"__proto__"!==t&&o!==n&&(l&&n&&(S.isPlainObject(n)||(r=Array.isArray(n)))?(i=o[t],i=r&&!Array.isArray(i)?[]:r||S.isPlainObject(i)?i:{},r=!1,o[t]=S.extend(l,i,n)):void 0!==n&&(o[t]=n));return o},S.extend({expando:"jQuery"+("3.5.1"+Math.random()).replace(/\D/g,""),isReady:!0,error:function(e){throw new Error(e)},noop:function(){},isPlainObject:function(e){return!(!e||"[object Object]"!==o.call(e)||(e=n(e))&&("function"!=typeof(e=v.call(e,"constructor")&&e.constructor)||a.call(e)!==u))},isEmptyObject:function(e){for(var t in e)return!1;return!0},globalEval:function(e,t,n){_(e,{nonce:t&&t.nonce},n)},each:function(e,t){var n,r=0;if(d(e))for(n=e.length;r<n&&!1!==t.call(e[r],r,e[r]);r++);else for(r in e)if(!1===t.call(e[r],r,e[r]))break;return e},makeArray:function(e,t){t=t||[];return null!=e&&(d(Object(e))?S.merge(t,"string"==typeof e?[e]:e):l.call(t,e)),t},inArray:function(e,t,n){return null==t?-1:i.call(t,e,n)},merge:function(e,t){for(var n=+t.length,r=0,i=e.length;r<n;r++)e[i++]=t[r];return e.length=i,e},grep:function(e,t,n){for(var r=[],i=0,o=e.length,a=!n;i<o;i++)!t(e[i],i)!=a&&r.push(e[i]);return r},map:function(e,t,n){var r,i,o=0,a=[];if(d(e))for(r=e.length;o<r;o++)null!=(i=t(e[o],o,n))&&a.push(i);else for(o in e)null!=(i=t(e[o],o,n))&&a.push(i);return g(a)},guid:1,support:y}),"function"==typeof Symbol&&(S.fn[Symbol.iterator]=t[Symbol.iterator]),S.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(e,t){r["[object "+t+"]"]=t.toLowerCase()});var f=function(n){function d(e,t){return e="0x"+e.slice(1)-65536,t||(e<0?String.fromCharCode(65536+e):String.fromCharCode(e>>10|55296,1023&e|56320))}function r(){x()}var e,p,_,o,i,h,f,m,w,l,u,x,E,a,T,g,s,c,v,S="sizzle"+ +new Date,y=n.document,k=0,b=0,C=le(),A=le(),D=le(),R=le(),L=function(e,t){return e===t&&(u=!0),0},N={}.hasOwnProperty,t=[],O=t.pop,j=t.push,I=t.push,P=t.slice,F=function(e,t){for(var n=0,r=e.length;n<r;n++)if(e[n]===t)return n;return-1},M="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",q="[\\x20\\t\\r\\n\\f]",H="(?:\\\\[\\da-fA-F]{1,6}"+q+"?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+",B="\\["+q+"*("+H+")(?:"+q+"*([*^$|!~]?=)"+q+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+H+"))|)"+q+"*\\]",U=":("+H+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+B+")*)|.*)\\)|)",G=new RegExp(q+"+","g"),W=new RegExp("^"+q+"+|((?:^|[^\\\\])(?:\\\\.)*)"+q+"+$","g"),$=new RegExp("^"+q+"*,"+q+"*"),z=new RegExp("^"+q+"*([>+~]|"+q+")"+q+"*"),V=new RegExp(q+"|>"),X=new RegExp(U),Q=new RegExp("^"+H+"$"),Y={ID:new RegExp("^#("+H+")"),CLASS:new RegExp("^\\.("+H+")"),TAG:new RegExp("^("+H+"|[*])"),ATTR:new RegExp("^"+B),PSEUDO:new RegExp("^"+U),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+q+"*(even|odd|(([+-]|)(\\d*)n|)"+q+"*(?:([+-]|)"+q+"*(\\d+)|))"+q+"*\\)|)","i"),bool:new RegExp("^(?:"+M+")$","i"),needsContext:new RegExp("^"+q+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+q+"*((?:-\\d)?\\d*)"+q+"*\\)|)(?=[^-]|$)","i")},K=/HTML$/i,J=/^(?:input|select|textarea|button)$/i,Z=/^h\d$/i,ee=/^[^{]+\{\s*\[native \w/,te=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,ne=/[+~]/,re=new RegExp("\\\\[\\da-fA-F]{1,6}"+q+"?|\\\\([^\\r\\n\\f])","g"),ie=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\0-\x1f\x7f-\uFFFF\w-]/g,oe=function(e,t){return t?"\0"===e?"�":e.slice(0,-1)+"\\"+e.charCodeAt(e.length-1).toString(16)+" ":"\\"+e},ae=ye(function(e){return!0===e.disabled&&"fieldset"===e.nodeName.toLowerCase()},{dir:"parentNode",next:"legend"});try{I.apply(t=P.call(y.childNodes),y.childNodes),t[y.childNodes.length].nodeType}catch(e){I={apply:t.length?function(e,t){j.apply(e,P.call(t))}:function(e,t){for(var n=e.length,r=0;e[n++]=t[r++];);e.length=n-1}}}function se(e,t,n,r){var i,o,a,s,l,u,c,d=t&&t.ownerDocument,f=t?t.nodeType:9;if(n=n||[],"string"!=typeof e||!e||1!==f&&9!==f&&11!==f)return n;if(!r&&(x(t),t=t||E,T)){if(11!==f&&(l=te.exec(e)))if(i=l[1]){if(9===f){if(!(a=t.getElementById(i)))return n;if(a.id===i)return n.push(a),n}else if(d&&(a=d.getElementById(i))&&v(t,a)&&a.id===i)return n.push(a),n}else{if(l[2])return I.apply(n,t.getElementsByTagName(e)),n;if((i=l[3])&&p.getElementsByClassName&&t.getElementsByClassName)return I.apply(n,t.getElementsByClassName(i)),n}if(p.qsa&&!R[e+" "]&&(!g||!g.test(e))&&(1!==f||"object"!==t.nodeName.toLowerCase())){if(c=e,d=t,1===f&&(V.test(e)||z.test(e))){for((d=ne.test(e)&&me(t.parentNode)||t)===t&&p.scope||((s=t.getAttribute("id"))?s=s.replace(ie,oe):t.setAttribute("id",s=S)),o=(u=h(e)).length;o--;)u[o]=(s?"#"+s:":scope")+" "+ve(u[o]);c=u.join(",")}try{return I.apply(n,d.querySelectorAll(c)),n}catch(t){R(e,!0)}finally{s===S&&t.removeAttribute("id")}}}return m(e.replace(W,"$1"),t,n,r)}function le(){var r=[];return function e(t,n){return r.push(t+" ")>_.cacheLength&&delete e[r.shift()],e[t+" "]=n}}function ue(e){return e[S]=!0,e}function ce(e){var t=E.createElement("fieldset");try{return!!e(t)}catch(e){return!1}finally{t.parentNode&&t.parentNode.removeChild(t),t=null}}function de(e,t){for(var n=e.split("|"),r=n.length;r--;)_.attrHandle[n[r]]=t}function fe(e,t){var n=t&&e,r=n&&1===e.nodeType&&1===t.nodeType&&e.sourceIndex-t.sourceIndex;if(r)return r;if(n)for(;n=n.nextSibling;)if(n===t)return-1;return e?1:-1}function pe(t){return function(e){return"form"in e?e.parentNode&&!1===e.disabled?"label"in e?"label"in e.parentNode?e.parentNode.disabled===t:e.disabled===t:e.isDisabled===t||e.isDisabled!==!t&&ae(e)===t:e.disabled===t:"label"in e&&e.disabled===t}}function he(a){return ue(function(o){return o=+o,ue(function(e,t){for(var n,r=a([],e.length,o),i=r.length;i--;)e[n=r[i]]&&(e[n]=!(t[n]=e[n]))})})}function me(e){return e&&void 0!==e.getElementsByTagName&&e}for(e in p=se.support={},i=se.isXML=function(e){var t=e.namespaceURI,e=(e.ownerDocument||e).documentElement;return!K.test(t||e&&e.nodeName||"HTML")},x=se.setDocument=function(e){var t,e=e?e.ownerDocument||e:y;return e!=E&&9===e.nodeType&&e.documentElement&&(a=(E=e).documentElement,T=!i(E),y!=E&&(t=E.defaultView)&&t.top!==t&&(t.addEventListener?t.addEventListener("unload",r,!1):t.attachEvent&&t.attachEvent("onunload",r)),p.scope=ce(function(e){return a.appendChild(e).appendChild(E.createElement("div")),void 0!==e.querySelectorAll&&!e.querySelectorAll(":scope fieldset div").length}),p.attributes=ce(function(e){return e.className="i",!e.getAttribute("className")}),p.getElementsByTagName=ce(function(e){return e.appendChild(E.createComment("")),!e.getElementsByTagName("*").length}),p.getElementsByClassName=ee.test(E.getElementsByClassName),p.getById=ce(function(e){return a.appendChild(e).id=S,!E.getElementsByName||!E.getElementsByName(S).length}),p.getById?(_.filter.ID=function(e){var t=e.replace(re,d);return function(e){return e.getAttribute("id")===t}},_.find.ID=function(e,t){if(void 0!==t.getElementById&&T){e=t.getElementById(e);return e?[e]:[]}}):(_.filter.ID=function(e){var t=e.replace(re,d);return function(e){e=void 0!==e.getAttributeNode&&e.getAttributeNode("id");return e&&e.value===t}},_.find.ID=function(e,t){if(void 0!==t.getElementById&&T){var n,r,i,o=t.getElementById(e);if(o){if((n=o.getAttributeNode("id"))&&n.value===e)return[o];for(i=t.getElementsByName(e),r=0;o=i[r++];)if((n=o.getAttributeNode("id"))&&n.value===e)return[o]}return[]}}),_.find.TAG=p.getElementsByTagName?function(e,t){return void 0!==t.getElementsByTagName?t.getElementsByTagName(e):p.qsa?t.querySelectorAll(e):void 0}:function(e,t){var n,r=[],i=0,o=t.getElementsByTagName(e);if("*"!==e)return o;for(;n=o[i++];)1===n.nodeType&&r.push(n);return r},_.find.CLASS=p.getElementsByClassName&&function(e,t){if(void 0!==t.getElementsByClassName&&T)return t.getElementsByClassName(e)},s=[],g=[],(p.qsa=ee.test(E.querySelectorAll))&&(ce(function(e){var t;a.appendChild(e).innerHTML="<a id='"+S+"'></a><select id='"+S+"-\r\\' msallowcapture=''><option selected=''></option></select>",e.querySelectorAll("[msallowcapture^='']").length&&g.push("[*^$]="+q+"*(?:''|\"\")"),e.querySelectorAll("[selected]").length||g.push("\\["+q+"*(?:value|"+M+")"),e.querySelectorAll("[id~="+S+"-]").length||g.push("~="),(t=E.createElement("input")).setAttribute("name",""),e.appendChild(t),e.querySelectorAll("[name='']").length||g.push("\\["+q+"*name"+q+"*="+q+"*(?:''|\"\")"),e.querySelectorAll(":checked").length||g.push(":checked"),e.querySelectorAll("a#"+S+"+*").length||g.push(".#.+[+~]"),e.querySelectorAll("\\\f"),g.push("[\\r\\n\\f]")}),ce(function(e){e.innerHTML="<a href='' disabled='disabled'></a><select disabled='disabled'><option/></select>";var t=E.createElement("input");t.setAttribute("type","hidden"),e.appendChild(t).setAttribute("name","D"),e.querySelectorAll("[name=d]").length&&g.push("name"+q+"*[*^$|!~]?="),2!==e.querySelectorAll(":enabled").length&&g.push(":enabled",":disabled"),a.appendChild(e).disabled=!0,2!==e.querySelectorAll(":disabled").length&&g.push(":enabled",":disabled"),e.querySelectorAll("*,:x"),g.push(",.*:")})),(p.matchesSelector=ee.test(c=a.matches||a.webkitMatchesSelector||a.mozMatchesSelector||a.oMatchesSelector||a.msMatchesSelector))&&ce(function(e){p.disconnectedMatch=c.call(e,"*"),c.call(e,"[s!='']:x"),s.push("!=",U)}),g=g.length&&new RegExp(g.join("|")),s=s.length&&new RegExp(s.join("|")),t=ee.test(a.compareDocumentPosition),v=t||ee.test(a.contains)?function(e,t){var n=9===e.nodeType?e.documentElement:e,t=t&&t.parentNode;return e===t||!(!t||1!==t.nodeType||!(n.contains?n.contains(t):e.compareDocumentPosition&&16&e.compareDocumentPosition(t)))}:function(e,t){if(t)for(;t=t.parentNode;)if(t===e)return!0;return!1},L=t?function(e,t){if(e===t)return u=!0,0;var n=!e.compareDocumentPosition-!t.compareDocumentPosition;return n||(1&(n=(e.ownerDocument||e)==(t.ownerDocument||t)?e.compareDocumentPosition(t):1)||!p.sortDetached&&t.compareDocumentPosition(e)===n?e==E||e.ownerDocument==y&&v(y,e)?-1:t==E||t.ownerDocument==y&&v(y,t)?1:l?F(l,e)-F(l,t):0:4&n?-1:1)}:function(e,t){if(e===t)return u=!0,0;var n,r=0,i=e.parentNode,o=t.parentNode,a=[e],s=[t];if(!i||!o)return e==E?-1:t==E?1:i?-1:o?1:l?F(l,e)-F(l,t):0;if(i===o)return fe(e,t);for(n=e;n=n.parentNode;)a.unshift(n);for(n=t;n=n.parentNode;)s.unshift(n);for(;a[r]===s[r];)r++;return r?fe(a[r],s[r]):a[r]==y?-1:s[r]==y?1:0}),E},se.matches=function(e,t){return se(e,null,null,t)},se.matchesSelector=function(e,t){if(x(e),p.matchesSelector&&T&&!R[t+" "]&&(!s||!s.test(t))&&(!g||!g.test(t)))try{var n=c.call(e,t);if(n||p.disconnectedMatch||e.document&&11!==e.document.nodeType)return n}catch(e){R(t,!0)}return 0<se(t,E,null,[e]).length},se.contains=function(e,t){return(e.ownerDocument||e)!=E&&x(e),v(e,t)},se.attr=function(e,t){(e.ownerDocument||e)!=E&&x(e);var n=_.attrHandle[t.toLowerCase()],n=n&&N.call(_.attrHandle,t.toLowerCase())?n(e,t,!T):void 0;return void 0!==n?n:p.attributes||!T?e.getAttribute(t):(n=e.getAttributeNode(t))&&n.specified?n.value:null},se.escape=function(e){return(e+"").replace(ie,oe)},se.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},se.uniqueSort=function(e){var t,n=[],r=0,i=0;if(u=!p.detectDuplicates,l=!p.sortStable&&e.slice(0),e.sort(L),u){for(;t=e[i++];)t===e[i]&&(r=n.push(i));for(;r--;)e.splice(n[r],1)}return l=null,e},o=se.getText=function(e){var t,n="",r=0,i=e.nodeType;if(i){if(1===i||9===i||11===i){if("string"==typeof e.textContent)return e.textContent;for(e=e.firstChild;e;e=e.nextSibling)n+=o(e)}else if(3===i||4===i)return e.nodeValue}else for(;t=e[r++];)n+=o(t);return n},(_=se.selectors={cacheLength:50,createPseudo:ue,match:Y,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(re,d),e[3]=(e[3]||e[4]||e[5]||"").replace(re,d),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||se.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&se.error(e[0]),e},PSEUDO:function(e){var t,n=!e[6]&&e[2];return Y.CHILD.test(e[0])?null:(e[3]?e[2]=e[4]||e[5]||"":n&&X.test(n)&&(t=h(n,!0))&&(t=n.indexOf(")",n.length-t)-n.length)&&(e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(re,d).toLowerCase();return"*"===e?function(){return!0}:function(e){return e.nodeName&&e.nodeName.toLowerCase()===t}},CLASS:function(e){var t=C[e+" "];return t||(t=new RegExp("(^|"+q+")"+e+"("+q+"|$)"))&&C(e,function(e){return t.test("string"==typeof e.className&&e.className||void 0!==e.getAttribute&&e.getAttribute("class")||"")})},ATTR:function(t,n,r){return function(e){e=se.attr(e,t);return null==e?"!="===n:!n||(e+="","="===n?e===r:"!="===n?e!==r:"^="===n?r&&0===e.indexOf(r):"*="===n?r&&-1<e.indexOf(r):"$="===n?r&&e.slice(-r.length)===r:"~="===n?-1<(" "+e.replace(G," ")+" ").indexOf(r):"|="===n&&(e===r||e.slice(0,r.length+1)===r+"-"))}},CHILD:function(h,e,t,m,g){var v="nth"!==h.slice(0,3),y="last"!==h.slice(-4),b="of-type"===e;return 1===m&&0===g?function(e){return!!e.parentNode}:function(e,t,n){var r,i,o,a,s,l,u=v!=y?"nextSibling":"previousSibling",c=e.parentNode,d=b&&e.nodeName.toLowerCase(),f=!n&&!b,p=!1;if(c){if(v){for(;u;){for(a=e;a=a[u];)if(b?a.nodeName.toLowerCase()===d:1===a.nodeType)return!1;l=u="only"===h&&!l&&"nextSibling"}return!0}if(l=[y?c.firstChild:c.lastChild],y&&f){for(p=(s=(r=(i=(o=(a=c)[S]||(a[S]={}))[a.uniqueID]||(o[a.uniqueID]={}))[h]||[])[0]===k&&r[1])&&r[2],a=s&&c.childNodes[s];a=++s&&a&&a[u]||(p=s=0)||l.pop();)if(1===a.nodeType&&++p&&a===e){i[h]=[k,s,p];break}}else if(f&&(p=s=(r=(i=(o=(a=e)[S]||(a[S]={}))[a.uniqueID]||(o[a.uniqueID]={}))[h]||[])[0]===k&&r[1]),!1===p)for(;(a=++s&&a&&a[u]||(p=s=0)||l.pop())&&((b?a.nodeName.toLowerCase()!==d:1!==a.nodeType)||!++p||(f&&((i=(o=a[S]||(a[S]={}))[a.uniqueID]||(o[a.uniqueID]={}))[h]=[k,p]),a!==e)););return(p-=g)===m||p%m==0&&0<=p/m}}},PSEUDO:function(e,o){var t,a=_.pseudos[e]||_.setFilters[e.toLowerCase()]||se.error("unsupported pseudo: "+e);return a[S]?a(o):1<a.length?(t=[e,e,"",o],_.setFilters.hasOwnProperty(e.toLowerCase())?ue(function(e,t){for(var n,r=a(e,o),i=r.length;i--;)e[n=F(e,r[i])]=!(t[n]=r[i])}):function(e){return a(e,0,t)}):a}},pseudos:{not:ue(function(e){var r=[],i=[],s=f(e.replace(W,"$1"));return s[S]?ue(function(e,t,n,r){for(var i,o=s(e,null,r,[]),a=e.length;a--;)(i=o[a])&&(e[a]=!(t[a]=i))}):function(e,t,n){return r[0]=e,s(r,null,n,i),r[0]=null,!i.pop()}}),has:ue(function(t){return function(e){return 0<se(t,e).length}}),contains:ue(function(t){return t=t.replace(re,d),function(e){return-1<(e.textContent||o(e)).indexOf(t)}}),lang:ue(function(n){return Q.test(n||"")||se.error("unsupported lang: "+n),n=n.replace(re,d).toLowerCase(),function(e){var t;do{if(t=T?e.lang:e.getAttribute("xml:lang")||e.getAttribute("lang"))return(t=t.toLowerCase())===n||0===t.indexOf(n+"-")}while((e=e.parentNode)&&1===e.nodeType);return!1}}),target:function(e){var t=n.location&&n.location.hash;return t&&t.slice(1)===e.id},root:function(e){return e===a},focus:function(e){return e===E.activeElement&&(!E.hasFocus||E.hasFocus())&&!!(e.type||e.href||~e.tabIndex)},enabled:pe(!1),disabled:pe(!0),checked:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&!!e.checked||"option"===t&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,!0===e.selected},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeType<6)return!1;return!0},parent:function(e){return!_.pseudos.empty(e)},header:function(e){return Z.test(e.nodeName)},input:function(e){return J.test(e.nodeName)},button:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&"button"===e.type||"button"===t},text:function(e){return"input"===e.nodeName.toLowerCase()&&"text"===e.type&&(null==(e=e.getAttribute("type"))||"text"===e.toLowerCase())},first:he(function(){return[0]}),last:he(function(e,t){return[t-1]}),eq:he(function(e,t,n){return[n<0?n+t:n]}),even:he(function(e,t){for(var n=0;n<t;n+=2)e.push(n);return e}),odd:he(function(e,t){for(var n=1;n<t;n+=2)e.push(n);return e}),lt:he(function(e,t,n){for(var r=n<0?n+t:t<n?t:n;0<=--r;)e.push(r);return e}),gt:he(function(e,t,n){for(var r=n<0?n+t:n;++r<t;)e.push(r);return e})}}).pseudos.nth=_.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})_.pseudos[e]=function(t){return function(e){return"input"===e.nodeName.toLowerCase()&&e.type===t}}(e);for(e in{submit:!0,reset:!0})_.pseudos[e]=function(n){return function(e){var t=e.nodeName.toLowerCase();return("input"===t||"button"===t)&&e.type===n}}(e);function ge(){}function ve(e){for(var t=0,n=e.length,r="";t<n;t++)r+=e[t].value;return r}function ye(a,e,t){var s=e.dir,l=e.next,u=l||s,c=t&&"parentNode"===u,d=b++;return e.first?function(e,t,n){for(;e=e[s];)if(1===e.nodeType||c)return a(e,t,n);return!1}:function(e,t,n){var r,i,o=[k,d];if(n){for(;e=e[s];)if((1===e.nodeType||c)&&a(e,t,n))return!0}else for(;e=e[s];)if(1===e.nodeType||c)if(r=(i=e[S]||(e[S]={}))[e.uniqueID]||(i[e.uniqueID]={}),l&&l===e.nodeName.toLowerCase())e=e[s]||e;else{if((i=r[u])&&i[0]===k&&i[1]===d)return o[2]=i[2];if((r[u]=o)[2]=a(e,t,n))return!0}return!1}}function be(i){return 1<i.length?function(e,t,n){for(var r=i.length;r--;)if(!i[r](e,t,n))return!1;return!0}:i[0]}function _e(e,t,n,r,i){for(var o,a=[],s=0,l=e.length,u=null!=t;s<l;s++)(o=e[s])&&(n&&!n(o,r,i)||(a.push(o),u&&t.push(s)));return a}function we(e){for(var r,t,n,i=e.length,o=_.relative[e[0].type],a=o||_.relative[" "],s=o?1:0,l=ye(function(e){return e===r},a,!0),u=ye(function(e){return-1<F(r,e)},a,!0),c=[function(e,t,n){n=!o&&(n||t!==w)||((r=t).nodeType?l:u)(e,t,n);return r=null,n}];s<i;s++)if(t=_.relative[e[s].type])c=[ye(be(c),t)];else{if((t=_.filter[e[s].type].apply(null,e[s].matches))[S]){for(n=++s;n<i&&!_.relative[e[n].type];n++);return function e(p,h,m,g,v,t){return g&&!g[S]&&(g=e(g)),v&&!v[S]&&(v=e(v,t)),ue(function(e,t,n,r){var i,o,a,s=[],l=[],u=t.length,c=e||function(e,t,n){for(var r=0,i=t.length;r<i;r++)se(e,t[r],n);return n}(h||"*",n.nodeType?[n]:n,[]),d=!p||!e&&h?c:_e(c,s,p,n,r),f=m?v||(e?p:u||g)?[]:t:d;if(m&&m(d,f,n,r),g)for(i=_e(f,l),g(i,[],n,r),o=i.length;o--;)(a=i[o])&&(f[l[o]]=!(d[l[o]]=a));if(e){if(v||p){if(v){for(i=[],o=f.length;o--;)(a=f[o])&&i.push(d[o]=a);v(null,f=[],i,r)}for(o=f.length;o--;)(a=f[o])&&-1<(i=v?F(e,a):s[o])&&(e[i]=!(t[i]=a))}}else f=_e(f===t?f.splice(u,f.length):f),v?v(null,t,f,r):I.apply(t,f)})}(1<s&&be(c),1<s&&ve(e.slice(0,s-1).concat({value:" "===e[s-2].type?"*":""})).replace(W,"$1"),t,s<n&&we(e.slice(s,n)),n<i&&we(e=e.slice(n)),n<i&&ve(e))}c.push(t)}return be(c)}return ge.prototype=_.filters=_.pseudos,_.setFilters=new ge,h=se.tokenize=function(e,t){var n,r,i,o,a,s,l,u=A[e+" "];if(u)return t?0:u.slice(0);for(a=e,s=[],l=_.preFilter;a;){for(o in n&&!(r=$.exec(a))||(r&&(a=a.slice(r[0].length)||a),s.push(i=[])),n=!1,(r=z.exec(a))&&(n=r.shift(),i.push({value:n,type:r[0].replace(W," ")}),a=a.slice(n.length)),_.filter)!(r=Y[o].exec(a))||l[o]&&!(r=l[o](r))||(n=r.shift(),i.push({value:n,type:o,matches:r}),a=a.slice(n.length));if(!n)break}return t?a.length:a?se.error(e):A(e,s).slice(0)},f=se.compile=function(e,t){var n,g,v,y,b,r,i=[],o=[],a=D[e+" "];if(!a){for(n=(t=t||h(e)).length;n--;)((a=we(t[n]))[S]?i:o).push(a);(a=D(e,(g=o,y=0<(v=i).length,b=0<g.length,r=function(e,t,n,r,i){var o,a,s,l=0,u="0",c=e&&[],d=[],f=w,p=e||b&&_.find.TAG("*",i),h=k+=null==f?1:Math.random()||.1,m=p.length;for(i&&(w=t==E||t||i);u!==m&&null!=(o=p[u]);u++){if(b&&o){for(a=0,t||o.ownerDocument==E||(x(o),n=!T);s=g[a++];)if(s(o,t||E,n)){r.push(o);break}i&&(k=h)}y&&((o=!s&&o)&&l--,e&&c.push(o))}if(l+=u,y&&u!==l){for(a=0;s=v[a++];)s(c,d,t,n);if(e){if(0<l)for(;u--;)c[u]||d[u]||(d[u]=O.call(r));d=_e(d)}I.apply(r,d),i&&!e&&0<d.length&&1<l+v.length&&se.uniqueSort(r)}return i&&(k=h,w=f),c},y?ue(r):r))).selector=e}return a},m=se.select=function(e,t,n,r){var i,o,a,s,l,u="function"==typeof e&&e,c=!r&&h(e=u.selector||e);if(n=n||[],1===c.length){if(2<(o=c[0]=c[0].slice(0)).length&&"ID"===(a=o[0]).type&&9===t.nodeType&&T&&_.relative[o[1].type]){if(!(t=(_.find.ID(a.matches[0].replace(re,d),t)||[])[0]))return n;u&&(t=t.parentNode),e=e.slice(o.shift().value.length)}for(i=Y.needsContext.test(e)?0:o.length;i--&&(a=o[i],!_.relative[s=a.type]);)if((l=_.find[s])&&(r=l(a.matches[0].replace(re,d),ne.test(o[0].type)&&me(t.parentNode)||t))){if(o.splice(i,1),!(e=r.length&&ve(o)))return I.apply(n,r),n;break}}return(u||f(e,c))(r,t,!T,n,!t||ne.test(e)&&me(t.parentNode)||t),n},p.sortStable=S.split("").sort(L).join("")===S,p.detectDuplicates=!!u,x(),p.sortDetached=ce(function(e){return 1&e.compareDocumentPosition(E.createElement("fieldset"))}),ce(function(e){return e.innerHTML="<a href='#'></a>","#"===e.firstChild.getAttribute("href")})||de("type|href|height|width",function(e,t,n){if(!n)return e.getAttribute(t,"type"===t.toLowerCase()?1:2)}),p.attributes&&ce(function(e){return e.innerHTML="<input/>",e.firstChild.setAttribute("value",""),""===e.firstChild.getAttribute("value")})||de("value",function(e,t,n){if(!n&&"input"===e.nodeName.toLowerCase())return e.defaultValue}),ce(function(e){return null==e.getAttribute("disabled")})||de(M,function(e,t,n){if(!n)return!0===e[t]?t.toLowerCase():(t=e.getAttributeNode(t))&&t.specified?t.value:null}),se}(E);S.find=f,S.expr=f.selectors,S.expr[":"]=S.expr.pseudos,S.uniqueSort=S.unique=f.uniqueSort,S.text=f.getText,S.isXMLDoc=f.isXML,S.contains=f.contains,S.escapeSelector=f.escape;function p(e,t,n){for(var r=[],i=void 0!==n;(e=e[t])&&9!==e.nodeType;)if(1===e.nodeType){if(i&&S(e).is(n))break;r.push(e)}return r}function w(e,t){for(var n=[];e;e=e.nextSibling)1===e.nodeType&&e!==t&&n.push(e);return n}var x=S.expr.match.needsContext;function k(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()}var C=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;function A(e,n,r){return b(n)?S.grep(e,function(e,t){return!!n.call(e,t,e)!==r}):n.nodeType?S.grep(e,function(e){return e===n!==r}):"string"!=typeof n?S.grep(e,function(e){return-1<i.call(n,e)!==r}):S.filter(n,e,r)}S.filter=function(e,t,n){var r=t[0];return n&&(e=":not("+e+")"),1===t.length&&1===r.nodeType?S.find.matchesSelector(r,e)?[r]:[]:S.find.matches(e,S.grep(t,function(e){return 1===e.nodeType}))},S.fn.extend({find:function(e){var t,n,r=this.length,i=this;if("string"!=typeof e)return this.pushStack(S(e).filter(function(){for(t=0;t<r;t++)if(S.contains(i[t],this))return!0}));for(n=this.pushStack([]),t=0;t<r;t++)S.find(e,i[t],n);return 1<r?S.uniqueSort(n):n},filter:function(e){return this.pushStack(A(this,e||[],!1))},not:function(e){return this.pushStack(A(this,e||[],!0))},is:function(e){return!!A(this,"string"==typeof e&&x.test(e)?S(e):e||[],!1).length}});var D,R=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/;(S.fn.init=function(e,t,n){if(!e)return this;if(n=n||D,"string"!=typeof e)return e.nodeType?(this[0]=e,this.length=1,this):b(e)?void 0!==n.ready?n.ready(e):e(S):S.makeArray(e,this);if(!(r="<"===e[0]&&">"===e[e.length-1]&&3<=e.length?[null,e,null]:R.exec(e))||!r[1]&&t)return(!t||t.jquery?t||n:this.constructor(t)).find(e);if(r[1]){if(t=t instanceof S?t[0]:t,S.merge(this,S.parseHTML(r[1],t&&t.nodeType?t.ownerDocument||t:T,!0)),C.test(r[1])&&S.isPlainObject(t))for(var r in t)b(this[r])?this[r](t[r]):this.attr(r,t[r]);return this}return(e=T.getElementById(r[2]))&&(this[0]=e,this.length=1),this}).prototype=S.fn,D=S(T);var L=/^(?:parents|prev(?:Until|All))/,N={children:!0,contents:!0,next:!0,prev:!0};function O(e,t){for(;(e=e[t])&&1!==e.nodeType;);return e}S.fn.extend({has:function(e){var t=S(e,this),n=t.length;return this.filter(function(){for(var e=0;e<n;e++)if(S.contains(this,t[e]))return!0})},closest:function(e,t){var n,r=0,i=this.length,o=[],a="string"!=typeof e&&S(e);if(!x.test(e))for(;r<i;r++)for(n=this[r];n&&n!==t;n=n.parentNode)if(n.nodeType<11&&(a?-1<a.index(n):1===n.nodeType&&S.find.matchesSelector(n,e))){o.push(n);break}return this.pushStack(1<o.length?S.uniqueSort(o):o)},index:function(e){return e?"string"==typeof e?i.call(S(e),this[0]):i.call(this,e.jquery?e[0]:e):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){return this.pushStack(S.uniqueSort(S.merge(this.get(),S(e,t))))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}}),S.each({parent:function(e){e=e.parentNode;return e&&11!==e.nodeType?e:null},parents:function(e){return p(e,"parentNode")},parentsUntil:function(e,t,n){return p(e,"parentNode",n)},next:function(e){return O(e,"nextSibling")},prev:function(e){return O(e,"previousSibling")},nextAll:function(e){return p(e,"nextSibling")},prevAll:function(e){return p(e,"previousSibling")},nextUntil:function(e,t,n){return p(e,"nextSibling",n)},prevUntil:function(e,t,n){return p(e,"previousSibling",n)},siblings:function(e){return w((e.parentNode||{}).firstChild,e)},children:function(e){return w(e.firstChild)},contents:function(e){return null!=e.contentDocument&&n(e.contentDocument)?e.contentDocument:(k(e,"template")&&(e=e.content||e),S.merge([],e.childNodes))}},function(r,i){S.fn[r]=function(e,t){var n=S.map(this,i,e);return"Until"!==r.slice(-5)&&(t=e),t&&"string"==typeof t&&(n=S.filter(t,n)),1<this.length&&(N[r]||S.uniqueSort(n),L.test(r)&&n.reverse()),this.pushStack(n)}});var j=/[^\x20\t\r\n\f]+/g;function I(e){return e}function P(e){throw e}function F(e,t,n,r){var i;try{e&&b(i=e.promise)?i.call(e).done(t).fail(n):e&&b(i=e.then)?i.call(e,t,n):t.apply(void 0,[e].slice(r))}catch(e){n.apply(void 0,[e])}}S.Callbacks=function(r){var n;r="string"==typeof r?(n={},S.each(r.match(j)||[],function(e,t){n[t]=!0}),n):S.extend({},r);function i(){for(a=a||r.once,t=o=!0;l.length;u=-1)for(e=l.shift();++u<s.length;)!1===s[u].apply(e[0],e[1])&&r.stopOnFalse&&(u=s.length,e=!1);r.memory||(e=!1),o=!1,a&&(s=e?[]:"")}var o,e,t,a,s=[],l=[],u=-1,c={add:function(){return s&&(e&&!o&&(u=s.length-1,l.push(e)),function n(e){S.each(e,function(e,t){b(t)?r.unique&&c.has(t)||s.push(t):t&&t.length&&"string"!==h(t)&&n(t)})}(arguments),e&&!o&&i()),this},remove:function(){return S.each(arguments,function(e,t){for(var n;-1<(n=S.inArray(t,s,n));)s.splice(n,1),n<=u&&u--}),this},has:function(e){return e?-1<S.inArray(e,s):0<s.length},empty:function(){return s=s&&[],this},disable:function(){return a=l=[],s=e="",this},disabled:function(){return!s},lock:function(){return a=l=[],e||o||(s=e=""),this},locked:function(){return!!a},fireWith:function(e,t){return a||(t=[e,(t=t||[]).slice?t.slice():t],l.push(t),o||i()),this},fire:function(){return c.fireWith(this,arguments),this},fired:function(){return!!t}};return c},S.extend({Deferred:function(e){var o=[["notify","progress",S.Callbacks("memory"),S.Callbacks("memory"),2],["resolve","done",S.Callbacks("once memory"),S.Callbacks("once memory"),0,"resolved"],["reject","fail",S.Callbacks("once memory"),S.Callbacks("once memory"),1,"rejected"]],i="pending",a={state:function(){return i},always:function(){return s.done(arguments).fail(arguments),this},catch:function(e){return a.then(null,e)},pipe:function(){var i=arguments;return S.Deferred(function(r){S.each(o,function(e,t){var n=b(i[t[4]])&&i[t[4]];s[t[1]](function(){var e=n&&n.apply(this,arguments);e&&b(e.promise)?e.promise().progress(r.notify).done(r.resolve).fail(r.reject):r[t[0]+"With"](this,n?[e]:arguments)})}),i=null}).promise()},then:function(t,n,r){var l=0;function u(i,o,a,s){return function(){function e(){var e,t;if(!(i<l)){if((e=a.apply(n,r))===o.promise())throw new TypeError("Thenable self-resolution");t=e&&("object"==_typeof(e)||"function"==typeof e)&&e.then,b(t)?s?t.call(e,u(l,o,I,s),u(l,o,P,s)):(l++,t.call(e,u(l,o,I,s),u(l,o,P,s),u(l,o,I,o.notifyWith))):(a!==I&&(n=void 0,r=[e]),(s||o.resolveWith)(n,r))}}var n=this,r=arguments,t=s?e:function(){try{e()}catch(e){S.Deferred.exceptionHook&&S.Deferred.exceptionHook(e,t.stackTrace),l<=i+1&&(a!==P&&(n=void 0,r=[e]),o.rejectWith(n,r))}};i?t():(S.Deferred.getStackHook&&(t.stackTrace=S.Deferred.getStackHook()),E.setTimeout(t))}}return S.Deferred(function(e){o[0][3].add(u(0,e,b(r)?r:I,e.notifyWith)),o[1][3].add(u(0,e,b(t)?t:I)),o[2][3].add(u(0,e,b(n)?n:P))}).promise()},promise:function(e){return null!=e?S.extend(e,a):a}},s={};return S.each(o,function(e,t){var n=t[2],r=t[5];a[t[1]]=n.add,r&&n.add(function(){i=r},o[3-e][2].disable,o[3-e][3].disable,o[0][2].lock,o[0][3].lock),n.add(t[3].fire),s[t[0]]=function(){return s[t[0]+"With"](this===s?void 0:this,arguments),this},s[t[0]+"With"]=n.fireWith}),a.promise(s),e&&e.call(s,s),s},when:function(e){function t(t){return function(e){i[t]=this,o[t]=1<arguments.length?s.call(arguments):e,--n||a.resolveWith(i,o)}}var n=arguments.length,r=n,i=Array(r),o=s.call(arguments),a=S.Deferred();if(n<=1&&(F(e,a.done(t(r)).resolve,a.reject,!n),"pending"===a.state()||b(o[r]&&o[r].then)))return a.then();for(;r--;)F(o[r],t(r),a.reject);return a.promise()}});var M=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;S.Deferred.exceptionHook=function(e,t){E.console&&E.console.warn&&e&&M.test(e.name)&&E.console.warn("jQuery.Deferred exception: "+e.message,e.stack,t)},S.readyException=function(e){E.setTimeout(function(){throw e})};var q=S.Deferred();function H(){T.removeEventListener("DOMContentLoaded",H),E.removeEventListener("load",H),S.ready()}S.fn.ready=function(e){return q.then(e).catch(function(e){S.readyException(e)}),this},S.extend({isReady:!1,readyWait:1,ready:function(e){(!0===e?--S.readyWait:S.isReady)||(S.isReady=!0)!==e&&0<--S.readyWait||q.resolveWith(T,[S])}}),S.ready.then=q.then,"complete"===T.readyState||"loading"!==T.readyState&&!T.documentElement.doScroll?E.setTimeout(S.ready):(T.addEventListener("DOMContentLoaded",H),E.addEventListener("load",H));function B(e,t,n,r,i,o,a){var s=0,l=e.length,u=null==n;if("object"===h(n))for(s in i=!0,n)B(e,t,s,n[s],!0,o,a);else if(void 0!==r&&(i=!0,b(r)||(a=!0),u&&(t=a?(t.call(e,r),null):(u=t,function(e,t,n){return u.call(S(e),n)})),t))for(;s<l;s++)t(e[s],n,a?r:r.call(e[s],s,t(e[s],n)));return i?e:u?t.call(e):l?t(e[0],n):o}var U=/^-ms-/,G=/-([a-z])/g;function W(e,t){return t.toUpperCase()}function $(e){return e.replace(U,"ms-").replace(G,W)}function z(e){return 1===e.nodeType||9===e.nodeType||!+e.nodeType}function V(){this.expando=S.expando+V.uid++}V.uid=1,V.prototype={cache:function(e){var t=e[this.expando];return t||(t={},z(e)&&(e.nodeType?e[this.expando]=t:Object.defineProperty(e,this.expando,{value:t,configurable:!0}))),t},set:function(e,t,n){var r,i=this.cache(e);if("string"==typeof t)i[$(t)]=n;else for(r in t)i[$(r)]=t[r];return i},get:function(e,t){return void 0===t?this.cache(e):e[this.expando]&&e[this.expando][$(t)]},access:function(e,t,n){return void 0===t||t&&"string"==typeof t&&void 0===n?this.get(e,t):(this.set(e,t,n),void 0!==n?n:t)},remove:function(e,t){var n,r=e[this.expando];if(void 0!==r){if(void 0!==t){n=(t=Array.isArray(t)?t.map($):(t=$(t))in r?[t]:t.match(j)||[]).length;for(;n--;)delete r[t[n]]}void 0!==t&&!S.isEmptyObject(r)||(e.nodeType?e[this.expando]=void 0:delete e[this.expando])}},hasData:function(e){e=e[this.expando];return void 0!==e&&!S.isEmptyObject(e)}};var X=new V,Q=new V,Y=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,K=/[A-Z]/g;function J(e,t,n){var r,i;if(void 0===n&&1===e.nodeType)if(r="data-"+t.replace(K,"-$&").toLowerCase(),"string"==typeof(n=e.getAttribute(r))){try{n="true"===(i=n)||"false"!==i&&("null"===i?null:i===+i+""?+i:Y.test(i)?JSON.parse(i):i)}catch(e){}Q.set(e,t,n)}else n=void 0;return n}S.extend({hasData:function(e){return Q.hasData(e)||X.hasData(e)},data:function(e,t,n){return Q.access(e,t,n)},removeData:function(e,t){Q.remove(e,t)},_data:function(e,t,n){return X.access(e,t,n)},_removeData:function(e,t){X.remove(e,t)}}),S.fn.extend({data:function(n,e){var t,r,i,o=this[0],a=o&&o.attributes;if(void 0!==n)return"object"==_typeof(n)?this.each(function(){Q.set(this,n)}):B(this,function(e){var t;return o&&void 0===e?void 0!==(t=Q.get(o,n))||void 0!==(t=J(o,n))?t:void 0:void this.each(function(){Q.set(this,n,e)})},null,e,1<arguments.length,null,!0);if(this.length&&(i=Q.get(o),1===o.nodeType&&!X.get(o,"hasDataAttrs"))){for(t=a.length;t--;)a[t]&&0===(r=a[t].name).indexOf("data-")&&(r=$(r.slice(5)),J(o,r,i[r]));X.set(o,"hasDataAttrs",!0)}return i},removeData:function(e){return this.each(function(){Q.remove(this,e)})}}),S.extend({queue:function(e,t,n){var r;if(e)return t=(t||"fx")+"queue",r=X.get(e,t),n&&(!r||Array.isArray(n)?r=X.access(e,t,S.makeArray(n)):r.push(n)),r||[]},dequeue:function(e,t){t=t||"fx";var n=S.queue(e,t),r=n.length,i=n.shift(),o=S._queueHooks(e,t);"inprogress"===i&&(i=n.shift(),r--),i&&("fx"===t&&n.unshift("inprogress"),delete o.stop,i.call(e,function(){S.dequeue(e,t)},o)),!r&&o&&o.empty.fire()},_queueHooks:function(e,t){var n=t+"queueHooks";return X.get(e,n)||X.access(e,n,{empty:S.Callbacks("once memory").add(function(){X.remove(e,[t+"queue",n])})})}}),S.fn.extend({queue:function(t,n){var e=2;return"string"!=typeof t&&(n=t,t="fx",e--),arguments.length<e?S.queue(this[0],t):void 0===n?this:this.each(function(){var e=S.queue(this,t,n);S._queueHooks(this,t),"fx"===t&&"inprogress"!==e[0]&&S.dequeue(this,t)})},dequeue:function(e){return this.each(function(){S.dequeue(this,e)})},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){function n(){--i||o.resolveWith(a,[a])}var r,i=1,o=S.Deferred(),a=this,s=this.length;for("string"!=typeof e&&(t=e,e=void 0),e=e||"fx";s--;)(r=X.get(a[s],e+"queueHooks"))&&r.empty&&(i++,r.empty.add(n));return n(),o.promise(t)}});var Z=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,ee=new RegExp("^(?:([+-])=|)("+Z+")([a-z%]*)$","i"),te=["Top","Right","Bottom","Left"],ne=T.documentElement,re=function(e){return S.contains(e.ownerDocument,e)},ie={composed:!0};ne.getRootNode&&(re=function(e){return S.contains(e.ownerDocument,e)||e.getRootNode(ie)===e.ownerDocument});function oe(e,t){return"none"===(e=t||e).style.display||""===e.style.display&&re(e)&&"none"===S.css(e,"display")}function ae(e,t,n,r){var i,o,a=20,s=r?function(){return r.cur()}:function(){return S.css(e,t,"")},l=s(),u=n&&n[3]||(S.cssNumber[t]?"":"px"),c=e.nodeType&&(S.cssNumber[t]||"px"!==u&&+l)&&ee.exec(S.css(e,t));if(c&&c[3]!==u){for(l/=2,u=u||c[3],c=+l||1;a--;)S.style(e,t,c+u),(1-o)*(1-(o=s()/l||.5))<=0&&(a=0),c/=o;c*=2,S.style(e,t,c+u),n=n||[]}return n&&(c=+c||+l||0,i=n[1]?c+(n[1]+1)*n[2]:+n[2],r&&(r.unit=u,r.start=c,r.end=i)),i}var se={};function le(e,t){for(var n,r,i,o,a,s,l=[],u=0,c=e.length;u<c;u++)(r=e[u]).style&&(n=r.style.display,t?("none"===n&&(l[u]=X.get(r,"display")||null,l[u]||(r.style.display="")),""===r.style.display&&oe(r)&&(l[u]=(s=o=i=void 0,o=r.ownerDocument,a=r.nodeName,(s=se[a])||(i=o.body.appendChild(o.createElement(a)),s=S.css(i,"display"),i.parentNode.removeChild(i),"none"===s&&(s="block"),se[a]=s)))):"none"!==n&&(l[u]="none",X.set(r,"display",n)));for(u=0;u<c;u++)null!=l[u]&&(e[u].style.display=l[u]);return e}S.fn.extend({show:function(){return le(this,!0)},hide:function(){return le(this)},toggle:function(e){return"boolean"==typeof e?e?this.show():this.hide():this.each(function(){oe(this)?S(this).show():S(this).hide()})}});var ue=/^(?:checkbox|radio)$/i,ce=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,de=/^$|^module$|\/(?:java|ecma)script/i,fe=T.createDocumentFragment().appendChild(T.createElement("div"));(f=T.createElement("input")).setAttribute("type","radio"),f.setAttribute("checked","checked"),f.setAttribute("name","t"),fe.appendChild(f),y.checkClone=fe.cloneNode(!0).cloneNode(!0).lastChild.checked,fe.innerHTML="<textarea>x</textarea>",y.noCloneChecked=!!fe.cloneNode(!0).lastChild.defaultValue,fe.innerHTML="<option></option>",y.option=!!fe.lastChild;var pe={thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};function he(e,t){var n=void 0!==e.getElementsByTagName?e.getElementsByTagName(t||"*"):void 0!==e.querySelectorAll?e.querySelectorAll(t||"*"):[];return void 0===t||t&&k(e,t)?S.merge([e],n):n}function me(e,t){for(var n=0,r=e.length;n<r;n++)X.set(e[n],"globalEval",!t||X.get(t[n],"globalEval"))}pe.tbody=pe.tfoot=pe.colgroup=pe.caption=pe.thead,pe.th=pe.td,y.option||(pe.optgroup=pe.option=[1,"<select multiple='multiple'>","</select>"]);var ge=/<|&#?\w+;/;function ve(e,t,n,r,i){for(var o,a,s,l,u,c=t.createDocumentFragment(),d=[],f=0,p=e.length;f<p;f++)if((o=e[f])||0===o)if("object"===h(o))S.merge(d,o.nodeType?[o]:o);else if(ge.test(o)){for(a=a||c.appendChild(t.createElement("div")),s=(ce.exec(o)||["",""])[1].toLowerCase(),s=pe[s]||pe._default,a.innerHTML=s[1]+S.htmlPrefilter(o)+s[2],u=s[0];u--;)a=a.lastChild;S.merge(d,a.childNodes),(a=c.firstChild).textContent=""}else d.push(t.createTextNode(o));for(c.textContent="",f=0;o=d[f++];)if(r&&-1<S.inArray(o,r))i&&i.push(o);else if(l=re(o),a=he(c.appendChild(o),"script"),l&&me(a),n)for(u=0;o=a[u++];)de.test(o.type||"")&&n.push(o);return c}var ye=/^key/,be=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,_e=/^([^.]*)(?:\.(.+)|)/;function we(){return!0}function xe(){return!1}function Ee(e,t){return e===function(){try{return T.activeElement}catch(e){}}()==("focus"===t)}function Te(e,t,n,r,i,o){var a,s;if("object"==_typeof(t)){for(s in"string"!=typeof n&&(r=r||n,n=void 0),t)Te(e,s,n,r,t[s],o);return e}if(null==r&&null==i?(i=n,r=n=void 0):null==i&&("string"==typeof n?(i=r,r=void 0):(i=r,r=n,n=void 0)),!1===i)i=xe;else if(!i)return e;return 1===o&&(a=i,(i=function(e){return S().off(e),a.apply(this,arguments)}).guid=a.guid||(a.guid=S.guid++)),e.each(function(){S.event.add(this,t,i,r,n)})}function Se(e,i,o){o?(X.set(e,i,!1),S.event.add(e,i,{namespace:!1,handler:function(e){var t,n,r=X.get(this,i);if(1&e.isTrigger&&this[i]){if(r.length)(S.event.special[i]||{}).delegateType&&e.stopPropagation();else if(r=s.call(arguments),X.set(this,i,r),t=o(this,i),this[i](),r!==(n=X.get(this,i))||t?X.set(this,i,!1):n={},r!==n)return e.stopImmediatePropagation(),e.preventDefault(),n.value}else r.length&&(X.set(this,i,{value:S.event.trigger(S.extend(r[0],S.Event.prototype),r.slice(1),this)}),e.stopImmediatePropagation())}})):void 0===X.get(e,i)&&S.event.add(e,i,we)}S.event={global:{},add:function(t,e,n,r,i){var o,a,s,l,u,c,d,f,p,h=X.get(t);if(z(t))for(n.handler&&(n=(o=n).handler,i=o.selector),i&&S.find.matchesSelector(ne,i),n.guid||(n.guid=S.guid++),(s=h.events)||(s=h.events=Object.create(null)),(a=h.handle)||(a=h.handle=function(e){return void 0!==S&&S.event.triggered!==e.type?S.event.dispatch.apply(t,arguments):void 0}),l=(e=(e||"").match(j)||[""]).length;l--;)d=p=(u=_e.exec(e[l])||[])[1],f=(u[2]||"").split(".").sort(),d&&(c=S.event.special[d]||{},d=(i?c.delegateType:c.bindType)||d,c=S.event.special[d]||{},u=S.extend({type:d,origType:p,data:r,handler:n,guid:n.guid,selector:i,needsContext:i&&S.expr.match.needsContext.test(i),namespace:f.join(".")},o),(p=s[d])||((p=s[d]=[]).delegateCount=0,c.setup&&!1!==c.setup.call(t,r,f,a)||t.addEventListener&&t.addEventListener(d,a)),c.add&&(c.add.call(t,u),u.handler.guid||(u.handler.guid=n.guid)),i?p.splice(p.delegateCount++,0,u):p.push(u),S.event.global[d]=!0)},remove:function(e,t,n,r,i){var o,a,s,l,u,c,d,f,p,h,m,g=X.hasData(e)&&X.get(e);if(g&&(l=g.events)){for(u=(t=(t||"").match(j)||[""]).length;u--;)if(p=m=(s=_e.exec(t[u])||[])[1],h=(s[2]||"").split(".").sort(),p){for(d=S.event.special[p]||{},f=l[p=(r?d.delegateType:d.bindType)||p]||[],s=s[2]&&new RegExp("(^|\\.)"+h.join("\\.(?:.*\\.|)")+"(\\.|$)"),a=o=f.length;o--;)c=f[o],!i&&m!==c.origType||n&&n.guid!==c.guid||s&&!s.test(c.namespace)||r&&r!==c.selector&&("**"!==r||!c.selector)||(f.splice(o,1),c.selector&&f.delegateCount--,d.remove&&d.remove.call(e,c));a&&!f.length&&(d.teardown&&!1!==d.teardown.call(e,h,g.handle)||S.removeEvent(e,p,g.handle),delete l[p])}else for(p in l)S.event.remove(e,p+t[u],n,r,!0);S.isEmptyObject(l)&&X.remove(e,"handle events")}},dispatch:function(e){var t,n,r,i,o,a=new Array(arguments.length),s=S.event.fix(e),l=(X.get(this,"events")||Object.create(null))[s.type]||[],e=S.event.special[s.type]||{};for(a[0]=s,t=1;t<arguments.length;t++)a[t]=arguments[t];if(s.delegateTarget=this,!e.preDispatch||!1!==e.preDispatch.call(this,s)){for(o=S.event.handlers.call(this,s,l),t=0;(r=o[t++])&&!s.isPropagationStopped();)for(s.currentTarget=r.elem,n=0;(i=r.handlers[n++])&&!s.isImmediatePropagationStopped();)s.rnamespace&&!1!==i.namespace&&!s.rnamespace.test(i.namespace)||(s.handleObj=i,s.data=i.data,void 0!==(i=((S.event.special[i.origType]||{}).handle||i.handler).apply(r.elem,a))&&!1===(s.result=i)&&(s.preventDefault(),s.stopPropagation()));return e.postDispatch&&e.postDispatch.call(this,s),s.result}},handlers:function(e,t){var n,r,i,o,a,s=[],l=t.delegateCount,u=e.target;if(l&&u.nodeType&&!("click"===e.type&&1<=e.button))for(;u!==this;u=u.parentNode||this)if(1===u.nodeType&&("click"!==e.type||!0!==u.disabled)){for(o=[],a={},n=0;n<l;n++)void 0===a[i=(r=t[n]).selector+" "]&&(a[i]=r.needsContext?-1<S(i,this).index(u):S.find(i,this,null,[u]).length),a[i]&&o.push(r);o.length&&s.push({elem:u,handlers:o})}return u=this,l<t.length&&s.push({elem:u,handlers:t.slice(l)}),s},addProp:function(t,e){Object.defineProperty(S.Event.prototype,t,{enumerable:!0,configurable:!0,get:b(e)?function(){if(this.originalEvent)return e(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[t]},set:function(e){Object.defineProperty(this,t,{enumerable:!0,configurable:!0,writable:!0,value:e})}})},fix:function(e){return e[S.expando]?e:new S.Event(e)},special:{load:{noBubble:!0},click:{setup:function(e){e=this||e;return ue.test(e.type)&&e.click&&k(e,"input")&&Se(e,"click",we),!1},trigger:function(e){e=this||e;return ue.test(e.type)&&e.click&&k(e,"input")&&Se(e,"click"),!0},_default:function(e){e=e.target;return ue.test(e.type)&&e.click&&k(e,"input")&&X.get(e,"click")||k(e,"a")}},beforeunload:{postDispatch:function(e){void 0!==e.result&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}}},S.removeEvent=function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n)},S.Event=function(e,t){if(!(this instanceof S.Event))return new S.Event(e,t);e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||void 0===e.defaultPrevented&&!1===e.returnValue?we:xe,this.target=e.target&&3===e.target.nodeType?e.target.parentNode:e.target,this.currentTarget=e.currentTarget,this.relatedTarget=e.relatedTarget):this.type=e,t&&S.extend(this,t),this.timeStamp=e&&e.timeStamp||Date.now(),this[S.expando]=!0},S.Event.prototype={constructor:S.Event,isDefaultPrevented:xe,isPropagationStopped:xe,isImmediatePropagationStopped:xe,isSimulated:!1,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=we,e&&!this.isSimulated&&e.preventDefault()},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=we,e&&!this.isSimulated&&e.stopPropagation()},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=we,e&&!this.isSimulated&&e.stopImmediatePropagation(),this.stopPropagation()}},S.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,char:!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:function(e){var t=e.button;return null==e.which&&ye.test(e.type)?null!=e.charCode?e.charCode:e.keyCode:!e.which&&void 0!==t&&be.test(e.type)?1&t?1:2&t?3:4&t?2:0:e.which}},S.event.addProp),S.each({focus:"focusin",blur:"focusout"},function(e,t){S.event.special[e]={setup:function(){return Se(this,e,Ee),!1},trigger:function(){return Se(this,e),!0},delegateType:t}}),S.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(e,i){S.event.special[e]={delegateType:i,bindType:i,handle:function(e){var t,n=e.relatedTarget,r=e.handleObj;return n&&(n===this||S.contains(this,n))||(e.type=r.origType,t=r.handler.apply(this,arguments),e.type=i),t}}}),S.fn.extend({on:function(e,t,n,r){return Te(this,e,t,n,r)},one:function(e,t,n,r){return Te(this,e,t,n,r,1)},off:function(e,t,n){var r,i;if(e&&e.preventDefault&&e.handleObj)return r=e.handleObj,S(e.delegateTarget).off(r.namespace?r.origType+"."+r.namespace:r.origType,r.selector,r.handler),this;if("object"!=_typeof(e))return!1!==t&&"function"!=typeof t||(n=t,t=void 0),!1===n&&(n=xe),this.each(function(){S.event.remove(this,e,n,t)});for(i in e)this.off(i,t,e[i]);return this}});var ke=/<script|<style|<link/i,Ce=/checked\s*(?:[^=]|=\s*.checked.)/i,Ae=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g;function De(e,t){return k(e,"table")&&k(11!==t.nodeType?t:t.firstChild,"tr")&&S(e).children("tbody")[0]||e}function Re(e){return e.type=(null!==e.getAttribute("type"))+"/"+e.type,e}function Le(e){return"true/"===(e.type||"").slice(0,5)?e.type=e.type.slice(5):e.removeAttribute("type"),e}function Ne(e,t){var n,r,i,o;if(1===t.nodeType){if(X.hasData(e)&&(o=X.get(e).events))for(i in X.remove(t,"handle events"),o)for(n=0,r=o[i].length;n<r;n++)S.event.add(t,i,o[i][n]);Q.hasData(e)&&(e=Q.access(e),e=S.extend({},e),Q.set(t,e))}}function Oe(n,r,i,o){r=g(r);var e,t,a,s,l,u,c=0,d=n.length,f=d-1,p=r[0],h=b(p);if(h||1<d&&"string"==typeof p&&!y.checkClone&&Ce.test(p))return n.each(function(e){var t=n.eq(e);h&&(r[0]=p.call(this,e,t.html())),Oe(t,r,i,o)});if(d&&(t=(e=ve(r,n[0].ownerDocument,!1,n,o)).firstChild,1===e.childNodes.length&&(e=t),t||o)){for(s=(a=S.map(he(e,"script"),Re)).length;c<d;c++)l=e,c!==f&&(l=S.clone(l,!0,!0),s&&S.merge(a,he(l,"script"))),i.call(n[c],l,c);if(s)for(u=a[a.length-1].ownerDocument,S.map(a,Le),c=0;c<s;c++)l=a[c],de.test(l.type||"")&&!X.access(l,"globalEval")&&S.contains(u,l)&&(l.src&&"module"!==(l.type||"").toLowerCase()?S._evalUrl&&!l.noModule&&S._evalUrl(l.src,{nonce:l.nonce||l.getAttribute("nonce")},u):_(l.textContent.replace(Ae,""),l,u))}return n}function je(e,t,n){for(var r,i=t?S.filter(t,e):e,o=0;null!=(r=i[o]);o++)n||1!==r.nodeType||S.cleanData(he(r)),r.parentNode&&(n&&re(r)&&me(he(r,"script")),r.parentNode.removeChild(r));return e}S.extend({htmlPrefilter:function(e){return e},clone:function(e,t,n){var r,i,o,a,s,l,u,c=e.cloneNode(!0),d=re(e);if(!(y.noCloneChecked||1!==e.nodeType&&11!==e.nodeType||S.isXMLDoc(e)))for(a=he(c),r=0,i=(o=he(e)).length;r<i;r++)s=o[r],"input"===(u=(l=a[r]).nodeName.toLowerCase())&&ue.test(s.type)?l.checked=s.checked:"input"!==u&&"textarea"!==u||(l.defaultValue=s.defaultValue);if(t)if(n)for(o=o||he(e),a=a||he(c),r=0,i=o.length;r<i;r++)Ne(o[r],a[r]);else Ne(e,c);return 0<(a=he(c,"script")).length&&me(a,!d&&he(e,"script")),c},cleanData:function(e){for(var t,n,r,i=S.event.special,o=0;void 0!==(n=e[o]);o++)if(z(n)){if(t=n[X.expando]){if(t.events)for(r in t.events)i[r]?S.event.remove(n,r):S.removeEvent(n,r,t.handle);n[X.expando]=void 0}n[Q.expando]&&(n[Q.expando]=void 0)}}}),S.fn.extend({detach:function(e){return je(this,e,!0)},remove:function(e){return je(this,e)},text:function(e){return B(this,function(e){return void 0===e?S.text(this):this.empty().each(function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=e)})},null,e,arguments.length)},append:function(){return Oe(this,arguments,function(e){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||De(this,e).appendChild(e)})},prepend:function(){return Oe(this,arguments,function(e){var t;1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(t=De(this,e)).insertBefore(e,t.firstChild)})},before:function(){return Oe(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this)})},after:function(){return Oe(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)})},empty:function(){for(var e,t=0;null!=(e=this[t]);t++)1===e.nodeType&&(S.cleanData(he(e,!1)),e.textContent="");return this},clone:function(e,t){return e=null!=e&&e,t=null==t?e:t,this.map(function(){return S.clone(this,e,t)})},html:function(e){return B(this,function(e){var t=this[0]||{},n=0,r=this.length;if(void 0===e&&1===t.nodeType)return t.innerHTML;if("string"==typeof e&&!ke.test(e)&&!pe[(ce.exec(e)||["",""])[1].toLowerCase()]){e=S.htmlPrefilter(e);try{for(;n<r;n++)1===(t=this[n]||{}).nodeType&&(S.cleanData(he(t,!1)),t.innerHTML=e);t=0}catch(e){}}t&&this.empty().append(e)},null,e,arguments.length)},replaceWith:function(){var n=[];return Oe(this,arguments,function(e){var t=this.parentNode;S.inArray(this,n)<0&&(S.cleanData(he(this)),t&&t.replaceChild(e,this))},n)}}),S.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(e,a){S.fn[e]=function(e){for(var t,n=[],r=S(e),i=r.length-1,o=0;o<=i;o++)t=o===i?this:this.clone(!0),S(r[o])[a](t),l.apply(n,t.get());return this.pushStack(n)}});function Ie(e,t,n){var r,i={};for(r in t)i[r]=e.style[r],e.style[r]=t[r];for(r in n=n.call(e),t)e.style[r]=i[r];return n}var Pe,Fe,Me,qe,He,Be,Ue,Ge,We=new RegExp("^("+Z+")(?!px)[a-z%]+$","i"),$e=function(e){var t=e.ownerDocument.defaultView;return t&&t.opener||(t=E),t.getComputedStyle(e)},ze=new RegExp(te.join("|"),"i");function Ve(e,t,n){var r,i,o=e.style;return(n=n||$e(e))&&(""!==(i=n.getPropertyValue(t)||n[t])||re(e)||(i=S.style(e,t)),!y.pixelBoxStyles()&&We.test(i)&&ze.test(t)&&(r=o.width,e=o.minWidth,t=o.maxWidth,o.minWidth=o.maxWidth=o.width=i,i=n.width,o.width=r,o.minWidth=e,o.maxWidth=t)),void 0!==i?i+"":i}function Xe(e,t){return{get:function(){if(!e())return(this.get=t).apply(this,arguments);delete this.get}}}function Qe(){var e;Ge&&(Ue.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",Ge.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",ne.appendChild(Ue).appendChild(Ge),e=E.getComputedStyle(Ge),Pe="1%"!==e.top,Be=12===Ye(e.marginLeft),Ge.style.right="60%",qe=36===Ye(e.right),Fe=36===Ye(e.width),Ge.style.position="absolute",Me=12===Ye(Ge.offsetWidth/3),ne.removeChild(Ue),Ge=null)}function Ye(e){return Math.round(parseFloat(e))}Ue=T.createElement("div"),(Ge=T.createElement("div")).style&&(Ge.style.backgroundClip="content-box",Ge.cloneNode(!0).style.backgroundClip="",y.clearCloneStyle="content-box"===Ge.style.backgroundClip,S.extend(y,{boxSizingReliable:function(){return Qe(),Fe},pixelBoxStyles:function(){return Qe(),qe},pixelPosition:function(){return Qe(),Pe},reliableMarginLeft:function(){return Qe(),Be},scrollboxSize:function(){return Qe(),Me},reliableTrDimensions:function(){var e,t,n;return null==He&&(e=T.createElement("table"),n=T.createElement("tr"),t=T.createElement("div"),e.style.cssText="position:absolute;left:-11111px",n.style.height="1px",t.style.height="9px",ne.appendChild(e).appendChild(n).appendChild(t),n=E.getComputedStyle(n),He=3<parseInt(n.height),ne.removeChild(e)),He}}));var Ke=["Webkit","Moz","ms"],Je=T.createElement("div").style,Ze={};function et(e){return S.cssProps[e]||Ze[e]||(e in Je?e:Ze[e]=function(e){for(var t=e[0].toUpperCase()+e.slice(1),n=Ke.length;n--;)if((e=Ke[n]+t)in Je)return e}(e)||e)}var tt=/^(none|table(?!-c[ea]).+)/,nt=/^--/,rt={position:"absolute",visibility:"hidden",display:"block"},it={letterSpacing:"0",fontWeight:"400"};function ot(e,t,n){var r=ee.exec(t);return r?Math.max(0,r[2]-(n||0))+(r[3]||"px"):t}function at(e,t,n,r,i,o){var a="width"===t?1:0,s=0,l=0;if(n===(r?"border":"content"))return 0;for(;a<4;a+=2)"margin"===n&&(l+=S.css(e,n+te[a],!0,i)),r?("content"===n&&(l-=S.css(e,"padding"+te[a],!0,i)),"margin"!==n&&(l-=S.css(e,"border"+te[a]+"Width",!0,i))):(l+=S.css(e,"padding"+te[a],!0,i),"padding"!==n?l+=S.css(e,"border"+te[a]+"Width",!0,i):s+=S.css(e,"border"+te[a]+"Width",!0,i));return!r&&0<=o&&(l+=Math.max(0,Math.ceil(e["offset"+t[0].toUpperCase()+t.slice(1)]-o-l-s-.5))||0),l}function st(e,t,n){var r=$e(e),i=(!y.boxSizingReliable()||n)&&"border-box"===S.css(e,"boxSizing",!1,r),o=i,a=Ve(e,t,r),s="offset"+t[0].toUpperCase()+t.slice(1);if(We.test(a)){if(!n)return a;a="auto"}return(!y.boxSizingReliable()&&i||!y.reliableTrDimensions()&&k(e,"tr")||"auto"===a||!parseFloat(a)&&"inline"===S.css(e,"display",!1,r))&&e.getClientRects().length&&(i="border-box"===S.css(e,"boxSizing",!1,r),(o=s in e)&&(a=e[s])),(a=parseFloat(a)||0)+at(e,t,n||(i?"border":"content"),o,r,a)+"px"}function lt(e,t,n,r,i){return new lt.prototype.init(e,t,n,r,i)}S.extend({cssHooks:{opacity:{get:function(e,t){if(t){e=Ve(e,"opacity");return""===e?"1":e}}}},cssNumber:{animationIterationCount:!0,columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{},style:function(e,t,n,r){if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style){var i,o,a,s=$(t),l=nt.test(t),u=e.style;if(l||(t=et(s)),a=S.cssHooks[t]||S.cssHooks[s],void 0===n)return a&&"get"in a&&void 0!==(i=a.get(e,!1,r))?i:u[t];"string"===(o=_typeof(n))&&(i=ee.exec(n))&&i[1]&&(n=ae(e,t,i),o="number"),null!=n&&n==n&&("number"!==o||l||(n+=i&&i[3]||(S.cssNumber[s]?"":"px")),y.clearCloneStyle||""!==n||0!==t.indexOf("background")||(u[t]="inherit"),a&&"set"in a&&void 0===(n=a.set(e,n,r))||(l?u.setProperty(t,n):u[t]=n))}},css:function(e,t,n,r){var i,o=$(t);return nt.test(t)||(t=et(o)),(o=S.cssHooks[t]||S.cssHooks[o])&&"get"in o&&(i=o.get(e,!0,n)),void 0===i&&(i=Ve(e,t,r)),"normal"===i&&t in it&&(i=it[t]),""===n||n?(t=parseFloat(i),!0===n||isFinite(t)?t||0:i):i}}),S.each(["height","width"],function(e,s){S.cssHooks[s]={get:function(e,t,n){if(t)return!tt.test(S.css(e,"display"))||e.getClientRects().length&&e.getBoundingClientRect().width?st(e,s,n):Ie(e,rt,function(){return st(e,s,n)})},set:function(e,t,n){var r,i=$e(e),o=!y.scrollboxSize()&&"absolute"===i.position,a=(o||n)&&"border-box"===S.css(e,"boxSizing",!1,i),n=n?at(e,s,n,a,i):0;return a&&o&&(n-=Math.ceil(e["offset"+s[0].toUpperCase()+s.slice(1)]-parseFloat(i[s])-at(e,s,"border",!1,i)-.5)),n&&(r=ee.exec(t))&&"px"!==(r[3]||"px")&&(e.style[s]=t,t=S.css(e,s)),ot(0,t,n)}}}),S.cssHooks.marginLeft=Xe(y.reliableMarginLeft,function(e,t){if(t)return(parseFloat(Ve(e,"marginLeft"))||e.getBoundingClientRect().left-Ie(e,{marginLeft:0},function(){return e.getBoundingClientRect().left}))+"px"}),S.each({margin:"",padding:"",border:"Width"},function(i,o){S.cssHooks[i+o]={expand:function(e){for(var t=0,n={},r="string"==typeof e?e.split(" "):[e];t<4;t++)n[i+te[t]+o]=r[t]||r[t-2]||r[0];return n}},"margin"!==i&&(S.cssHooks[i+o].set=ot)}),S.fn.extend({css:function(e,t){return B(this,function(e,t,n){var r,i,o={},a=0;if(Array.isArray(t)){for(r=$e(e),i=t.length;a<i;a++)o[t[a]]=S.css(e,t[a],!1,r);return o}return void 0!==n?S.style(e,t,n):S.css(e,t)},e,t,1<arguments.length)}}),((S.Tween=lt).prototype={constructor:lt,init:function(e,t,n,r,i,o){this.elem=e,this.prop=n,this.easing=i||S.easing._default,this.options=t,this.start=this.now=this.cur(),this.end=r,this.unit=o||(S.cssNumber[n]?"":"px")},cur:function(){var e=lt.propHooks[this.prop];return(e&&e.get?e:lt.propHooks._default).get(this)},run:function(e){var t,n=lt.propHooks[this.prop];return this.options.duration?this.pos=t=S.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):this.pos=t=e,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),(n&&n.set?n:lt.propHooks._default).set(this),this}}).init.prototype=lt.prototype,(lt.propHooks={_default:{get:function(e){return 1!==e.elem.nodeType||null!=e.elem[e.prop]&&null==e.elem.style[e.prop]?e.elem[e.prop]:(e=S.css(e.elem,e.prop,""))&&"auto"!==e?e:0},set:function(e){S.fx.step[e.prop]?S.fx.step[e.prop](e):1!==e.elem.nodeType||!S.cssHooks[e.prop]&&null==e.elem.style[et(e.prop)]?e.elem[e.prop]=e.now:S.style(e.elem,e.prop,e.now+e.unit)}}}).scrollTop=lt.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},S.easing={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2},_default:"swing"},S.fx=lt.prototype.init,S.fx.step={};var ut,ct,dt=/^(?:toggle|show|hide)$/,ft=/queueHooks$/;function pt(){ct&&(!1===T.hidden&&E.requestAnimationFrame?E.requestAnimationFrame(pt):E.setTimeout(pt,S.fx.interval),S.fx.tick())}function ht(){return E.setTimeout(function(){ut=void 0}),ut=Date.now()}function mt(e,t){var n,r=0,i={height:e};for(t=t?1:0;r<4;r+=2-t)i["margin"+(n=te[r])]=i["padding"+n]=e;return t&&(i.opacity=i.width=e),i}function gt(e,t,n){for(var r,i=(vt.tweeners[t]||[]).concat(vt.tweeners["*"]),o=0,a=i.length;o<a;o++)if(r=i[o].call(n,t,e))return r}function vt(i,e,t){var n,o,r=0,a=vt.prefilters.length,s=S.Deferred().always(function(){delete l.elem}),l=function(){if(o)return!1;for(var e=ut||ht(),e=Math.max(0,u.startTime+u.duration-e),t=1-(e/u.duration||0),n=0,r=u.tweens.length;n<r;n++)u.tweens[n].run(t);return s.notifyWith(i,[u,t,e]),t<1&&r?e:(r||s.notifyWith(i,[u,1,0]),s.resolveWith(i,[u]),!1)},u=s.promise({elem:i,props:S.extend({},e),opts:S.extend(!0,{specialEasing:{},easing:S.easing._default},t),originalProperties:e,originalOptions:t,startTime:ut||ht(),duration:t.duration,tweens:[],createTween:function(e,t){e=S.Tween(i,u.opts,e,t,u.opts.specialEasing[e]||u.opts.easing);return u.tweens.push(e),e},stop:function(e){var t=0,n=e?u.tweens.length:0;if(o)return this;for(o=!0;t<n;t++)u.tweens[t].run(1);return e?(s.notifyWith(i,[u,1,0]),s.resolveWith(i,[u,e])):s.rejectWith(i,[u,e]),this}}),c=u.props;for(function(e,t){var n,r,i,o,a;for(n in e)if(i=t[r=$(n)],o=e[n],Array.isArray(o)&&(i=o[1],o=e[n]=o[0]),n!==r&&(e[r]=o,delete e[n]),(a=S.cssHooks[r])&&"expand"in a)for(n in o=a.expand(o),delete e[r],o)n in e||(e[n]=o[n],t[n]=i);else t[r]=i}(c,u.opts.specialEasing);r<a;r++)if(n=vt.prefilters[r].call(u,i,c,u.opts))return b(n.stop)&&(S._queueHooks(u.elem,u.opts.queue).stop=n.stop.bind(n)),n;return S.map(c,gt,u),b(u.opts.start)&&u.opts.start.call(i,u),u.progress(u.opts.progress).done(u.opts.done,u.opts.complete).fail(u.opts.fail).always(u.opts.always),S.fx.timer(S.extend(l,{elem:i,anim:u,queue:u.opts.queue})),u}S.Animation=S.extend(vt,{tweeners:{"*":[function(e,t){var n=this.createTween(e,t);return ae(n.elem,e,ee.exec(t),n),n}]},tweener:function(e,t){for(var n,r=0,i=(e=b(e)?(t=e,["*"]):e.match(j)).length;r<i;r++)n=e[r],vt.tweeners[n]=vt.tweeners[n]||[],vt.tweeners[n].unshift(t)},prefilters:[function(e,t,n){var r,i,o,a,s,l,u,c="width"in t||"height"in t,d=this,f={},p=e.style,h=e.nodeType&&oe(e),m=X.get(e,"fxshow");for(r in n.queue||(null==(a=S._queueHooks(e,"fx")).unqueued&&(a.unqueued=0,s=a.empty.fire,a.empty.fire=function(){a.unqueued||s()}),a.unqueued++,d.always(function(){d.always(function(){a.unqueued--,S.queue(e,"fx").length||a.empty.fire()})})),t)if(i=t[r],dt.test(i)){if(delete t[r],o=o||"toggle"===i,i===(h?"hide":"show")){if("show"!==i||!m||void 0===m[r])continue;h=!0}f[r]=m&&m[r]||S.style(e,r)}if((l=!S.isEmptyObject(t))||!S.isEmptyObject(f))for(r in c&&1===e.nodeType&&(n.overflow=[p.overflow,p.overflowX,p.overflowY],null==(u=m&&m.display)&&(u=X.get(e,"display")),"none"===(c=S.css(e,"display"))&&(u?c=u:(le([e],!0),u=e.style.display||u,c=S.css(e,"display"),le([e]))),("inline"===c||"inline-block"===c&&null!=u)&&"none"===S.css(e,"float")&&(l||(d.done(function(){p.display=u}),null==u&&(c=p.display,u="none"===c?"":c)),p.display="inline-block")),n.overflow&&(p.overflow="hidden",d.always(function(){p.overflow=n.overflow[0],p.overflowX=n.overflow[1],p.overflowY=n.overflow[2]})),l=!1,f)l||(m?"hidden"in m&&(h=m.hidden):m=X.access(e,"fxshow",{display:u}),o&&(m.hidden=!h),h&&le([e],!0),d.done(function(){for(r in h||le([e]),X.remove(e,"fxshow"),f)S.style(e,r,f[r])})),l=gt(h?m[r]:0,r,d),r in m||(m[r]=l.start,h&&(l.end=l.start,l.start=0))}],prefilter:function(e,t){t?vt.prefilters.unshift(e):vt.prefilters.push(e)}}),S.speed=function(e,t,n){var r=e&&"object"==_typeof(e)?S.extend({},e):{complete:n||!n&&t||b(e)&&e,duration:e,easing:n&&t||t&&!b(t)&&t};return S.fx.off?r.duration=0:"number"!=typeof r.duration&&(r.duration in S.fx.speeds?r.duration=S.fx.speeds[r.duration]:r.duration=S.fx.speeds._default),null!=r.queue&&!0!==r.queue||(r.queue="fx"),r.old=r.complete,r.complete=function(){b(r.old)&&r.old.call(this),r.queue&&S.dequeue(this,r.queue)},r},S.fn.extend({fadeTo:function(e,t,n,r){return this.filter(oe).css("opacity",0).show().end().animate({opacity:t},e,n,r)},animate:function(t,e,n,r){var i=S.isEmptyObject(t),o=S.speed(e,n,r),r=function(){var e=vt(this,S.extend({},t),o);(i||X.get(this,"finish"))&&e.stop(!0)};return r.finish=r,i||!1===o.queue?this.each(r):this.queue(o.queue,r)},stop:function(i,e,o){function a(e){var t=e.stop;delete e.stop,t(o)}return"string"!=typeof i&&(o=e,e=i,i=void 0),e&&this.queue(i||"fx",[]),this.each(function(){var e=!0,t=null!=i&&i+"queueHooks",n=S.timers,r=X.get(this);if(t)r[t]&&r[t].stop&&a(r[t]);else for(t in r)r[t]&&r[t].stop&&ft.test(t)&&a(r[t]);for(t=n.length;t--;)n[t].elem!==this||null!=i&&n[t].queue!==i||(n[t].anim.stop(o),e=!1,n.splice(t,1));!e&&o||S.dequeue(this,i)})},finish:function(a){return!1!==a&&(a=a||"fx"),this.each(function(){var e,t=X.get(this),n=t[a+"queue"],r=t[a+"queueHooks"],i=S.timers,o=n?n.length:0;for(t.finish=!0,S.queue(this,a,[]),r&&r.stop&&r.stop.call(this,!0),e=i.length;e--;)i[e].elem===this&&i[e].queue===a&&(i[e].anim.stop(!0),i.splice(e,1));for(e=0;e<o;e++)n[e]&&n[e].finish&&n[e].finish.call(this);delete t.finish})}}),S.each(["toggle","show","hide"],function(e,r){var i=S.fn[r];S.fn[r]=function(e,t,n){return null==e||"boolean"==typeof e?i.apply(this,arguments):this.animate(mt(r,!0),e,t,n)}}),S.each({slideDown:mt("show"),slideUp:mt("hide"),slideToggle:mt("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(e,r){S.fn[e]=function(e,t,n){return this.animate(r,e,t,n)}}),S.timers=[],S.fx.tick=function(){var e,t=0,n=S.timers;for(ut=Date.now();t<n.length;t++)(e=n[t])()||n[t]!==e||n.splice(t--,1);n.length||S.fx.stop(),ut=void 0},S.fx.timer=function(e){S.timers.push(e),S.fx.start()},S.fx.interval=13,S.fx.start=function(){ct||(ct=!0,pt())},S.fx.stop=function(){ct=null},S.fx.speeds={slow:600,fast:200,_default:400},S.fn.delay=function(r,e){return r=S.fx&&S.fx.speeds[r]||r,e=e||"fx",this.queue(e,function(e,t){var n=E.setTimeout(e,r);t.stop=function(){E.clearTimeout(n)}})},fe=T.createElement("input"),Z=T.createElement("select").appendChild(T.createElement("option")),fe.type="checkbox",y.checkOn=""!==fe.value,y.optSelected=Z.selected,(fe=T.createElement("input")).value="t",fe.type="radio",y.radioValue="t"===fe.value;var yt,bt=S.expr.attrHandle;S.fn.extend({attr:function(e,t){return B(this,S.attr,e,t,1<arguments.length)},removeAttr:function(e){return this.each(function(){S.removeAttr(this,e)})}}),S.extend({attr:function(e,t,n){var r,i,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return void 0===e.getAttribute?S.prop(e,t,n):(1===o&&S.isXMLDoc(e)||(i=S.attrHooks[t.toLowerCase()]||(S.expr.match.bool.test(t)?yt:void 0)),void 0!==n?null===n?void S.removeAttr(e,t):i&&"set"in i&&void 0!==(r=i.set(e,n,t))?r:(e.setAttribute(t,n+""),n):!(i&&"get"in i&&null!==(r=i.get(e,t)))&&null==(r=S.find.attr(e,t))?void 0:r)},attrHooks:{type:{set:function(e,t){if(!y.radioValue&&"radio"===t&&k(e,"input")){var n=e.value;return e.setAttribute("type",t),n&&(e.value=n),t}}}},removeAttr:function(e,t){var n,r=0,i=t&&t.match(j);if(i&&1===e.nodeType)for(;n=i[r++];)e.removeAttribute(n)}}),yt={set:function(e,t,n){return!1===t?S.removeAttr(e,n):e.setAttribute(n,n),n}},S.each(S.expr.match.bool.source.match(/\w+/g),function(e,t){var a=bt[t]||S.find.attr;bt[t]=function(e,t,n){var r,i,o=t.toLowerCase();return n||(i=bt[o],bt[o]=r,r=null!=a(e,t,n)?o:null,bt[o]=i),r}});var _t=/^(?:input|select|textarea|button)$/i,wt=/^(?:a|area)$/i;function xt(e){return(e.match(j)||[]).join(" ")}function Et(e){return e.getAttribute&&e.getAttribute("class")||""}function Tt(e){return Array.isArray(e)?e:"string"==typeof e&&e.match(j)||[]}S.fn.extend({prop:function(e,t){return B(this,S.prop,e,t,1<arguments.length)},removeProp:function(e){return this.each(function(){delete this[S.propFix[e]||e]})}}),S.extend({prop:function(e,t,n){var r,i,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return 1===o&&S.isXMLDoc(e)||(t=S.propFix[t]||t,i=S.propHooks[t]),void 0!==n?i&&"set"in i&&void 0!==(r=i.set(e,n,t))?r:e[t]=n:i&&"get"in i&&null!==(r=i.get(e,t))?r:e[t]},propHooks:{tabIndex:{get:function(e){var t=S.find.attr(e,"tabindex");return t?parseInt(t,10):_t.test(e.nodeName)||wt.test(e.nodeName)&&e.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),y.optSelected||(S.propHooks.selected={get:function(e){e=e.parentNode;return e&&e.parentNode&&e.parentNode.selectedIndex,null},set:function(e){e=e.parentNode;e&&(e.selectedIndex,e.parentNode&&e.parentNode.selectedIndex)}}),S.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){S.propFix[this.toLowerCase()]=this}),S.fn.extend({addClass:function(t){var e,n,r,i,o,a,s=0;if(b(t))return this.each(function(e){S(this).addClass(t.call(this,e,Et(this)))});if((e=Tt(t)).length)for(;n=this[s++];)if(a=Et(n),r=1===n.nodeType&&" "+xt(a)+" "){for(o=0;i=e[o++];)r.indexOf(" "+i+" ")<0&&(r+=i+" ");a!==(a=xt(r))&&n.setAttribute("class",a)}return this},removeClass:function(t){var e,n,r,i,o,a,s=0;if(b(t))return this.each(function(e){S(this).removeClass(t.call(this,e,Et(this)))});if(!arguments.length)return this.attr("class","");if((e=Tt(t)).length)for(;n=this[s++];)if(a=Et(n),r=1===n.nodeType&&" "+xt(a)+" "){for(o=0;i=e[o++];)for(;-1<r.indexOf(" "+i+" ");)r=r.replace(" "+i+" "," ");a!==(a=xt(r))&&n.setAttribute("class",a)}return this},toggleClass:function(i,t){var o=_typeof(i),a="string"===o||Array.isArray(i);return"boolean"==typeof t&&a?t?this.addClass(i):this.removeClass(i):b(i)?this.each(function(e){S(this).toggleClass(i.call(this,e,Et(this),t),t)}):this.each(function(){var e,t,n,r;if(a)for(t=0,n=S(this),r=Tt(i);e=r[t++];)n.hasClass(e)?n.removeClass(e):n.addClass(e);else void 0!==i&&"boolean"!==o||((e=Et(this))&&X.set(this,"__className__",e),this.setAttribute&&this.setAttribute("class",!e&&!1!==i&&X.get(this,"__className__")||""))})},hasClass:function(e){for(var t,n=0,r=" "+e+" ";t=this[n++];)if(1===t.nodeType&&-1<(" "+xt(Et(t))+" ").indexOf(r))return!0;return!1}});var St=/\r/g;S.fn.extend({val:function(t){var n,e,r,i=this[0];return arguments.length?(r=b(t),this.each(function(e){1===this.nodeType&&(null==(e=r?t.call(this,e,S(this).val()):t)?e="":"number"==typeof e?e+="":Array.isArray(e)&&(e=S.map(e,function(e){return null==e?"":e+""})),(n=S.valHooks[this.type]||S.valHooks[this.nodeName.toLowerCase()])&&"set"in n&&void 0!==n.set(this,e,"value")||(this.value=e))})):i?(n=S.valHooks[i.type]||S.valHooks[i.nodeName.toLowerCase()])&&"get"in n&&void 0!==(e=n.get(i,"value"))?e:"string"==typeof(e=i.value)?e.replace(St,""):null==e?"":e:void 0}}),S.extend({valHooks:{option:{get:function(e){var t=S.find.attr(e,"value");return null!=t?t:xt(S.text(e))}},select:{get:function(e){for(var t,n=e.options,r=e.selectedIndex,i="select-one"===e.type,o=i?null:[],a=i?r+1:n.length,s=r<0?a:i?r:0;s<a;s++)if(((t=n[s]).selected||s===r)&&!t.disabled&&(!t.parentNode.disabled||!k(t.parentNode,"optgroup"))){if(t=S(t).val(),i)return t;o.push(t)}return o},set:function(e,t){for(var n,r,i=e.options,o=S.makeArray(t),a=i.length;a--;)((r=i[a]).selected=-1<S.inArray(S.valHooks.option.get(r),o))&&(n=!0);return n||(e.selectedIndex=-1),o}}}}),S.each(["radio","checkbox"],function(){S.valHooks[this]={set:function(e,t){if(Array.isArray(t))return e.checked=-1<S.inArray(S(e).val(),t)}},y.checkOn||(S.valHooks[this].get=function(e){return null===e.getAttribute("value")?"on":e.value})}),y.focusin="onfocusin"in E;function kt(e){e.stopPropagation()}var Ct=/^(?:focusinfocus|focusoutblur)$/;S.extend(S.event,{trigger:function(e,t,n,r){var i,o,a,s,l,u,c,d=[n||T],f=v.call(e,"type")?e.type:e,p=v.call(e,"namespace")?e.namespace.split("."):[],h=c=o=n=n||T;if(3!==n.nodeType&&8!==n.nodeType&&!Ct.test(f+S.event.triggered)&&(-1<f.indexOf(".")&&(f=(p=f.split(".")).shift(),p.sort()),s=f.indexOf(":")<0&&"on"+f,(e=e[S.expando]?e:new S.Event(f,"object"==_typeof(e)&&e)).isTrigger=r?2:3,e.namespace=p.join("."),e.rnamespace=e.namespace?new RegExp("(^|\\.)"+p.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,e.result=void 0,e.target||(e.target=n),t=null==t?[e]:S.makeArray(t,[e]),u=S.event.special[f]||{},r||!u.trigger||!1!==u.trigger.apply(n,t))){if(!r&&!u.noBubble&&!m(n)){for(a=u.delegateType||f,Ct.test(a+f)||(h=h.parentNode);h;h=h.parentNode)d.push(h),o=h;o===(n.ownerDocument||T)&&d.push(o.defaultView||o.parentWindow||E)}for(i=0;(h=d[i++])&&!e.isPropagationStopped();)c=h,e.type=1<i?a:u.bindType||f,(l=(X.get(h,"events")||Object.create(null))[e.type]&&X.get(h,"handle"))&&l.apply(h,t),(l=s&&h[s])&&l.apply&&z(h)&&(e.result=l.apply(h,t),!1===e.result&&e.preventDefault());return e.type=f,r||e.isDefaultPrevented()||u._default&&!1!==u._default.apply(d.pop(),t)||!z(n)||s&&b(n[f])&&!m(n)&&((o=n[s])&&(n[s]=null),S.event.triggered=f,e.isPropagationStopped()&&c.addEventListener(f,kt),n[f](),e.isPropagationStopped()&&c.removeEventListener(f,kt),S.event.triggered=void 0,o&&(n[s]=o)),e.result}},simulate:function(e,t,n){e=S.extend(new S.Event,n,{type:e,isSimulated:!0});S.event.trigger(e,null,t)}}),S.fn.extend({trigger:function(e,t){return this.each(function(){S.event.trigger(e,t,this)})},triggerHandler:function(e,t){var n=this[0];if(n)return S.event.trigger(e,t,n,!0)}}),y.focusin||S.each({focus:"focusin",blur:"focusout"},function(n,r){function i(e){S.event.simulate(r,e.target,S.event.fix(e))}S.event.special[r]={setup:function(){var e=this.ownerDocument||this.document||this,t=X.access(e,r);t||e.addEventListener(n,i,!0),X.access(e,r,(t||0)+1)},teardown:function(){var e=this.ownerDocument||this.document||this,t=X.access(e,r)-1;t?X.access(e,r,t):(e.removeEventListener(n,i,!0),X.remove(e,r))}}});var At=E.location,Dt={guid:Date.now()},Rt=/\?/;S.parseXML=function(e){var t;if(!e||"string"!=typeof e)return null;try{t=(new E.DOMParser).parseFromString(e,"text/xml")}catch(e){t=void 0}return t&&!t.getElementsByTagName("parsererror").length||S.error("Invalid XML: "+e),t};var Lt=/\[\]$/,Nt=/\r?\n/g,Ot=/^(?:submit|button|image|reset|file)$/i,jt=/^(?:input|select|textarea|keygen)/i;S.param=function(e,t){function n(e,t){t=b(t)?t():t,i[i.length]=encodeURIComponent(e)+"="+encodeURIComponent(null==t?"":t)}var r,i=[];if(null==e)return"";if(Array.isArray(e)||e.jquery&&!S.isPlainObject(e))S.each(e,function(){n(this.name,this.value)});else for(r in e)!function n(r,e,i,o){if(Array.isArray(e))S.each(e,function(e,t){i||Lt.test(r)?o(r,t):n(r+"["+("object"==_typeof(t)&&null!=t?e:"")+"]",t,i,o)});else if(i||"object"!==h(e))o(r,e);else for(var t in e)n(r+"["+t+"]",e[t],i,o)}(r,e[r],t,n);return i.join("&")},S.fn.extend({serialize:function(){return S.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var e=S.prop(this,"elements");return e?S.makeArray(e):this}).filter(function(){var e=this.type;return this.name&&!S(this).is(":disabled")&&jt.test(this.nodeName)&&!Ot.test(e)&&(this.checked||!ue.test(e))}).map(function(e,t){var n=S(this).val();return null==n?null:Array.isArray(n)?S.map(n,function(e){return{name:t.name,value:e.replace(Nt,"\r\n")}}):{name:t.name,value:n.replace(Nt,"\r\n")}}).get()}});var It=/%20/g,Pt=/#.*$/,Ft=/([?&])_=[^&]*/,Mt=/^(.*?):[ \t]*([^\r\n]*)$/gm,qt=/^(?:GET|HEAD)$/,Ht=/^\/\//,Bt={},Ut={},Gt="*/".concat("*"),Wt=T.createElement("a");function $t(o){return function(e,t){"string"!=typeof e&&(t=e,e="*");var n,r=0,i=e.toLowerCase().match(j)||[];if(b(t))for(;n=i[r++];)"+"===n[0]?(n=n.slice(1)||"*",(o[n]=o[n]||[]).unshift(t)):(o[n]=o[n]||[]).push(t)}}function zt(t,r,i,o){var a={},s=t===Ut;function l(e){var n;return a[e]=!0,S.each(t[e]||[],function(e,t){t=t(r,i,o);return"string"!=typeof t||s||a[t]?s?!(n=t):void 0:(r.dataTypes.unshift(t),l(t),!1)}),n}return l(r.dataTypes[0])||!a["*"]&&l("*")}function Vt(e,t){var n,r,i=S.ajaxSettings.flatOptions||{};for(n in t)void 0!==t[n]&&((i[n]?e:r=r||{})[n]=t[n]);return r&&S.extend(!0,e,r),e}Wt.href=At.href,S.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:At.href,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(At.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Gt,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":S.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(e,t){return t?Vt(Vt(e,S.ajaxSettings),t):Vt(S.ajaxSettings,e)},ajaxPrefilter:$t(Bt),ajaxTransport:$t(Ut),ajax:function(e,t){"object"==_typeof(e)&&(t=e,e=void 0),t=t||{};var l,u,c,n,d,r,f,p,i,o,h=S.ajaxSetup({},t),m=h.context||h,g=h.context&&(m.nodeType||m.jquery)?S(m):S.event,v=S.Deferred(),y=S.Callbacks("once memory"),b=h.statusCode||{},a={},s={},_="canceled",w={readyState:0,getResponseHeader:function(e){var t;if(f){if(!n)for(n={};t=Mt.exec(c);)n[t[1].toLowerCase()+" "]=(n[t[1].toLowerCase()+" "]||[]).concat(t[2]);t=n[e.toLowerCase()+" "]}return null==t?null:t.join(", ")},getAllResponseHeaders:function(){return f?c:null},setRequestHeader:function(e,t){return null==f&&(e=s[e.toLowerCase()]=s[e.toLowerCase()]||e,a[e]=t),this},overrideMimeType:function(e){return null==f&&(h.mimeType=e),this},statusCode:function(e){if(e)if(f)w.always(e[w.status]);else for(var t in e)b[t]=[b[t],e[t]];return this},abort:function(e){e=e||_;return l&&l.abort(e),x(0,e),this}};if(v.promise(w),h.url=((e||h.url||At.href)+"").replace(Ht,At.protocol+"//"),h.type=t.method||t.type||h.method||h.type,h.dataTypes=(h.dataType||"*").toLowerCase().match(j)||[""],null==h.crossDomain){r=T.createElement("a");try{r.href=h.url,r.href=r.href,h.crossDomain=Wt.protocol+"//"+Wt.host!=r.protocol+"//"+r.host}catch(e){h.crossDomain=!0}}if(h.data&&h.processData&&"string"!=typeof h.data&&(h.data=S.param(h.data,h.traditional)),zt(Bt,h,t,w),f)return w;for(i in(p=S.event&&h.global)&&0==S.active++&&S.event.trigger("ajaxStart"),h.type=h.type.toUpperCase(),h.hasContent=!qt.test(h.type),u=h.url.replace(Pt,""),h.hasContent?h.data&&h.processData&&0===(h.contentType||"").indexOf("application/x-www-form-urlencoded")&&(h.data=h.data.replace(It,"+")):(o=h.url.slice(u.length),h.data&&(h.processData||"string"==typeof h.data)&&(u+=(Rt.test(u)?"&":"?")+h.data,delete h.data),!1===h.cache&&(u=u.replace(Ft,"$1"),o=(Rt.test(u)?"&":"?")+"_="+Dt.guid+++o),h.url=u+o),h.ifModified&&(S.lastModified[u]&&w.setRequestHeader("If-Modified-Since",S.lastModified[u]),S.etag[u]&&w.setRequestHeader("If-None-Match",S.etag[u])),(h.data&&h.hasContent&&!1!==h.contentType||t.contentType)&&w.setRequestHeader("Content-Type",h.contentType),w.setRequestHeader("Accept",h.dataTypes[0]&&h.accepts[h.dataTypes[0]]?h.accepts[h.dataTypes[0]]+("*"!==h.dataTypes[0]?", "+Gt+"; q=0.01":""):h.accepts["*"]),h.headers)w.setRequestHeader(i,h.headers[i]);if(h.beforeSend&&(!1===h.beforeSend.call(m,w,h)||f))return w.abort();if(_="abort",y.add(h.complete),w.done(h.success),w.fail(h.error),l=zt(Ut,h,t,w)){if(w.readyState=1,p&&g.trigger("ajaxSend",[w,h]),f)return w;h.async&&0<h.timeout&&(d=E.setTimeout(function(){w.abort("timeout")},h.timeout));try{f=!1,l.send(a,x)}catch(e){if(f)throw e;x(-1,e)}}else x(-1,"No Transport");function x(e,t,n,r){var i,o,a,s=t;f||(f=!0,d&&E.clearTimeout(d),l=void 0,c=r||"",w.readyState=0<e?4:0,r=200<=e&&e<300||304===e,n&&(a=function(e,t,n){for(var r,i,o,a,s=e.contents,l=e.dataTypes;"*"===l[0];)l.shift(),void 0===r&&(r=e.mimeType||t.getResponseHeader("Content-Type"));if(r)for(i in s)if(s[i]&&s[i].test(r)){l.unshift(i);break}if(l[0]in n)o=l[0];else{for(i in n){if(!l[0]||e.converters[i+" "+l[0]]){o=i;break}a=a||i}o=o||a}if(o)return o!==l[0]&&l.unshift(o),n[o]}(h,w,n)),!r&&-1<S.inArray("script",h.dataTypes)&&(h.converters["text script"]=function(){}),a=function(e,t,n,r){var i,o,a,s,l,u={},c=e.dataTypes.slice();if(c[1])for(a in e.converters)u[a.toLowerCase()]=e.converters[a];for(o=c.shift();o;)if(e.responseFields[o]&&(n[e.responseFields[o]]=t),!l&&r&&e.dataFilter&&(t=e.dataFilter(t,e.dataType)),l=o,o=c.shift())if("*"===o)o=l;else if("*"!==l&&l!==o){if(!(a=u[l+" "+o]||u["* "+o]))for(i in u)if((s=i.split(" "))[1]===o&&(a=u[l+" "+s[0]]||u["* "+s[0]])){!0===a?a=u[i]:!0!==u[i]&&(o=s[0],c.unshift(s[1]));break}if(!0!==a)if(a&&e.throws)t=a(t);else try{t=a(t)}catch(e){return{state:"parsererror",error:a?e:"No conversion from "+l+" to "+o}}}return{state:"success",data:t}}(h,a,w,r),r?(h.ifModified&&((n=w.getResponseHeader("Last-Modified"))&&(S.lastModified[u]=n),(n=w.getResponseHeader("etag"))&&(S.etag[u]=n)),204===e||"HEAD"===h.type?s="nocontent":304===e?s="notmodified":(s=a.state,i=a.data,r=!(o=a.error))):(o=s,!e&&s||(s="error",e<0&&(e=0))),w.status=e,w.statusText=(t||s)+"",r?v.resolveWith(m,[i,s,w]):v.rejectWith(m,[w,s,o]),w.statusCode(b),b=void 0,p&&g.trigger(r?"ajaxSuccess":"ajaxError",[w,h,r?i:o]),y.fireWith(m,[w,s]),p&&(g.trigger("ajaxComplete",[w,h]),--S.active||S.event.trigger("ajaxStop")))}return w},getJSON:function(e,t,n){return S.get(e,t,n,"json")},getScript:function(e,t){return S.get(e,void 0,t,"script")}}),S.each(["get","post"],function(e,i){S[i]=function(e,t,n,r){return b(t)&&(r=r||n,n=t,t=void 0),S.ajax(S.extend({url:e,type:i,dataType:r,data:t,success:n},S.isPlainObject(e)&&e))}}),S.ajaxPrefilter(function(e){for(var t in e.headers)"content-type"===t.toLowerCase()&&(e.contentType=e.headers[t]||"")}),S._evalUrl=function(e,t,n){return S.ajax({url:e,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,converters:{"text script":function(){}},dataFilter:function(e){S.globalEval(e,t,n)}})},S.fn.extend({wrapAll:function(e){return this[0]&&(b(e)&&(e=e.call(this[0])),e=S(e,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&e.insertBefore(this[0]),e.map(function(){for(var e=this;e.firstElementChild;)e=e.firstElementChild;return e}).append(this)),this},wrapInner:function(n){return b(n)?this.each(function(e){S(this).wrapInner(n.call(this,e))}):this.each(function(){var e=S(this),t=e.contents();t.length?t.wrapAll(n):e.append(n)})},wrap:function(t){var n=b(t);return this.each(function(e){S(this).wrapAll(n?t.call(this,e):t)})},unwrap:function(e){return this.parent(e).not("body").each(function(){S(this).replaceWith(this.childNodes)}),this}}),S.expr.pseudos.hidden=function(e){return!S.expr.pseudos.visible(e)},S.expr.pseudos.visible=function(e){return!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)},S.ajaxSettings.xhr=function(){try{return new E.XMLHttpRequest}catch(e){}};var Xt={0:200,1223:204},Qt=S.ajaxSettings.xhr();y.cors=!!Qt&&"withCredentials"in Qt,y.ajax=Qt=!!Qt,S.ajaxTransport(function(i){var o,a;if(y.cors||Qt&&!i.crossDomain)return{send:function(e,t){var n,r=i.xhr();if(r.open(i.type,i.url,i.async,i.username,i.password),i.xhrFields)for(n in i.xhrFields)r[n]=i.xhrFields[n];for(n in i.mimeType&&r.overrideMimeType&&r.overrideMimeType(i.mimeType),i.crossDomain||e["X-Requested-With"]||(e["X-Requested-With"]="XMLHttpRequest"),e)r.setRequestHeader(n,e[n]);o=function(e){return function(){o&&(o=a=r.onload=r.onerror=r.onabort=r.ontimeout=r.onreadystatechange=null,"abort"===e?r.abort():"error"===e?"number"!=typeof r.status?t(0,"error"):t(r.status,r.statusText):t(Xt[r.status]||r.status,r.statusText,"text"!==(r.responseType||"text")||"string"!=typeof r.responseText?{binary:r.response}:{text:r.responseText},r.getAllResponseHeaders()))}},r.onload=o(),a=r.onerror=r.ontimeout=o("error"),void 0!==r.onabort?r.onabort=a:r.onreadystatechange=function(){4===r.readyState&&E.setTimeout(function(){o&&a()})},o=o("abort");try{r.send(i.hasContent&&i.data||null)}catch(e){if(o)throw e}},abort:function(){o&&o()}}}),S.ajaxPrefilter(function(e){e.crossDomain&&(e.contents.script=!1)}),S.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(e){return S.globalEval(e),e}}}),S.ajaxPrefilter("script",function(e){void 0===e.cache&&(e.cache=!1),e.crossDomain&&(e.type="GET")}),S.ajaxTransport("script",function(n){var r,i;if(n.crossDomain||n.scriptAttrs)return{send:function(e,t){r=S("<script>").attr(n.scriptAttrs||{}).prop({charset:n.scriptCharset,src:n.url}).on("load error",i=function(e){r.remove(),i=null,e&&t("error"===e.type?404:200,e.type)}),T.head.appendChild(r[0])},abort:function(){i&&i()}}});var Yt=[],Kt=/(=)\?(?=&|$)|\?\?/;S.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=Yt.pop()||S.expando+"_"+Dt.guid++;return this[e]=!0,e}}),S.ajaxPrefilter("json jsonp",function(e,t,n){var r,i,o,a=!1!==e.jsonp&&(Kt.test(e.url)?"url":"string"==typeof e.data&&0===(e.contentType||"").indexOf("application/x-www-form-urlencoded")&&Kt.test(e.data)&&"data");if(a||"jsonp"===e.dataTypes[0])return r=e.jsonpCallback=b(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback,a?e[a]=e[a].replace(Kt,"$1"+r):!1!==e.jsonp&&(e.url+=(Rt.test(e.url)?"&":"?")+e.jsonp+"="+r),e.converters["script json"]=function(){return o||S.error(r+" was not called"),o[0]},e.dataTypes[0]="json",i=E[r],E[r]=function(){o=arguments},n.always(function(){void 0===i?S(E).removeProp(r):E[r]=i,e[r]&&(e.jsonpCallback=t.jsonpCallback,Yt.push(r)),o&&b(i)&&i(o[0]),o=i=void 0}),"script"}),y.createHTMLDocument=((fe=T.implementation.createHTMLDocument("").body).innerHTML="<form></form><form></form>",2===fe.childNodes.length),S.parseHTML=function(e,t,n){return"string"!=typeof e?[]:("boolean"==typeof t&&(n=t,t=!1),t||(y.createHTMLDocument?((r=(t=T.implementation.createHTMLDocument("")).createElement("base")).href=T.location.href,t.head.appendChild(r)):t=T),r=!n&&[],(n=C.exec(e))?[t.createElement(n[1])]:(n=ve([e],t,r),r&&r.length&&S(r).remove(),S.merge([],n.childNodes)));var r},S.fn.load=function(e,t,n){var r,i,o,a=this,s=e.indexOf(" ");return-1<s&&(r=xt(e.slice(s)),e=e.slice(0,s)),b(t)?(n=t,t=void 0):t&&"object"==_typeof(t)&&(i="POST"),0<a.length&&S.ajax({url:e,type:i||"GET",dataType:"html",data:t}).done(function(e){o=arguments,a.html(r?S("<div>").append(S.parseHTML(e)).find(r):e)}).always(n&&function(e,t){a.each(function(){n.apply(this,o||[e.responseText,t,e])})}),this},S.expr.pseudos.animated=function(t){return S.grep(S.timers,function(e){return t===e.elem}).length},S.offset={setOffset:function(e,t,n){var r,i,o,a,s=S.css(e,"position"),l=S(e),u={};"static"===s&&(e.style.position="relative"),o=l.offset(),r=S.css(e,"top"),a=S.css(e,"left"),a=("absolute"===s||"fixed"===s)&&-1<(r+a).indexOf("auto")?(i=(s=l.position()).top,s.left):(i=parseFloat(r)||0,parseFloat(a)||0),b(t)&&(t=t.call(e,n,S.extend({},o))),null!=t.top&&(u.top=t.top-o.top+i),null!=t.left&&(u.left=t.left-o.left+a),"using"in t?t.using.call(e,u):("number"==typeof u.top&&(u.top+="px"),"number"==typeof u.left&&(u.left+="px"),l.css(u))}},S.fn.extend({offset:function(t){if(arguments.length)return void 0===t?this:this.each(function(e){S.offset.setOffset(this,t,e)});var e,n=this[0];return n?n.getClientRects().length?(e=n.getBoundingClientRect(),n=n.ownerDocument.defaultView,{top:e.top+n.pageYOffset,left:e.left+n.pageXOffset}):{top:0,left:0}:void 0},position:function(){if(this[0]){var e,t,n,r=this[0],i={top:0,left:0};if("fixed"===S.css(r,"position"))t=r.getBoundingClientRect();else{for(t=this.offset(),n=r.ownerDocument,e=r.offsetParent||n.documentElement;e&&(e===n.body||e===n.documentElement)&&"static"===S.css(e,"position");)e=e.parentNode;e&&e!==r&&1===e.nodeType&&((i=S(e).offset()).top+=S.css(e,"borderTopWidth",!0),i.left+=S.css(e,"borderLeftWidth",!0))}return{top:t.top-i.top-S.css(r,"marginTop",!0),left:t.left-i.left-S.css(r,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var e=this.offsetParent;e&&"static"===S.css(e,"position");)e=e.offsetParent;return e||ne})}}),S.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(t,i){var o="pageYOffset"===i;S.fn[t]=function(e){return B(this,function(e,t,n){var r;return m(e)?r=e:9===e.nodeType&&(r=e.defaultView),void 0===n?r?r[i]:e[t]:void(r?r.scrollTo(o?r.pageXOffset:n,o?n:r.pageYOffset):e[t]=n)},t,e,arguments.length)}}),S.each(["top","left"],function(e,n){S.cssHooks[n]=Xe(y.pixelPosition,function(e,t){if(t)return t=Ve(e,n),We.test(t)?S(e).position()[n]+"px":t})}),S.each({Height:"height",Width:"width"},function(a,s){S.each({padding:"inner"+a,content:s,"":"outer"+a},function(r,o){S.fn[o]=function(e,t){var n=arguments.length&&(r||"boolean"!=typeof e),i=r||(!0===e||!0===t?"margin":"border");return B(this,function(e,t,n){var r;return m(e)?0===o.indexOf("outer")?e["inner"+a]:e.document.documentElement["client"+a]:9===e.nodeType?(r=e.documentElement,Math.max(e.body["scroll"+a],r["scroll"+a],e.body["offset"+a],r["offset"+a],r["client"+a])):void 0===n?S.css(e,t,i):S.style(e,t,n,i)},s,n?e:void 0,n)}})}),S.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(e,t){S.fn[t]=function(e){return this.on(t,e)}}),S.fn.extend({bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,n,r){return this.on(t,e,n,r)},undelegate:function(e,t,n){return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",n)},hover:function(e,t){return this.mouseenter(e).mouseleave(t||e)}}),S.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),function(e,n){S.fn[n]=function(e,t){return 0<arguments.length?this.on(n,null,e,t):this.trigger(n)}});var Jt=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g;S.proxy=function(e,t){var n,r;if("string"==typeof t&&(r=e[t],t=e,e=r),b(e))return n=s.call(arguments,2),(r=function(){return e.apply(t||this,n.concat(s.call(arguments)))}).guid=e.guid=e.guid||S.guid++,r},S.holdReady=function(e){e?S.readyWait++:S.ready(!0)},S.isArray=Array.isArray,S.parseJSON=JSON.parse,S.nodeName=k,S.isFunction=b,S.isWindow=m,S.camelCase=$,S.type=h,S.now=Date.now,S.isNumeric=function(e){var t=S.type(e);return("number"===t||"string"===t)&&!isNaN(e-parseFloat(e))},S.trim=function(e){return null==e?"":(e+"").replace(Jt,"")},"function"==typeof define&&define.amd&&define("jquery",[],function(){return S});var Zt=E.jQuery,en=E.$;return S.noConflict=function(e){return E.$===S&&(E.$=en),e&&E.jQuery===S&&(E.jQuery=Zt),S},void 0===e&&(E.jQuery=E.$=S),S}),function(e,t){"object"==("undefined"==typeof exports?"undefined":_typeof(exports))&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):e.Popper=t()}(this,function(){"use strict";function i(e){return e&&"[object Function]"==={}.toString.call(e)}function d(e,t){if(1!==e.nodeType)return[];e=e.ownerDocument.defaultView.getComputedStyle(e,null);return t?e[t]:e}function u(e){return"HTML"===e.nodeName?e:e.parentNode||e.host}function c(e){if(!e)return document.body;switch(e.nodeName){case"HTML":case"BODY":return e.ownerDocument.body;case"#document":return e.body}var t=d(e),n=t.overflow,r=t.overflowX,t=t.overflowY;return/(auto|scroll|overlay)/.test(n+t+r)?e:c(u(e))}function f(e){return e&&e.referenceNode?e.referenceNode:e}function p(e){return 11===e?G:10!==e&&G||W}function m(e){if(!e)return document.documentElement;for(var t=p(10)?document.body:null,n=e.offsetParent||null;n===t&&e.nextElementSibling;)n=(e=e.nextElementSibling).offsetParent;var r=n&&n.nodeName;return r&&"BODY"!==r&&"HTML"!==r?-1!==["TH","TD","TABLE"].indexOf(n.nodeName)&&"static"===d(n,"position")?m(n):n:(e?e.ownerDocument:document).documentElement}function o(e){return null===e.parentNode?e:o(e.parentNode)}function h(e,t){if(!(e&&e.nodeType&&t&&t.nodeType))return document.documentElement;var n=e.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_FOLLOWING,r=n?e:t,i=n?t:e,n=document.createRange();n.setStart(r,0),n.setEnd(i,0);n=n.commonAncestorContainer;if(e!==n&&t!==n||r.contains(i))return"BODY"===(i=(r=n).nodeName)||"HTML"!==i&&m(r.firstElementChild)!==r?m(n):n;n=o(e);return n.host?h(n.host,t):h(e,o(t).host)}function g(e,t){var n="top"===(1<arguments.length&&void 0!==t?t:"top")?"scrollTop":"scrollLeft",t=e.nodeName;if("BODY"!==t&&"HTML"!==t)return e[n];t=e.ownerDocument.documentElement;return(e.ownerDocument.scrollingElement||t)[n]}function l(e,t){var n="x"===t?"Left":"Top",t="Left"==n?"Right":"Bottom";return parseFloat(e["border"+n+"Width"])+parseFloat(e["border"+t+"Width"])}function r(e,t,n,r){return q(t["offset"+e],t["scroll"+e],n["client"+e],n["offset"+e],n["scroll"+e],p(10)?parseInt(n["offset"+e])+parseInt(r["margin"+("Height"===e?"Top":"Left")])+parseInt(r["margin"+("Height"===e?"Bottom":"Right")]):0)}function v(e){var t=e.body,n=e.documentElement,e=p(10)&&getComputedStyle(n);return{height:r("Height",t,n,e),width:r("Width",t,n,e)}}function y(e){return V({},e,{right:e.left+e.width,bottom:e.top+e.height})}function b(e){var t,n,r={};try{p(10)?(r=e.getBoundingClientRect(),t=g(e,"top"),n=g(e,"left"),r.top+=t,r.left+=n,r.bottom+=t,r.right+=n):r=e.getBoundingClientRect()}catch(e){}var i={left:r.left,top:r.top,width:r.right-r.left,height:r.bottom-r.top},o="HTML"===e.nodeName?v(e.ownerDocument):{},a=o.width||e.clientWidth||i.width,s=o.height||e.clientHeight||i.height,o=e.offsetWidth-a,a=e.offsetHeight-s;return(o||a)&&(o-=l(s=d(e),"x"),a-=l(s,"y"),i.width-=o,i.height-=a),y(i)}function _(e,t,n){var r=2<arguments.length&&void 0!==n&&n,i=p(10),o="HTML"===t.nodeName,a=b(e),s=b(t),l=c(e),u=d(t),n=parseFloat(u.borderTopWidth),e=parseFloat(u.borderLeftWidth);r&&o&&(s.top=q(s.top,0),s.left=q(s.left,0));a=y({top:a.top-s.top-n,left:a.left-s.left-e,width:a.width,height:a.height});return a.marginTop=0,a.marginLeft=0,!i&&o&&(o=parseFloat(u.marginTop),u=parseFloat(u.marginLeft),a.top-=n-o,a.bottom-=n-o,a.left-=e-u,a.right-=e-u,a.marginTop=o,a.marginLeft=u),(i&&!r?t.contains(l):t===l&&"BODY"!==l.nodeName)&&(a=function(e,t,n){var r=2<arguments.length&&void 0!==n&&n,n=g(t,"top"),t=g(t,"left"),r=r?-1:1;return e.top+=n*r,e.bottom+=n*r,e.left+=t*r,e.right+=t*r,e}(a,t)),a}function w(e){if(!e||!e.parentElement||p())return document.documentElement;for(var t=e.parentElement;t&&"none"===d(t,"transform");)t=t.parentElement;return t||document.documentElement}function x(e,t,n,r,i){var o,a=4<arguments.length&&void 0!==i&&i,s={top:0,left:0},i=a?w(e):h(e,f(t));"viewport"===r?s=function(e,t){var n=1<arguments.length&&void 0!==t&&t,r=e.ownerDocument.documentElement,i=_(e,r),o=q(r.clientWidth,window.innerWidth||0),t=q(r.clientHeight,window.innerHeight||0),e=n?0:g(r),r=n?0:g(r,"left");return y({top:e-i.top+i.marginTop,left:r-i.left+i.marginLeft,width:o,height:t})}(i,a):("scrollParent"===r?"BODY"===(o=c(u(t))).nodeName&&(o=e.ownerDocument.documentElement):o="window"===r?e.ownerDocument.documentElement:r,l=_(o,i,a),"HTML"!==o.nodeName||function e(t){var n=t.nodeName;if("BODY"===n||"HTML"===n)return!1;if("fixed"===d(t,"position"))return!0;t=u(t);return!!t&&e(t)}(i)?s=l:(e=(i=v(e.ownerDocument)).height,i=i.width,s.top+=l.top-l.marginTop,s.bottom=e+l.top,s.left+=l.left-l.marginLeft,s.right=i+l.left));var l="number"==typeof(n=n||0);return s.left+=l?n:n.left||0,s.top+=l?n:n.top||0,s.right-=l?n:n.right||0,s.bottom-=l?n:n.bottom||0,s}function a(e,t,n,r,i,o){o=5<arguments.length&&void 0!==o?o:0;if(-1===e.indexOf("auto"))return e;var i=x(n,r,o,i),a={top:{width:i.width,height:t.top-i.top},right:{width:i.right-t.right,height:i.height},bottom:{width:i.width,height:i.bottom-t.bottom},left:{width:t.left-i.left,height:i.height}},t=Object.keys(a).map(function(e){return V({key:e},a[e],{area:(e=a[e]).width*e.height})}).sort(function(e,t){return t.area-e.area}),i=t.filter(function(e){var t=e.width,e=e.height;return t>=n.clientWidth&&e>=n.clientHeight}),t=(0<i.length?i:t)[0].key,e=e.split("-")[1];return t+(e?"-"+e:"")}function s(e,t,n,r){r=3<arguments.length&&void 0!==r?r:null;return _(n,r?w(t):h(t,f(n)),r)}function E(e){var t=e.ownerDocument.defaultView.getComputedStyle(e),n=parseFloat(t.marginTop||0)+parseFloat(t.marginBottom||0),t=parseFloat(t.marginLeft||0)+parseFloat(t.marginRight||0);return{width:e.offsetWidth+t,height:e.offsetHeight+n}}function T(e){var t={left:"right",right:"left",bottom:"top",top:"bottom"};return e.replace(/left|right|bottom|top/g,function(e){return t[e]})}function S(e,t,n){n=n.split("-")[0];var r=E(e),i={width:r.width,height:r.height},o=-1!==["right","left"].indexOf(n),a=o?"top":"left",s=o?"left":"top",e=o?"height":"width",o=o?"width":"height";return i[a]=t[a]+t[e]/2-r[e]/2,i[s]=n===s?t[s]-r[o]:t[T(s)],i}function k(e,t){return Array.prototype.find?e.find(t):e.filter(t)[0]}function C(e,n,t){return(void 0===t?e:e.slice(0,function(e,t,n){if(Array.prototype.findIndex)return e.findIndex(function(e){return e[t]===n});var r=k(e,function(e){return e[t]===n});return e.indexOf(r)}(e,"name",t))).forEach(function(e){e.function;var t=e.function||e.fn;e.enabled&&i(t)&&(n.offsets.popper=y(n.offsets.popper),n.offsets.reference=y(n.offsets.reference),n=t(n,e))}),n}function e(e,n){return e.some(function(e){var t=e.name;return e.enabled&&t===n})}function A(e){for(var t=[!1,"ms","Webkit","Moz","O"],n=e.charAt(0).toUpperCase()+e.slice(1),r=0;r<t.length;r++){var i=t[r],i=i?""+i+n:e;if(void 0!==document.body.style[i])return i}return null}function D(e){e=e.ownerDocument;return e?e.defaultView:window}function t(e,t,n,r){n.updateBound=r,D(e).addEventListener("resize",n.updateBound,{passive:!0});e=c(e);return function e(t,n,r,i){var o="BODY"===t.nodeName,t=o?t.ownerDocument.defaultView:t;t.addEventListener(n,r,{passive:!0}),o||e(c(t.parentNode),n,r,i),i.push(t)}(e,"scroll",n.updateBound,n.scrollParents),n.scrollElement=e,n.eventsEnabled=!0,n}function n(){var e,t;this.state.eventsEnabled&&(cancelAnimationFrame(this.scheduleUpdate),this.state=(e=this.reference,t=this.state,D(e).removeEventListener("resize",t.updateBound),t.scrollParents.forEach(function(e){e.removeEventListener("scroll",t.updateBound)}),t.updateBound=null,t.scrollParents=[],t.scrollElement=null,t.eventsEnabled=!1,t))}function R(e){return""!==e&&!isNaN(parseFloat(e))&&isFinite(e)}function L(n,r){Object.keys(r).forEach(function(e){var t="";-1!==["width","height","top","right","bottom","left"].indexOf(e)&&R(r[e])&&(t="px"),n.style[e]=r[e]+t})}function N(e,t,n){var r=k(e,function(e){return e.name===t}),e=!!r&&e.some(function(e){return e.name===n&&e.enabled&&e.order<r.order});return e||0,e}function O(e,t){t=1<arguments.length&&void 0!==t&&t,e=Y.indexOf(e),e=Y.slice(e+1).concat(Y.slice(0,e));return t?e.reverse():e}function j(e,i,o,t){var a=[0,0],s=-1!==["right","left"].indexOf(t),n=e.split(/(\+|\-)/).map(function(e){return e.trim()}),t=n.indexOf(k(n,function(e){return-1!==e.search(/,|\s/)}));n[t]&&n[t].indexOf(",");e=/\s*,\s*|\s+/;return(-1===t?[n]:[n.slice(0,t).concat([n[t].split(e)[0]]),[n[t].split(e)[1]].concat(n.slice(t+1))]).map(function(e,t){var n=(1===t?!s:s)?"height":"width",r=!1;return e.reduce(function(e,t){return""===e[e.length-1]&&-1!==["+","-"].indexOf(t)?(e[e.length-1]=t,r=!0,e):r?(e[e.length-1]+=t,r=!1,e):e.concat(t)},[]).map(function(e){return function(e,t,n,r){var i,o=+(a=e.match(/((?:\-|\+)?\d*\.?\d*)(.*)/))[1],a=a[2];if(!o)return e;if(0!==a.indexOf("%"))return"vh"!==a&&"vw"!==a?o:("vh"===a?q(document.documentElement.clientHeight,window.innerHeight||0):q(document.documentElement.clientWidth,window.innerWidth||0))/100*o;switch(a){case"%p":i=n;break;case"%":case"%r":default:i=r}return y(i)[t]/100*o}(e,n,i,o)})}).forEach(function(n,r){n.forEach(function(e,t){R(e)&&(a[r]+=e*("-"===n[t-1]?-1:1))})}),a}function I(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var P=Math.min,F=Math.floor,M=Math.round,q=Math.max,H="undefined"!=typeof window&&"undefined"!=typeof document&&"undefined"!=typeof navigator,B=function(){for(var e=["Edge","Trident","Firefox"],t=0;t<e.length;t+=1)if(H&&0<=navigator.userAgent.indexOf(e[t]))return 1;return 0}(),U=H&&window.Promise?function(e){var t=!1;return function(){t||(t=!0,window.Promise.resolve().then(function(){t=!1,e()}))}}:function(e){var t=!1;return function(){t||(t=!0,setTimeout(function(){t=!1,e()},B))}},G=H&&!(!window.MSInputMethodContext||!document.documentMode),W=H&&/MSIE 10/.test(navigator.userAgent),$=function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},z=function(e,t,n){return t&&te(e.prototype,t),n&&te(e,n),e},V=Object.assign||function(e){for(var t,n=1;n<arguments.length;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},X=H&&/Firefox/i.test(navigator.userAgent),Q=["auto-start","auto","auto-end","top-start","top","top-end","right-start","right","right-end","bottom-end","bottom","bottom-start","left-end","left","left-start"],Y=Q.slice(3),K="flip",J="clockwise",Z="counterclockwise",z=(z(ee,[{key:"update",value:function(){return function(){var e;this.state.isDestroyed||((e={instance:this,styles:{},arrowStyles:{},attributes:{},flipped:!1,offsets:{}}).offsets.reference=s(this.state,this.popper,this.reference,this.options.positionFixed),e.placement=a(this.options.placement,e.offsets.reference,this.popper,this.reference,this.options.modifiers.flip.boundariesElement,this.options.modifiers.flip.padding),e.originalPlacement=e.placement,e.positionFixed=this.options.positionFixed,e.offsets.popper=S(this.popper,e.offsets.reference,e.placement),e.offsets.popper.position=this.options.positionFixed?"fixed":"absolute",e=C(this.modifiers,e),this.state.isCreated?this.options.onUpdate(e):(this.state.isCreated=!0,this.options.onCreate(e)))}.call(this)}},{key:"destroy",value:function(){return function(){return this.state.isDestroyed=!0,e(this.modifiers,"applyStyle")&&(this.popper.removeAttribute("x-placement"),this.popper.style.position="",this.popper.style.top="",this.popper.style.left="",this.popper.style.right="",this.popper.style.bottom="",this.popper.style.willChange="",this.popper.style[A("transform")]=""),this.disableEventListeners(),this.options.removeOnDestroy&&this.popper.parentNode.removeChild(this.popper),this}.call(this)}},{key:"enableEventListeners",value:function(){return function(){this.state.eventsEnabled||(this.state=t(this.reference,this.options,this.state,this.scheduleUpdate))}.call(this)}},{key:"disableEventListeners",value:function(){return n.call(this)}}]),ee);function ee(e,t){var n=this,r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{};$(this,ee),this.scheduleUpdate=function(){return requestAnimationFrame(n.update)},this.update=U(this.update.bind(this)),this.options=V({},ee.Defaults,r),this.state={isDestroyed:!1,isCreated:!1,scrollParents:[]},this.reference=e&&e.jquery?e[0]:e,this.popper=t&&t.jquery?t[0]:t,this.options.modifiers={},Object.keys(V({},ee.Defaults.modifiers,r.modifiers)).forEach(function(e){n.options.modifiers[e]=V({},ee.Defaults.modifiers[e]||{},r.modifiers?r.modifiers[e]:{})}),this.modifiers=Object.keys(this.options.modifiers).map(function(e){return V({name:e},n.options.modifiers[e])}).sort(function(e,t){return e.order-t.order}),this.modifiers.forEach(function(e){e.enabled&&i(e.onLoad)&&e.onLoad(n.reference,n.popper,n.options,e,n.state)}),this.update();t=this.options.eventsEnabled;t&&this.enableEventListeners(),this.state.eventsEnabled=t}function te(e,t){for(var n,r=0;r<t.length;r++)(n=t[r]).enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}return z.Utils=("undefined"==typeof window?global:window).PopperUtils,z.placements=Q,z.Defaults={placement:"bottom",positionFixed:!1,eventsEnabled:!0,removeOnDestroy:!1,onCreate:function(){},onUpdate:function(){},modifiers:{shift:{order:100,enabled:!0,fn:function(e){var t,n,r=e.placement,i=r.split("-")[0],o=r.split("-")[1];return o&&(t=(n=e.offsets).reference,r=n.popper,i=(n=-1!==["bottom","top"].indexOf(i))?"width":"height",i={start:I({},n=n?"left":"top",t[n]),end:I({},n,t[n]+t[i]-r[i])},e.offsets.popper=V({},r,i[o])),e}},offset:{order:200,enabled:!0,fn:function(e,t){var n=t.offset,r=e.placement,t=(i=e.offsets).popper,i=i.reference,r=r.split("-")[0],i=R(+n)?[+n,0]:j(n,t,i,r);return"left"===r?(t.top+=i[0],t.left-=i[1]):"right"===r?(t.top+=i[0],t.left+=i[1]):"top"===r?(t.left+=i[0],t.top-=i[1]):"bottom"===r&&(t.left+=i[0],t.top+=i[1]),e.popper=t,e},offset:0},preventOverflow:{order:300,enabled:!0,fn:function(e,r){var t=r.boundariesElement||m(e.instance.popper);e.instance.reference===t&&(t=m(t));var n=A("transform"),i=e.instance.popper.style,o=i.top,a=i.left,s=i[n];i.top="",i.left="",i[n]="";var l=x(e.instance.popper,e.instance.reference,r.padding,t,e.positionFixed);i.top=o,i.left=a,i[n]=s,r.boundaries=l;var s=r.priority,u=e.offsets.popper,c={primary:function(e){var t=u[e];return u[e]<l[e]&&!r.escapeWithReference&&(t=q(u[e],l[e])),I({},e,t)},secondary:function(e){var t="right"===e?"left":"top",n=u[t];return u[e]>l[e]&&!r.escapeWithReference&&(n=P(u[t],l[e]-("right"===e?u.width:u.height))),I({},t,n)}};return s.forEach(function(e){var t=-1===["left","top"].indexOf(e)?"secondary":"primary";u=V({},u,c[t](e))}),e.offsets.popper=u,e},priority:["left","right","top","bottom"],padding:5,boundariesElement:"scrollParent"},keepTogether:{order:400,enabled:!0,fn:function(e){var t=e.offsets,n=t.popper,r=t.reference,i=e.placement.split("-")[0],o=F,a=-1!==["top","bottom"].indexOf(i),t=a?"right":"bottom",i=a?"left":"top",a=a?"width":"height";return n[t]<o(r[i])&&(e.offsets.popper[i]=o(r[i])-n[a]),n[i]>o(r[t])&&(e.offsets.popper[i]=o(r[t])),e}},arrow:{order:500,enabled:!0,fn:function(e,t){if(!N(e.instance.modifiers,"arrow","keepTogether"))return e;var n=t.element;if("string"==typeof n){if(!(n=e.instance.popper.querySelector(n)))return e}else if(!e.instance.popper.contains(n))return e;var r=e.placement.split("-")[0],i=e.offsets,o=i.popper,a=i.reference,s=-1!==["left","right"].indexOf(r),l=s?"height":"width",u=s?"Top":"Left",c=u.toLowerCase(),t=s?"left":"top",i=s?"bottom":"right",r=E(n)[l];a[i]-r<o[c]&&(e.offsets.popper[c]-=o[c]-(a[i]-r)),a[c]+r>o[i]&&(e.offsets.popper[c]+=a[c]+r-o[i]),e.offsets.popper=y(e.offsets.popper);s=a[c]+a[l]/2-r/2,i=d(e.instance.popper),a=parseFloat(i["margin"+u]),u=parseFloat(i["border"+u+"Width"]),u=s-e.offsets.popper[c]-a-u,u=q(P(o[l]-r,u),0);return e.arrowElement=n,e.offsets.arrow=(I(n={},c,M(u)),I(n,t,""),n),e},element:"[x-arrow]"},flip:{order:600,enabled:!0,fn:function(u,c){if(e(u.instance.modifiers,"inner"))return u;if(u.flipped&&u.placement===u.originalPlacement)return u;var d=x(u.instance.popper,u.instance.reference,c.padding,c.boundariesElement,u.positionFixed),f=u.placement.split("-")[0],p=T(f),h=u.placement.split("-")[1]||"",m=[];switch(c.behavior){case K:m=[f,p];break;case J:m=O(f);break;case Z:m=O(f,!0);break;default:m=c.behavior}return m.forEach(function(e,t){if(f!==e||m.length===t+1)return u;f=u.placement.split("-")[0],p=T(f);var n=u.offsets.popper,r=u.offsets.reference,i=F,o="left"===f&&i(n.right)>i(r.left)||"right"===f&&i(n.left)<i(r.right)||"top"===f&&i(n.bottom)>i(r.top)||"bottom"===f&&i(n.top)<i(r.bottom),a=i(n.left)<i(d.left),s=i(n.right)>i(d.right),l=i(n.top)<i(d.top),e=i(n.bottom)>i(d.bottom),r="left"===f&&a||"right"===f&&s||"top"===f&&l||"bottom"===f&&e,n=-1!==["top","bottom"].indexOf(f),i=!!c.flipVariations&&(n&&"start"===h&&a||n&&"end"===h&&s||!n&&"start"===h&&l||!n&&"end"===h&&e),l=!!c.flipVariationsByContent&&(n&&"start"===h&&s||n&&"end"===h&&a||!n&&"start"===h&&e||!n&&"end"===h&&l),l=i||l;(o||r||l)&&(u.flipped=!0,(o||r)&&(f=m[t+1]),l&&(h="end"===(l=h)?"start":"start"===l?"end":l),u.placement=f+(h?"-"+h:""),u.offsets.popper=V({},u.offsets.popper,S(u.instance.popper,u.offsets.reference,u.placement)),u=C(u.instance.modifiers,u,"flip"))}),u},behavior:"flip",padding:5,boundariesElement:"viewport",flipVariations:!1,flipVariationsByContent:!1},inner:{order:700,enabled:!1,fn:function(e){var t=e.placement,n=t.split("-")[0],r=e.offsets,i=r.popper,o=r.reference,a=-1!==["left","right"].indexOf(n),r=-1===["top","left"].indexOf(n);return i[a?"left":"top"]=o[n]-(r?i[a?"width":"height"]:0),e.placement=T(t),e.offsets.popper=y(i),e}},hide:{order:800,enabled:!0,fn:function(e){if(!N(e.instance.modifiers,"hide","preventOverflow"))return e;var t=e.offsets.reference,n=k(e.instance.modifiers,function(e){return"preventOverflow"===e.name}).boundaries;if(t.bottom<n.top||t.left>n.right||t.top>n.bottom||t.right<n.left){if(!0===e.hide)return e;e.hide=!0,e.attributes["x-out-of-boundaries"]=""}else{if(!1===e.hide)return e;e.hide=!1,e.attributes["x-out-of-boundaries"]=!1}return e}},computeStyle:{order:850,enabled:!0,fn:function(e,t){var n,r,i,o,a=t.x,s=t.y,l=e.offsets.popper,u=k(e.instance.modifiers,function(e){return"applyStyle"===e.name}).gpuAcceleration,c=void 0===u?t.gpuAcceleration:u,d=m(e.instance.popper),f=b(d),p={position:l.position},t=(n=e,r=window.devicePixelRatio<2||!X,h=(o=n.offsets).popper,i=o.reference,t=function(e){return e},l=(u=M)(i.width),o=u(h.width),i=-1!==["left","right"].indexOf(n.placement),n=-1!==n.placement.indexOf("-"),i=r?i||n||l%2==o%2?u:F:t,t=r?u:t,{left:i(1==l%2&&1==o%2&&!n&&r?h.left-1:h.left),top:t(h.top),bottom:t(h.bottom),right:i(h.right)}),i="bottom"===a?"top":"bottom",h="right"===s?"left":"right",a=A("transform"),s="bottom"==i?"HTML"===d.nodeName?-d.clientHeight+t.bottom:-f.height+t.bottom:t.top,t="right"==h?"HTML"===d.nodeName?-d.clientWidth+t.right:-f.width+t.right:t.left;c&&a?(p[a]="translate3d("+t+"px, "+s+"px, 0)",p[i]=0,p[h]=0,p.willChange="transform"):(c="bottom"==i?-1:1,a="right"==h?-1:1,p[i]=s*c,p[h]=t*a,p.willChange=i+", "+h);var h={"x-placement":e.placement};return e.attributes=V({},h,e.attributes),e.styles=V({},p,e.styles),e.arrowStyles=V({},e.offsets.arrow,e.arrowStyles),e},gpuAcceleration:!0,x:"bottom",y:"right"},applyStyle:{order:900,enabled:!0,fn:function(e){return L(e.instance.popper,e.styles),t=e.instance.popper,n=e.attributes,Object.keys(n).forEach(function(e){!1===n[e]?t.removeAttribute(e):t.setAttribute(e,n[e])}),e.arrowElement&&Object.keys(e.arrowStyles).length&&L(e.arrowElement,e.arrowStyles),e;var t,n},onLoad:function(e,t,n,r,i){i=s(i,t,e,n.positionFixed),e=a(n.placement,i,t,e,n.modifiers.flip.boundariesElement,n.modifiers.flip.padding);return t.setAttribute("x-placement",e),L(t,{position:n.positionFixed?"fixed":"absolute"}),n},gpuAcceleration:void 0}}},z}),function(e,t){"object"==("undefined"==typeof exports?"undefined":_typeof(exports))&&"undefined"!=typeof module?t(exports,require("jquery"),require("popper.js")):"function"==typeof define&&define.amd?define(["exports","jquery","popper.js"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).bootstrap={},e.jQuery,e.Popper)}(this,function(e,t,n){"use strict";function r(e){return e&&"object"==_typeof(e)&&"default"in e?e:{default:e}}var c=r(t),i=r(n);function o(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function a(e,t,n){return t&&o(e.prototype,t),n&&o(e,n),e}function s(){return(s=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n,r=arguments[t];for(n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var d={TRANSITION_END:"bsTransitionEnd",getUID:function(e){for(;e+=~~(1e6*Math.random()),document.getElementById(e););return e},getSelectorFromElement:function(e){var t,n=e.getAttribute("data-target");n&&"#"!==n||(n=(t=e.getAttribute("href"))&&"#"!==t?t.trim():"");try{return document.querySelector(n)?n:null}catch(e){return null}},getTransitionDurationFromElement:function(e){if(!e)return 0;var t=c.default(e).css("transition-duration"),n=c.default(e).css("transition-delay"),r=parseFloat(t),e=parseFloat(n);return r||e?(t=t.split(",")[0],n=n.split(",")[0],1e3*(parseFloat(t)+parseFloat(n))):0},reflow:function(e){return e.offsetHeight},triggerTransitionEnd:function(e){c.default(e).trigger("transitionend")},supportsTransitionEnd:function(){return Boolean("transitionend")},isElement:function(e){return(e[0]||e).nodeType},typeCheckConfig:function(e,t,n){for(var r in n)if(Object.prototype.hasOwnProperty.call(n,r)){var i=n[r],o=t[r],o=o&&d.isElement(o)?"element":null===o||void 0===o?""+o:{}.toString.call(o).match(/\s([a-z]+)/i)[1].toLowerCase();if(!new RegExp(i).test(o))throw new Error(e.toUpperCase()+': Option "'+r+'" provided type "'+o+'" but expected type "'+i+'".')}},findShadowRoot:function(e){if(!document.documentElement.attachShadow)return null;if("function"!=typeof e.getRootNode)return e instanceof ShadowRoot?e:e.parentNode?d.findShadowRoot(e.parentNode):null;e=e.getRootNode();return e instanceof ShadowRoot?e:null},jQueryDetection:function(){if(void 0===c.default)throw new TypeError("Bootstrap's JavaScript requires jQuery. jQuery must be included before Bootstrap's JavaScript.");var e=c.default.fn.jquery.split(" ")[0].split(".");if(e[0]<2&&e[1]<9||1===e[0]&&9===e[1]&&e[2]<1||4<=e[0])throw new Error("Bootstrap's JavaScript requires at least jQuery v1.9.1 but less than v4.0.0")}};d.jQueryDetection(),c.default.fn.emulateTransitionEnd=function(e){var t=this,n=!1;return c.default(this).one(d.TRANSITION_END,function(){n=!0}),setTimeout(function(){n||d.triggerTransitionEnd(t)},e),this},c.default.event.special[d.TRANSITION_END]={bindType:"transitionend",delegateType:"transitionend",handle:function(e){if(c.default(e.target).is(this))return e.handleObj.handler.apply(this,arguments)}};var l="alert",u=c.default.fn[l],f=((n=p.prototype).close=function(e){var t=this._element;e&&(t=this._getRootElement(e)),this._triggerCloseEvent(t).isDefaultPrevented()||this._removeElement(t)},n.dispose=function(){c.default.removeData(this._element,"bs.alert"),this._element=null},n._getRootElement=function(e){var t=d.getSelectorFromElement(e),n=!1;return t&&(n=document.querySelector(t)),n=n||c.default(e).closest(".alert")[0]},n._triggerCloseEvent=function(e){var t=c.default.Event("close.bs.alert");return c.default(e).trigger(t),t},n._removeElement=function(t){var e,n=this;c.default(t).removeClass("show"),c.default(t).hasClass("fade")?(e=d.getTransitionDurationFromElement(t),c.default(t).one(d.TRANSITION_END,function(e){return n._destroyElement(t,e)}).emulateTransitionEnd(e)):this._destroyElement(t)},n._destroyElement=function(e){c.default(e).detach().trigger("closed.bs.alert").remove()},p._jQueryInterface=function(n){return this.each(function(){var e=c.default(this),t=e.data("bs.alert");t||(t=new p(this),e.data("bs.alert",t)),"close"===n&&t[n](this)})},p._handleDismiss=function(t){return function(e){e&&e.preventDefault(),t.close(this)}},a(p,null,[{key:"VERSION",get:function(){return"4.5.3"}}]),p);function p(e){this._element=e}c.default(document).on("click.bs.alert.data-api",'[data-dismiss="alert"]',f._handleDismiss(new f)),c.default.fn[l]=f._jQueryInterface,c.default.fn[l].Constructor=f,c.default.fn[l].noConflict=function(){return c.default.fn[l]=u,f._jQueryInterface};var h=c.default.fn.button,m=((n=g.prototype).toggle=function(){var e,t=!0,n=!0,r=c.default(this._element).closest('[data-toggle="buttons"]')[0];!r||(e=this._element.querySelector('input:not([type="hidden"])'))&&("radio"===e.type&&(e.checked&&this._element.classList.contains("active")?t=!1:(r=r.querySelector(".active"))&&c.default(r).removeClass("active")),t&&("checkbox"!==e.type&&"radio"!==e.type||(e.checked=!this._element.classList.contains("active")),this.shouldAvoidTriggerChange||c.default(e).trigger("change")),e.focus(),n=!1),this._element.hasAttribute("disabled")||this._element.classList.contains("disabled")||(n&&this._element.setAttribute("aria-pressed",!this._element.classList.contains("active")),t&&c.default(this._element).toggleClass("active"))},n.dispose=function(){c.default.removeData(this._element,"bs.button"),this._element=null},g._jQueryInterface=function(n,r){return this.each(function(){var e=c.default(this),t=e.data("bs.button");t||(t=new g(this),e.data("bs.button",t)),t.shouldAvoidTriggerChange=r,"toggle"===n&&t[n]()})},a(g,null,[{key:"VERSION",get:function(){return"4.5.3"}}]),g);function g(e){this._element=e,this.shouldAvoidTriggerChange=!1}c.default(document).on("click.bs.button.data-api",'[data-toggle^="button"]',function(e){var t,n=e.target,r=n;c.default(n).hasClass("btn")||(n=c.default(n).closest(".btn")[0]),!n||n.hasAttribute("disabled")||n.classList.contains("disabled")||(t=n.querySelector('input:not([type="hidden"])'))&&(t.hasAttribute("disabled")||t.classList.contains("disabled"))?e.preventDefault():"INPUT"!==r.tagName&&"LABEL"===n.tagName||m._jQueryInterface.call(c.default(n),"toggle","INPUT"===r.tagName)}).on("focus.bs.button.data-api blur.bs.button.data-api",'[data-toggle^="button"]',function(e){var t=c.default(e.target).closest(".btn")[0];c.default(t).toggleClass("focus",/^focus(in)?$/.test(e.type))}),c.default(window).on("load.bs.button.data-api",function(){for(var e=[].slice.call(document.querySelectorAll('[data-toggle="buttons"] .btn')),t=0,n=e.length;t<n;t++){var r=e[t],i=r.querySelector('input:not([type="hidden"])');i.checked||i.hasAttribute("checked")?r.classList.add("active"):r.classList.remove("active")}for(var o=0,a=(e=[].slice.call(document.querySelectorAll('[data-toggle="button"]'))).length;o<a;o++){var s=e[o];"true"===s.getAttribute("aria-pressed")?s.classList.add("active"):s.classList.remove("active")}}),c.default.fn.button=m._jQueryInterface,c.default.fn.button.Constructor=m,c.default.fn.button.noConflict=function(){return c.default.fn.button=h,m._jQueryInterface};var v="carousel",y=c.default.fn[v],b={interval:5e3,keyboard:!0,slide:!1,pause:"hover",wrap:!0,touch:!0},_={interval:"(number|boolean)",keyboard:"boolean",slide:"(boolean|string)",pause:"(string|boolean)",wrap:"boolean",touch:"boolean"},w={TOUCH:"touch",PEN:"pen"},x=((n=E.prototype).next=function(){this._isSliding||this._slide("next")},n.nextWhenVisible=function(){var e=c.default(this._element);!document.hidden&&e.is(":visible")&&"hidden"!==e.css("visibility")&&this.next()},n.prev=function(){this._isSliding||this._slide("prev")},n.pause=function(e){e||(this._isPaused=!0),this._element.querySelector(".carousel-item-next, .carousel-item-prev")&&(d.triggerTransitionEnd(this._element),this.cycle(!0)),clearInterval(this._interval),this._interval=null},n.cycle=function(e){e||(this._isPaused=!1),this._interval&&(clearInterval(this._interval),this._interval=null),this._config.interval&&!this._isPaused&&(this._interval=setInterval((document.visibilityState?this.nextWhenVisible:this.next).bind(this),this._config.interval))},n.to=function(e){var t=this;this._activeElement=this._element.querySelector(".active.carousel-item");var n=this._getItemIndex(this._activeElement);if(!(e>this._items.length-1||e<0))if(this._isSliding)c.default(this._element).one("slid.bs.carousel",function(){return t.to(e)});else{if(n===e)return this.pause(),void this.cycle();n=n<e?"next":"prev";this._slide(n,this._items[e])}},n.dispose=function(){c.default(this._element).off(".bs.carousel"),c.default.removeData(this._element,"bs.carousel"),this._items=null,this._config=null,this._element=null,this._interval=null,this._isPaused=null,this._isSliding=null,this._activeElement=null,this._indicatorsElement=null},n._getConfig=function(e){return e=s({},b,e),d.typeCheckConfig(v,e,_),e},n._handleSwipe=function(){var e=Math.abs(this.touchDeltaX);e<=40||(e=e/this.touchDeltaX,(this.touchDeltaX=0)<e&&this.prev(),e<0&&this.next())},n._addEventListeners=function(){var t=this;this._config.keyboard&&c.default(this._element).on("keydown.bs.carousel",function(e){return t._keydown(e)}),"hover"===this._config.pause&&c.default(this._element).on("mouseenter.bs.carousel",function(e){return t.pause(e)}).on("mouseleave.bs.carousel",function(e){return t.cycle(e)}),this._config.touch&&this._addTouchEventListeners()},n._addTouchEventListeners=function(){var e,t,n=this;this._touchSupported&&(e=function(e){n._pointerEvent&&w[e.originalEvent.pointerType.toUpperCase()]?n.touchStartX=e.originalEvent.clientX:n._pointerEvent||(n.touchStartX=e.originalEvent.touches[0].clientX)},t=function(e){n._pointerEvent&&w[e.originalEvent.pointerType.toUpperCase()]&&(n.touchDeltaX=e.originalEvent.clientX-n.touchStartX),n._handleSwipe(),"hover"===n._config.pause&&(n.pause(),n.touchTimeout&&clearTimeout(n.touchTimeout),n.touchTimeout=setTimeout(function(e){return n.cycle(e)},500+n._config.interval))},c.default(this._element.querySelectorAll(".carousel-item img")).on("dragstart.bs.carousel",function(e){return e.preventDefault()}),this._pointerEvent?(c.default(this._element).on("pointerdown.bs.carousel",e),c.default(this._element).on("pointerup.bs.carousel",t),this._element.classList.add("pointer-event")):(c.default(this._element).on("touchstart.bs.carousel",e),c.default(this._element).on("touchmove.bs.carousel",function(e){(e=e).originalEvent.touches&&1<e.originalEvent.touches.length?n.touchDeltaX=0:n.touchDeltaX=e.originalEvent.touches[0].clientX-n.touchStartX}),c.default(this._element).on("touchend.bs.carousel",t)))},n._keydown=function(e){if(!/input|textarea/i.test(e.target.tagName))switch(e.which){case 37:e.preventDefault(),this.prev();break;case 39:e.preventDefault(),this.next()}},n._getItemIndex=function(e){return this._items=e&&e.parentNode?[].slice.call(e.parentNode.querySelectorAll(".carousel-item")):[],this._items.indexOf(e)},n._getItemByDirection=function(e,t){var n="next"===e,r="prev"===e,i=this._getItemIndex(t),o=this._items.length-1;if((r&&0===i||n&&i===o)&&!this._config.wrap)return t;e=(i+("prev"===e?-1:1))%this._items.length;return-1==e?this._items[this._items.length-1]:this._items[e]},n._triggerSlideEvent=function(e,t){var n=this._getItemIndex(e),r=this._getItemIndex(this._element.querySelector(".active.carousel-item")),n=c.default.Event("slide.bs.carousel",{relatedTarget:e,direction:t,from:r,to:n});return c.default(this._element).trigger(n),n},n._setActiveIndicatorElement=function(e){var t;this._indicatorsElement&&(t=[].slice.call(this._indicatorsElement.querySelectorAll(".active")),c.default(t).removeClass("active"),(e=this._indicatorsElement.children[this._getItemIndex(e)])&&c.default(e).addClass("active"))},n._slide=function(e,t){var n,r,i,o=this,a=this._element.querySelector(".active.carousel-item"),s=this._getItemIndex(a),l=t||a&&this._getItemByDirection(e,a),u=this._getItemIndex(l),t=Boolean(this._interval),e="next"===e?(n="carousel-item-left",r="carousel-item-next","left"):(n="carousel-item-right",r="carousel-item-prev","right");l&&c.default(l).hasClass("active")?this._isSliding=!1:!this._triggerSlideEvent(l,e).isDefaultPrevented()&&a&&l&&(this._isSliding=!0,t&&this.pause(),this._setActiveIndicatorElement(l),i=c.default.Event("slid.bs.carousel",{relatedTarget:l,direction:e,from:s,to:u}),c.default(this._element).hasClass("slide")?(c.default(l).addClass(r),d.reflow(l),c.default(a).addClass(n),c.default(l).addClass(n),(u=parseInt(l.getAttribute("data-interval"),10))?(this._config.defaultInterval=this._config.defaultInterval||this._config.interval,this._config.interval=u):this._config.interval=this._config.defaultInterval||this._config.interval,u=d.getTransitionDurationFromElement(a),c.default(a).one(d.TRANSITION_END,function(){c.default(l).removeClass(n+" "+r).addClass("active"),c.default(a).removeClass("active "+r+" "+n),o._isSliding=!1,setTimeout(function(){return c.default(o._element).trigger(i)},0)}).emulateTransitionEnd(u)):(c.default(a).removeClass("active"),c.default(l).addClass("active"),this._isSliding=!1,c.default(this._element).trigger(i)),t&&this.cycle())},E._jQueryInterface=function(r){return this.each(function(){var e=c.default(this).data("bs.carousel"),t=s({},b,c.default(this).data());"object"==_typeof(r)&&(t=s({},t,r));var n="string"==typeof r?r:t.slide;if(e||(e=new E(this,t),c.default(this).data("bs.carousel",e)),"number"==typeof r)e.to(r);else if("string"==typeof n){if(void 0===e[n])throw new TypeError('No method named "'+n+'"');e[n]()}else t.interval&&t.ride&&(e.pause(),e.cycle())})},E._dataApiClickHandler=function(e){var t,n,r=d.getSelectorFromElement(this);!r||(t=c.default(r)[0])&&c.default(t).hasClass("carousel")&&(n=s({},c.default(t).data(),c.default(this).data()),(r=this.getAttribute("data-slide-to"))&&(n.interval=!1),E._jQueryInterface.call(c.default(t),n),r&&c.default(t).data("bs.carousel").to(r),e.preventDefault())},a(E,null,[{key:"VERSION",get:function(){return"4.5.3"}},{key:"Default",get:function(){return b}}]),E);function E(e,t){this._items=null,this._interval=null,this._activeElement=null,this._isPaused=!1,this._isSliding=!1,this.touchTimeout=null,this.touchStartX=0,this.touchDeltaX=0,this._config=this._getConfig(t),this._element=e,this._indicatorsElement=this._element.querySelector(".carousel-indicators"),this._touchSupported="ontouchstart"in document.documentElement||0<navigator.maxTouchPoints,this._pointerEvent=Boolean(window.PointerEvent||window.MSPointerEvent),this._addEventListeners()}c.default(document).on("click.bs.carousel.data-api","[data-slide], [data-slide-to]",x._dataApiClickHandler),c.default(window).on("load.bs.carousel.data-api",function(){for(var e=[].slice.call(document.querySelectorAll('[data-ride="carousel"]')),t=0,n=e.length;t<n;t++){var r=c.default(e[t]);x._jQueryInterface.call(r,r.data())}}),c.default.fn[v]=x._jQueryInterface,c.default.fn[v].Constructor=x,c.default.fn[v].noConflict=function(){return c.default.fn[v]=y,x._jQueryInterface};var T="collapse",S=c.default.fn[T],k={toggle:!0,parent:""},C={toggle:"boolean",parent:"(string|element)"},A=((n=D.prototype).toggle=function(){c.default(this._element).hasClass("show")?this.hide():this.show()},n.show=function(){var e,t,n,r,i=this;this._isTransitioning||c.default(this._element).hasClass("show")||(this._parent&&0===(r=[].slice.call(this._parent.querySelectorAll(".show, .collapsing")).filter(function(e){return"string"==typeof i._config.parent?e.getAttribute("data-parent")===i._config.parent:e.classList.contains("collapse")})).length&&(r=null),r&&(n=c.default(r).not(this._selector).data("bs.collapse"))&&n._isTransitioning)||(e=c.default.Event("show.bs.collapse"),c.default(this._element).trigger(e),e.isDefaultPrevented()||(r&&(D._jQueryInterface.call(c.default(r).not(this._selector),"hide"),n||c.default(r).data("bs.collapse",null)),t=this._getDimension(),c.default(this._element).removeClass("collapse").addClass("collapsing"),this._element.style[t]=0,this._triggerArray.length&&c.default(this._triggerArray).removeClass("collapsed").attr("aria-expanded",!0),this.setTransitioning(!0),n="scroll"+(t[0].toUpperCase()+t.slice(1)),r=d.getTransitionDurationFromElement(this._element),c.default(this._element).one(d.TRANSITION_END,function(){c.default(i._element).removeClass("collapsing").addClass("collapse show"),i._element.style[t]="",i.setTransitioning(!1),c.default(i._element).trigger("shown.bs.collapse")}).emulateTransitionEnd(r),this._element.style[t]=this._element[n]+"px"))},n.hide=function(){var e=this;if(!this._isTransitioning&&c.default(this._element).hasClass("show")){var t=c.default.Event("hide.bs.collapse");if(c.default(this._element).trigger(t),!t.isDefaultPrevented()){t=this._getDimension();this._element.style[t]=this._element.getBoundingClientRect()[t]+"px",d.reflow(this._element),c.default(this._element).addClass("collapsing").removeClass("collapse show");var n=this._triggerArray.length;if(0<n)for(var r=0;r<n;r++){var i=this._triggerArray[r],o=d.getSelectorFromElement(i);null!==o&&(c.default([].slice.call(document.querySelectorAll(o))).hasClass("show")||c.default(i).addClass("collapsed").attr("aria-expanded",!1))}this.setTransitioning(!0),this._element.style[t]="";t=d.getTransitionDurationFromElement(this._element);c.default(this._element).one(d.TRANSITION_END,function(){e.setTransitioning(!1),c.default(e._element).removeClass("collapsing").addClass("collapse").trigger("hidden.bs.collapse")}).emulateTransitionEnd(t)}}},n.setTransitioning=function(e){this._isTransitioning=e},n.dispose=function(){c.default.removeData(this._element,"bs.collapse"),this._config=null,this._parent=null,this._element=null,this._triggerArray=null,this._isTransitioning=null},n._getConfig=function(e){return(e=s({},k,e)).toggle=Boolean(e.toggle),d.typeCheckConfig(T,e,C),e},n._getDimension=function(){return c.default(this._element).hasClass("width")?"width":"height"},n._getParent=function(){var e,n=this;d.isElement(this._config.parent)?(e=this._config.parent,void 0!==this._config.parent.jquery&&(e=this._config.parent[0])):e=document.querySelector(this._config.parent);var t='[data-toggle="collapse"][data-parent="'+this._config.parent+'"]',t=[].slice.call(e.querySelectorAll(t));return c.default(t).each(function(e,t){n._addAriaAndCollapsedClass(D._getTargetFromElement(t),[t])}),e},n._addAriaAndCollapsedClass=function(e,t){e=c.default(e).hasClass("show");t.length&&c.default(t).toggleClass("collapsed",!e).attr("aria-expanded",e)},D._getTargetFromElement=function(e){e=d.getSelectorFromElement(e);return e?document.querySelector(e):null},D._jQueryInterface=function(r){return this.each(function(){var e=c.default(this),t=e.data("bs.collapse"),n=s({},k,e.data(),"object"==_typeof(r)&&r?r:{});if(!t&&n.toggle&&"string"==typeof r&&/show|hide/.test(r)&&(n.toggle=!1),t||(t=new D(this,n),e.data("bs.collapse",t)),"string"==typeof r){if(void 0===t[r])throw new TypeError('No method named "'+r+'"');t[r]()}})},a(D,null,[{key:"VERSION",get:function(){return"4.5.3"}},{key:"Default",get:function(){return k}}]),D);function D(t,e){this._isTransitioning=!1,this._element=t,this._config=this._getConfig(e),this._triggerArray=[].slice.call(document.querySelectorAll('[data-toggle="collapse"][href="#'+t.id+'"],[data-toggle="collapse"][data-target="#'+t.id+'"]'));for(var n=[].slice.call(document.querySelectorAll('[data-toggle="collapse"]')),r=0,i=n.length;r<i;r++){var o=n[r],a=d.getSelectorFromElement(o),s=[].slice.call(document.querySelectorAll(a)).filter(function(e){return e===t});null!==a&&0<s.length&&(this._selector=a,this._triggerArray.push(o))}this._parent=this._config.parent?this._getParent():null,this._config.parent||this._addAriaAndCollapsedClass(this._element,this._triggerArray),this._config.toggle&&this.toggle()}c.default(document).on("click.bs.collapse.data-api",'[data-toggle="collapse"]',function(e){"A"===e.currentTarget.tagName&&e.preventDefault();var n=c.default(this),e=d.getSelectorFromElement(this),e=[].slice.call(document.querySelectorAll(e));c.default(e).each(function(){var e=c.default(this),t=e.data("bs.collapse")?"toggle":n.data();A._jQueryInterface.call(e,t)})}),c.default.fn[T]=A._jQueryInterface,c.default.fn[T].Constructor=A,c.default.fn[T].noConflict=function(){return c.default.fn[T]=S,A._jQueryInterface};var R="dropdown",L=c.default.fn[R],N=new RegExp("38|40|27"),O={offset:0,flip:!0,boundary:"scrollParent",reference:"toggle",display:"dynamic",popperConfig:null},j={offset:"(number|string|function)",flip:"boolean",boundary:"(string|element)",reference:"(string|element)",display:"string",popperConfig:"(null|object)"},I=((n=P.prototype).toggle=function(){var e;this._element.disabled||c.default(this._element).hasClass("disabled")||(e=c.default(this._menu).hasClass("show"),P._clearMenus(),e||this.show(!0))},n.show=function(e){if(void 0===e&&(e=!1),!(this._element.disabled||c.default(this._element).hasClass("disabled")||c.default(this._menu).hasClass("show"))){var t={relatedTarget:this._element},n=c.default.Event("show.bs.dropdown",t),r=P._getParentFromElement(this._element);if(c.default(r).trigger(n),!n.isDefaultPrevented()){if(!this._inNavbar&&e){if(void 0===i.default)throw new TypeError("Bootstrap's dropdowns require Popper.js (https://popper.js.org/)");e=this._element;"parent"===this._config.reference?e=r:d.isElement(this._config.reference)&&(e=this._config.reference,void 0!==this._config.reference.jquery&&(e=this._config.reference[0])),"scrollParent"!==this._config.boundary&&c.default(r).addClass("position-static"),this._popper=new i.default(e,this._menu,this._getPopperConfig())}"ontouchstart"in document.documentElement&&0===c.default(r).closest(".navbar-nav").length&&c.default(document.body).children().on("mouseover",null,c.default.noop),this._element.focus(),this._element.setAttribute("aria-expanded",!0),c.default(this._menu).toggleClass("show"),c.default(r).toggleClass("show").trigger(c.default.Event("shown.bs.dropdown",t))}}},n.hide=function(){var e,t,n;this._element.disabled||c.default(this._element).hasClass("disabled")||!c.default(this._menu).hasClass("show")||(e={relatedTarget:this._element},t=c.default.Event("hide.bs.dropdown",e),n=P._getParentFromElement(this._element),c.default(n).trigger(t),t.isDefaultPrevented()||(this._popper&&this._popper.destroy(),c.default(this._menu).toggleClass("show"),c.default(n).toggleClass("show").trigger(c.default.Event("hidden.bs.dropdown",e))))},n.dispose=function(){c.default.removeData(this._element,"bs.dropdown"),c.default(this._element).off(".bs.dropdown"),this._element=null,(this._menu=null)!==this._popper&&(this._popper.destroy(),this._popper=null)},n.update=function(){this._inNavbar=this._detectNavbar(),null!==this._popper&&this._popper.scheduleUpdate()},n._addEventListeners=function(){var t=this;c.default(this._element).on("click.bs.dropdown",function(e){e.preventDefault(),e.stopPropagation(),t.toggle()})},n._getConfig=function(e){return e=s({},this.constructor.Default,c.default(this._element).data(),e),d.typeCheckConfig(R,e,this.constructor.DefaultType),e},n._getMenuElement=function(){var e;return this._menu||(e=P._getParentFromElement(this._element))&&(this._menu=e.querySelector(".dropdown-menu")),this._menu},n._getPlacement=function(){var e=c.default(this._element.parentNode),t="bottom-start";return e.hasClass("dropup")?t=c.default(this._menu).hasClass("dropdown-menu-right")?"top-end":"top-start":e.hasClass("dropright")?t="right-start":e.hasClass("dropleft")?t="left-start":c.default(this._menu).hasClass("dropdown-menu-right")&&(t="bottom-end"),t},n._detectNavbar=function(){return 0<c.default(this._element).closest(".navbar").length},n._getOffset=function(){var t=this,e={};return"function"==typeof this._config.offset?e.fn=function(e){return e.offsets=s({},e.offsets,t._config.offset(e.offsets,t._element)||{}),e}:e.offset=this._config.offset,e},n._getPopperConfig=function(){var e={placement:this._getPlacement(),modifiers:{offset:this._getOffset(),flip:{enabled:this._config.flip},preventOverflow:{boundariesElement:this._config.boundary}}};return"static"===this._config.display&&(e.modifiers.applyStyle={enabled:!1}),s({},e,this._config.popperConfig)},P._jQueryInterface=function(t){return this.each(function(){var e=c.default(this).data("bs.dropdown");if(e||(e=new P(this,"object"==_typeof(t)?t:null),c.default(this).data("bs.dropdown",e)),"string"==typeof t){if(void 0===e[t])throw new TypeError('No method named "'+t+'"');e[t]()}})},P._clearMenus=function(e){if(!e||3!==e.which&&("keyup"!==e.type||9===e.which))for(var t=[].slice.call(document.querySelectorAll('[data-toggle="dropdown"]')),n=0,r=t.length;n<r;n++){var i,o,a=P._getParentFromElement(t[n]),s=c.default(t[n]).data("bs.dropdown"),l={relatedTarget:t[n]};e&&"click"===e.type&&(l.clickEvent=e),s&&(i=s._menu,!c.default(a).hasClass("show")||e&&("click"===e.type&&/input|textarea/i.test(e.target.tagName)||"keyup"===e.type&&9===e.which)&&c.default.contains(a,e.target)||(o=c.default.Event("hide.bs.dropdown",l),c.default(a).trigger(o),o.isDefaultPrevented()||("ontouchstart"in document.documentElement&&c.default(document.body).children().off("mouseover",null,c.default.noop),t[n].setAttribute("aria-expanded","false"),s._popper&&s._popper.destroy(),c.default(i).removeClass("show"),c.default(a).removeClass("show").trigger(c.default.Event("hidden.bs.dropdown",l)))))}},P._getParentFromElement=function(e){var t,n=d.getSelectorFromElement(e);return n&&(t=document.querySelector(n)),t||e.parentNode},P._dataApiKeydownHandler=function(e){if(!(/input|textarea/i.test(e.target.tagName)?32===e.which||27!==e.which&&(40!==e.which&&38!==e.which||c.default(e.target).closest(".dropdown-menu").length):!N.test(e.which))&&!this.disabled&&!c.default(this).hasClass("disabled")){var t=P._getParentFromElement(this),n=c.default(t).hasClass("show");if(n||27!==e.which){if(e.preventDefault(),e.stopPropagation(),!n||27===e.which||32===e.which)return 27===e.which&&c.default(t.querySelector('[data-toggle="dropdown"]')).trigger("focus"),void c.default(this).trigger("click");n=[].slice.call(t.querySelectorAll(".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)")).filter(function(e){return c.default(e).is(":visible")});0!==n.length&&(t=n.indexOf(e.target),38===e.which&&0<t&&t--,40===e.which&&t<n.length-1&&t++,t<0&&(t=0),n[t].focus())}}},a(P,null,[{key:"VERSION",get:function(){return"4.5.3"}},{key:"Default",get:function(){return O}},{key:"DefaultType",get:function(){return j}}]),P);function P(e,t){this._element=e,this._popper=null,this._config=this._getConfig(t),this._menu=this._getMenuElement(),this._inNavbar=this._detectNavbar(),this._addEventListeners()}c.default(document).on("keydown.bs.dropdown.data-api",'[data-toggle="dropdown"]',I._dataApiKeydownHandler).on("keydown.bs.dropdown.data-api",".dropdown-menu",I._dataApiKeydownHandler).on("click.bs.dropdown.data-api keyup.bs.dropdown.data-api",I._clearMenus).on("click.bs.dropdown.data-api",'[data-toggle="dropdown"]',function(e){e.preventDefault(),e.stopPropagation(),I._jQueryInterface.call(c.default(this),"toggle")}).on("click.bs.dropdown.data-api",".dropdown form",function(e){e.stopPropagation()}),c.default.fn[R]=I._jQueryInterface,c.default.fn[R].Constructor=I,c.default.fn[R].noConflict=function(){return c.default.fn[R]=L,I._jQueryInterface};var F=c.default.fn.modal,M={backdrop:!0,keyboard:!0,focus:!0,show:!0},q={backdrop:"(boolean|string)",keyboard:"boolean",focus:"boolean",show:"boolean"},H=((n=B.prototype).toggle=function(e){return this._isShown?this.hide():this.show(e)},n.show=function(e){var t,n=this;this._isShown||this._isTransitioning||(c.default(this._element).hasClass("fade")&&(this._isTransitioning=!0),t=c.default.Event("show.bs.modal",{relatedTarget:e}),c.default(this._element).trigger(t),this._isShown||t.isDefaultPrevented()||(this._isShown=!0,this._checkScrollbar(),this._setScrollbar(),this._adjustDialog(),this._setEscapeEvent(),this._setResizeEvent(),c.default(this._element).on("click.dismiss.bs.modal",'[data-dismiss="modal"]',function(e){return n.hide(e)}),c.default(this._dialog).on("mousedown.dismiss.bs.modal",function(){c.default(n._element).one("mouseup.dismiss.bs.modal",function(e){c.default(e.target).is(n._element)&&(n._ignoreBackdropClick=!0)})}),this._showBackdrop(function(){return n._showElement(e)})))},n.hide=function(e){var t=this;e&&e.preventDefault(),this._isShown&&!this._isTransitioning&&(e=c.default.Event("hide.bs.modal"),c.default(this._element).trigger(e),this._isShown&&!e.isDefaultPrevented()&&(this._isShown=!1,(e=c.default(this._element).hasClass("fade"))&&(this._isTransitioning=!0),this._setEscapeEvent(),this._setResizeEvent(),c.default(document).off("focusin.bs.modal"),c.default(this._element).removeClass("show"),c.default(this._element).off("click.dismiss.bs.modal"),c.default(this._dialog).off("mousedown.dismiss.bs.modal"),e?(e=d.getTransitionDurationFromElement(this._element),c.default(this._element).one(d.TRANSITION_END,function(e){return t._hideModal(e)}).emulateTransitionEnd(e)):this._hideModal()))},n.dispose=function(){[window,this._element,this._dialog].forEach(function(e){return c.default(e).off(".bs.modal")}),c.default(document).off("focusin.bs.modal"),c.default.removeData(this._element,"bs.modal"),this._config=null,this._element=null,this._dialog=null,this._backdrop=null,this._isShown=null,this._isBodyOverflowing=null,this._ignoreBackdropClick=null,this._isTransitioning=null,this._scrollbarWidth=null},n.handleUpdate=function(){this._adjustDialog()},n._getConfig=function(e){return e=s({},M,e),d.typeCheckConfig("modal",e,q),e},n._triggerBackdropTransition=function(){var e,t,n,r=this;"static"===this._config.backdrop?(e=c.default.Event("hidePrevented.bs.modal"),c.default(this._element).trigger(e),e.isDefaultPrevented()||((t=this._element.scrollHeight>document.documentElement.clientHeight)||(this._element.style.overflowY="hidden"),this._element.classList.add("modal-static"),n=d.getTransitionDurationFromElement(this._dialog),c.default(this._element).off(d.TRANSITION_END),c.default(this._element).one(d.TRANSITION_END,function(){r._element.classList.remove("modal-static"),t||c.default(r._element).one(d.TRANSITION_END,function(){r._element.style.overflowY=""}).emulateTransitionEnd(r._element,n)}).emulateTransitionEnd(n),this._element.focus())):this.hide()},n._showElement=function(e){var t=this,n=c.default(this._element).hasClass("fade"),r=this._dialog?this._dialog.querySelector(".modal-body"):null;this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE||document.body.appendChild(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),c.default(this._dialog).hasClass("modal-dialog-scrollable")&&r?r.scrollTop=0:this._element.scrollTop=0,n&&d.reflow(this._element),c.default(this._element).addClass("show"),this._config.focus&&this._enforceFocus();var i=c.default.Event("shown.bs.modal",{relatedTarget:e}),e=function(){t._config.focus&&t._element.focus(),t._isTransitioning=!1,c.default(t._element).trigger(i)};n?(n=d.getTransitionDurationFromElement(this._dialog),c.default(this._dialog).one(d.TRANSITION_END,e).emulateTransitionEnd(n)):e()},n._enforceFocus=function(){var t=this;c.default(document).off("focusin.bs.modal").on("focusin.bs.modal",function(e){document!==e.target&&t._element!==e.target&&0===c.default(t._element).has(e.target).length&&t._element.focus()})},n._setEscapeEvent=function(){var t=this;this._isShown?c.default(this._element).on("keydown.dismiss.bs.modal",function(e){t._config.keyboard&&27===e.which?(e.preventDefault(),t.hide()):t._config.keyboard||27!==e.which||t._triggerBackdropTransition()}):this._isShown||c.default(this._element).off("keydown.dismiss.bs.modal")},n._setResizeEvent=function(){var t=this;this._isShown?c.default(window).on("resize.bs.modal",function(e){return t.handleUpdate(e)}):c.default(window).off("resize.bs.modal")},n._hideModal=function(){var e=this;this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._showBackdrop(function(){c.default(document.body).removeClass("modal-open"),e._resetAdjustments(),e._resetScrollbar(),c.default(e._element).trigger("hidden.bs.modal")})},n._removeBackdrop=function(){this._backdrop&&(c.default(this._backdrop).remove(),this._backdrop=null)},n._showBackdrop=function(e){var t,n=this,r=c.default(this._element).hasClass("fade")?"fade":"";this._isShown&&this._config.backdrop?(this._backdrop=document.createElement("div"),this._backdrop.className="modal-backdrop",r&&this._backdrop.classList.add(r),c.default(this._backdrop).appendTo(document.body),c.default(this._element).on("click.dismiss.bs.modal",function(e){n._ignoreBackdropClick?n._ignoreBackdropClick=!1:e.target===e.currentTarget&&n._triggerBackdropTransition()}),r&&d.reflow(this._backdrop),c.default(this._backdrop).addClass("show"),e&&(r?(t=d.getTransitionDurationFromElement(this._backdrop),c.default(this._backdrop).one(d.TRANSITION_END,e).emulateTransitionEnd(t)):e())):!this._isShown&&this._backdrop?(c.default(this._backdrop).removeClass("show"),r=function(){n._removeBackdrop(),e&&e()},c.default(this._element).hasClass("fade")?(t=d.getTransitionDurationFromElement(this._backdrop),c.default(this._backdrop).one(d.TRANSITION_END,r).emulateTransitionEnd(t)):r()):e&&e()},n._adjustDialog=function(){var e=this._element.scrollHeight>document.documentElement.clientHeight;!this._isBodyOverflowing&&e&&(this._element.style.paddingLeft=this._scrollbarWidth+"px"),this._isBodyOverflowing&&!e&&(this._element.style.paddingRight=this._scrollbarWidth+"px")},n._resetAdjustments=function(){this._element.style.paddingLeft="",this._element.style.paddingRight=""},n._checkScrollbar=function(){var e=document.body.getBoundingClientRect();this._isBodyOverflowing=Math.round(e.left+e.right)<window.innerWidth,this._scrollbarWidth=this._getScrollbarWidth()},n._setScrollbar=function(){var e,t,i=this;this._isBodyOverflowing&&(e=[].slice.call(document.querySelectorAll(".fixed-top, .fixed-bottom, .is-fixed, .sticky-top")),t=[].slice.call(document.querySelectorAll(".sticky-top")),c.default(e).each(function(e,t){var n=t.style.paddingRight,r=c.default(t).css("padding-right");c.default(t).data("padding-right",n).css("padding-right",parseFloat(r)+i._scrollbarWidth+"px")}),c.default(t).each(function(e,t){var n=t.style.marginRight,r=c.default(t).css("margin-right");c.default(t).data("margin-right",n).css("margin-right",parseFloat(r)-i._scrollbarWidth+"px")}),e=document.body.style.paddingRight,t=c.default(document.body).css("padding-right"),c.default(document.body).data("padding-right",e).css("padding-right",parseFloat(t)+this._scrollbarWidth+"px")),c.default(document.body).addClass("modal-open")},n._resetScrollbar=function(){var e=[].slice.call(document.querySelectorAll(".fixed-top, .fixed-bottom, .is-fixed, .sticky-top"));c.default(e).each(function(e,t){var n=c.default(t).data("padding-right");c.default(t).removeData("padding-right"),t.style.paddingRight=n||""});e=[].slice.call(document.querySelectorAll(".sticky-top"));c.default(e).each(function(e,t){var n=c.default(t).data("margin-right");void 0!==n&&c.default(t).css("margin-right",n).removeData("margin-right")});e=c.default(document.body).data("padding-right");c.default(document.body).removeData("padding-right"),document.body.style.paddingRight=e||""},n._getScrollbarWidth=function(){var e=document.createElement("div");e.className="modal-scrollbar-measure",document.body.appendChild(e);var t=e.getBoundingClientRect().width-e.clientWidth;return document.body.removeChild(e),t},B._jQueryInterface=function(n,r){return this.each(function(){var e=c.default(this).data("bs.modal"),t=s({},M,c.default(this).data(),"object"==_typeof(n)&&n?n:{});if(e||(e=new B(this,t),c.default(this).data("bs.modal",e)),"string"==typeof n){if(void 0===e[n])throw new TypeError('No method named "'+n+'"');e[n](r)}else t.show&&e.show(r)})},a(B,null,[{key:"VERSION",get:function(){return"4.5.3"}},{key:"Default",get:function(){return M}}]),B);function B(e,t){this._config=this._getConfig(t),this._element=e,this._dialog=e.querySelector(".modal-dialog"),this._backdrop=null,this._isShown=!1,this._isBodyOverflowing=!1,this._ignoreBackdropClick=!1,this._isTransitioning=!1,this._scrollbarWidth=0}c.default(document).on("click.bs.modal.data-api",'[data-toggle="modal"]',function(e){var t,n=this,r=d.getSelectorFromElement(this);r&&(t=document.querySelector(r));r=c.default(t).data("bs.modal")?"toggle":s({},c.default(t).data(),c.default(this).data());"A"!==this.tagName&&"AREA"!==this.tagName||e.preventDefault();var i=c.default(t).one("show.bs.modal",function(e){e.isDefaultPrevented()||i.one("hidden.bs.modal",function(){c.default(n).is(":visible")&&n.focus()})});H._jQueryInterface.call(c.default(t),r,this)}),c.default.fn.modal=H._jQueryInterface,c.default.fn.modal.Constructor=H,c.default.fn.modal.noConflict=function(){return c.default.fn.modal=F,H._jQueryInterface};var U=["background","cite","href","itemtype","longdesc","poster","src","xlink:href"],G=/^(?:(?:https?|mailto|ftp|tel|file):|[^#&/:?]*(?:[#/?]|$))/gi,W=/^data:(?:image\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\/(?:mpeg|mp4|ogg|webm)|audio\/(?:mp3|oga|ogg|opus));base64,[\d+/a-z]+=*$/i;function $(e,i,t){if(0===e.length)return e;if(t&&"function"==typeof t)return t(e);for(var e=(new window.DOMParser).parseFromString(e,"text/html"),o=Object.keys(i),a=[].slice.call(e.body.querySelectorAll("*")),n=0,r=a.length;n<r;n++)!function(e){var t=a[e],n=t.nodeName.toLowerCase();if(-1===o.indexOf(t.nodeName.toLowerCase()))return t.parentNode.removeChild(t);var e=[].slice.call(t.attributes),r=[].concat(i["*"]||[],i[n]||[]);e.forEach(function(e){!function(e,t){var n=e.nodeName.toLowerCase();if(-1!==t.indexOf(n))return-1===U.indexOf(n)||Boolean(e.nodeValue.match(G)||e.nodeValue.match(W));for(var r=t.filter(function(e){return e instanceof RegExp}),i=0,o=r.length;i<o;i++)if(n.match(r[i]))return 1}(e,r)&&t.removeAttribute(e.nodeName)})}(n);return e.body.innerHTML}var z="tooltip",V=c.default.fn[z],X=new RegExp("(^|\\s)bs-tooltip\\S+","g"),Q=["sanitize","whiteList","sanitizeFn"],Y={animation:"boolean",template:"string",title:"(string|element|function)",trigger:"string",delay:"(number|object)",html:"boolean",selector:"(string|boolean)",placement:"(string|function)",offset:"(number|string|function)",container:"(string|element|boolean)",fallbackPlacement:"(string|array)",boundary:"(string|element)",sanitize:"boolean",sanitizeFn:"(null|function)",whiteList:"object",popperConfig:"(null|object)"},K={AUTO:"auto",TOP:"top",RIGHT:"right",BOTTOM:"bottom",LEFT:"left"},J={animation:!0,template:'<div class="tooltip" role="tooltip"><div class="arrow"></div><div class="tooltip-inner"></div></div>',trigger:"hover focus",title:"",delay:0,html:!1,selector:!1,placement:"top",offset:0,container:!1,fallbackPlacement:"flip",boundary:"scrollParent",sanitize:!0,sanitizeFn:null,whiteList:{"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],div:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},popperConfig:null},Z={HIDE:"hide.bs.tooltip",HIDDEN:"hidden.bs.tooltip",SHOW:"show.bs.tooltip",SHOWN:"shown.bs.tooltip",INSERTED:"inserted.bs.tooltip",CLICK:"click.bs.tooltip",FOCUSIN:"focusin.bs.tooltip",FOCUSOUT:"focusout.bs.tooltip",MOUSEENTER:"mouseenter.bs.tooltip",MOUSELEAVE:"mouseleave.bs.tooltip"},ee=((n=te.prototype).enable=function(){this._isEnabled=!0},n.disable=function(){this._isEnabled=!1},n.toggleEnabled=function(){this._isEnabled=!this._isEnabled},n.toggle=function(e){var t,n;this._isEnabled&&(e?(t=this.constructor.DATA_KEY,(n=c.default(e.currentTarget).data(t))||(n=new this.constructor(e.currentTarget,this._getDelegateConfig()),c.default(e.currentTarget).data(t,n)),n._activeTrigger.click=!n._activeTrigger.click,n._isWithActiveTrigger()?n._enter(null,n):n._leave(null,n)):c.default(this.getTipElement()).hasClass("show")?this._leave(null,this):this._enter(null,this))},n.dispose=function(){clearTimeout(this._timeout),c.default.removeData(this.element,this.constructor.DATA_KEY),c.default(this.element).off(this.constructor.EVENT_KEY),c.default(this.element).closest(".modal").off("hide.bs.modal",this._hideModalHandler),this.tip&&c.default(this.tip).remove(),this._isEnabled=null,this._timeout=null,this._hoverState=null,this._activeTrigger=null,this._popper&&this._popper.destroy(),this._popper=null,this.element=null,this.config=null,this.tip=null},n.show=function(){var t=this;if("none"===c.default(this.element).css("display"))throw new Error("Please use show on visible elements");var e,n,r=c.default.Event(this.constructor.Event.SHOW);this.isWithContent()&&this._isEnabled&&(c.default(this.element).trigger(r),n=d.findShadowRoot(this.element),e=c.default.contains(null!==n?n:this.element.ownerDocument.documentElement,this.element),!r.isDefaultPrevented()&&e&&(n=this.getTipElement(),r=d.getUID(this.constructor.NAME),n.setAttribute("id",r),this.element.setAttribute("aria-describedby",r),this.setContent(),this.config.animation&&c.default(n).addClass("fade"),e="function"==typeof this.config.placement?this.config.placement.call(this,n,this.element):this.config.placement,r=this._getAttachment(e),this.addAttachmentClass(r),e=this._getContainer(),c.default(n).data(this.constructor.DATA_KEY,this),c.default.contains(this.element.ownerDocument.documentElement,this.tip)||c.default(n).appendTo(e),c.default(this.element).trigger(this.constructor.Event.INSERTED),this._popper=new i.default(this.element,n,this._getPopperConfig(r)),c.default(n).addClass("show"),"ontouchstart"in document.documentElement&&c.default(document.body).children().on("mouseover",null,c.default.noop),r=function(){t.config.animation&&t._fixTransition();var e=t._hoverState;t._hoverState=null,c.default(t.element).trigger(t.constructor.Event.SHOWN),"out"===e&&t._leave(null,t)},c.default(this.tip).hasClass("fade")?(n=d.getTransitionDurationFromElement(this.tip),c.default(this.tip).one(d.TRANSITION_END,r).emulateTransitionEnd(n)):r()))},n.hide=function(e){function t(){"show"!==n._hoverState&&r.parentNode&&r.parentNode.removeChild(r),n._cleanTipClass(),n.element.removeAttribute("aria-describedby"),c.default(n.element).trigger(n.constructor.Event.HIDDEN),null!==n._popper&&n._popper.destroy(),e&&e()}var n=this,r=this.getTipElement(),i=c.default.Event(this.constructor.Event.HIDE);c.default(this.element).trigger(i),i.isDefaultPrevented()||(c.default(r).removeClass("show"),"ontouchstart"in document.documentElement&&c.default(document.body).children().off("mouseover",null,c.default.noop),this._activeTrigger.click=!1,this._activeTrigger.focus=!1,this._activeTrigger.hover=!1,c.default(this.tip).hasClass("fade")?(i=d.getTransitionDurationFromElement(r),c.default(r).one(d.TRANSITION_END,t).emulateTransitionEnd(i)):t(),this._hoverState="")},n.update=function(){null!==this._popper&&this._popper.scheduleUpdate()},n.isWithContent=function(){return Boolean(this.getTitle())},n.addAttachmentClass=function(e){c.default(this.getTipElement()).addClass("bs-tooltip-"+e)},n.getTipElement=function(){return this.tip=this.tip||c.default(this.config.template)[0],this.tip},n.setContent=function(){var e=this.getTipElement();this.setElementContent(c.default(e.querySelectorAll(".tooltip-inner")),this.getTitle()),c.default(e).removeClass("fade show")},n.setElementContent=function(e,t){"object"!=_typeof(t)||!t.nodeType&&!t.jquery?this.config.html?(this.config.sanitize&&(t=$(t,this.config.whiteList,this.config.sanitizeFn)),e.html(t)):e.text(t):this.config.html?c.default(t).parent().is(e)||e.empty().append(t):e.text(c.default(t).text())},n.getTitle=function(){return this.element.getAttribute("data-original-title")||("function"==typeof this.config.title?this.config.title.call(this.element):this.config.title)},n._getPopperConfig=function(e){var t=this;return s({},{placement:e,modifiers:{offset:this._getOffset(),flip:{behavior:this.config.fallbackPlacement},arrow:{element:".arrow"},preventOverflow:{boundariesElement:this.config.boundary}},onCreate:function(e){e.originalPlacement!==e.placement&&t._handlePopperPlacementChange(e)},onUpdate:function(e){return t._handlePopperPlacementChange(e)}},this.config.popperConfig)},n._getOffset=function(){var t=this,e={};return"function"==typeof this.config.offset?e.fn=function(e){return e.offsets=s({},e.offsets,t.config.offset(e.offsets,t.element)||{}),e}:e.offset=this.config.offset,e},n._getContainer=function(){return!1===this.config.container?document.body:d.isElement(this.config.container)?c.default(this.config.container):c.default(document).find(this.config.container)},n._getAttachment=function(e){return K[e.toUpperCase()]},n._setListeners=function(){var n=this;this.config.trigger.split(" ").forEach(function(e){var t;"click"===e?c.default(n.element).on(n.constructor.Event.CLICK,n.config.selector,function(e){return n.toggle(e)}):"manual"!==e&&(t="hover"===e?n.constructor.Event.MOUSEENTER:n.constructor.Event.FOCUSIN,e="hover"===e?n.constructor.Event.MOUSELEAVE:n.constructor.Event.FOCUSOUT,c.default(n.element).on(t,n.config.selector,function(e){return n._enter(e)}).on(e,n.config.selector,function(e){return n._leave(e)}))}),this._hideModalHandler=function(){n.element&&n.hide()},c.default(this.element).closest(".modal").on("hide.bs.modal",this._hideModalHandler),this.config.selector?this.config=s({},this.config,{trigger:"manual",selector:""}):this._fixTitle()},n._fixTitle=function(){var e=_typeof(this.element.getAttribute("data-original-title"));!this.element.getAttribute("title")&&"string"===e||(this.element.setAttribute("data-original-title",this.element.getAttribute("title")||""),this.element.setAttribute("title",""))},n._enter=function(e,t){var n=this.constructor.DATA_KEY;(t=t||c.default(e.currentTarget).data(n))||(t=new this.constructor(e.currentTarget,this._getDelegateConfig()),c.default(e.currentTarget).data(n,t)),e&&(t._activeTrigger["focusin"===e.type?"focus":"hover"]=!0),c.default(t.getTipElement()).hasClass("show")||"show"===t._hoverState?t._hoverState="show":(clearTimeout(t._timeout),t._hoverState="show",t.config.delay&&t.config.delay.show?t._timeout=setTimeout(function(){"show"===t._hoverState&&t.show()},t.config.delay.show):t.show())},n._leave=function(e,t){var n=this.constructor.DATA_KEY;(t=t||c.default(e.currentTarget).data(n))||(t=new this.constructor(e.currentTarget,this._getDelegateConfig()),c.default(e.currentTarget).data(n,t)),e&&(t._activeTrigger["focusout"===e.type?"focus":"hover"]=!1),t._isWithActiveTrigger()||(clearTimeout(t._timeout),t._hoverState="out",t.config.delay&&t.config.delay.hide?t._timeout=setTimeout(function(){"out"===t._hoverState&&t.hide()},t.config.delay.hide):t.hide())},n._isWithActiveTrigger=function(){for(var e in this._activeTrigger)if(this._activeTrigger[e])return!0;return!1},n._getConfig=function(e){var t=c.default(this.element).data();return Object.keys(t).forEach(function(e){-1!==Q.indexOf(e)&&delete t[e]}),"number"==typeof(e=s({},this.constructor.Default,t,"object"==_typeof(e)&&e?e:{})).delay&&(e.delay={show:e.delay,hide:e.delay}),"number"==typeof e.title&&(e.title=e.title.toString()),"number"==typeof e.content&&(e.content=e.content.toString()),d.typeCheckConfig(z,e,this.constructor.DefaultType),e.sanitize&&(e.template=$(e.template,e.whiteList,e.sanitizeFn)),e},n._getDelegateConfig=function(){var e={};if(this.config)for(var t in this.config)this.constructor.Default[t]!==this.config[t]&&(e[t]=this.config[t]);return e},n._cleanTipClass=function(){var e=c.default(this.getTipElement()),t=e.attr("class").match(X);null!==t&&t.length&&e.removeClass(t.join(""))},n._handlePopperPlacementChange=function(e){this.tip=e.instance.popper,this._cleanTipClass(),this.addAttachmentClass(this._getAttachment(e.placement))},n._fixTransition=function(){var e=this.getTipElement(),t=this.config.animation;null===e.getAttribute("x-placement")&&(c.default(e).removeClass("fade"),this.config.animation=!1,this.hide(),this.show(),this.config.animation=t)},te._jQueryInterface=function(r){return this.each(function(){var e=c.default(this),t=e.data("bs.tooltip"),n="object"==_typeof(r)&&r;if((t||!/dispose|hide/.test(r))&&(t||(t=new te(this,n),e.data("bs.tooltip",t)),"string"==typeof r)){if(void 0===t[r])throw new TypeError('No method named "'+r+'"');t[r]()}})},a(te,null,[{key:"VERSION",get:function(){return"4.5.3"}},{key:"Default",get:function(){return J}},{key:"NAME",get:function(){return z}},{key:"DATA_KEY",get:function(){return"bs.tooltip"}},{key:"Event",get:function(){return Z}},{key:"EVENT_KEY",get:function(){return".bs.tooltip"}},{key:"DefaultType",get:function(){return Y}}]),te);function te(e,t){if(void 0===i.default)throw new TypeError("Bootstrap's tooltips require Popper.js (https://popper.js.org/)");this._isEnabled=!0,this._timeout=0,this._hoverState="",this._activeTrigger={},this._popper=null,this.element=e,this.config=this._getConfig(t),this.tip=null,this._setListeners()}c.default.fn[z]=ee._jQueryInterface,c.default.fn[z].Constructor=ee,c.default.fn[z].noConflict=function(){return c.default.fn[z]=V,ee._jQueryInterface};var ne="popover",re=c.default.fn[ne],ie=new RegExp("(^|\\s)bs-popover\\S+","g"),oe=s({},ee.Default,{placement:"right",trigger:"click",content:"",template:'<div class="popover" role="tooltip"><div class="arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>'}),ae=s({},ee.DefaultType,{content:"(string|element|function)"}),se={HIDE:"hide.bs.popover",HIDDEN:"hidden.bs.popover",SHOW:"show.bs.popover",SHOWN:"shown.bs.popover",INSERTED:"inserted.bs.popover",CLICK:"click.bs.popover",FOCUSIN:"focusin.bs.popover",FOCUSOUT:"focusout.bs.popover",MOUSEENTER:"mouseenter.bs.popover",MOUSELEAVE:"mouseleave.bs.popover"},le=function(e){var t;function r(){return e.apply(this,arguments)||this}n=e,(t=r).prototype=Object.create(n.prototype),(t.prototype.constructor=t).__proto__=n;var n=r.prototype;return n.isWithContent=function(){return this.getTitle()||this._getContent()},n.addAttachmentClass=function(e){c.default(this.getTipElement()).addClass("bs-popover-"+e)},n.getTipElement=function(){return this.tip=this.tip||c.default(this.config.template)[0],this.tip},n.setContent=function(){var e=c.default(this.getTipElement());this.setElementContent(e.find(".popover-header"),this.getTitle());var t=this._getContent();"function"==typeof t&&(t=t.call(this.element)),this.setElementContent(e.find(".popover-body"),t),e.removeClass("fade show")},n._getContent=function(){return this.element.getAttribute("data-content")||this.config.content},n._cleanTipClass=function(){var e=c.default(this.getTipElement()),t=e.attr("class").match(ie);null!==t&&0<t.length&&e.removeClass(t.join(""))},r._jQueryInterface=function(n){return this.each(function(){var e=c.default(this).data("bs.popover"),t="object"==_typeof(n)?n:null;if((e||!/dispose|hide/.test(n))&&(e||(e=new r(this,t),c.default(this).data("bs.popover",e)),"string"==typeof n)){if(void 0===e[n])throw new TypeError('No method named "'+n+'"');e[n]()}})},a(r,null,[{key:"VERSION",get:function(){return"4.5.3"}},{key:"Default",get:function(){return oe}},{key:"NAME",get:function(){return ne}},{key:"DATA_KEY",get:function(){return"bs.popover"}},{key:"Event",get:function(){return se}},{key:"EVENT_KEY",get:function(){return".bs.popover"}},{key:"DefaultType",get:function(){return ae}}]),r}(ee);c.default.fn[ne]=le._jQueryInterface,c.default.fn[ne].Constructor=le,c.default.fn[ne].noConflict=function(){return c.default.fn[ne]=re,le._jQueryInterface};var ue="scrollspy",ce=c.default.fn[ue],de={offset:10,method:"auto",target:""},fe={offset:"number",method:"string",target:"(string|element)"},pe=((n=he.prototype).refresh=function(){var t=this,e=this._scrollElement===this._scrollElement.window?"offset":"position",r="auto"===this._config.method?e:this._config.method,i="position"===r?this._getScrollTop():0;this._offsets=[],this._targets=[],this._scrollHeight=this._getScrollHeight(),[].slice.call(document.querySelectorAll(this._selector)).map(function(e){var t,n=d.getSelectorFromElement(e);if(n&&(t=document.querySelector(n)),t){e=t.getBoundingClientRect();if(e.width||e.height)return[c.default(t)[r]().top+i,n]}return null}).filter(function(e){return e}).sort(function(e,t){return e[0]-t[0]}).forEach(function(e){t._offsets.push(e[0]),t._targets.push(e[1])})},n.dispose=function(){c.default.removeData(this._element,"bs.scrollspy"),c.default(this._scrollElement).off(".bs.scrollspy"),this._element=null,this._scrollElement=null,this._config=null,this._selector=null,this._offsets=null,this._targets=null,this._activeTarget=null,this._scrollHeight=null},n._getConfig=function(e){var t;return"string"!=typeof(e=s({},de,"object"==_typeof(e)&&e?e:{})).target&&d.isElement(e.target)&&((t=c.default(e.target).attr("id"))||(t=d.getUID(ue),c.default(e.target).attr("id",t)),e.target="#"+t),d.typeCheckConfig(ue,e,fe),e},n._getScrollTop=function(){return this._scrollElement===window?this._scrollElement.pageYOffset:this._scrollElement.scrollTop},n._getScrollHeight=function(){return this._scrollElement.scrollHeight||Math.max(document.body.scrollHeight,document.documentElement.scrollHeight)},n._getOffsetHeight=function(){return this._scrollElement===window?window.innerHeight:this._scrollElement.getBoundingClientRect().height},n._process=function(){var e=this._getScrollTop()+this._config.offset,t=this._getScrollHeight(),n=this._config.offset+t-this._getOffsetHeight();if(this._scrollHeight!==t&&this.refresh(),n<=e){n=this._targets[this._targets.length-1];this._activeTarget!==n&&this._activate(n)}else{if(this._activeTarget&&e<this._offsets[0]&&0<this._offsets[0])return this._activeTarget=null,void this._clear();for(var r=this._offsets.length;r--;)this._activeTarget!==this._targets[r]&&e>=this._offsets[r]&&(void 0===this._offsets[r+1]||e<this._offsets[r+1])&&this._activate(this._targets[r])}},n._activate=function(t){this._activeTarget=t,this._clear();var e=this._selector.split(",").map(function(e){return e+'[data-target="'+t+'"],'+e+'[href="'+t+'"]'}),e=c.default([].slice.call(document.querySelectorAll(e.join(","))));e.hasClass("dropdown-item")?(e.closest(".dropdown").find(".dropdown-toggle").addClass("active"),e.addClass("active")):(e.addClass("active"),e.parents(".nav, .list-group").prev(".nav-link, .list-group-item").addClass("active"),e.parents(".nav, .list-group").prev(".nav-item").children(".nav-link").addClass("active")),c.default(this._scrollElement).trigger("activate.bs.scrollspy",{relatedTarget:t})},n._clear=function(){[].slice.call(document.querySelectorAll(this._selector)).filter(function(e){return e.classList.contains("active")}).forEach(function(e){return e.classList.remove("active")})},he._jQueryInterface=function(t){return this.each(function(){var e=c.default(this).data("bs.scrollspy");if(e||(e=new he(this,"object"==_typeof(t)&&t),c.default(this).data("bs.scrollspy",e)),"string"==typeof t){if(void 0===e[t])throw new TypeError('No method named "'+t+'"');e[t]()}})},a(he,null,[{key:"VERSION",get:function(){return"4.5.3"}},{key:"Default",get:function(){return de}}]),he);function he(e,t){var n=this;this._element=e,this._scrollElement="BODY"===e.tagName?window:e,this._config=this._getConfig(t),this._selector=this._config.target+" .nav-link,"+this._config.target+" .list-group-item,"+this._config.target+" .dropdown-item",this._offsets=[],this._targets=[],this._activeTarget=null,this._scrollHeight=0,c.default(this._scrollElement).on("scroll.bs.scrollspy",function(e){return n._process(e)}),this.refresh(),this._process()}c.default(window).on("load.bs.scrollspy.data-api",function(){for(var e=[].slice.call(document.querySelectorAll('[data-spy="scroll"]')),t=e.length;t--;){var n=c.default(e[t]);pe._jQueryInterface.call(n,n.data())}}),c.default.fn[ue]=pe._jQueryInterface,c.default.fn[ue].Constructor=pe,c.default.fn[ue].noConflict=function(){return c.default.fn[ue]=ce,pe._jQueryInterface};var me=c.default.fn.tab,ge=((n=ve.prototype).show=function(){var e,t,n,r,i,o,a=this;this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE&&c.default(this._element).hasClass("active")||c.default(this._element).hasClass("disabled")||(o=c.default(this._element).closest(".nav, .list-group")[0],t=d.getSelectorFromElement(this._element),o&&(i="UL"===o.nodeName||"OL"===o.nodeName?"> li > .active":".active",n=(n=c.default.makeArray(c.default(o).find(i)))[n.length-1]),r=c.default.Event("hide.bs.tab",{relatedTarget:this._element}),i=c.default.Event("show.bs.tab",{relatedTarget:n}),n&&c.default(n).trigger(r),c.default(this._element).trigger(i),i.isDefaultPrevented()||r.isDefaultPrevented()||(t&&(e=document.querySelector(t)),this._activate(this._element,o),o=function(){var e=c.default.Event("hidden.bs.tab",{relatedTarget:a._element}),t=c.default.Event("shown.bs.tab",{relatedTarget:n});c.default(n).trigger(e),c.default(a._element).trigger(t)},e?this._activate(e,e.parentNode,o):o()))},n.dispose=function(){c.default.removeData(this._element,"bs.tab"),this._element=null},n._activate=function(e,t,n){var r=this,i=(!t||"UL"!==t.nodeName&&"OL"!==t.nodeName?c.default(t).children(".active"):c.default(t).find("> li > .active"))[0],o=n&&i&&c.default(i).hasClass("fade"),t=function(){return r._transitionComplete(e,i,n)};i&&o?(o=d.getTransitionDurationFromElement(i),c.default(i).removeClass("show").one(d.TRANSITION_END,t).emulateTransitionEnd(o)):t()},n._transitionComplete=function(e,t,n){var r;t&&(c.default(t).removeClass("active"),(r=c.default(t.parentNode).find("> .dropdown-menu .active")[0])&&c.default(r).removeClass("active"),"tab"===t.getAttribute("role")&&t.setAttribute("aria-selected",!1)),c.default(e).addClass("active"),"tab"===e.getAttribute("role")&&e.setAttribute("aria-selected",!0),d.reflow(e),e.classList.contains("fade")&&e.classList.add("show"),e.parentNode&&c.default(e.parentNode).hasClass("dropdown-menu")&&((t=c.default(e).closest(".dropdown")[0])&&(t=[].slice.call(t.querySelectorAll(".dropdown-toggle")),c.default(t).addClass("active")),e.setAttribute("aria-expanded",!0)),n&&n()},ve._jQueryInterface=function(n){return this.each(function(){var e=c.default(this),t=e.data("bs.tab");if(t||(t=new ve(this),e.data("bs.tab",t)),"string"==typeof n){if(void 0===t[n])throw new TypeError('No method named "'+n+'"');t[n]()}})},a(ve,null,[{key:"VERSION",get:function(){return"4.5.3"}}]),ve);function ve(e){this._element=e}c.default(document).on("click.bs.tab.data-api",'[data-toggle="tab"], [data-toggle="pill"], [data-toggle="list"]',function(e){e.preventDefault(),ge._jQueryInterface.call(c.default(this),"show")}),c.default.fn.tab=ge._jQueryInterface,c.default.fn.tab.Constructor=ge,c.default.fn.tab.noConflict=function(){return c.default.fn.tab=me,ge._jQueryInterface};var ye=c.default.fn.toast,be={animation:"boolean",autohide:"boolean",delay:"number"},_e={animation:!0,autohide:!0,delay:500},we=((n=xe.prototype).show=function(){var e,t=this,n=c.default.Event("show.bs.toast");c.default(this._element).trigger(n),n.isDefaultPrevented()||(this._clearTimeout(),this._config.animation&&this._element.classList.add("fade"),e=function(){t._element.classList.remove("showing"),t._element.classList.add("show"),c.default(t._element).trigger("shown.bs.toast"),t._config.autohide&&(t._timeout=setTimeout(function(){t.hide()},t._config.delay))},this._element.classList.remove("hide"),d.reflow(this._element),this._element.classList.add("showing"),this._config.animation?(n=d.getTransitionDurationFromElement(this._element),c.default(this._element).one(d.TRANSITION_END,e).emulateTransitionEnd(n)):e())},n.hide=function(){var e;this._element.classList.contains("show")&&(e=c.default.Event("hide.bs.toast"),c.default(this._element).trigger(e),e.isDefaultPrevented()||this._close())},n.dispose=function(){this._clearTimeout(),this._element.classList.contains("show")&&this._element.classList.remove("show"),c.default(this._element).off("click.dismiss.bs.toast"),c.default.removeData(this._element,"bs.toast"),this._element=null,this._config=null},n._getConfig=function(e){return e=s({},_e,c.default(this._element).data(),"object"==_typeof(e)&&e?e:{}),d.typeCheckConfig("toast",e,this.constructor.DefaultType),e},n._setListeners=function(){var e=this;c.default(this._element).on("click.dismiss.bs.toast",'[data-dismiss="toast"]',function(){return e.hide()})},n._close=function(){function e(){n._element.classList.add("hide"),c.default(n._element).trigger("hidden.bs.toast")}var t,n=this;this._element.classList.remove("show"),this._config.animation?(t=d.getTransitionDurationFromElement(this._element),c.default(this._element).one(d.TRANSITION_END,e).emulateTransitionEnd(t)):e()},n._clearTimeout=function(){clearTimeout(this._timeout),this._timeout=null},xe._jQueryInterface=function(n){return this.each(function(){var e=c.default(this),t=e.data("bs.toast");if(t||(t=new xe(this,"object"==_typeof(n)&&n),e.data("bs.toast",t)),"string"==typeof n){if(void 0===t[n])throw new TypeError('No method named "'+n+'"');t[n](this)}})},a(xe,null,[{key:"VERSION",get:function(){return"4.5.3"}},{key:"DefaultType",get:function(){return be}},{key:"Default",get:function(){return _e}}]),xe);function xe(e,t){this._element=e,this._config=this._getConfig(t),this._timeout=null,this._setListeners()}c.default.fn.toast=we._jQueryInterface,c.default.fn.toast.Constructor=we,c.default.fn.toast.noConflict=function(){return c.default.fn.toast=ye,we._jQueryInterface},e.Alert=f,e.Button=m,e.Carousel=x,e.Collapse=A,e.Dropdown=I,e.Modal=H,e.Popover=le,e.Scrollspy=pe,e.Tab=ge,e.Toast=we,e.Tooltip=ee,e.Util=d,Object.defineProperty(e,"__esModule",{value:!0})});var browser,isChrome=!navigator.userAgent.includes("Edg")&&!navigator.userAgent.includes("OPR")&&(navigator.userAgent.includes("Chrome")||navigator.userAgent.includes("Crios")),chromeVersion=isChrome&&Number((navigator.userAgent.match(/(?:Chrome|Crios)\/(\d+)/)||[])[1]||0)||0;navigator.userAgent.includes("Chrome")&&("function"==typeof Window?window.browser=chrome:browser=chrome);var extension_source="chrome",enable_yt=!1,enable_check_update=!1,freeLimit={hqDownload:{maxCount:1,hours:1},streamDownload:{maxCount:1,hours:1},videoRecord:{maxCount:1,hours:1},audioRecord:{maxCount:1,hours:1},tabRecord:{maxCount:1,hours:1},screenRecord:{maxCount:1,hours:1}},configuration={BRAND:"VidHelper",DOMAIN:"https://vidhelper.app",FAQ:"https://vidhelper.app#faqs",MEASUREMENT_ID:"G-LNBWCVYXVX",API_SECRET:"vzBAqqNRSb6ugOR-mqf6CA",LICENSE_BASEURL:"https://vidhelper.app/api",CHECK_FOR_UPDATE:enable_check_update,UPDATE_INTERVAL:36e5,CHECK_UPDATE_URL:"https://app.vidhelper.app/latest",HOWTO_INSTAL_URL:"https://vidhelper.app/how-to-manually-install-vidhelper-extension",STORE_ID:19749,PRODUCT_IDS:[170497,170523,170502,170524,170494,170522],PRODUCT:{name:browser.i18n.getMessage("Pro"),features:[{name:browser.i18n.getMessage("UnlimitMediaDL"),free:browser.i18n.getMessage("Yes"),paid:browser.i18n.getMessage("Yes")},{name:browser.i18n.getMessage("sitesSupported"),free:browser.i18n.getMessage("Yes"),paid:browser.i18n.getMessage("Yes")},{name:browser.i18n.getMessage("1080pSupport"),free:browser.i18n.getMessage("NumEveryHours").replace("{{1}}",freeLimit.hqDownload.maxCount).replace("{{2}}",freeLimit.hqDownload.hours),paid:browser.i18n.getMessage("Unlimited")},{name:browser.i18n.getMessage("NumStreamLimit"),free:browser.i18n.getMessage("NumEveryHours").replace("{{1}}",freeLimit.streamDownload.maxCount).replace("{{2}}",freeLimit.streamDownload.hours),paid:browser.i18n.getMessage("Unlimited")},{name:browser.i18n.getMessage("EachRecordLimit"),free:browser.i18n.getMessage("NumEveryHours").replace("{{1}}",freeLimit.videoRecord.maxCount).replace("{{2}}",freeLimit.videoRecord.hours),paid:browser.i18n.getMessage("Unlimited")},{name:browser.i18n.getMessage("hlsDashMerge"),free:browser.i18n.getMessage("No"),paid:browser.i18n.getMessage("Yes")}],discount:browser.i18n.getMessage("discountInfo"),plans:[{name:browser.i18n.getMessage("Monthly"),checkoutUrl:"https://vidhelper.app/checkout?plan=monthly",price:"$5.9",priceMonthly:"$5.9",origPriceMonthly:"$7.9",description:browser.i18n.getMessage("MonthlyDes")},{name:browser.i18n.getMessage("Yearly"),checkoutUrl:"https://vidhelper.app/checkout?plan=yearly",price:"$29",priceMonthly:"$2.4",origPriceMonthly:"$3.2",description:browser.i18n.getMessage("YearlyDes").replace("{{1}}","$29").replace("{{2}}","$30"),mostPopular:!0},{name:browser.i18n.getMessage("Lifetime"),checkoutUrl:"https://vidhelper.app/checkout?plan=lifetime",price:"$55",origPrice:"$75",description:browser.i18n.getMessage("LifetimeDes")}]},sentryDSN:"https://<EMAIL>/4508324004036608",FREE_LIMIT:freeLimit,GENERAL:{showMergeInfo:!1,showVideoElRecordTip:!0,showTabRecordTip:!1,showDownloadStreamTip:!0,recordCountDown:3,useDedicatedDownloader:!1,dedicatedDownloader:"downloadingPage",defaultDownloader:"downloadingFrame",enableRegionSelector:!0,enableScreenRecord:!0,tabRecordType:"recordingPage"},MEDIA_FILTER:{VIDEO:{enable:!0,size:1048576,range:[0,104857600],step:1048576,candisable:!1},AUDIO:{enable:!0,size:307200,range:[0,20971520],step:1024,candisable:!1}},CSP_LIST:["www.facebook.com","www.instagram.com","twitter.com","x.com","youtube.com","vimeo.com","www.tokopedia.com"],SHAKA_PLAYER_LIST:["vk.com","ok.ru"],SKIP_BG_DOWNLOAD:["www.tiktok.com","streamplay.pw"],ENABLE_POPUP_QUERY:!1,INFOBOX_UP:!1,CONTACT_US:"mailto:<EMAIL>",RATE_US:"https://chrome.google.com/webstore/detail/".concat(browser.runtime.id,"/reviews"),DROPBOX_KEY:"6awh8g09fftibys",ENABLE_CONTEXTMENU:!1,CONTEXTMENU_TITLE:"Search Videos for",ENABLE_INSTALL_URL:!1,INSTAL_SUFFIX:"/install",ENABLE_UNINSTALL_URL:!1,UNINSTAL_SUFFIX:"/uninstall",ENABLE_UPDATE_URL:!1,UPDATE_URL:"/uninstall",DISALLOW_YOUTUBE:!enable_yt,DISALLOW_DOMAINS_REGEXP:enable_yt?null:/^(?:(?:https?:)?\/\/)?(?:[^/]*.)?youtube.com/,DISABLE_HEADRECEIVE_DOMAINS_REGEXP:/^(?:(?:https?:)?\/\/)?(?:[^/]*.)?(?:www.tiktok.com|www.instagram.com|www.facebook.com|vimeo.com|youtube.com|www.bilibili.com|www.bilibili.tv)/,DISABLE_IMAGE_HEADRECEIVE_DOMAINS_REGEXP:/^(?:(?:https?:)?\/\/)?(?:[^/]*.)?(?:vidhelper.app)/,INSTALL_DAYS:30,USE_COUNT:10,MAX_USE_STATISTICS:60,ENABLE_FINDERS:_objectSpread(_objectSpread({vimeo:"VMFinder",dailymotion:"DMFinder",facebook:"FBFinder",tiktok:"TTFinder",instagram:"IGFinder",soundcloud:"SCFinder"},enable_yt?{youtube:"YTFinder"}:{}),{},{bilibili:"BILIFinder",xx:"XXFinder"}),FINDER_MATCHES:["<all_urls>"],FINDER_EXCLUDE_MATCHES:["*://https://vidhelper.app/*"]},contentMap={m3u8:"video/mp4",m3u:"video/mp4",mp4:"video/mp4","3gp":"video/3gpp",flv:"video/x-flv",mov:"video/quicktime",avi:"video/x-msvideo",wmv:"video/x-ms-wmv",webm:"video/webm",ogg:"video/ogg",ogv:"video/ogg",f4v:"video/x-f4v",acc:"application/vnd.americandynamics.acc",mkv:"video/x-matroska",rmvb:"application/vnd.rn-realmedia-vbr",m4s:"video/iso.segment"},streamExts=["m3u8","m3u","mpd"],audioExts=["aac","mid","mp3","oga","wav","wma","flac","acc"],videoExts=["webm","3gp","3g2","rmvb","mp4","3gp2","flv","mov","avi","wmv","ogg","ogv","f4v","mkv"],imageExts=["bmp","gif","avif","ico","png","apng","svg","tif","tiff","webp","jpeg","jpg"],imageFormats={"image/bmp":["bmp"],"image/gif":["gif"],"image/avif":["avif"],"image/x-icon":["ico"],"image/vnd.microsoft.icon":["ico"],"image/png":["png"],"image/apng":["apng"],"image/svg+xml":["svg"],"image/tiff":["tif","tiff"],"image/webp":["webp"],"image/jpeg":["jpeg","jpg"],"image/jpg":["jpeg","jpg"]},imageMimeTypes=Object.keys(imageFormats),audioFormats={"audio/mp3":["mp3"],"audio/mp4":["m4a"],"audio/aac":["aac"],"audio/midi":["mid"],"audio/x-midi":["mid"],"audio/mpeg":["mp3"],"audio/ogg":["oga"],"audio/wav":["wav"],"audio/x-wav":["wav"],"audio/vnd.wave":["wav"],"audio/wave":["wav"],"audio/x-pn-wav":["wav"],"audio/webm":["webm"],"audio/3gpp":["3gp"],"audio/3gpp2":["3g2"],"audio/x-ms-wma":["wma"],"audio/flac":["flac"]},audioMimeTypes=Object.keys(audioFormats),videoFormats={"application/vnd.americandynamics.acc":["acc"],"application/vnd.rn-realmedia-vbr":["rmvb"],"video/mp4":["mp4","m4s","m4v"],"video/3gpp":["3gp"],"video/3gpp2":["3gp2"],"video/x-flv":["flv"],"video/x-m4v":["m4v"],"video/quicktime":["mov"],"video/x-msvideo":["avi"],"video/x-ms-wmv":["wmv"],"video/webm":["webm"],"video/ogg":["ogg","ogv"],"application/ogg":["ogg"],"video/x-f4v":["f4v"],"video/x-matroska":["mkv"],"video/iso.segment":["m4s"],"application/mp4":["mp4"]},videoMimeTypes=Object.keys(videoFormats),streamFormats={"audio/mpegurl":["m3u8","m3u"],"audio/x-mpegurl":["m3u8","m3u"],"application/vnd.apple.mpegurl":["m3u8","m3u"],"application/x-mpegurl":["m3u8","m3u"],"application/dash+xml":["mpd"],"application/octet-stream":["m3u8","m3u","mpd","mp4","webm","avi","ogg","flv","mkv","3gp","ts","m4v","mp3","wav"],"binary/octet-stream":["m3u8","m3u","mpd","mp4","webm","avi","ogg","flv","mkv","3gp","ts","m4v","mp3","wav"]},streamMimeTypes=Object.keys(streamFormats),mimeTypeFormats={audio:audioFormats,video:videoFormats,stream:streamFormats},allowedExtensions=[audioExts,videoExts,streamExts].flat(),typeExts=Object.entries(mimeTypeFormats).flatMap(function(e){var t=_slicedToArray(e,2),e=t[0],t=t[1];if("stream"!==e)return _defineProperty({},e,Object.values(t).flatMap(function(e){return e}))}).reduce(function(e,t){return Object.assign(e,t)},{}),mimeTypes=Object.entries(mimeTypeFormats).flatMap(function(e){e=_slicedToArray(e,2),e[0],e=e[1];return Object.keys(e)});function getTypeFromExt(e){if("string"!=typeof e)return null;for(var t=0,n=Object.keys(mimeTypeFormats);t<n.length;t++){var r=n[t],i=Object.values(mimeTypeFormats[r]).flat();if("stream"!=r&&-1!=i.indexOf(e))return r}return null}function getFileNameFromURL(e){var t=e.match(/\/?([^/?#]+)(?:$|\?|#)/);return(t=t||e.match(/\/([^/]+)\/?$/))?t[1]:"unknown"}function getFileNameFromURLPathName(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:64,n=2<arguments.length&&void 0!==arguments[2]&&arguments[2];try{var r,i=new URL(t).pathname;n&&(r=i.match(_wrapRegExp(/(.+?)(?:\.(\w{2,4}))?$/,{name:1,ext:2})),r&&(i=r.groups.name),r.groups.ext);var i=i.split("").reverse().join(""),o=_toConsumableArray(i.matchAll(/\//g)).map(function(e){return e.index}),a=e;if(0<o.length)for(var s=o.length-1;0<=s;s--)if(e>=o[s]){a=o[s];break}return i.slice(0,a).split("").map(function(e){return"/"===e?"-":e}).join("").split("").reverse().join("")}catch(e){return getFileNameFromURL(t)}}function getTitleExtFromFileName(e){var t=_slicedToArray(e.split(/\.(\w{2,4})$/),2),e=t[0],t=t[1],t=void 0===t?null:t;return{title:e,ext:(null==t?void 0:t.toLowerCase())||null}}function getTitleExtFromUrl(e){var t=1<arguments.length&&void 0!==arguments[1]&&arguments[1],n={title:"unknow",ext:null};return"string"!=typeof e||(n=getTitleExtFromFileName(getFileNameFromURL(e))).ext&&t&&allowedExtensions.indexOf(n.ext)<0&&(n.ext=null),n}function getTypeFromExt(e){return videoExts.includes(e)?"video":audioExts.includes(e)?"audio":streamExts.includes(e)?"stream":null}function getTypeFormatFromExtMime(e,t){var n={type:null,ext:null};if("string"!=typeof(t=t&&t.toLowerCase().split(";")[0]))return n;for(var r=0,i=Object.keys(mimeTypeFormats);r<i.length;r++){var o=i[r],a=mimeTypeFormats[o][t];if("stream"!=o&&a){var s=a.indexOf(e);return n.type=o,n.ext=s<0?a[0]:a[s],n}}if(mimeTypeFormats.stream[t]){var l=mimeTypeFormats.stream[t].indexOf(e);if(n.type="stream",0<=t.indexOf("octet-stream")){n.ext=l<0?null:mimeTypeFormats.stream[t][l];for(var u=0,c=Object.entries(typeExts);u<c.length;u++){var d=_slicedToArray(c[u],2),f=d[0];if(d[1].includes(n.ext)){n.type=f;break}}}else n.ext=l<0?mimeTypeFormats.stream[t][0]:mimeTypeFormats.stream[t][l];return n}return n}function areObjectsEqual(e,t){var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(var i=0,o=n;i<o.length;i++){var a=o[i];if(null==t||!t.key||e[a]!==t[a])return!1}return!0}function p(e){return _p.apply(this,arguments)}function _p(){return(_p=_asyncToGenerator(_regeneratorRuntime().mark(function e(r){var t,i,n,o=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:for(t=o.length,i=new Array(1<t?t-1:0),n=1;n<t;n++)i[n-1]=o[n];return e.abrupt("return",new Promise(function(t,n){r.apply(void 0,i.concat([function(e){browser.runtime.lastError?n(browser.runtime.lastError):t(e)}]))}));case 2:case"end":return e.stop()}},e)}))).apply(this,arguments)}function isDevelop(){return browser.runtime.getManifest().name.startsWith("Develop")}function parse_match_pattern(e){if("string"!=typeof e)return null;function t(e){return e.replace(/[[^$.|?*+(){}\\]/g,"\\$&")}var n="(?:^",r=/^(\*|https?|file|ftp|chrome-extension):\/\//.exec(e);if(!r)return null;if(e=e.substr(r[0].length),n+="*"===r[1]?"https?://":r[1]+"://","file"!==r[1]){if(!(r=/^(?:\*|(\*\.)?([^\/*]+))(?=\/)/.exec(e)))return null;e=e.substr(r[0].length),"*"===r[0]?n+="[^/]+":(r[1]&&(n+="(?:[^/]+\\.)?"),n+=t(r[2]))}return n+=e.split("*").map(t).join(".*"),n+="$)"}function formatBytes(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:2,n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:1024;if("string"==typeof e&&(e=parseInt(e)),0===e)return"0 Bytes";var r=t<0?0:t,t=Math.floor(Math.log(e)/Math.log(n));return parseFloat((e/Math.pow(n,t)).toFixed(r))+" "+["Bytes","KB","MB","GB","TB","PB","EB","ZB","YB"][t]}function is1080pOrHigher(e){if(null!=e&&e.width&&null!=e&&e.height){if(1920<=e.width&&1080<=e.height||1080<=e.width&&1920<=e.height)return!0}else if(e.resolution)try{var t=e.resolution.split("x"),n=parseInt(t[0],10),t=parseInt(t[1],10);if(1920<=n&&1080<=t||1080<=n&&1920<=t)return!0}catch(e){}else if(e.quality)try{if(1080<=parseInt(e.quality.split("p")[0]))return!0}catch(e){}return!1}function getFileNameFromcontentDisposition(e){e=e.match(/filename=["']?([^'"\s;]+)["']?/i);return e?e[1]:null}function getRangeInfo(e){var t=e.split(" ");if(2===t.length){e=t[1].split("/");if(2===e.length){t=parseInt(e[1]);if(t)return{chunk:e[0],total:t}}}return null}function getSizeFromReceivedHeader(e){var t=e.get("content-length",null),e=e.get("content-range",null);if(t)return parseInt(t);if(e){e=getRangeInfo(e);if(e)return e.total}return null}function jsonHeadersToResponseHeaders(e){var n=_objectSpread({},e);return{get:function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:null,t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:null;if(n){if(e){e=e.toLowerCase();return n[e]||t}return n}return n}}}function responseHeadersToJson(e){var t={},n=_createForOfIteratorHelper(e.entries());try{for(n.s();!(i=n.n()).done;){var r=_slicedToArray(i.value,2),i=r[0],r=r[1];t[i]=r}}catch(e){n.e(e)}finally{n.f()}return t}function needPicker(e){if(1610612736<(null==e?void 0:e.size))return!0;if(null!=e&&e.duration){var t,n,r=0;if(null!=e&&e.bandwidth?r=e.bandwidth:e.width&&e.height?(n=(null==e?void 0:e.frameRate)||30,r=e.width*e.height*n*.1):e.resolution&&(t=e.resolution.split("x"),n=(null==e?void 0:e.frameRate)||30,r=t[0]*t[1]*n*.1),1610612736<e.duration*r/8)return!0}return!1}function hms(e){var t,n=e<3600?(t=14,5):(t=11,8);return new Date(1e3*e).toISOString().substr(t,n)}function getQualityFromVideoLink(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"";return e.width&&e.height&&(e.resolution="".concat(e.width,"x").concat(e.height)),e.bandwidth&&e.resolution?t="".concat(Math.floor(e.bandwidth/1e3)," kbps (").concat(e.resolution,")"):e.resolution?t=e.resolution:e.bandwidth&&e.quality?t="".concat(Math.floor(e.bandwidth/1e3)," kbps (").concat(e.quality,")"):e.quality?t=e.quality:e.bandwidth?t="".concat(Math.floor(e.bandwidth/1e3)," kbps"):e.size?t=formatBytes(e.size):e.duration?t=e.duration:e.name&&(t=e.name),t}function formatResolution(e,t){for(var n=0,r=Object.entries({"144p":[256,144],"240p":[426,240],"360p":[640,360],"480p":[854,480],"720p":[1280,720],"1080p":[1920,1080],"2K":[2048,1080],QHD:[2560,1440],"4K":[3840,2160],"DCI 4K":[4096,2160],"8K":[7680,4320]});n<r.length;n++){var i=_slicedToArray(r[n],2),o=i[0],a=_slicedToArray(i[1],2),i=a[0],a=a[1];if(e===i&&t===a)return o}return"".concat(e,"x").concat(t)}function getQualityFromVideoLink_new(e){var t=[];e.width&&e.height&&t.push("".concat(formatResolution(e.width,e.height))),e.frameRate&&t.push("".concat(e.frameRate," fps")),e.bandwidth?t.push("".concat(Math.floor(e.bandwidth/1e3)," kbps")):e.bitrate&&t.push("".concat(e.bitrate," kbps"));try{e.duration&&t.push(hms(e.duration))}catch(e){}return e.size&&t.push(formatBytes(e.size)),"audio"==e.type&&e.name&&t.push(e.name),t.join(" | ")}function checkAttr(e,t,n){for(var r=0,i=e.length;r<i;r++)if(e[r][t]==n)return!0;return!1}function checkScript(e,t){return checkAttr(document.scripts,e,t)}function inIframe(){try{return window.self!==window.top}catch(e){return!0}}function delay(t){return new Promise(function(e){return setTimeout(e,t)})}function waitHeaderLoaded(){return _waitHeaderLoaded.apply(this,arguments)}function _waitHeaderLoaded(){return(_waitHeaderLoaded=_asyncToGenerator(_regeneratorRuntime().mark(function e(){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",new Promise(function(e){function t(){return document.head||document.documentElement}var n;t()?e():(n=new MutationObserver(function(){t()&&(n.disconnect(),e())})).observe(document,{childList:!0,subtree:!0})}));case 1:case"end":return e.stop()}},e)}))).apply(this,arguments)}function waitBodyLoaded(){return _waitBodyLoaded.apply(this,arguments)}function _waitBodyLoaded(){return(_waitBodyLoaded=_asyncToGenerator(_regeneratorRuntime().mark(function e(){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",new Promise(function(e){function t(){return document.body}var n;t()?e():(n=new MutationObserver(function(){t()&&(n.disconnect(),e())})).observe(document,{childList:!0,subtree:!0})}));case 1:case"end":return e.stop()}},e)}))).apply(this,arguments)}function waitLoaded(){return _waitLoaded.apply(this,arguments)}function _waitLoaded(){return(_waitLoaded=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var n,r,t=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(n=0<t.length&&void 0!==t[0]?t[0]:null,"loading"!==document.readyState)return e.abrupt("return");e.next=5;break;case 5:return r=null,e.abrupt("return",new Promise(function(e,t){"number"==typeof n&&(r=setTimeout(function(){t(new Error("Timeout exceeded"))},n)),document.addEventListener("DOMContentLoaded",function(){null!=r&&(clearTimeout(r),r=null),e()})}));case 7:case"end":return e.stop()}},e)}))).apply(this,arguments)}function loadScript(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:null,n=!(2<arguments.length&&void 0!==arguments[2])||arguments[2]?document.head||document.documentElement:document.body,r=document.createElement("script"),i=setTimeout(function(){t(r)},1e3);if(e.src?r.src=e.src:e.textContent?r.textContent=e.textContent:e.innerHTML&&(r.innerHTML=e.innerHTML),e.attrs)for(var o=0,a=Object.entries(e.attrs);o<a.length;o++){var s=_slicedToArray(a[o],2),l=s[0],s=s[1],s=void 0===s||s;r.setAttribute(l,s)}t&&(r.onload=function(){null!=i&&(clearTimeout(i),i=null),t(r)}),n.appendChild(r)}function loadScriptPromise(n){var r=1<arguments.length&&void 0!==arguments[1]&&arguments[1];return new Promise(function(t,e){loadScript(n,function(e){t(e)},r)})}function checkLoadScript(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"src",n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;"innerHTML"==t?checkScript(t,e[t])||loadScript(e,n):e.attrs&&e.attrs[t]&&!checkScript(t,e.attrs[t])&&loadScript(e,n)}function loadLink(e){var t=document.head,n=document.createElement("link");if(n.rel="stylesheet",n.href=e.href,e.attrs)for(var r=0,i=Object.entries(e.attrs);r<i.length;r++){var o=_slicedToArray(i[r],2),a=o[0],o=o[1],o=void 0===o||o;n.setAttribute(a,o)}t.appendChild(n)}function checkLink(e,t){return checkAttr(document.styleSheets,e,t)}function checkLoadLink(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"href";e.attrs&&e.attrs[t]&&!checkLink(t,e.attrs[t])&&loadLink(e)}function sendMessage(e){return _sendMessage.apply(this,arguments)}function _sendMessage(){return(_sendMessage=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",p(browser.runtime.sendMessage,t).catch(function(e){return null}));case 1:case"end":return e.stop()}},e)}))).apply(this,arguments)}function XMLRequest(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:null;return null==t&&(t={method:"GET"}),t.cache=t.cache||"default",fetch(e,t).then(function(e){return e})}function syncStorageGet(e){return _syncStorageGet.apply(this,arguments)}function _syncStorageGet(){return(_syncStorageGet=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,p(browser.storage.sync.get.bind(browser.storage.sync),t).catch(function(e){});case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e)}))).apply(this,arguments)}function syncStorageSet(e){return _syncStorageSet.apply(this,arguments)}function _syncStorageSet(){return(_syncStorageSet=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,p(browser.storage.sync.set.bind(browser.storage.sync),t).catch(function(e){});case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e)}))).apply(this,arguments)}function syncStorageRemove(e){return _syncStorageRemove.apply(this,arguments)}function _syncStorageRemove(){return(_syncStorageRemove=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,p(browser.storage.sync.remove.bind(browser.storage.sync),t).catch(function(e){});case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e)}))).apply(this,arguments)}function localStorageGet(e){return _localStorageGet.apply(this,arguments)}function _localStorageGet(){return(_localStorageGet=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,p(browser.storage.local.get.bind(browser.storage.local),t).catch(function(e){});case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e)}))).apply(this,arguments)}function localStorageSet(e){return _localStorageSet.apply(this,arguments)}function _localStorageSet(){return(_localStorageSet=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,p(browser.storage.local.set.bind(browser.storage.local),t).catch(function(e){});case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e)}))).apply(this,arguments)}function localStorageRemove(e){return _localStorageRemove.apply(this,arguments)}function _localStorageRemove(){return(_localStorageRemove=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,p(browser.storage.local.remove.bind(browser.storage.local),t).catch(function(e){});case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e)}))).apply(this,arguments)}function isDocumentReady(){return!(!document.head&&!document.documentElement)}function toBlobURL(e,t){return _toBlobURL.apply(this,arguments)}function _toBlobURL(){return(_toBlobURL=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,n){var r,i=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return r=2<i.length&&void 0!==i[2]?i[2]:void 0,e.next=3,fetch(t,r);case 3:return e.next=5,e.sent.arrayBuffer();case 5:return r=e.sent,r=new Blob([r],{type:n}),e.abrupt("return",URL.createObjectURL(r));case 8:case"end":return e.stop()}},e)}))).apply(this,arguments)}function mediaIsMP4(e){return 0===e[0]&&0===e[1]&&0===e[2]&&(24===e[3]||32===e[3])&&102===e[4]&&116===e[5]&&121===e[6]&&112===e[7]}function mediaIsTs(e){if(71!==e[0])return!1;for(var t=188;t<Math.min(e.length,940);t+=188)if(71!==e[t])return!1;return!0}function determineStreamType(e){if(!e||"string"!=typeof e)return null;var t=Math.min(e.length,1e3),t=e.slice(0,t);return t.includes("#EXTM3U")?"hls":/\s*<MPD\s/.test(t)?"dash":null}function getResponseInfoSafe(e){return _getResponseInfoSafe.apply(this,arguments)}function _getResponseInfoSafe(){return(_getResponseInfoSafe=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var n,r=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return n=1<r.length&&void 0!==r[1]?r[1]:{},e.abrupt("return",getResponseInfo(t,n).catch(function(e){return null}));case 2:case"end":return e.stop()}},e)}))).apply(this,arguments)}function getResponseInfo(e){return _getResponseInfo.apply(this,arguments)}function _getResponseInfo(){return(_getResponseInfo=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var n,r,i,o,a,s=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return r=(a=1<s.length&&void 0!==s[1]?s[1]:{}).init,n=void 0===r?null:r,r=a.redirect,r=void 0===r?"follow":r,a=a.abortData,i=void 0===a||a,o=new AbortController,a=o.signal,n=null!=n?n:{headers:{accept:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9"},credentials:"include",redirect:r,method:"GET"},e.abrupt("return",XMLRequest(t,_objectSpread(_objectSpread({},n),{},{signal:a})).then(function(e){return i&&o.abort(),e}));case 6:case"end":return e.stop()}},e)}))).apply(this,arguments)}function setHeaders(e,t){return _setHeaders.apply(this,arguments)}function _setHeaders(){return(_setHeaders=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,n){var r,i,o,a,s;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:for(r=[],i=[],o=Date.now()%Math.pow(2,31),a=0;a<t.length;a++)s=a+o,t[a]&&t[a].startsWith("http")&&(r.push({id:s,priority:1,action:{type:"modifyHeaders",requestHeaders:n.map(function(e){return{operation:e.operation,header:e.header,value:e.value}})},condition:{urlFilter:"||".concat(new URL(t[a]).hostname)}}),i.push(s));return e.next=6,browser.declarativeNetRequest.updateSessionRules({addRules:r,removeRuleIds:i});case 6:return e.abrupt("return",i);case 7:case"end":return e.stop()}},e)}))).apply(this,arguments)}function removeRulesForTabs(e){return _removeRulesForTabs.apply(this,arguments)}function _removeRulesForTabs(){return(_removeRulesForTabs=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var n;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,chrome.declarativeNetRequest.getSessionRules();case 2:if(n=e.sent,0<(n=n.filter(function(e){return null===(e=e.condition)||void 0===e||null===(e=e.tabIds)||void 0===e?void 0:e.some(function(e){return t.includes(e)})}).map(function(e){return e.id})).length)return e.next=7,chrome.declarativeNetRequest.updateSessionRules({removeRuleIds:n});e.next=7;break;case 7:case"end":return e.stop()}},e)}))).apply(this,arguments)}function updateSessionRules(e,t){return _updateSessionRules.apply(this,arguments)}function _updateSessionRules(){return(_updateSessionRules=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,n){var r,i,o,a,s,l,u,c,d,f=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:for(r=(o=2<f.length&&void 0!==f[2]?f[2]:{}).tabIds,i=void 0===r?null:r,o=o.attrs,a=void 0===o?[]:o,n&&a.push({header:"Referer",value:n}),s=[],l=[],u=Date.now()%Math.pow(2,31),c=0;c<t.length;c++)d=c+u,t[c]&&t[c].startsWith("http")&&(s.push({id:d,priority:1,action:{type:"modifyHeaders",requestHeaders:a.map(function(e){return{operation:(null==e?void 0:e.operation)||"set",header:e.header,value:e.value}})},condition:{urlFilter:"||".concat(new URL(t[c]).hostname),tabIds:i||[browser.tabs.TAB_ID_NONE]}}),l.push(d));return e.prev=7,e.next=10,browser.declarativeNetRequest.updateSessionRules({addRules:s,removeRuleIds:l});case 10:e.next=19;break;case 12:if(e.prev=12,e.t0=e.catch(7),i)return e.next=17,removeRulesForTabs(i);e.next=17;break;case 17:return e.next=19,browser.declarativeNetRequest.updateSessionRules({addRules:s,removeRuleIds:l});case 19:return e.abrupt("return",l);case 20:case"end":return e.stop()}},e,null,[[7,12]])}))).apply(this,arguments)}function filterHeaders(){var e,t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},n=["x-client-data","referer","user-agent","origin","cache-control","pragma","accept-encoding","accept-language","sec-ch-ua","sec-ch-ua-mobile","sec-ch-ua-platform","sec-fetch-dest","sec-fetch-mode","sec-fetch-site"],r={};for(e in t)n.includes(e)||(r[e]=t[e]);return r}function playVideoLink(p,e){var o=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null,t=3<arguments.length&&void 0!==arguments[3]?arguments[3]:["Html5","Shaka"],n=(null==p?void 0:p.download_url)||(null==p?void 0:p.url),r="video/mp4",r=n&&"video"!==p.type||null==p||!p.manifestUrl?(0<n.indexOf(".webm")?r="video/webm":0<n.indexOf(".flv")?r="video/x-flv":0<n.indexOf(".mpd")?r="application/dash+xml":0<n.indexOf(".m3u8")&&(r="application/x-mpegURL"),{type:r,src:updatePandavideoToken(n)}):("hls"===p.streamType?r="application/x-mpegURL":"dash"===p.streamType&&(r="application/dash+xml"),{type:r,src:updatePandavideoToken(p.manifestUrl)});videojs.log.level="debug";var h=videojs(e,{controlBar:{fullscreenToggle:!1},techOrder:t,playbackRates:[.5,1,1.25,1.5,2,4]});return h.ready(function(){h.controlBar.subsCapsButton.hide()}),h.src(r),h.on("loadedmetadata",function(){if("audio"==p.type&&h.audioOnlyMode(!0),"Shaka"==h.techName_){var e,t=h.tech(!0).shaka_;t.configure({abr:{enabled:!1}});var n,r="audio"==p.type?p.language:(null==p||null===(e=p.audioLink)||void 0===e?void 0:e.language)||null;!r||(n=t.getAudioLanguagesAndRoles().filter(function(e){return e.language==r})).length&&t.selectAudioLanguage(n[0].language,n[0].role),"video"!=p.type||(n=t.getVariantTracks().filter(function(e){return!("variant"!==e.type||(null==p?void 0:p.width)!=(null==e?void 0:e.width)||(null==p?void 0:p.height)!=(null==e?void 0:e.height)||null!=p&&p.bandwidth&&(null==p?void 0:p.bandwidth)!=((null==e?void 0:e.videoBandwidth)||(null==e?void 0:e.bandwidth)))})).length&&t.selectVariantTrack(n[0],!0)}else{var i,o="audio"==p.type?p.name:(null==p||null===(i=p.audioLink)||void 0===i?void 0:i.name)||null;if(!o||(i=Array.from(h.audioTracks()).find(function(e){return e.label===o}))&&(i.enabled=!0),"video"==p.type){var a=h.qualityLevels().levels_;if(1<a.length&&(p.width&&p.height||p.bandwidth)&&1<a.length&&p){for(var s=p.width&&p.height?p.width*p.height:null,l=p.bandwidth||null,u=!1,c=0;c<a.length;c++){var d=a[c],f=s&&d.width*d.height===s||l&&d.bitrate===l;d.enabled=f,u=u||f}u||(a[0].enabled=!0)}}}}),h.on("xhr-hooks-ready",function(){isPrivacyCombrUrl(h.currentSrc())&&h.tech(!0).vhs.xhr.onRequest(function(e){return e.beforeSend=function(t){var e,n=updatePrivacyCombrHeaders(t.url,filterHeaders((null==p||null===(e=p.init)||void 0===e?void 0:e.headers)||{}));Object.keys(n).forEach(function(e){t.setRequestHeader(e,n[e])})},e});var i=!1;h.tech(!0).vhs.xhr.onResponse(function(e,t,n){var r;403==n.statusCode&&0==i&&isPandavideoTokenUrl(h.currentSrc())&&(i=!0,r=(null==p||null===(r=p.init)||void 0===r||null===(r=r.headers)||void 0===r?void 0:r.referer)||o||playUrl,updateSessionRules([e.url],r))})}),h.on(["loadstart","play","playing","firstplay","pause","ended","adplay","adplaying","adfirstplay","adpause","adended","contentplay","contentplaying","contentfirstplay","contentpause","contentended","contentupdate","loadeddata","loadedmetadata"],function(e){}),h}function getDataFromBg(e){return _getDataFromBg.apply(this,arguments)}function _getDataFromBg(){return(_getDataFromBg=_asyncToGenerator(_regeneratorRuntime().mark(function e(l){var u,c,t=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return u=1<t.length&&void 0!==t[1]?t[1]:{},c=2<t.length&&void 0!==t[2]?t[2]:location.href,e.abrupt("return",new Promise(function(n,r){function i(){e&&(clearTimeout(e),e=null)}function o(e){if(e.message==="".concat(l,"-chunk")&&e.uniqueEventName===a&&(i(),s+=e.data,e.done))try{chrome.runtime.onMessage.removeListener(o);var t=JSON.parse(s);n(t)}catch(e){r(new Error("Error parsing tab data: ".concat(e.message,", ").concat(c,", ").concat(l,", ").concat(JSON.stringify(u))))}}var a="".concat(l,"-").concat(Date.now(),"-").concat(Math.random()),s="",e=null;chrome.runtime.onMessage.addListener(o),sendMessage(_objectSpread({message:l,uniqueEventName:a},u)).then(function(e){i(),e.data&&(chrome.runtime.onMessage.removeListener(o),n(e.data))}).catch(function(e){r(e)}),e=setTimeout(function(){chrome.runtime.onMessage.removeListener(o),r(new Error("".concat(l," timed out")))},1e4)}));case 3:case"end":return e.stop()}},e)}))).apply(this,arguments)}function _linkClickDownload(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"",n=document.createElement("a");n.setAttribute("download",configuration.BRAND+"/"+t),n.setAttribute("target","_blank"),n.href=e,document.body.appendChild(n),n.click(),n.remove()}function _backgroundDownload2(e,t){return _backgroundDownload.apply(this,arguments)}function _backgroundDownload(){return(_backgroundDownload=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,n){var r,i,o,a=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(i={message:"download-video-link",url:t,fileName:n},"object"==_typeof(r=2<a.length&&void 0!==a[2]?a[2]:{}))for(o in r)i[o]=r[o];return e.next=5,sendMessage(i);case 5:if(e.t0=e.sent,e.t0){e.next=8;break}e.t0={};case 8:return e.abrupt("return",e.t0);case 9:case"end":return e.stop()}},e)}))).apply(this,arguments)}var AbstractDownloader=(_defineProperty(_class=function(){"use strict";function n(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:null,t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:null;_classCallCheck(this,n),this.stateListener=e,this.progressListener=t}var r,i,t,o;return _createClass(n,[{key:"handleProgress",value:function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{};this.progressListener&&e&&this.progressListener(e,t,n)}},{key:"handleStateChange",value:function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:"";this.stateListener&&e&&this.stateListener(e,t,n)}},{key:"xmlDownload",value:(o=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,n){var r,i,o,a,s,l,u,c,d,f,p,h,m=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return o=2<m.length&&void 0!==m[2]?m[2]:{},r=new AbortController,i=null,o=o.timeoutMillis,i=setTimeout(function(){r.abort()},void 0===o?3e4:o),this.handleStateChange(n,LinkDownloader.STATE.START),e.prev=6,e.next=9,XMLRequest(t.download_url,{signal:r.signal});case 9:if(a=e.sent,clearTimeout(i),a.ok){e.next=13;break}throw new Error("Network response was not ok");case 13:s=null,l=t.title+t.ext,null!==(f=a.headers.get("content-length"))&&(s=parseInt(f,10)),u=a.body.getReader(),c=0,d=[];case 20:return e.next=23,u.read();case 23:if(p=e.sent,f=p.done,p=p.value,f)return h=URL.createObjectURL(new Blob(d)),this.linkClickDownload(h,l),URL.revokeObjectURL(h),this.handleProgress(n,1),this.handleStateChange(n,LinkDownloader.STATE.COMPLETE),e.abrupt("break",39);e.next=33;break;case 33:c+=p.length,d.push(p),null!==s&&(h=Math.floor(c/s*100),this.handleProgress(n,h)),e.next=20;break;case 39:e.next=47;break;case 41:e.prev=41,e.t0=e.catch(6),clearTimeout(i),r.abort(),this.handleStateChange(n,LinkDownloader.STATE.FAILED);case 47:case"end":return e.stop()}},e,this,[[6,41]])})),function(e,t){return o.apply(this,arguments)})},{key:"linkClickDownload",value:function(e){return _linkClickDownload(e,1<arguments.length&&void 0!==arguments[1]?arguments[1]:"")}},{key:"windowOpen",value:(t=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:window.open(t);case 1:case"end":return e.stop()}},e)})),function(e){return t.apply(this,arguments)})},{key:"backgroundDownload",value:(i=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,n){var r,i=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return r=2<i.length&&void 0!==i[2]?i[2]:{},e.abrupt("return",_backgroundDownload2(t,n,r));case 2:case"end":return e.stop()}},e)})),function(e,t){return i.apply(this,arguments)})},{key:"download",value:(r=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,n){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:throw new Error("You have to implement the method doSomething!");case 2:case"end":return e.stop()}},e)})),function(e,t){return r.apply(this,arguments)})}]),n}(),"STATE",{START:"start",DOWNLOADING:"downloading",PAUSED:"paused",INTERRUPTED:"interrupted",COMPLETE:"complete",FAILED:"failed"}),_class),LinkDownloader=function(){"use strict";_inherits(o,AbstractDownloader);var n,r=_createSuper(o);function o(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:null,t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:null;return _classCallCheck(this,o),(t=r.call(this,e,t)).listener=t.initListener(),t}return _createClass(o,[{key:"initListener",value:function(){function e(e,t,n){switch(e.message){case"download-changed":r.handleStateChange(e.key,e.state,(null==e?void 0:e.errmsg)||""),n();break;case"progress-changed":r.handleProgress(e.key,e.progress),n();break;case"complete":n()}}var r=this;return browser.runtime.onMessage.addListener(e),e}},{key:"dispose",value:function(){this.listener&&(browser.runtime.onMessage.removeListener(this.listener),this.listener=null),this.stateListener=null,this.progressListener=null}},{key:"download",value:(n=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,n){var r,i;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return i=t.title,new RegExp("."+t.ext+"$").test(t.title)||(i=t.title+"."+t.ext),this.handleStateChange(n,o.STATE.START),r=t.download_url,e.prev=5,e.next=8,this.backgroundDownload(r,i);case 8:"result"in(i=e.sent)&&0<i.result?this.handleStateChange(n,o.STATE.DOWNLOADING):this.handleStateChange(n,o.STATE.FAILED),e.next=15;break;case 12:e.prev=12,e.t0=e.catch(5),this.xmlDownload(t);case 15:case"end":return e.stop()}},e,this,[[5,12]])})),function(e,t){return n.apply(this,arguments)})}]),o}();function isPandavideoUrl(e){return/https?:\/\/(?:[^.]+\.)*(?:[^/]+)\.pandavideo\.com(?:\.[^/]+)?\/.*\.m3u8/.test(e)}function isPandavideoNoTokenUrl(e){return/https?:\/\/(?:[^.]+\.)*(?:[^/]+)\.pandavideo\.com(?:\.[^/]+)?\/.*\.m3u8(?:(?!.token=[a-zA-Z0-9]{64})[?&][^&]*)?$/.test(e)}function isPandavideoTokenUrl(e){return _wrapRegExp(/https?:\/\/(?:[^.]+\.)*(?:[^/]+)\.pandavideo\.com(?:\.[^/]+)?\/.*\.m3u8\?(?:[^&]+&)?token=([a-zA-Z0-9]{64})/,{token:1}).test(e)}function updatePandavideoToken(e){var t=_wrapRegExp(/https?:\/\/(?:[^.]+\.)*(?:[^/]+)\.pandavideo\.com(?:\.[^/]+)?\/.*\.m3u8\?(?:[^&]+&)?token=([a-zA-Z0-9]{64})/,{token:1}),t=e.match(t);return t&&(e=e.replace(t.groups.token,function(){for(var e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",t="",n=0;n<64;n++){var r=Math.floor(Math.random()*e.length);t+=e.charAt(r)}return t}())),e}function isPolyvNetUrl(e){return!(null==e||!e.match(/^https?:\/\/hls\.videocc\.net/))}function getPolyvNetManifestId(e){e=null==e?void 0:e.match(/^https?:\/\/hls\.videocc\.net\/.*?\/([^\/_]+)(_\d+\.key|(_\d+)?.m3u8)/);return e?e[1]:null}function getPolyvNetManifestToken(e){e=null==e?void 0:e.match(/^https?:\/\/hls\.videocc\.net\/.*?\/([^\/]+)_\d+\.key\?token=([^&]+)/);return e?{manifestId:e[1],token:e[2]}:null}function updatePolyvNetKeyToken(e,t){var n;try{if(null!=e&&null!==(n=e[0])&&void 0!==n&&null!==(n=n.segments)&&void 0!==n&&null!==(n=n[0])&&void 0!==n&&null!==(n=n.key)&&void 0!==n&&null!==(n=n.url)&&void 0!==n&&n.match(/^https?:\/\/hls\.videocc\.net\/.*?\/([^\/]+)_\d+\.key/)){var r=_createForOfIteratorHelper(e);try{for(r.s();!(i=r.n()).done;){var i=i.value,o=_createForOfIteratorHelper(null==i?void 0:i.segments);try{for(o.s();!(s=o.n()).done;){var a=s.value,s=new URL(a.key.url);s.searchParams.set("token",t),a.key.url=s.toString()}}catch(e){o.e(e)}finally{o.f()}}}catch(e){r.e(e)}finally{r.f()}}}catch(e){}return e}function isBoomstreamUrl(e){return!(null==e||!e.match(/^https?:\/\/(?:[^\.]+\.)*?boomstream\.com/))}function updateLoomstreamMediaPlaylist(e,t,n){var r=/(?:#EXT-X-MEDIA-READY: *(\w+))\r?\n?/.exec(e);if(!r||!t)return e;var i="bv17b7v24iedrvzoaihwvugef89ewy7834f35",o="001a5068005b176d560504";function a(e,t){for(var n=[];t.length<e.length;)t+=t;for(var r=0;r<e.length;r+=2){var i=e.substring(r,r+2),i=parseInt(i,16)^t.charCodeAt(r/2);n.push(String.fromCharCode(i))}return n.join("")}var s=a(r=r[1],a(o,i)),r=(r=s,new Uint8Array([].map.call(r,function(e){return e.charCodeAt(0)})).buffer),r="0x"+(r=r.slice(20,36),Array.prototype.map.call(new Uint8Array(r),function(e){return("00"+e.toString(16)).slice(-2)}).join("")),i=n+"/process/"+function(e,t){for(var n=[];t.length<e.length;)t+=t;for(var r=0;r<e.length;r++){var i=(e.charCodeAt(r)^t.charCodeAt(r)).toString(16);i.length<2&&(i="0"+i),n.push(i)}return n.join("")}(s.slice(0,20)+t,a(o,i));return(e=e.replace("[KEY]",i)).replace("[IV]",r)}function isPrivacyCombrUrl(e){return!(null==e||!e.match(/^https?:\/\/(?:[^\.]+\.)*?privacy.com\.br/))}function updatePrivacyCombrHeaders(e,t){if(t["x-content-uri"]&&isPrivacyCombrUrl(e))try{var n=new URL(e).pathname.split("/").filter(function(e){return e});0<n.length&&(t["x-content-uri"]=n[n.length-1])}catch(e){}return t}function middleTruncate_old(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:64;if(e.length<=t)return e;var n=Math.ceil((t-3)/2),t=Math.floor((t-3)/2);return e.slice(0,n)+"..."+e.slice(e.length-t)}function middleTruncate(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:64,n=new TextEncoder;if(n.encode(e).length<=t)return e;for(var r=Math.ceil((t-3)/2),i=Math.floor((t-3)/2),o="",a="",s=0;s<e.length;s++){var l=e[s];if(n.encode(o+l).length>r)break;o+=l}for(var u=e.length-1;0<=u;u--){var c=e[u];if(n.encode(c+a).length>i)break;a=c+a}return o+"..."+a}function truncate_utf8_bak(e,t){if("string"!=typeof e)throw new Error("Input must be string");for(var n,r,i,o,a=e.length,s=0,l=0;l<a;l+=1){if(n=e.charCodeAt(l),r=e[l],55296<=(o=n)&&o<=56319&&(56320<=(i=e.charCodeAt(l+1))&&i<=57343)&&(r+=e[l+=1]),(s+=unescape(encodeURIComponent(r)).length)===t)return e.slice(0,l+1);if(t<s)return e.slice(0,l-r.length+1)}return e}function truncate_utf8(e,t){var n=!(2<arguments.length&&void 0!==arguments[2])||arguments[2];if("string"!=typeof e)throw new Error("Input must be a string");var r=_toConsumableArray(e),i=0;if(n)for(var o=0;o<r.length;o++){var a=r[o];if(t<(i+=unescape(encodeURIComponent(a)).length))return r.slice(0,o).join("")}else for(var s=r.length-1;0<=s;s--){var l=r[s];if(t<(i+=unescape(encodeURIComponent(l)).length))return r.slice(s+1).join("")}return e}function sanitizeFilename(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:100;try{return truncate_utf8(e=e.replace(/^\./,"_").replace(/[\u0000-\u001f\u007f-\u009f\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200b-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,"").replace(/&quot;/g,"").replace(/&amp;/g,"&").replace(/[\\/:*?<>|~↵"\t]/g,"_"),t)}catch(e){}}function reportMsg(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"error";chrome.runtime.sendMessage({message:"sentry-report-msg",data:{message:e,level:t}},function(){})}function sendGaPreview(e,t){return sendMessage({message:"ga-msg",type:"pageview",title:e,location:t,params:2<arguments.length&&void 0!==arguments[2]?arguments[2]:{}},function(){})}function sendGaEvent(e){return sendMessage({message:"ga-msg",type:"event",name:e,params:1<arguments.length&&void 0!==arguments[1]?arguments[1]:{}},function(){})}function sendGaException(e){return sendMessage({message:"ga-msg",type:"exception",error:e,params:1<arguments.length&&void 0!==arguments[1]?arguments[1]:{}},function(){})}function onError(r){"object"==_typeof(window.onerror)&&(window.onerror=function(e,t,n){sendGaException("page: ".concat(r,": url:").concat(t," err: ").concat(e))})}function localizeHtmlPage(){for(var e=document.getElementsByTagName("html"),t=0;t<e.length;t++){var n=e[t],r=n.innerHTML.toString(),i=r.replace(/__MSG_(\w+)__/g,function(e,t){return t?browser.i18n.getMessage(t):""});i!=r&&(n.innerHTML=i)}}function url_host_split_bak(e){try{var t=e.match(/^(?:(?:https?:)?\/\/)?((?:[\w-]+\.)?(([\w-]+)\.[^?\/]+))/);return t.shift(),t.reverse(),t}catch(e){return null}}function url_host_split(e){try{for(var t=[],n=e.match(/^(?:(?:https?:)?\/\/)?((?:[\w-]+\.)?[^?\/]+)/)[1].split("."),r=n.length,i=0;i<r-1;i++)t.unshift(n.join(".")),2<n.length&&n.shift();return t.unshift(n[0]),t}catch(e){return null}}function storeGeneral(e){return _storeGeneral.apply(this,arguments)}function _storeGeneral(){return(_storeGeneral=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,syncStorageSet(_defineProperty({},"".concat(browser.runtime.id,"_general"),t));case 2:case"end":return e.stop()}},e)}))).apply(this,arguments)}function getStoredGeneral(){return _getStoredGeneral.apply(this,arguments)}function _getStoredGeneral(){return(_getStoredGeneral=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t,n;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t="".concat(browser.runtime.id,"_general"),e.next=3,syncStorageGet(t);case 3:return n=e.sent,e.abrupt("return",(null==n?void 0:n[t])||{});case 5:case"end":return e.stop()}},e)}))).apply(this,arguments)}function storeFilter(e){return _storeFilter.apply(this,arguments)}function _storeFilter(){return(_storeFilter=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,syncStorageSet(_defineProperty({},"".concat(browser.runtime.id,"_filter"),t));case 2:case"end":return e.stop()}},e)}))).apply(this,arguments)}function getStoredFilter(){return _getStoredFilter.apply(this,arguments)}function _getStoredFilter(){return(_getStoredFilter=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t,n;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t="".concat(browser.runtime.id,"_filter"),e.next=3,syncStorageGet(t);case 3:return n=e.sent,e.abrupt("return",(null==n?void 0:n[t])||{});case 5:case"end":return e.stop()}},e)}))).apply(this,arguments)}function updateFilter(e,t){return _updateFilter.apply(this,arguments)}function _updateFilter(){return(_updateFilter=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,n){var r,i,o,a,s,l,u;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:for(o in i=function(e,t,n){return void 0!==e&&e>=t[0]&&e<=t[1]?e:n},r={},n)l=t[o]||{},a=void 0===(u=l.enable)?n[o].enable:u,s=l.size,u=n[o],l=u.range,u=u.size,r[o]={enable:a,size:i(s,l,u)};return e.abrupt("return",r);case 4:case"end":return e.stop()}},e)}))).apply(this,arguments)}function needFilter(e,t){return!t||(e<t.size||0==t.enable)}function getStoredCspList(){return _getStoredCspList.apply(this,arguments)}function _getStoredCspList(){return(_getStoredCspList=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t,n;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t="".concat(browser.runtime.id,"_csplist"),e.next=3,syncStorageGet(t);case 3:return n=e.sent,e.abrupt("return",(null==n?void 0:n[t])||null);case 5:case"end":return e.stop()}},e)}))).apply(this,arguments)}function storeCspList(e){return _storeCspList.apply(this,arguments)}function _storeCspList(){return(_storeCspList=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,syncStorageSet(_defineProperty({},"".concat(browser.runtime.id,"_csplist"),t));case 2:case"end":return e.stop()}},e)}))).apply(this,arguments)}function addElement(e,t){switch(2<arguments.length&&void 0!==arguments[2]?arguments[2]:"prepend"){case"insertBefore":t.insertAdjacentElement("beforebegin",e);break;case"insertAfter":t.insertAdjacentElement("afterend",e);break;case"prepend":t.prepend(e);break;case"append":t.append(e);break;case"appendChild":t.appendChild(e)}}var Base64={_keyStr:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",encode:function(e){var t,n,r,i,o,a,s="",l=0;for(e=Base64._utf8_encode(e);l<e.length;)r=(a=e.charCodeAt(l++))>>2,i=(3&a)<<4|(t=e.charCodeAt(l++))>>4,o=(15&t)<<2|(n=e.charCodeAt(l++))>>6,a=63&n,isNaN(t)?o=a=64:isNaN(n)&&(a=64),s=s+this._keyStr.charAt(r)+this._keyStr.charAt(i)+this._keyStr.charAt(o)+this._keyStr.charAt(a);return s},decode:function(e){var t,n,r,i,o,a="",s=0;for(e=e.replace(/[^A-Za-z0-9+/=]/g,"");s<e.length;)t=this._keyStr.indexOf(e.charAt(s++))<<2|(r=this._keyStr.indexOf(e.charAt(s++)))>>4,n=(15&r)<<4|(i=this._keyStr.indexOf(e.charAt(s++)))>>2,r=(3&i)<<6|(o=this._keyStr.indexOf(e.charAt(s++))),a+=String.fromCharCode(t),64!=i&&(a+=String.fromCharCode(n)),64!=o&&(a+=String.fromCharCode(r));return a=Base64._utf8_decode(a)},_utf8_encode:function(e){var t="";e=e.replace(/\r\n/g,"\n");for(var n=0;n<e.length;n++){var r=e.charCodeAt(n);r<128?t+=String.fromCharCode(r):(127<r&&r<2048?t+=String.fromCharCode(r>>6|192):(t+=String.fromCharCode(r>>12|224),t+=String.fromCharCode(r>>6&63|128)),t+=String.fromCharCode(63&r|128))}return t},_utf8_decode:function(e){for(var t,n,r="",i=0,o=t=0;i<e.length;)(o=e.charCodeAt(i))<128?(r+=String.fromCharCode(o),i++):191<o&&o<224?(t=e.charCodeAt(i+1),r+=String.fromCharCode((31&o)<<6|63&t),i+=2):(t=e.charCodeAt(i+1),n=e.charCodeAt(i+2),r+=String.fromCharCode((15&o)<<12|(63&t)<<6|63&n),i+=3);return r}};function buf2hex(e){return _toConsumableArray(new Uint8Array(e)).map(function(e){return e.toString(16).padStart(2,"0")}).join("")}function hex2buf(e){return new Uint8Array(e.match(/[\da-f]{2}/gi).map(function(e){return parseInt(e,16)}))}function genUniqueId(){for(var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:8,t="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",n="",r=0;r<e;r++)n+=t[Math.floor(Math.random()*t.length)];return n}!function(e,t){"object"==("undefined"==typeof exports?"undefined":_typeof(exports))&&"object"==("undefined"==typeof module?"undefined":_typeof(module))?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==("undefined"==typeof exports?"undefined":_typeof(exports))?exports.FFmpegWASM=t():e.FFmpegWASM=t()}(self,function(){"use strict";var i,e,r={d:function(e,t){for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},o:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r:function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};r.r(t),r.d(t,{FFmpeg:function(){return s}}),(e=i=i||{}).LOAD="LOAD",e.EXEC="EXEC",e.WRITE_FILE="WRITE_FILE",e.READ_FILE="READ_FILE",e.DELETE_FILE="DELETE_FILE",e.RENAME="RENAME",e.CREATE_DIR="CREATE_DIR",e.LIST_DIR="LIST_DIR",e.DELETE_DIR="DELETE_DIR",e.ERROR="ERROR",e.DOWNLOAD="DOWNLOAD",e.PROGRESS="PROGRESS",e.LOG="LOG",e.MOUNT="MOUNT",e.UNMOUNT="UNMOUNT";var n,l=(n=0,function(){return n++}),u=(new Error("unknown message type"),new Error("ffmpeg is not loaded, call `await ffmpeg.load()` first")),o=new Error("called FFmpeg.terminate()");new Error("failed to import ffmpeg-core.js");var c=new WeakMap,d=new WeakMap,f=new WeakMap,a=new WeakMap,p=new WeakMap,h=new WeakMap,m=new WeakMap,s=function(){function t(){var s=this,e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:null;_classCallCheck(this,t),_classPrivateFieldInitSpec(this,c,{writable:!0,value:null}),_classPrivateFieldInitSpec(this,d,{writable:!0,value:{}}),_classPrivateFieldInitSpec(this,f,{writable:!0,value:{}}),_classPrivateFieldInitSpec(this,a,{writable:!0,value:[]}),_classPrivateFieldInitSpec(this,p,{writable:!0,value:[]}),_defineProperty(this,"loaded",!1),_defineProperty(this,"createWorker",void 0),_classPrivateFieldInitSpec(this,h,{writable:!0,value:function(){_classPrivateFieldGet(s,c)&&(_classPrivateFieldGet(s,c).onmessage=function(e){var t=e.data,n=t.id,e=t.type,r=t.data;switch(e){case i.LOAD:s.loaded=!0,_classPrivateFieldGet(s,d)[n](r);break;case i.MOUNT:case i.UNMOUNT:case i.EXEC:case i.WRITE_FILE:case i.READ_FILE:case i.DELETE_FILE:case i.RENAME:case i.CREATE_DIR:case i.LIST_DIR:case i.DELETE_DIR:_classPrivateFieldGet(s,d)[n](r);break;case i.LOG:_classPrivateFieldGet(s,a).forEach(function(e){return e(r)});break;case i.PROGRESS:_classPrivateFieldGet(s,p).forEach(function(e){return e(r)});break;case i.ERROR:_classPrivateFieldGet(s,f)[n](r)}delete _classPrivateFieldGet(s,d)[n],delete _classPrivateFieldGet(s,f)[n]})}}),_classPrivateFieldInitSpec(this,m,{writable:!0,value:function(e){var r=e.type,i=e.data,o=1<arguments.length&&void 0!==arguments[1]?arguments[1]:[],a=2<arguments.length?arguments[2]:void 0;return _classPrivateFieldGet(s,c)?new Promise(function(e,t){var n=l();_classPrivateFieldGet(s,c)&&_classPrivateFieldGet(s,c).postMessage({id:n,type:r,data:i},o),_classPrivateFieldGet(s,d)[n]=e,_classPrivateFieldGet(s,f)[n]=t,null!=a&&a.addEventListener("abort",function(){t(new DOMException("Message # ".concat(n," was aborted"),"AbortError"))},{once:!0})}):Promise.reject(u)}}),_defineProperty(this,"load",function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},t=e.classWorkerURL,n=void 0===t?"./worker.js":t,r=e.workerType,t=void 0===r?"classic":r,r=_objectWithoutProperties(e,_excluded),e=(1<arguments.length&&void 0!==arguments[1]?arguments[1]:{}).signal;return _classPrivateFieldGet(s,c)||(_classPrivateFieldSet(s,c,s.createWorker(new URL(n,location.href),{type:t})),_classPrivateFieldGet(s,h).call(s)),_classPrivateFieldGet(s,m).call(s,{type:i.LOAD,data:r},void 0,e)}),_defineProperty(this,"exec",function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:-1,n=(2<arguments.length&&void 0!==arguments[2]?arguments[2]:{}).signal;return _classPrivateFieldGet(s,m).call(s,{type:i.EXEC,data:{args:e,timeout:t}},void 0,n)}),_defineProperty(this,"terminate",function(){for(var e=0,t=Object.keys(_classPrivateFieldGet(s,f));e<t.length;e++){var n=t[e];_classPrivateFieldGet(s,f)[n](o),delete _classPrivateFieldGet(s,f)[n],delete _classPrivateFieldGet(s,d)[n]}_classPrivateFieldGet(s,c)&&(_classPrivateFieldGet(s,c).terminate(),_classPrivateFieldSet(s,c,null),s.loaded=!1)}),_defineProperty(this,"writeFile",function(e,t){var n=(2<arguments.length&&void 0!==arguments[2]?arguments[2]:{}).signal,r=[];return t instanceof Uint8Array&&r.push(t.buffer),_classPrivateFieldGet(s,m).call(s,{type:i.WRITE_FILE,data:{path:e,data:t}},r,n)}),_defineProperty(this,"mount",function(e,t,n){return _classPrivateFieldGet(s,m).call(s,{type:i.MOUNT,data:{fsType:e,options:t,mountPoint:n}},[])}),_defineProperty(this,"unmount",function(e){return _classPrivateFieldGet(s,m).call(s,{type:i.UNMOUNT,data:{mountPoint:e}},[])}),_defineProperty(this,"readFile",function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"binary",n=(2<arguments.length&&void 0!==arguments[2]?arguments[2]:{}).signal;return _classPrivateFieldGet(s,m).call(s,{type:i.READ_FILE,data:{path:e,encoding:t}},void 0,n)}),_defineProperty(this,"deleteFile",function(e){var t=(1<arguments.length&&void 0!==arguments[1]?arguments[1]:{}).signal;return _classPrivateFieldGet(s,m).call(s,{type:i.DELETE_FILE,data:{path:e}},void 0,t)}),_defineProperty(this,"rename",function(e,t){var n=(2<arguments.length&&void 0!==arguments[2]?arguments[2]:{}).signal;return _classPrivateFieldGet(s,m).call(s,{type:i.RENAME,data:{oldPath:e,newPath:t}},void 0,n)}),_defineProperty(this,"createDir",function(e){var t=(1<arguments.length&&void 0!==arguments[1]?arguments[1]:{}).signal;return _classPrivateFieldGet(s,m).call(s,{type:i.CREATE_DIR,data:{path:e}},void 0,t)}),_defineProperty(this,"listDir",function(e){var t=(1<arguments.length&&void 0!==arguments[1]?arguments[1]:{}).signal;return _classPrivateFieldGet(s,m).call(s,{type:i.LIST_DIR,data:{path:e}},void 0,t)}),_defineProperty(this,"deleteDir",function(e){var t=(1<arguments.length&&void 0!==arguments[1]?arguments[1]:{}).signal;return _classPrivateFieldGet(s,m).call(s,{type:i.DELETE_DIR,data:{path:e}},void 0,t)}),this.createWorker=e||function(e,t){return new Worker(e,t)}}return _createClass(t,[{key:"on",value:function(e,t){"log"===e?_classPrivateFieldGet(this,a).push(t):"progress"===e&&_classPrivateFieldGet(this,p).push(t)}},{key:"off",value:function(e,t){"log"===e?_classPrivateFieldSet(this,a,_classPrivateFieldGet(this,a).filter(function(e){return e!==t})):"progress"===e&&_classPrivateFieldSet(this,p,_classPrivateFieldGet(this,p).filter(function(e){return e!==t}))}}]),t}();return t});var Recording=function(){"use strict";function e(){_classCallCheck(this,e),_defineProperty(this,"tab",null),_defineProperty(this,"recorder",null),_defineProperty(this,"mediaChunks",[]),_defineProperty(this,"timeStart",null),_defineProperty(this,"timeEnd",null),_defineProperty(this,"cancel",!1),_defineProperty(this,"videoStream",null),_defineProperty(this,"micStream",null),this.init()}var t,n,r,i,o,a,s,l,u,c,d,f,h,m;return _createClass(e,[{key:"initListeners",value:function(){var i=this;window.addEventListener("beforeunload",this.stopRecording.bind(this)),browser.runtime.onMessage.addListener(function(){var r=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,n,r){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:e.t0=t.message,e.next="do-start-record"===e.t0?3:"do-cancel-record"===e.t0?6:"do-stop-record"===e.t0?10:13;break;case 3:return i.startRecording(t.tab),r(),e.abrupt("break",13);case 6:return i.cancel=!0,i.stopRecording(i.tab.id),r(),e.abrupt("break",13);case 10:return i.stopRecording(i.tab.id),r(),e.abrupt("break",13);case 13:case"end":return e.stop()}},e)}));return function(e,t,n){return r.apply(this,arguments)}}())}},{key:"init",value:(m=_asyncToGenerator(_regeneratorRuntime().mark(function e(){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return this.initListeners(),localizeHtmlPage(),e.next=4,this.getTargetTabInfo();case 4:case"end":return e.stop()}},e,this)})),function(){return m.apply(this,arguments)})},{key:"getTargetTabInfo",value:(h=_asyncToGenerator(_regeneratorRuntime().mark(function e(){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(this.tab){e.next=4;break}return e.next=3,sendMessage({message:"get-target-tab"});case 3:this.tab=e.sent;case 4:case"end":return e.stop()}},e,this)})),function(){return h.apply(this,arguments)})},{key:"getStreamId",value:(f=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,p(chrome.tabCapture.getMediaStreamId,{targetTabId:t});case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e)})),function(e){return f.apply(this,arguments)})},{key:"getCameraStream",value:(d=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t,n,r,i=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t=0<i.length&&void 0!==i[0]?i[0]:"default",r=this.getWidthHeight(),n=r.width,r=r.height,r={audio:!1,video:{deviceId:t?{exact:t}:void 0,width:{ideal:n},height:{ideal:r},frameRate:{ideal:30}}},e.next=5,navigator.mediaDevices.getUserMedia(r).then(function(e){return e}).catch(function(e){return navigator.mediaDevices.getUserMedia({audio:!1,video:!0}).then(function(e){return e}).catch(function(e){return null})});case 5:return r=e.sent,e.abrupt("return",r);case 7:case"end":return e.stop()}},e,this)})),function(){return d.apply(this,arguments)})},{key:"getMicStream",value:(c=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t,n=arguments;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t={mimeType:"video/webm;codecs=vp8,opus",audio:{deviceId:{exact:0<n.length&&void 0!==n[0]?n[0]:"default"},echoCancellation:!0,noiseSuppression:!0,autoGainControl:!0}},e.next=4,navigator.mediaDevices.getUserMedia(t).then(function(e){return e}).catch(function(){var t=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var n;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return n={mimeType:"video/webm;codecs=vp8,opus",audio:!0},e.abrupt("return",navigator.mediaDevices.getUserMedia(n).then(function(e){return e}).catch(function(e){return null}));case 2:case"end":return e.stop()}},e)}));return function(e){return t.apply(this,arguments)}}());case 4:return t=e.sent,e.abrupt("return",t);case 6:case"end":return e.stop()}},e)})),function(){return c.apply(this,arguments)})},{key:"getMediByCurrentTab",value:(u=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var n,r;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return r=this.getWidthHeight(),n=r.width,r=r.height,e.next=3,navigator.mediaDevices.getDisplayMedia({preferCurrentTab:!0,audio:!0,video:"Tab"===t&&{frameRate:30,width:{ideal:n},height:{ideal:r}}});case 3:return e.abrupt("return",e.sent);case 4:case"end":return e.stop()}},e,this)})),function(e){return u.apply(this,arguments)})},{key:"getMediBytStreamId",value:(l=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,n){var r,i;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return i=this.getWidthHeight(),r=i.width,i=i.height,e.next=3,navigator.mediaDevices.getUserMedia({audio:{mandatory:{chromeMediaSource:"tab",chromeMediaSourceId:t}},video:"Tab"===n&&{mandatory:{chromeMediaSource:"tab",chromeMediaSourceId:t,maxWidth:r,maxHeight:i,maxFrameRate:30}}});case 3:return e.abrupt("return",e.sent);case 4:case"end":return e.stop()}},e,this)})),function(e,t){return l.apply(this,arguments)})},{key:"getDisplayMedia",value:(s=_asyncToGenerator(_regeneratorRuntime().mark(function e(){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,navigator.mediaDevices.getDisplayMedia({video:!0,audio:!0});case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e)})),function(){return s.apply(this,arguments)})},{key:"playVideoStream",value:function(e){var t=document.getElementById("player");t&&(t.addEventListener("canplay",function(){this.volume=.75,this.muted=!0,this.play()}),t.setAttribute("controls","1"),t.srcObject=e)}},{key:"stopVideo",value:function(){document.getElementById("player").srcObject=null}},{key:"downloadBlobEx",value:(a=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,n){var r;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,_backgroundDownload2(t,n);case 2:0<(null==(r=e.sent)?void 0:r.result)||_linkClickDownload(t,n);case 4:case"end":return e.stop()}},e)})),function(e,t){return a.apply(this,arguments)})},{key:"getbitsPerSecond",value:function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:"1080p",t=128e3,n=25e5;return"4k"===e?(t=192e3,n=4e7):"1080p"===e?(t=192e3,n=8e6):"720p"===e?(t=128e3,n=5e6):"480p"===e?(t=96e3,n=25e5):"360p"===e?(t=96e3,n=1e6):"240p"===e&&(t=64e3,n=5e5),{audioBitsPerSecond:t,videoBitsPerSecond:n}}},{key:"getWidthHeight",value:function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:"1080p",t=1920,n=1080;return"4k"===e?(t=4096,n=2160):"1080p"===e?(t=1920,n=1080):"720p"===e?(t=1280,n=720):"480p"===e?(t=854,n=480):"360p"===e?(t=640,n=360):"240p"===e&&(t=426,n=240),{width:t,height:n}}},{key:"requestCropTarget",value:(o=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",new Promise(function(n,r){function e(e){var t=e.data;"res-crop-target"===t.action&&(t=(e=t.data).result,e=e.target,"start"===t?clearTimeout(i):"success"===t?(n(e),o()):"cancel"===t&&(r(new Error("Selection cancelled")),o()))}var i=setTimeout(function(){o(),r(new Error("Region selector not responding"))},5e3),o=function(){return window.removeEventListener("message",e)};window.addEventListener("message",e),window.parent.postMessage({action:"req-crop-target"},"*")}));case 1:case"end":return e.stop()}},e)})),function(e){return o.apply(this,arguments)})},{key:"hideRegionSelector",value:function(){window.parent.postMessage({action:"hide-region-selector"},"*")}},{key:"startRecording",value:(i=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var n,r,i,o,a,s,l,u,c=this;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if("recording"===(null===(n=this.recorder)||void 0===n?void 0:n.state))throw new Error("Called startRecording while recording is in progress.");e.next=2;break;case 2:if(!t.crop){e.next=10;break}if(inIframe())return e.next=6,this.requestCropTarget(t);e.next=9;break;case 6:t.cropTarget=e.sent,e.next=10;break;case 9:case 10:if(this.timeStart=Date.now(),this.tab=t,"Screen"==this.tab.mode)return e.next=15,this.getDisplayMedia();e.next=18;break;case 15:this.videoStream=e.sent,e.next=39;break;case 18:if(this.tab.camera)return e.next=21,this.getCameraStream(this.tab.cameraDeviceId);e.next=24;break;case 21:this.videoStream=e.sent,e.next=39;break;case 24:if(inIframe()&&"Audio"!=this.tab.mode)return e.next=27,this.getMediByCurrentTab(this.tab.mode);e.next=30;break;case 27:this.videoStream=e.sent,e.next=39;break;case 30:return e.next=32,this.getStreamId(this.tab.id);case 32:return n=e.sent,e.next=35,this.getMediBytStreamId(n,this.tab.mode);case 35:this.videoStream=e.sent,o=new AudioContext,o.createMediaStreamSource(this.videoStream).connect(o.destination);case 39:if(r=new AudioContext,i=r.createMediaStreamDestination(),0<this.videoStream.getAudioTracks().length&&(o=r.createMediaStreamSource(this.videoStream),a=r.createGain(),o.connect(a).connect(i)),this.tab.mic)return e.next=45,this.getMicStream(this.tab.micDeviceId);e.next=47;break;case 45:this.micStream=e.sent,this.micStream&&(a=r.createMediaStreamSource(this.micStream),s=r.createGain(),this.tab.micActive||(s.gain.value=0),a.connect(s).connect(i));case 47:if(l=new MediaStream,0<(null===(s=this.videoStream)||void 0===s?void 0:s.getVideoTracks().length)&&l.addTrack(this.videoStream.getVideoTracks()[0]),0<(null===(s=i.stream)||void 0===s?void 0:s.getAudioTracks().length)&&l.addTrack(i.stream.getAudioTracks()[0]),this.tab.crop&&0<l.getVideoTracks().length)return u=l.getVideoTracks()[0],e.next=54,u.cropTo(this.tab.cropTarget);e.next=54;break;case 54:l.getVideoTracks().forEach(function(e){return e.onended=function(){c.stopRecording()}}),inIframe()||this.playVideoStream(l),u="Audio"==this.tab.mode?"audio/webm":MediaRecorder.isTypeSupported("video/x-matroska;codecs=avc1")?"video/x-matroska;codecs=avc1":MediaRecorder.isTypeSupported("video/mp4;codecs=h264,aac")?"video/mp4;codecs=h264,aac":MediaRecorder.isTypeSupported("video/mp4;codecs=avc1.42E01E,mp4a.40.2")?"video/mp4;codecs=avc1.42E01E,mp4a.40.2":"video/webm;codecs=vp9,opus",u=_objectSpread(_objectSpread({mimeType:u},this.getbitsPerSecond()),{},{frameRate:30}),this.recorder=new MediaRecorder(l,u),this.recorder.ondataavailable=function(){var t=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:c.mediaChunks.push(t.data);case 1:case"end":return e.stop()}},e)}));return function(e){return t.apply(this,arguments)}}(),this.recorder.onstart=function(){document.fullscreenElement&&document.addEventListener("fullscreenchange",function(e){document.fullscreenElement||c.stopRecording()})},this.recorder.onstop=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t,n,r,i;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(c.timeEnd=Date.now(),n="mkv",!(t=null)===c.cancel){e.next=23;break}if(c.cancel=!1,r=new Blob(c.mediaChunks,{type:c.recorder.mimeType}),"Audio"==c.tab.mode)return n="mp3",e.t0=c,e.next=11,r.arrayBuffer();e.next=18;break;case 11:return e.t1=e.sent,e.next=14,e.t0.toMp3.call(e.t0,e.t1);case 14:i=e.sent,r=new Blob([i],{type:"audio/mp3"}),e.next=19;break;case 18:c.recorder.mimeType.includes("mp4")?n="mp4":c.recorder.mimeType.includes("webm")&&(n="webm");case 19:return i=getTitleExtFromUrl(c.tab.url),t=URL.createObjectURL(r),e.next=23,c.downloadBlobEx(t,"".concat(i.title,".").concat(n));case 23:c.recorder=void 0,c.mediaChunks.length=0,c.updateStatus("stoped"),setTimeout(function(){t&&URL.revokeObjectURL(t),window.close()},500);case 27:case"end":return e.stop()}},e)})),this.recorder.start(1e3),this.updateStatus("recording");case 67:case"end":return e.stop()}},e,this)})),function(e){return i.apply(this,arguments)})},{key:"stopRecording",value:(r=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var t,n;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:null!=this&&null!==(t=this.tab)&&void 0!==t&&t.cropTarget&&this.hideRegionSelector(),this.recorder&&(null===(n=this.recorder)||void 0===n||n.stop(),null===(n=this.recorder)||void 0===n||null===(n=n.stream)||void 0===n||null===(n=n.getTracks())||void 0===n||n.forEach(function(e){return e.stop()})),this.videoStream&&(this.videoStream.getTracks().forEach(function(e){return e.stop()}),this.videoStream=void 0),this.micStream&&(this.micStream.getTracks().forEach(function(e){return e.stop()}),this.micStream=void 0);case 4:case"end":return e.stop()}},e,this)})),function(){return r.apply(this,arguments)})},{key:"updateStatus",value:function(e){sendMessage({message:"update-status",tab:{id:this.tab.id,index:this.tab.index,url:this.tab.url},mode:this.tab.mode,status:{status:e,timeStart:this.timeStart,timeEnd:this.timeEnd}})}},{key:"loadFfmpeg",value:(n=_asyncToGenerator(_regeneratorRuntime().mark(function e(){var n=this;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return this.ffmpeg||(this.ffmpeg=new FFmpegWASM.FFmpeg,this.ffmpeg.on("log",function(e){e.type,e.message}),this.ffmpeg.on("progress",function(e){var t=e.progress;e.time;null!=n&&n.mergingKey&&n.handleProgress(null==n?void 0:n.mergingKey,Math.floor(100*t))})),e.next=3,this.ffmpeg.load({coreURL:"/js/core.js",classWorkerURL:"/js/worker.js"});case 3:case"end":return e.stop()}},e,this)})),function(){return n.apply(this,arguments)})},{key:"toMp3",value:(t=_asyncToGenerator(_regeneratorRuntime().mark(function e(t){var n;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.loadFfmpeg();case 2:return e.next=4,this.ffmpeg.writeFile("input_video.mp4",new Uint8Array(t));case 4:return e.next=6,this.ffmpeg.exec(["-i","input_video.mp4","-acodec","libmp3lame","audio.mp3"]);case 6:return e.next=8,this.ffmpeg.readFile("audio.mp3");case 8:return n=e.sent,e.next=11,this.ffmpeg.deleteFile("input_video.mp4");case 11:return e.next=13,this.ffmpeg.deleteFile("audio.mp3");case 13:return e.abrupt("return",n.buffer);case 14:case"end":return e.stop()}},e,this)})),function(e){return t.apply(this,arguments)})}]),e}(),recording=new Recording;