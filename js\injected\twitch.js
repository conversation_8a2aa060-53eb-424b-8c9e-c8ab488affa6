window.fechCallback=(e,t,i)=>{if(/gql.twitch.tv\/gql/i.test(e)&&"POST"===t?.method&&t?.body?.includes("VideoAccessToken_Clip"))return i.clone().json().then((e=>{try{let t=[],i=deepKeyValueSearch(e,"playbackAccessToken"),a={sig:i.signature,token:i.value},l=deepKeyValueSearch(e,"videoQualities");for(let i of l){const l={title:JSON.stringify(e).match(/"title":"([^"]+)"/)?.[1]||"",download_url:i.sourceURL+"?"+new URLSearchParams(a),quality:i.quality+"p",fps:i.frameRate,type:"video",ext:"mp4",webpage_url:location.href};t.push(l)}if(t.length){const e=new CustomEvent("add-video-links",{detail:{videoLinks:t}});window.dispatchEvent(e)}}catch(e){}}))};