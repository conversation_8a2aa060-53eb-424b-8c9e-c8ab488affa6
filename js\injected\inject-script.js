const inject_script=(e,t,r)=>{const{MutationObserver:s}=e.defaultView,a=["get-globals.js","set-global.js","get-el-obj-func.js","do-xhr-request.js","parse-throttled-url.js"],n=t=>{e.head||e.documentElement?(e.head||e.documentElement).appendChild(t):setTimeout((()=>n(t)),50)};return new Promise(((i,c)=>{const o=e.createElement("script");o.type="text/javascript",o.charset="utf-8",o.defer=!0,o.async=!0,((e,t)=>{if(!a.includes(t))throw new Error(`script with path ${t} doesn't declared in hashes table`);e.src=browser.runtime.getURL(`js/injected/targets/${t}`)})(o,t),o.setAttribute("data-params",JSON.stringify(r));const d=new s((e=>{e.forEach((e=>{if("attributes"===e.type&&e.attributeName&&"data-status"===e.attributeName){const t=e.target.getAttribute("data-status"),r=e.target.getAttribute("data-response"),s="undefined"===r?void 0:JSON.parse(r),a=e.target.getAttribute("data-type");switch(o.parentElement.removeChild(o),d.disconnect(),t){case"fulfilled":i(s);break;case"rejected":if(a&&"error"===a){const e=new Error;c(Object.assign(e,s))}else c(s);break;default:c(new Error("injectScript: unknown response"))}}}))}));d.observe(o,{attributes:!0}),n(o)}))};