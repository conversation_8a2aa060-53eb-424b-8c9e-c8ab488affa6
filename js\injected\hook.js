function deepKeyValueSearch(e,t,o=null){for(let r in e)if(e.hasOwnProperty(r)){if(r===t&&(null==o||e[r]===o))return e[r];if("object"==typeof e[r]&&null!==e[r]){var n=deepKeyValueSearch(e[r],t);if(void 0!==n)return n}}}if(void 0===window.fetchProxy){const e=window.fetch;window.fetchProxy=new Proxy(e,{apply(e,t,[o,n]){const r=e.apply(t,[o,n]);return window?.fechCallback&&r.then((e=>{try{window.fechCallback(o,n,e)}catch(e){}})),r}}),window.fetch=window.fetchProxy}if(void 0===window.xhrProxy){const e=XMLHttpRequest.prototype,t=e.open,o=e.send;XMLHttpRequest.prototype.open=new Proxy(t,{apply:(e,t,o)=>(t._method=o[0],t._url=o[1],e.apply(t,o))}),XMLHttpRequest.prototype.send=new Proxy(o,{apply(e,t,o){const n=t,r=n.onreadystatechange;return n.onreadystatechange=function(){if(4===n.readyState&&window?.xhrCallback)try{window.xhrCallback(n._url,n._method,o[0],n)}catch(e){}return r?.apply(this,arguments)},e.apply(n,o)}})}