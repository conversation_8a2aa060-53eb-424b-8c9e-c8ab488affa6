(()=>{async function e(e){try{new MutationObserver((e=>{e.forEach((e=>{e.addedNodes.forEach((e=>{"VIDEO"!==e.tagName&&"AUDIO"!==e.tagName||e.addEventListener("loadedmetadata",(()=>{if(e.src.startsWith("blob:"))!(function(e){const t=e.closest(".video-js");if(t?.player?.currentSources)if(sources=t?.player.currentSources(),source=t?.player.currentSource(),n=source.src,n?.match(/^https?:\/\/(?:[^\.]+\.)*?boomstream\.com/)){const e={title:source.name,duration:source.duration,code:source?.code,src:source.src,state:source.state,type:source.type,apiBase:t?.player?.bases?.api};window.dispatchEvent(new CustomEvent("add-boomstream-info",{detail:{info:e}}))}else window.dispatchEvent(new CustomEvent("videojs-sources",{detail:{sources:sources,source:source}}));var n})(e);else{const t={url:e.src,type:e.tagName.toLowerCase(),duration:e.duration};e.getAttribute("title")&&(t.title=e.getAttribute("title")),e.videoWidth&&(t.width=e.videoWidth),e.videoHeight&&(t.height=e.videoHeight),window.dispatchEvent(new CustomEvent("set-video-src",{detail:t}))}}))}))}))})).observe(e,{childList:!0,subtree:!0})}catch(e){}}void 0===window.originalAttachShadow&&(window.originalAttachShadow=Element.prototype.attachShadow,Element.prototype.attachShadow=new Proxy(originalAttachShadow,{apply(t,n,o){const r=Reflect.apply(t,n,o);return e(r),r}})),e(document.documentElement),void 0===window.originalAudio&&(window.originalAudio=Audio,Audio=class extends window.originalAudio{constructor(e){super(e),this.audioSrc(e)}get src(){return super.src}set src(e){super.src=e,this.audioSrc(e)}audioSrc=e=>{if(e){const t=new CustomEvent("set-audio-src",{detail:{src:e}});window.dispatchEvent(t)}}});const t=(e,t,n,o={})=>{const r=new CustomEvent("load-stream-src",{detail:{url:e,data:t,streamType:n,...o}});window.dispatchEvent(r)},n=new Map;function o(e){if(!e)return null;for(const[t,o]of n.entries())if(t&&e.includes(t)){clearTimeout(o.timer);const{data:e,streamType:r,extra:i}=o;return n.delete(t),{data:e,streamType:r,extra:i}}return null}const r=(e,o,r,i={})=>{if(!e)return void t(e,o,r,i);const s=setTimeout((()=>{const o=n.get(e);o&&(n.delete(e),t(e,o.data,o.streamType,o.extra))}),3e3);n.set(e,{data:o,streamType:r,extra:i,timer:s})},i=e=>{if(!e||"string"!=typeof e)return null;const t=Math.min(e.length,1e3),n=e.slice(0,t);return n.includes("#EXTM3U")?"hls":/\s*<MPD\s/.test(n)?"dash":null};function s(e){const t=[],n=/https?:\/\/.*\.m3u8/i,o=/https?:\/\/.*\.mpd/i;return(function e(r,s=""){if("string"==typeof r){const e=i(r);e?t.push({type:e,data:r}):(n.test(r)&&t.push({type:"HLS",url:r}),o.test(r)&&t.push({type:"DASH",url:r}))}else r&&"object"==typeof r&&Object.entries(r).forEach((([t,n])=>{e(n,s?`${s}.${t}`:t)}))})("string"==typeof e?JSON.parse(e):e),t}function a(e,t){try{const n=Object.getOwnPropertyDescriptor(window,e);if(!n){let n=window[e];return void Object.defineProperty(window,e,{configurable:!0,enumerable:!0,get:function(){return n},set:function(e){n=t(e)}})}if(!n.configurable){const n=window[e];if(n){const e=t(n);Object.getOwnPropertyNames(e).forEach((t=>{try{n[t]=e[t]}catch(e){}}))}return}const o=window[e];o&&(window[e]=t(o))}catch(e){}}function c(e){if("function"!=typeof e)return e;const t=e.prototype.loadSource;return e.prototype.loadSource=function(e){return r(e,null,"hls"),t.apply(this,arguments)},new Proxy(e,{construct(t,n){const o=new t(...n);return o.loadSource=e.prototype.loadSource.bind(o),o}})}window.fechCallback||window.xhrCallback||(window.fechCallback=(e,n,r)=>{const a=e?.url||e,c=o(a);if(c)return void r.clone().text().then((e=>{t(a,e,c.streamType,c.extra)}));const u=r.headers.get("Content-Type")?.toLowerCase();u&&["application/x-mpegurl","application/vnd.apple.mpegurl","audio/mpegurl","audio/x-mpegurl","application/dash+xml","video/vnd.mpeg.dash.mpd","text/plain","application/xml","application/json"].some((e=>u?.includes(e)))&&r.clone().text().then((e=>{try{const n=i(e);n&&(e.trim().startsWith("{")||e.trim().startsWith("[")?setTimeout((()=>{s(e).forEach((e=>{t(e?.url||null,e?.data||null,e.type)}))}),10):t(a,e,n))}catch(e){}}))},window.xhrCallback=(e,n,r,a)=>{try{const n=o(e);if(n){const o="json"===a.responseType?JSON.stringify(a.response):a.responseText;return void t(e,o,n.streamType,n.extra)}const r=a.getResponseHeader("Content-Type")?.toLowerCase().split(";")[0];if(r&&["application/x-mpegurl","application/vnd.apple.mpegurl","audio/mpegurl","audio/x-mpegurl","application/dash+xml","video/vnd.mpeg.dash.mpd","text/plain","application/xml","application/json"].some((e=>r?.includes(e)))&&["text","document","json",""].includes(a.responseType)){const e="json"===a.responseType?JSON.stringify(a.response):a.responseText,n=i(e);n&&(e.trim().startsWith("{")||e.trim().startsWith("[")?setTimeout((()=>{s(e).forEach((e=>{t(e?.url||null,e?.data||null,e.type)}))}),10):t(a.responseURL,e,n))}}catch(e){}}),a("Hls",c),a("hls",c),a("dashjs",(function(e){if("object"!=typeof e)return e;function t(e){return new Proxy(e,{apply:function(e,t,n){let o=Reflect.apply(e,t,n);return o.create=new Proxy(o.create,{apply:function(e,t,n){let o=Reflect.apply(e,t,n);return o.attachSource=new Proxy(o.attachSource,{apply:function(e,t,n){const o="string"==typeof n[0]?n[0]:n[0]?.url??n[0]?.originalUrl;return o&&r(o,null,"dash"),Reflect.apply(e,t,n)}}),o}}),o}})}return"function"==typeof e.MediaPlayer&&(e.MediaPlayer=t(e.MediaPlayer)),Object.defineProperty(e,"MediaPlayer",{configurable:!0,enumerable:!0,get:function(){return this._MediaPlayer},set:function(e){this._MediaPlayer=t(e)}}),e})),a("jwplayer",(function(e){return"function"!=typeof e?e:new Proxy(e,{apply(e,t,n){const o=e.apply(t,n);return o?.setup&&(o.setup=new Proxy(o.setup,{apply(e,t,n){const[o]=n;return(async function(e){const t=new CustomEvent("handle-jwplayer-setup",{detail:{config:e}});window.dispatchEvent(t)})(o),e.apply(t,n)}})),o}})}));const u=URL.createObjectURL;URL.createObjectURL=new Proxy(u,{apply(e,t,n){const o=e.apply(t,n);try{const[e]=n;if(e instanceof Blob){const t=1048576;if(e.size>t)return o;const n=(e.type||"").toLowerCase();["application/x-mpegurl","application/vnd.apple.mpegurl","audio/mpegurl","audio/x-mpegurl","application/dash+xml","video/vnd.mpeg.dash.mpd","text/plain","text/xml","application/xml","application/octet-stream","binary/octet-stream",""].includes(n)&&e.text().then((e=>{const t=i(e);t&&r(o,e,t)})).catch((e=>{}))}}catch(e){}return o}}),a("Clappr",(function(e){if("object"!=typeof e||!e.Player)return e;function t(e,t,n){const o="string"==typeof e?{source:e}:e,i=o.mimeType||t.mimeType,s=(function(e,t){if(t){if(t.includes("mpegurl")||t.includes("x-mpegurl"))return"hls";if(t.includes("dash"))return"dash"}if("string"==typeof e){if(e.includes(".m3u8"))return"hls";if(e.includes(".mpd"))return"dash"}return null})(o.source,i),a={width:n.width,height:n.height,poster:n.poster,mimeType:i};s?r(o.source,null,s,a):o.source&&window.dispatchEvent(new CustomEvent("set-video-src",{detail:{url:o.source,type:"video",...a}}))}const n=e.Player;return e.Player=new Proxy(n,{construct(e,n){const[o]=n;if(o){const e={width:o.width,height:o.height,poster:o.poster};o.source&&t(o.source,o,e),o.sources&&o.sources.forEach((n=>{t(n,o,e)}))}return new e(...n)}}),e}))})();