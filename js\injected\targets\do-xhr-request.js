(()=>{const t=document.currentScript;try{const{url:e,method:s,headers:a,body:r}=JSON.parse(t.getAttribute("data-params")),u=new XMLHttpRequest;u.open(s,e,!0),Object.keys(a).forEach((t=>u.setRequestHeader(t,a[t]))),u.onload=()=>{200===u.status||206===u.status?(t.setAttribute("data-response",JSON.stringify(u.response)),t.setAttribute("data-status","fulfilled")):(t.setAttribute("data-response","no content"),t.setAttribute("data-status","rejected"))},u.onerror=e=>{t.setAttribute("data-response",JSON.stringify(e)),t.setAttribute("data-status","rejected")},u.ontimeout=()=>{t.setAttribute("data-response","timeout"),t.setAttribute("data-status","rejected")},r?u.send(r):u.send()}catch(e){t.setAttribute("data-response",JSON.stringify(e)),t.setAttribute("data-status","rejected")}})();